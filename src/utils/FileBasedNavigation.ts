import { lazy, ComponentType } from 'react';
import { LEFT_MENU_CONFIGURATION, pathToFolderName, type MenuConfigurationItem } from '../config/LeftMenuConfiguration';

/**
 * Represents a route configuration for file-based navigation
 */
export interface FileBasedRoute {
  /** The route path (without leading slash for nested routes) */
  path?: string;
  /** Whether this is an index route */
  index?: boolean;
  /** The lazy-loaded React component for this route */
  Component: ComponentType<Record<string, never>>;
}

/**
 * Represents route discovery information for debugging
 */
export interface RouteDiscoveryInfo {
  /** Menu item title */
  menuItem: string;
  /** Route path */
  path: string;
  /** Corresponding folder name */
  folderName: string;
  /** Expected file path */
  expectedFile: string;
  /** Whether the file exists */
  exists: boolean;
}

/**
 * Validation result for route discovery
 */
export interface RouteValidationResult {
  /** Whether all routes are valid */
  valid: boolean;
  /** Array of missing file paths */
  missing: string[];
}

/**
 * File-Based Navigation System
 *
 * This system automatically discovers and creates routes based on:
 * 1. The LEFT_MENU_CONFIGURATION array (defines what routes should exist)
 * 2. The actual file structure in src/pages/
 *
 * Convention:
 * - LEFT_MENU_CONFIGURATION path: '/dashboard' -> src/pages/dashboard/index.tsx
 * - LEFT_MENU_CONFIGURATION path: '/candidates' -> src/pages/candidates/index.tsx
 * - LEFT_MENU_CONFIGURATION path: '/vendor-jobs' -> src/pages/vendor-jobs/index.tsx
 *
 * Features:
 * - Automatic route discovery using Vite's import.meta.glob
 * - Lazy loading for optimal performance
 * - Support for multiple file extensions (.js, .jsx, .ts, .tsx)
 * - Environment-agnostic operation
 */

// Type for module loader function
type ModuleLoader = () => Promise<{ default: ComponentType<unknown> }>;

// Cache for page modules to avoid repeated glob calls
let cachedPageModules: Record<string, ModuleLoader> | null = null;

/**
 * Get page modules with caching for performance
 */
function getPageModules(): Record<string, ModuleLoader> {
  if (cachedPageModules === null) {
    cachedPageModules = import.meta.glob('/src/pages/*/index.{js,jsx,ts,tsx}', { eager: false }) as Record<string, ModuleLoader>;
  }
  return cachedPageModules;
}

/**
 * Supported file extensions for page components
 */
const SUPPORTED_EXTENSIONS = ['tsx', 'ts', 'jsx', 'js'] as const;

/**
 * Find the module loader for a given folder name
 */
function findModuleLoader(folderName: string): ModuleLoader | null {
  const pageModules = getPageModules();

  for (const extension of SUPPORTED_EXTENSIONS) {
    const moduleKey = `/src/pages/${folderName}/index.${extension}`;
    const moduleLoader = pageModules[moduleKey];
    if (moduleLoader) {
      return moduleLoader;
    }
  }

  return null;
}

/**
 * Create a route configuration from a menu item
 */
function createRouteFromMenuItem(menuItem: MenuConfigurationItem): FileBasedRoute | null {
  const folderName = pathToFolderName(menuItem.path);
  const moduleLoader = findModuleLoader(folderName);

  if (!moduleLoader) {
    return null;
  }

  // Create lazy-loaded component
  const Component = lazy(moduleLoader);

  // Create route configuration
  if (menuItem.path === '/') {
    return {
      index: true,
      Component
    };
  } else {
    return {
      path: menuItem.path.substring(1), // Remove leading slash for nested routes
      Component
    };
  }
}

/**
 * Generate routes based on LEFT_MENU_CONFIGURATION and file structure
 * Uses Vite's import.meta.glob for dynamic discovery with performance optimizations
 */
export function generateFileBasedRoutes(): FileBasedRoute[] {
  const routes: FileBasedRoute[] = [];

  // Process each menu item in the exact order defined in LEFT_MENU_CONFIGURATION
  for (const menuItem of LEFT_MENU_CONFIGURATION) {
    const route = createRouteFromMenuItem(menuItem);
    if (route) {
      routes.push(route);
    }
  }

  return routes;
}

/**
 * Get discovered routes for debugging and validation
 */
export function getDiscoveredRoutes(): RouteDiscoveryInfo[] {
  return LEFT_MENU_CONFIGURATION.map((menuItem) => {
    const folderName = pathToFolderName(menuItem.path);
    const moduleLoader = findModuleLoader(folderName);

    return {
      menuItem: menuItem.title,
      path: menuItem.path,
      folderName,
      expectedFile: `/src/pages/${folderName}/index.tsx`, // Primary expected file for display
      exists: moduleLoader !== null
    };
  });
}

/**
 * Log all discovered routes and missing files for debugging
 * This function provides detailed information about route discovery status
 */
export function logDiscoveredRoutes(): void {
  const routes = getDiscoveredRoutes();

  // Existing routes (console removed as per your note)
  routes.filter(route => route.exists).forEach((route) => {
    // `${route.menuItem}: ${route.path} -> ${route.expectedFile}`
  });

  // Missing routes
  const missing = routes.filter(route => !route.exists);
  if (missing.length > 0) {
    missing.forEach((route) => {
      console.warn(`${route.menuItem}: ${route.path} -> ${route.expectedFile} (FILE NOT FOUND)`);
    });
  }
}


/**
 * Validate that all menu items have corresponding files
 */
export function validateRoutes(): { valid: boolean; missing: string[] } {
  const routes = getDiscoveredRoutes();
  const missing = routes.filter(route => !route.exists).map(route => route.expectedFile);
  
  return {
    valid: missing.length === 0,
    missing
  };
}
