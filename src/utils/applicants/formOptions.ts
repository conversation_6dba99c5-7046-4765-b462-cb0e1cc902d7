import { ApiSelectOption } from '@/types/applicants';




export const functionOptions: ApiSelectOption[] = [
  { value: 'd7f8a90a-27b6-4b5c-aef4-042c9a7ef1e3', key: 'Engineering' },
  { value: '6d5efed4-1b2a-4227-91f8-72b8f409e0ac', key: 'Product Management' },
  { value: 'c0e72b20-0177-4cfa-96f4-c3de29c4c7c4', key: 'Design' },
  { value: 'bde8c7f9-68e3-4a42-91f9-d8c0b0fce3b1', key: 'Marketing' },
  { value: '9bb65e08-62e5-49a2-81c1-b6d1cb8f5df8', key: 'Sales' },
  { value: 'ff9cfcd7-dbd4-48da-96e2-4e65d07b7e41', key: 'Operations' },
  { value: '4f7d04b6-1c48-4cc1-8881-91a34cf47bbf', key: 'Finance' },
  { value: '93d05a14-09e4-49eb-8b2f-cda620e31e7d', key: 'Human Resources' },
  { value: 'c0c0bfb7-c68a-4a67-9e90-7f7c312d24e6', key: 'Legal' },
  { value: 'ac92d77b-72cc-4c61-b4f4-5325b0c4e5b6', key: 'Customer Success' },
  { value: 'dff7cf84-f9d1-4b0a-bd38-d4d7e88f4af2', key: 'Data & Analytics' },
  { value: 'f9f67a3e-5087-4c63-9f0d-d688fc96c7a7', key: 'Security' },
  { value: 'd0c47f52-c3c2-487d-85c2-6db9a4f36c52', key: 'Quality Assurance' },
  { value: '0f3d7723-74d5-4c42-9ad5-84e20a09f4e2', key: 'DevOps' },
  { value: '8a5e1b78-b1d4-46c6-8e5f-5a9b9c372416', key: 'Research & Development' },
].sort((a, b) => a.key.localeCompare(b.key));


export const vendorContactOptions: ApiSelectOption[] = [
  { value: 'c9272f55-1a8b-45de-9d1f-40c5cf7f7f67', key: 'Vendor Contact 1' },
  { value: '6d5872c2-b6c5-4d8b-b85d-6577f5b18e02', key: 'Vendor Contact 2' }
];

export const ownershipOptions: ApiSelectOption[] = [
  { value: '3e4f8b9c-0c7d-41f1-9f1e-11f39d302e5b', key: 'Prudhvi Kanmuri' },
  { value: 'e6b2b0e0-1f8a-41aa-9d9b-8d9e49f8d6d4', key: 'Other Owner' },
];

