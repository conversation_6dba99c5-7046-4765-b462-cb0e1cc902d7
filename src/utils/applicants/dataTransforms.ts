import { generateUUID, mapUuidToName } from '@/lib/utils';
import { API_CONSTANTS } from '@/constants/apiConstants';
import { Applicant, Technology, ApplicantGroup, DocumentDetails } from '@/types';
import { ApplicantFormData } from '@/components/applicants/ApplicantFormWizard';
import { ApplicantCardData } from '@/components/applicants/ui';
import {
  ApplicantResponseDTO,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  FileUpload,
  GlobalListValueDTO
} from '@/types/applicants';

/**
 * Utility functions for transforming applicant data between different formats
 * 
 * Document Structure:
 * - Form layer uses DocumentDetails with fileIndex (simple index), file (File object), title, type (UUID), comments
 * - API payload maps to: fileIndex, title, comments, category (fixed UUID), sub_category (type UUID)
 * - Files are structured as: {fileIndex, file, title, comments, type}
 */

/**
 * Maps status UUID to status name using listValues
 * @param statusId - The status UUID from API
 * @param listValues - Array of GlobalListValueDTO from authContext
 * @returns The mapped status name, or 'applied' as fallback
 */
export const mapStatusIdToName = (
  statusId: string | undefined,
  listValues: GlobalListValueDTO[]
): string => {
  if (!statusId) return 'applied';
  
  // First try to map using the utility function
  const mappedName = mapUuidToName(statusId, listValues);
  
  // If not a valid status, return default
  return mappedName;
};

/**
 * Convert form data to applicant object (legacy)
 */
export const convertFormDataToApplicant = (
  formData: ApplicantFormData,
  existingId?: string
): Applicant => {
  const id = existingId || generateUUID();
  const personalInfo = formData.personalInfo;
  const professionalInfo = formData.professionalInfo;

  // Combine personal and professional info for backward compatibility
  const personalDetails = {
    ...personalInfo,
    ...professionalInfo,
    // Fix type issues
    applicantGroup: Array.isArray(professionalInfo.applicantGroup) 
      ? professionalInfo.applicantGroup 
      : professionalInfo.applicantGroup ? [professionalInfo.applicantGroup] : undefined,
    technology: Array.isArray(professionalInfo.technology) 
      ? professionalInfo.technology 
      : professionalInfo.technology ? [professionalInfo.technology] : undefined,
  };

  return {
    id,
    personalDetails,
    documents: formData.documents,
    workExperience: formData.workExperience,
    educationDetails: formData.educationDetails,
    certifications: formData.certifications,
    employerDetails: formData.employerDetails,
    languages: formData.languages,
    // Legacy fields for backward compatibility
    firstName: personalInfo.firstName || '',
    lastName: personalInfo.lastName || '',
    email: personalInfo.email ?? '',
    phone: personalInfo.homePhone ?? personalInfo.mobilePhone ?? '',
    position: professionalInfo.jobTitle ?? 'Not specified',
    department: 'General',
    applicationDate: new Date().toISOString().split('T')[0] as string,
    status: formData?.professionalInfo?.applicantStatus ?? 'applied',
    experience: parseInt(professionalInfo.experienceYears ?? '0', 10) || 0,
    skills: professionalInfo.skills ?? [],
    avatar: undefined,
  };
};

/**
 * Convert form data to API request format
 */
export const convertFormDataToApiRequest = (
  formData: ApplicantFormData,
  existingId?: string
): CreateApplicantRequest => {
  const personalInfo = formData.personalInfo;
  const professionalInfo = formData.professionalInfo;



  const result = {
    id: existingId,
    firstName: personalInfo.firstName ?? '',
    lastName: personalInfo.lastName ?? '',
    middleName: personalInfo.middleName,
    preferredName: personalInfo.nickName,
    email: personalInfo.email ?? '',
    dateOfBirth: personalInfo.dateOfBirth,
    contactInfo: {
      id: generateUUID(),
      homePhone: personalInfo.homePhone,
      mobilePhone: personalInfo.mobilePhone,
      workPhone: personalInfo.workPhone,
      otherPhone: personalInfo.otherPhone,
      alternateEmail: personalInfo.alternateEmail,
    },
    socialProfiles: {
      id: generateUUID(),
      linkedin: personalInfo.linkedinProfileUrl,
      facebook: personalInfo.facebookProfileUrl,
      twitter: personalInfo.twitterProfileUrl,
      skypeId: personalInfo.skypeId,
      videoReference: personalInfo.videoReference,
    },
    addresses: (personalInfo.country || personalInfo.state || personalInfo.city || personalInfo.zipCode) ? {
      id: generateUUID(),
      country: personalInfo.country || '',
      state: personalInfo.state || '',
      city: personalInfo.city || '',
      zipcode: personalInfo.zipCode || '',
      isPrimary: true,
    } : undefined,
    employerDetails: formData.employerDetails ? {
      id: generateUUID(),
      selectionType: formData.employerDetails.isNew ? 'NEW' : 'ADD_FROM_EXISTING_VENDOR_CONTACT_RECORDS',
      vendorContact: formData.employerDetails.vendorContact,
      firstName: formData.employerDetails.firstName || '',
      lastName: formData.employerDetails.lastName || '',
      employerName: formData.employerDetails.employerName || '',
      officeNumber: formData.employerDetails.officeNumber,
      emailId: formData.employerDetails.emailId || '',
      mobileNumber: formData.employerDetails.mobileNumber,
      status: formData.employerDetails.status ?? true,
    } : undefined,
    workExperience: formData.workExperience?.map(exp => ({
      id: generateUUID(),
      companyName: exp.companyName,
      jobTitle: exp.jobTitle,
      startDate: exp.startDate,
      endDate: exp.endDate,
      location: exp.location || '',
      employeeType: exp.employeeType || '',
      responsibilities: exp.responsibilities,
      isActive: exp.isActive !== false, // Include isActive field, default to true
    })),
    education: formData.educationDetails?.map(edu => ({
      id: generateUUID(),
      schoolName: edu.schoolName,
      degree: edu.degree,
      yearCompleted: edu.yearCompleted || '',
      minorStudy: edu.minorStudy,
      majorStudy: edu.majorStudy,
      gpa: edu.gpa,
      country: edu.country || '',
      state: edu.state || '',
      city: edu.city || '',
      isHigherEducation: edu.higherEducation ?? false,
      isActive: edu.isActive !== false, // Include isActive field, default to true
    })),
    certifications: formData.certifications?.map(cert => ({
      id: generateUUID(),
      certification: cert.certification,
      yearCompleted: cert.yearCompleted,
      comments: cert.comments,
      isActive: cert.isActive !== false, // Include isActive field, default to true
    })),
    additionalInfo: {
      id: generateUUID(),
      experience: {
        id: generateUUID(),
        years: professionalInfo.experienceYears || '',
        months: professionalInfo.experienceMonths || '',
      },
      jobTitle: professionalInfo.jobTitle,
      expectedPay: professionalInfo.expectedPayAmount ? {
        id: generateUUID(),
        currency: professionalInfo.expectedPayCurrency || '',
        amount: professionalInfo.expectedPayAmount,
      } : undefined,
      currentCTC: professionalInfo.currentCtcAmount ? {
        id: generateUUID(),
        currency: professionalInfo.currentCtcCurrency || '',
        amount: professionalInfo.currentCtcAmount,
      } : undefined,
      skills: professionalInfo.skills,
      primarySkills: professionalInfo.primarySkills,
      technology: Array.isArray(professionalInfo.technology) ? professionalInfo.technology[0] : professionalInfo.technology,
      industry: Array.isArray(professionalInfo.industry) ? professionalInfo.industry : (professionalInfo.industry ? [professionalInfo.industry] : []),
      function: Array.isArray(professionalInfo.function) ? professionalInfo.function : (professionalInfo.function ? [professionalInfo.function] : []),
      taxTerms: professionalInfo.taxTerms,
      noticePeriod: professionalInfo.noticePeriod,
      currentCompany: professionalInfo.currentCompany,
      applicantStatus: professionalInfo.applicantStatus,
      ownership: professionalInfo.ownership,
      relocation: professionalInfo.relocation === 'Yes',
      additionalComments: professionalInfo.additionalComments,
      panCardNumber: professionalInfo.panCardNumber,
      aadhaarNumber: professionalInfo.aadharNumber,
      gpa: professionalInfo.gpa,
      gender: professionalInfo.gender,
      raceEthnicity: professionalInfo.raceEthnicity,
      veteranStatus: professionalInfo.veteranStatus,
      veteranType: professionalInfo.veteranType,
      disability: professionalInfo.disability,
      workAuthorization: personalInfo.workAuthorization,
      clearance: personalInfo.clearance === 'Yes',
      source: professionalInfo.source,
      referredBy: professionalInfo.referredBy,
      ssn: personalInfo.ssn, // Moved from root level to additionalInfo
    },
    documents: formData.documents?.map((doc, index) => ({
      fileIndex: typeof doc.fileIndex === 'number' ? doc.fileIndex : index, // Preserve existing fileIndex or use sequential index
      id: doc.id || generateUUID(), // Use existing ID or generate new UUID
      title: doc.title,
      category: API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT, // Fixed category ID
      subcategory: doc.subcategory, // Selected document type from DocumentTreeSelectInput
      comments: doc.comments,
      filePath: doc.filePath || undefined, // Preserve existing filePath or set as undefined for new documents
      isActive: doc.isActive !== false, // Mark document as active unless explicitly false
    })),
  };



  return result;
};

/**
 * Convert API applicant response to legacy applicant format
 */
export const convertApiApplicantToLegacy = (
  apiApplicant: ApplicantDetailResponse,
  listValues: GlobalListValueDTO[] = []
): Applicant => {
  const personalDetails = {
    firstName: apiApplicant.firstName,
    lastName: apiApplicant.lastName,
    middleName: apiApplicant.middleName,
    nickName: apiApplicant.preferredName,
    email: apiApplicant.email,
    alternateEmail: apiApplicant.contactInfo?.alternateEmail,
    homePhone: apiApplicant.contactInfo?.homePhone,
    mobilePhone: apiApplicant.contactInfo?.mobilePhone,
    workPhone: apiApplicant.contactInfo?.workPhone,
    otherPhone: apiApplicant.contactInfo?.otherPhone,
    dateOfBirth: apiApplicant.dateOfBirth,
    ssn: apiApplicant.additionalInfo?.ssn,
    skypeId: apiApplicant.socialProfiles?.skypeId,
    linkedinProfileUrl: apiApplicant.socialProfiles?.linkedin,
    facebookProfileUrl: apiApplicant.socialProfiles?.facebook,
    twitterProfileUrl: apiApplicant.socialProfiles?.twitter,
    videoReference: apiApplicant.socialProfiles?.videoReference,
    workAuthorization: apiApplicant.additionalInfo?.workAuthorization,
    clearance: apiApplicant.additionalInfo?.clearance ? 'Yes' : 'No' as 'Yes' | 'No',
    address: (() => {
      const address = Array.isArray(apiApplicant.addresses) ? apiApplicant.addresses[0] : apiApplicant.addresses;
      return address ? `${address.city}, ${address.state}` : undefined;
    })(),
    city: (() => {
      const address = Array.isArray(apiApplicant.addresses) ? apiApplicant.addresses[0] : apiApplicant.addresses;
      return address?.city;
    })(),
    country: (() => {
      const address = Array.isArray(apiApplicant.addresses) ? apiApplicant.addresses[0] : apiApplicant.addresses;
      return address?.country;
    })(),
    state: (() => {
      const address = Array.isArray(apiApplicant.addresses) ? apiApplicant.addresses[0] : apiApplicant.addresses;
      return address?.state;
    })(),
    zipCode: (() => {
      const address = Array.isArray(apiApplicant.addresses) ? apiApplicant.addresses[0] : apiApplicant.addresses;
      return address?.zipcode;
    })(),
    // Professional info
    source: apiApplicant.additionalInfo?.source,
    experienceYears: apiApplicant.additionalInfo?.experience?.years,
    experienceMonths: apiApplicant.additionalInfo?.experience?.months,
    referredBy: apiApplicant.additionalInfo?.referredBy,
    applicantStatus: apiApplicant.additionalInfo?.applicantStatus,
    ownership: apiApplicant.additionalInfo?.ownership,
    jobTitle: apiApplicant.additionalInfo?.jobTitle,
    expectedPayCurrency: apiApplicant.additionalInfo?.expectedPay?.currency,
    expectedPayAmount: apiApplicant.additionalInfo?.expectedPay?.amount,
    currentCtcCurrency: apiApplicant.additionalInfo?.currentCTC?.currency,
    currentCtcAmount: apiApplicant.additionalInfo?.currentCTC?.amount,
    additionalComments: apiApplicant.additionalInfo?.additionalComments,
    relocation: apiApplicant.additionalInfo?.relocation ? 'Yes' : 'No' as 'Yes' | 'No',
    skills: apiApplicant.additionalInfo?.skills || [],
    primarySkills: apiApplicant.additionalInfo?.primarySkills || [],
    technology: apiApplicant.additionalInfo?.technology ? [apiApplicant.additionalInfo.technology] : [],
    taxTerms: apiApplicant.additionalInfo?.taxTerms,
    noticePeriod: apiApplicant.additionalInfo?.noticePeriod,
    industry: Array.isArray(apiApplicant.additionalInfo?.industry) ? apiApplicant.additionalInfo.industry : (apiApplicant.additionalInfo?.industry ? [apiApplicant.additionalInfo.industry] : []),
    currentCompany: apiApplicant.additionalInfo?.currentCompany,
    function: Array.isArray(apiApplicant.additionalInfo?.function) ? apiApplicant.additionalInfo.function : (apiApplicant.additionalInfo?.function ? [apiApplicant.additionalInfo.function] : []),
    panCardNumber: apiApplicant.additionalInfo?.panCardNumber,
    gpa: apiApplicant.additionalInfo?.gpa,
    aadharNumber: apiApplicant.additionalInfo?.aadhaarNumber,
    gender: apiApplicant.additionalInfo?.gender,
    raceEthnicity: apiApplicant.additionalInfo?.raceEthnicity,
    veteranStatus: apiApplicant.additionalInfo?.veteranStatus,
    veteranType: apiApplicant.additionalInfo?.veteranType,
    disability: apiApplicant.additionalInfo?.disability,
  };

  return {
    id: apiApplicant.id,
    personalDetails,
    documents: apiApplicant.documents?.map((doc, index) => ({
      id: doc.id || generateUUID(), // Use existing id or generate new one
      fileIndex: typeof doc.fileIndex === 'number' ? doc.fileIndex : index, // Use existing fileIndex as number or generate index
      title: doc.title,
      category: doc.category,
      subcategory: doc.subcategory,
      comments: doc.comments || '',
      filePath: doc.filePath, // Add filePath from API
      resumeVisibility: '', // Add missing optional property
      isActive: doc.isActive, // Preserve isActive field from API
    })) || [],
    workExperience: apiApplicant.workExperience?.map(exp => ({
      id: exp.id || generateUUID(),
      companyName: exp.companyName,
      jobTitle: exp.jobTitle,
      startDate: exp.startDate,
      endDate: exp.endDate,
      responsibilities: exp.responsibilities || '',
      location: exp.location,
      employeeType: exp.employeeType,
      isActive: exp.isActive !== false, // Preserve isActive field, default to true if not specified
    })) || [],
    educationDetails: apiApplicant.education?.map(edu => ({
      id: edu.id || generateUUID(),
      schoolName: edu.schoolName,
      degree: edu.degree,
      yearCompleted: edu.yearCompleted,
      majorStudy: edu.majorStudy,
      minorStudy: edu.minorStudy,
      gpa: edu.gpa,
      country: edu.country,
      state: edu.state,
      city: edu.city,
      higherEducation: edu.isHigherEducation,
      isActive: edu.isActive !== false, // Preserve isActive field, default to true if not specified
    })) || [],
    certifications: apiApplicant.certifications?.map(cert => ({
      id: cert.id || generateUUID(),
      certification: cert.certification,
      yearCompleted: cert.yearCompleted,
      comments: cert.comments,
      isActive: cert.isActive !== false, // Preserve isActive field, default to true if not specified
    })) || [],
    languages: [], // Not in API response, keeping empty for compatibility
    employerDetails: (() => {
      const employerDetails = apiApplicant.employerDetails;
      const employer = Array.isArray(employerDetails) ? employerDetails[0] : employerDetails;
      return employer ? {
        isNew: employer.selectionType === 'NEW',
        addFromExistingVendor: employer.selectionType === 'ADD_FROM_EXISTING_VENDOR_CONTACT_RECORDS',
        vendorContact: employer.vendorContact,
        firstName: employer.firstName,
        lastName: employer.lastName,
        employerName: employer.employerName,
        officeNumber: employer.officeNumber,
        emailId: employer.emailId,
        mobileNumber: employer.mobileNumber,
        status: employer.status,
      } : {
        isNew: true,
        addFromExistingVendor: false,
      };
    })(),
    // Legacy fields
    firstName: apiApplicant.firstName,
    lastName: apiApplicant.lastName,
    email: apiApplicant.email,
    phone: apiApplicant.contactInfo?.mobilePhone || apiApplicant.contactInfo?.homePhone || '',
    position: apiApplicant.additionalInfo?.jobTitle || 'Not specified',
    department: 'General',
    applicationDate: apiApplicant.applicationDate || '',
    status: mapStatusIdToName(apiApplicant?.status, listValues),
    experience: parseInt(apiApplicant.additionalInfo?.experience?.years || '0') || 0,
    skills: apiApplicant.additionalInfo?.skills || [],
    avatar: undefined,
    isActive: apiApplicant.isActive ?? true,
  };
};

/**
 * Convert API response DTO to legacy applicant format for list display
 */
export const convertApiResponseDTOToLegacy = (
  dto: ApplicantResponseDTO,
  listValues: GlobalListValueDTO[] = []
): Applicant => {
  return {
    id: dto.id,
    personalDetails: {
      firstName: dto.firstName,
      lastName: dto.lastName,
      email: dto.email,
    },
    documents: [], // Empty documents array with proper type
    workExperience: [],
    educationDetails: [],
    certifications: [],
    languages: [],
    employerDetails: {
      isNew: true,
      addFromExistingVendor: false,
    },
    // Legacy fields
    firstName: dto.firstName,
    lastName: dto.lastName,
    email: dto.email,
    phone: '',
    position: dto.summary?.currentTitle || 'Not specified',
    department: 'General',
    applicationDate: dto.applicationDate,
    status: mapStatusIdToName(dto.status, listValues),
    experience: dto.summary?.experienceYears || 0,
    skills: dto.summary?.primarySkills || [],
    avatar: undefined,
    isActive: dto?.isActive ? true : false, // Default to true for list items since DTO doesn't have isActive
  };
};

/**
 * Convert candidate to form data for editing (legacy function)
 */
export const convertCandidateToFormData = (candidate: Applicant): ApplicantFormData => {
  const personalDetails = candidate.personalDetails ?? {};
  
  return {
    personalInfo: {
      firstName: candidate.firstName ?? personalDetails.firstName ?? '',
      lastName: candidate.lastName ?? personalDetails.lastName ?? '',
      middleName: personalDetails.middleName ?? '',
      nickName: personalDetails.nickName ?? '',
      email: candidate.email ?? personalDetails.email ?? '',
      alternateEmail: personalDetails.alternateEmail ?? '',
      homePhone: personalDetails.homePhone ?? '',
      mobilePhone: personalDetails.mobilePhone ?? candidate.phone ?? '',
      workPhone: personalDetails.workPhone ?? '',
      otherPhone: personalDetails.otherPhone ?? '',
      dateOfBirth: personalDetails.dateOfBirth ?? '',
      ssn: personalDetails.ssn ?? '',
      skypeId: personalDetails.skypeId ?? '',
      linkedinProfileUrl: personalDetails.linkedinProfileUrl ?? '',
      facebookProfileUrl: personalDetails.facebookProfileUrl ?? '',
      twitterProfileUrl: personalDetails.twitterProfileUrl ?? '',
      videoReference: personalDetails.videoReference ?? '',
      workAuthorization: personalDetails.workAuthorization ?? '',
      workAuthorizationExpiry: personalDetails.workAuthorizationExpiry ?? '',
      clearance: personalDetails.clearance ?? 'No',
      address: personalDetails.address ?? '',
      city: personalDetails.city ?? '',
      country: personalDetails.country ?? '',
      state: personalDetails.state ?? '',
      zipCode: personalDetails.zipCode ?? '',
    },
    professionalInfo: {
      source: personalDetails.source ?? '',
      referredBy: personalDetails.referredBy ?? '',
      experienceYears: personalDetails.experienceYears ?? candidate.experience?.toString() ?? '',
      experienceMonths: personalDetails.experienceMonths ?? '',
      jobTitle: personalDetails.jobTitle ?? candidate.position ?? '',
      expectedPayCurrency: personalDetails.expectedPayCurrency ?? '',
      expectedPayAmount: personalDetails.expectedPayAmount ?? '',
      currentCtcCurrency: personalDetails.currentCtcCurrency ?? '',
      currentCtcAmount: personalDetails.currentCtcAmount ?? '',
      skills: personalDetails.skills ?? candidate.skills ?? [],
      primarySkills: personalDetails.primarySkills ?? [],
      technology: Array.isArray(personalDetails.technology) ? personalDetails.technology[0] as Technology : personalDetails.technology,
      industry: personalDetails.industry ?? [],
      function: personalDetails.function ?? [],
      taxTerms: personalDetails.taxTerms ?? '',
      noticePeriod: personalDetails.noticePeriod ?? '',
      currentCompany: personalDetails.currentCompany ?? '',
      applicantStatus: personalDetails.applicantStatus ?? '',
      applicantGroup: Array.isArray(personalDetails.applicantGroup) ? personalDetails.applicantGroup[0] as ApplicantGroup : personalDetails.applicantGroup,
      ownership: personalDetails.ownership ?? '',
    },
    documents: candidate.documents?.map((doc, index) => ({
      ...doc,
      id: doc.id || generateUUID(), // Ensure id exists
      fileIndex: typeof doc.fileIndex === 'number' ? doc.fileIndex : index, // Ensure fileIndex exists as number
    })) ?? [],
    workExperience: candidate.workExperience ?? [],
    educationDetails: candidate.educationDetails ?? [],
    certifications: candidate.certifications ?? [],
    employerDetails: candidate.employerDetails ?? {
      isNew: true,
      addFromExistingVendor: false,
      vendorContact: '',
      firstName: '',
      lastName: '',
      employerName: '',
      officeNumber: '',
      emailId: '',
      extension: '',
      mobileNumber: '',
      status: true,
    },
    languages: candidate.languages ?? [],
  };
};

/**
 * Convert applicant to card data for display
 */
export const convertApplicantToCardData = (applicant: Applicant): ApplicantCardData => {
  return {
    id: applicant.id,
    firstName: applicant.firstName,
    lastName: applicant.lastName,
    email: applicant.email,
    status: applicant.status,
    applicationDate: applicant.applicationDate,
    avatar: applicant.avatar,
    isActive: applicant?.isActive ? true : false, // Default to true if not specified
  };
};

/**
 * Generate applicant initials from name
 */
export const getApplicantInitials = (firstName: string, lastName: string): string => {
  return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
};

/**
 * Format candidate full name
 */
export const getCandidateFullName = (firstName: string, lastName: string, middleName?: string): string => {
  if (middleName) {
    return `${firstName} ${middleName} ${lastName}`;
  }
  return `${firstName} ${lastName}`;
};

/**
 * Format date for display
 */
export const formatApplicationDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return dateString;
  }
};

/**
 * Calculate years of experience from work experience array
 */
export const calculateTotalExperience = (workExperience: Array<{ startDate?: string; endDate?: string }>): number => {
  if (!workExperience || workExperience.length === 0) return 0;
  
  let totalMonths = 0;
  
  workExperience.forEach(exp => {
    if (exp.startDate) {
      const startDate = new Date(exp.startDate);
      const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
      
      const monthsDiff = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                        (endDate.getMonth() - startDate.getMonth());
      
      totalMonths += Math.max(0, monthsDiff);
    }
  });
  
  return Math.round(totalMonths / 12 * 10) / 10; // Round to 1 decimal place
};

/**
 * Create files array from DocumentDetails for the required structure
 * 
 * Note: This function checks for files in both 'file' and 'attachment' properties
 * to handle different form states and data structures.
 * 
 * Usage example:
 * const documents: DocumentDetails[] = [
 *   { id: "doc-1", fileIndex: 0, attachment: resumeFile, title: "My Resume", comments: "Updated 2025", subcategory: "resume-uuid" },
 *   { id: "doc-2", fileIndex: 1, file: photoFile, title: "Profile Photo", comments: "Recent headshot", subcategory: "photo-uuid" }
 * ];
 * 
 * const files = createFilesArray(documents);
 * // Output: [
 * //   { id:"doc-1", fileIndex:0, file: resumeFile, title: "My Resume", comments: "Updated 2025", subcategory:"resume-uuid" },
 * //   { id:"doc-2", fileIndex:1, file: photoFile, title: "Profile Photo", comments: "Recent headshot", subcategory:"photo-uuid" }
 * // ];
 */
export const createFilesArray = (documents: DocumentDetails[]): FileUpload[] => {
  return documents
    .filter(doc => {
      // Check for file in either 'file' or 'attachment' property
      const hasFile = doc.file instanceof File || doc.attachment instanceof File;
      return hasFile;
    })
    .map(doc => ({
      fileIndex: typeof doc.fileIndex === 'number' ? doc.fileIndex : 0, // Ensure fileIndex is a number
      file: (doc.file || doc.attachment) as File, // Use file property or fallback to attachment
      title: doc.title,
      comments: doc.comments || '',
      type: doc.subcategory, // Map subcategory to type for FileUpload interface
    }));
};

/**
 * Create a new document entry with proper structure for API submission
 * @param title - Document title
 * @param subcategory - Selected document type UUID from DocumentTreeSelectInput
 * @param comments - Optional comments
 * @param fileIndex - Sequential index for the document
 * @returns DocumentDetails object with random UUID and null filePath
 */
export const createNewDocumentEntry = (
  title: string = '',
  subcategory: string = '',
  comments: string = '',
  fileIndex: number
): DocumentDetails => {
  return {
    id: generateUUID(), // Generate random UUID for document id
    fileIndex, // Use provided index
    title,
    category: API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT, // Fixed category ID
    subcategory, // Document type selected from DocumentTreeSelectInput
    comments,
    filePath: undefined, // Set as undefined for new documents
    isActive: true, // New documents are active by default
  };
};

/**
 * NEW ADAPTER FUNCTIONS FOR API STRUCTURE PRESERVATION
 * These functions maintain the exact API response structure while enabling form editing
 */

/**
 * Convert ApplicantDetailResponse to ApplicantFormData for form initialization
 * This extracts form-friendly data from the API structure without losing the original structure
 */
export const convertApiResponseToFormData = (apiResponse: ApplicantDetailResponse): ApplicantFormData => {
  // Extract personal info from API structure
  const personalInfo = {
    firstName: apiResponse.firstName || '',
    lastName: apiResponse.lastName || '',
    middleName: apiResponse.middleName || '',
    nickName: apiResponse.preferredName || '',
    email: apiResponse.email || '',
    alternateEmail: apiResponse.contactInfo?.alternateEmail || '',
    homePhone: apiResponse.contactInfo?.homePhone || '',
    mobilePhone: apiResponse.contactInfo?.mobilePhone || '',
    workPhone: apiResponse.contactInfo?.workPhone || '',
    otherPhone: apiResponse.contactInfo?.otherPhone || '',
    dateOfBirth: apiResponse.dateOfBirth || '',
    ssn: apiResponse.additionalInfo?.ssn || '',
    skypeId: apiResponse.socialProfiles?.skypeId || '',
    linkedinProfileUrl: apiResponse.socialProfiles?.linkedin || '',
    facebookProfileUrl: apiResponse.socialProfiles?.facebook || '',
    twitterProfileUrl: apiResponse.socialProfiles?.twitter || '',
    videoReference: apiResponse.socialProfiles?.videoReference || '',
    workAuthorization: apiResponse.additionalInfo?.workAuthorization || '',
    workAuthorizationExpiry: '', // Not in API structure, keeping empty
    clearance: apiResponse.additionalInfo?.clearance ? 'Yes' as const : 'No' as const,
    address: Array.isArray(apiResponse.addresses) ? apiResponse.addresses[0]?.city || '' : apiResponse.addresses?.city || '',
    city: Array.isArray(apiResponse.addresses) ? apiResponse.addresses[0]?.city || '' : apiResponse.addresses?.city || '',
    country: Array.isArray(apiResponse.addresses) ? apiResponse.addresses[0]?.country || '' : apiResponse.addresses?.country || '',
    state: Array.isArray(apiResponse.addresses) ? apiResponse.addresses[0]?.state || '' : apiResponse.addresses?.state || '',
    zipCode: Array.isArray(apiResponse.addresses) ? apiResponse.addresses[0]?.zipcode || '' : apiResponse.addresses?.zipcode || '',
  };

  // Extract professional info from API structure
  const professionalInfo = {
    source: apiResponse.additionalInfo?.source || '',
    referredBy: apiResponse.additionalInfo?.referredBy || '',
    experienceYears: apiResponse.additionalInfo?.experience?.years || '',
    experienceMonths: apiResponse.additionalInfo?.experience?.months || '',
    jobTitle: apiResponse.additionalInfo?.jobTitle || '',
    expectedPayCurrency: apiResponse.additionalInfo?.expectedPay?.currency || '',
    expectedPayAmount: apiResponse.additionalInfo?.expectedPay?.amount || '',
    currentCtcCurrency: apiResponse.additionalInfo?.currentCTC?.currency || '',
    currentCtcAmount: apiResponse.additionalInfo?.currentCTC?.amount || '',
    skills: apiResponse.additionalInfo?.skills || [],
    primarySkills: apiResponse.additionalInfo?.primarySkills || [],
    technology: apiResponse.additionalInfo?.technology as Technology | undefined,
    industry: apiResponse.additionalInfo?.industry || [],
    function: apiResponse.additionalInfo?.function || [],
    taxTerms: apiResponse.additionalInfo?.taxTerms || '',
    noticePeriod: apiResponse.additionalInfo?.noticePeriod || '',
    currentCompany: apiResponse.additionalInfo?.currentCompany || '',
    applicantStatus: apiResponse.status || '',
    ownership: apiResponse.additionalInfo?.ownership || '',
    relocation: apiResponse.additionalInfo?.relocation ? 'Yes' : 'No' as 'Yes' | 'No',
    additionalComments: apiResponse.additionalComments || '',
    panCardNumber: apiResponse.additionalInfo?.panCardNumber || '',
    aadharNumber: apiResponse.additionalInfo?.aadhaarNumber || '',
    gpa: apiResponse.additionalInfo?.gpa || '',
    gender: apiResponse.additionalInfo?.gender || '',
    raceEthnicity: apiResponse.additionalInfo?.raceEthnicity || '',
    veteranStatus: apiResponse.additionalInfo?.veteranStatus || '',
    veteranType: apiResponse.additionalInfo?.veteranType || '',
    disability: apiResponse.additionalInfo?.disability || '',
  };

  // Convert documents to form format
  const documents: DocumentDetails[] = (apiResponse.documents || []).map((doc, index) => ({
    id: doc.id || generateUUID(),
    fileIndex: typeof doc.fileIndex === 'number' ? doc.fileIndex : index,
    title: doc.title || '',
    category: doc.category || '',
    subcategory: doc.subcategory || '',
    comments: doc.comments || '',
    filePath: doc.filePath,
    resumeVisibility: '', // resumeVisibility not in API Document type
    isActive: doc.isActive !== false, // Default to true if not specified
  }));

  return {
    personalInfo,
    professionalInfo,
    documents,
    workExperience: (apiResponse.workExperience || []).map(exp => ({
      ...exp,
      id: exp.id || generateUUID(),
      responsibilities: exp.responsibilities || '',
      isActive: exp.isActive !== false // Preserve isActive field, default to true if not specified
    })),
    educationDetails: (apiResponse.education || []).map(edu => ({
      ...edu,
      id: edu.id || generateUUID(),
      isActive: edu.isActive !== false // Preserve isActive field, default to true if not specified
    })),
    certifications: (apiResponse.certifications || []).map(cert => ({
      ...cert,
      id: cert.id || generateUUID(),
      isActive: cert.isActive !== false // Preserve isActive field, default to true if not specified
    })),
    employerDetails: (() => {
      const employerDetails = apiResponse.employerDetails;
      const employer = Array.isArray(employerDetails) ? employerDetails[0] : employerDetails;
      
      if (employer) {
        const isNew = employer.selectionType === 'NEW';
        const isExisting = employer.selectionType === 'ADD_FROM_EXISTING_VENDOR_CONTACT_RECORDS';
        
        // If selectionType is neither NEW nor ADD_FROM_EXISTING_VENDOR_CONTACT_RECORDS,
        // default to NEW if we have employer data without a vendor contact,
        // or EXISTING if we have a vendor contact
        const finalIsNew = isNew || (!isNew && !isExisting && !employer.vendorContact);
        const finalIsExisting = isExisting || (!isNew && !isExisting && !!employer.vendorContact);
        
        return {
          isNew: finalIsNew,
          addFromExistingVendor: finalIsExisting,
          vendorContact: employer.vendorContact,
          firstName: employer.firstName,
          lastName: employer.lastName,
          employerName: employer.employerName,
          officeNumber: employer.officeNumber,
          emailId: employer.emailId,
          mobileNumber: employer.mobileNumber,
          status: employer.status,
        };
      } else {
        // Default values when no employer details exist
        return {
          isNew: true,
          addFromExistingVendor: false,
          status: true,
        };
      }
    })(),
    languages: [], // Note: languages not in API structure, keeping empty for compatibility
  };
};

/**
 * Update ApplicantDetailResponse with changes from ApplicantFormData
 * This maintains the original API structure while applying form field updates
 */
export const updateApiResponseFromFormData = (
  apiResponse: ApplicantDetailResponse,
  formData: ApplicantFormData
): ApplicantDetailResponse => {
  // Create a deep copy of the original API response to avoid mutations
  const updatedResponse: ApplicantDetailResponse = JSON.parse(JSON.stringify(apiResponse));

  // Update basic fields from personal info
  updatedResponse.firstName = formData.personalInfo.firstName;
  updatedResponse.lastName = formData.personalInfo.lastName;
  updatedResponse.middleName = formData.personalInfo.middleName;
  updatedResponse.preferredName = formData.personalInfo.nickName;
  updatedResponse.email = formData.personalInfo.email;
  updatedResponse.dateOfBirth = formData.personalInfo.dateOfBirth;

  // Update contact info
  if (!updatedResponse.contactInfo) {
    updatedResponse.contactInfo = {};
  }
  updatedResponse.contactInfo.alternateEmail = formData.personalInfo.alternateEmail;
  updatedResponse.contactInfo.homePhone = formData.personalInfo.homePhone;
  updatedResponse.contactInfo.mobilePhone = formData.personalInfo.mobilePhone;
  updatedResponse.contactInfo.workPhone = formData.personalInfo.workPhone;
  updatedResponse.contactInfo.otherPhone = formData.personalInfo.otherPhone;

  // Update social profiles
  if (!updatedResponse.socialProfiles) {
    updatedResponse.socialProfiles = {};
  }
  updatedResponse.socialProfiles.skypeId = formData.personalInfo.skypeId;
  updatedResponse.socialProfiles.linkedin = formData.personalInfo.linkedinProfileUrl;
  updatedResponse.socialProfiles.facebook = formData.personalInfo.facebookProfileUrl;
  updatedResponse.socialProfiles.twitter = formData.personalInfo.twitterProfileUrl;
  updatedResponse.socialProfiles.videoReference = formData.personalInfo.videoReference;

  // Update addresses
  const addressData = {
    country: formData.personalInfo.country || '',
    state: formData.personalInfo.state || '',
    city: formData.personalInfo.city || '',
    zipcode: formData.personalInfo.zipCode || '',
    isPrimary: true,
  };

  if (Array.isArray(updatedResponse.addresses)) {
    if (updatedResponse.addresses.length > 0) {
      updatedResponse.addresses[0] = { ...updatedResponse.addresses[0], ...addressData };
    } else {
      updatedResponse.addresses = [addressData];
    }
  } else {
    updatedResponse.addresses = [addressData];
  }

  // Update additional info
  if (!updatedResponse.additionalInfo) {
    updatedResponse.additionalInfo = {};
  }

  updatedResponse.additionalInfo.ssn = formData.personalInfo.ssn;
  updatedResponse.additionalInfo.panCardNumber = formData.professionalInfo.panCardNumber;
  updatedResponse.additionalInfo.aadhaarNumber = formData.professionalInfo.aadharNumber;
  updatedResponse.additionalInfo.gpa = formData.professionalInfo.gpa;
  updatedResponse.additionalInfo.gender = formData.professionalInfo.gender;
  updatedResponse.additionalInfo.raceEthnicity = formData.professionalInfo.raceEthnicity;
  updatedResponse.additionalInfo.veteranStatus = formData.professionalInfo.veteranStatus;
  updatedResponse.additionalInfo.veteranType = formData.professionalInfo.veteranType;
  updatedResponse.additionalInfo.disability = formData.professionalInfo.disability;
  updatedResponse.additionalInfo.workAuthorization = formData.personalInfo.workAuthorization;
  updatedResponse.additionalInfo.clearance = formData.personalInfo.clearance === 'Yes';

  // Update professional info in additionalInfo
  updatedResponse.additionalInfo.source = formData.professionalInfo.source;
  updatedResponse.additionalInfo.referredBy = formData.professionalInfo.referredBy;
  updatedResponse.additionalInfo.jobTitle = formData.professionalInfo.jobTitle;
  updatedResponse.additionalInfo.skills = formData.professionalInfo.skills;
  updatedResponse.additionalInfo.primarySkills = formData.professionalInfo.primarySkills;
  updatedResponse.additionalInfo.technology = formData.professionalInfo.technology;
  updatedResponse.additionalInfo.industry = formData.professionalInfo.industry;
  updatedResponse.additionalInfo.function = formData.professionalInfo.function;
  updatedResponse.additionalInfo.taxTerms = formData.professionalInfo.taxTerms;
  updatedResponse.additionalInfo.noticePeriod = formData.professionalInfo.noticePeriod;
  updatedResponse.additionalInfo.currentCompany = formData.professionalInfo.currentCompany;
  updatedResponse.additionalInfo.ownership = formData.professionalInfo.ownership;
  updatedResponse.additionalInfo.relocation = formData.professionalInfo.relocation === 'Yes';

  // Update experience
  if (!updatedResponse.additionalInfo.experience) {
    updatedResponse.additionalInfo.experience = {
      id: generateUUID(),
      years: '',
      months: ''
    };
  }
  updatedResponse.additionalInfo.experience.years = formData.professionalInfo.experienceYears || '';
  updatedResponse.additionalInfo.experience.months = formData.professionalInfo.experienceMonths || '';

  // Update expected pay
  if (!updatedResponse.additionalInfo.expectedPay) {
    updatedResponse.additionalInfo.expectedPay = {
      id: generateUUID(),
      currency: '',
      amount: ''
    };
  }
  updatedResponse.additionalInfo.expectedPay.currency = formData.professionalInfo.expectedPayCurrency || '';
  updatedResponse.additionalInfo.expectedPay.amount = formData.professionalInfo.expectedPayAmount || '';

  // Update current CTC
  if (!updatedResponse.additionalInfo.currentCTC) {
    updatedResponse.additionalInfo.currentCTC = {
      id: generateUUID(),
      currency: '',
      amount: ''
    };
  }
  updatedResponse.additionalInfo.currentCTC.currency = formData.professionalInfo.currentCtcCurrency || '';
  updatedResponse.additionalInfo.currentCTC.amount = formData.professionalInfo.currentCtcAmount || '';

  // Update status and comments
  updatedResponse.status = formData.professionalInfo.applicantStatus;
  updatedResponse.additionalComments = formData.professionalInfo.additionalComments;

  // Update other sections with proper type conversion
  updatedResponse.workExperience = formData.workExperience.map(exp => ({
    ...exp,
    location: exp.location || '',
    employeeType: exp.employeeType || '',
    isActive: exp.isActive !== false // Include isActive field, default to true
  }));
  
  updatedResponse.education = formData.educationDetails.map(edu => ({
    ...edu,
    yearCompleted: edu.yearCompleted || '',
    country: edu.country || '',
    state: edu.state || '',
    city: edu.city || '',
    isHigherEducation: edu.higherEducation || false,
    isActive: edu.isActive !== false // Include isActive field, default to true
  }));
  
  updatedResponse.certifications = formData.certifications.map(cert => ({
    ...cert,
    isActive: cert.isActive !== false // Include isActive field, default to true
  }));

  // Update employer details with proper type conversion
  if (formData.employerDetails) {
    updatedResponse.employerDetails = {
      selectionType: formData.employerDetails.isNew ? 'NEW' : 'ADD_FROM_EXISTING_VENDOR_CONTACT_RECORDS',
      vendorContact: formData.employerDetails.vendorContact,
      firstName: formData.employerDetails.firstName || '',
      lastName: formData.employerDetails.lastName || '',
      employerName: formData.employerDetails.employerName || '',
      officeNumber: formData.employerDetails.officeNumber,
      emailId: formData.employerDetails.emailId || '',
      mobileNumber: formData.employerDetails.mobileNumber,
      status: formData.employerDetails.status ?? true,
    };
  }

  // Update documents (preserve existing documents and add new ones)
  updatedResponse.documents = formData.documents.map(doc => ({
    id: doc.id,
    fileIndex: doc.fileIndex,
    title: doc.title,
    category: doc.category,
    subcategory: doc.subcategory,
    comments: doc.comments,
    filePath: doc.filePath,
    resumeVisibility: doc.resumeVisibility,
    isActive: doc.isActive !== false,
  }));

  return updatedResponse;
};


