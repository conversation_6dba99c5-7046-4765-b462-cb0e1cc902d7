import { useState, useCallback } from 'react';
import { ApiError } from '@/lib/apiConfig';
import { createUserFriendlyErrorCallback, logError } from '@/lib/errorHandling';
import { API_CONSTANTS } from '@/constants/apiConstants';

export interface ApiCallState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface UseApiCallOptions<T = unknown> {
  onSuccess?: (data: T) => void;
  onError?: (message: string, originalError: ApiError) => void;
  loadingMessage?: string;
}

/**
 * Reusable hook for API calls with loading states and error handling
 */
export function useApiCall<T, P = unknown>(
  apiFunction: (params: P, errorCallback?: (error: ApiError) => void) => Promise<T | undefined>,
  options: UseApiCallOptions<T> = {}
) {
  const [state, setState] = useState<ApiCallState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(
    async (params?: P, context?: string) => {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
      }));

      try {
        const errorCallback = createUserFriendlyErrorCallback((message, originalError) => {
          setState(prev => ({
            ...prev,
            loading: false,
            error: message,
          }));

          // Log error for debugging
          if (context) {
            logError(context, originalError, { params });
          }

          options.onError?.(message, originalError);
        });

        const result = await apiFunction(params as P, errorCallback);

        if (result !== undefined) {
          setState({
            data: result,
            loading: false,
            error: null,
          });
          options.onSuccess?.(result);
        } else {
          // If result is undefined, it means an error occurred and was handled by errorCallback
          // Don't update state here as errorCallback already did
        }
      } catch (error) {
        const errorMessage = API_CONSTANTS.ERROR_MESSAGES.GENERIC_ERROR;
        setState({
          data: null,
          loading: false,
          error: errorMessage,
        });

        if (context) {
          logError(context, error as ApiError, { params });
        }

        options.onError?.(errorMessage, error as ApiError);
      }
    },
    [apiFunction, options]
  );

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
    });
  }, []);

  return {
    ...state,
    execute,
    reset,
  };
}

/**
 * Hook specifically for list/fetch operations
 */
export function useApiList<T, P = unknown>(
  apiFunction: (params?: P, errorCallback?: (error: ApiError) => void) => Promise<T | undefined>,
  options: UseApiCallOptions<T> = {}
) {
  return useApiCall(apiFunction, {
    loadingMessage: API_CONSTANTS.LOADING_MESSAGES.FETCHING_DATA,
    ...options,
  });
}

/**
 * Hook specifically for create/update operations
 */
export function useApiMutation<T, P = unknown>(
  apiFunction: (params: P, errorCallback?: (error: ApiError) => void) => Promise<T | undefined>,
  options: UseApiCallOptions<T> = {}
) {
  return useApiCall(apiFunction, {
    loadingMessage: API_CONSTANTS.LOADING_MESSAGES.SAVING_DATA,
    ...options,
  });
} 