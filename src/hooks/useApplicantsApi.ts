import { useCallback } from 'react';

import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useApiList, useApiMutation } from './useApiCall';
import {
  createApplicant,
  createApplicantWithFiles,
  getListValuesById,
  listApplicants,
  patchApplicantWithFiles,
  getApplicant,
  getDocumentTree,
  clearApplicantCache,
  clearDropdownCache
} from '@/api/memoizedApplicantsApi';
import {
  deleteApplicant,
  activateApplicant
} from '@/api/applicantsApi';
import {
  ApplicantListParams,
  GetAllApplicantsResponse,
  GlobalListValueDTO,
  PatchApplicantFormData,
  PatchApplicantResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  ApiSelectOption,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
  ApplicantDetailResponse
} from '@/types/applicants';

/**
 * Hook for listing applicants with pagination and filtering
 */
export function useApplicantsList() {
  const { toast } = useToast();
  
  return useApiList<GetAllApplicantsResponse, { query?: ApplicantListParams }>(listApplicants, {
    onError: (message) => {
      toast({
        title: "Error Loading Applicants",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for getting a single applicant by ID
 */
export function useApplicantDetail() {
  const { toast } = useToast();
  
  return useApiMutation<ApplicantDetailResponse, { pathParams: { applicantId: string } }>(getApplicant, {
    onError: (message) => {
      toast({
        title: "Error Loading Applicant",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for creating a new applicant (without files)
 */
export function useCreateApplicant() {
  const { toast } = useToast();
  
  return useApiMutation<CreateApplicantResponse, { body: CreateApplicantRequest }>(createApplicant, {
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Applicant created successfully",
        variant: "default",
      });
    },
    onError: (message) => {
      toast({
        title: "Error Creating Applicant",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for creating a new applicant with files
 */
export function useCreateApplicantWithFiles() {
  const { toast } = useToast();
  
  return useApiMutation<CreateApplicantResponse, CreateApplicantWithFilesRequest>(createApplicantWithFiles, {
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Applicant and files uploaded successfully",
        variant: "default",
      });
    },
    onError: (message) => {
      toast({
        title: "Error Creating Applicant",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for partially updating an applicant
 */
export function usePatchApplicant() {
  const { toast } = useToast();
  
  return useApiMutation<PatchApplicantResponse, { pathParams: { applicantId: string }; body: PatchApplicantFormData }>(patchApplicantWithFiles, {
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Applicant updated successfully",
        variant: "default",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: typeof error === 'string' ? error : "Failed to update applicant",
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for deleting an applicant
 */
export function useDeleteApplicant() {
  const { toast } = useToast();
  
  return useApiMutation<DeleteApplicantResponse, { pathParams: { applicantId: string } }>(deleteApplicant, {
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Applicant deleted successfully",
        variant: "default",
      });
    },
    onError: (message) => {
      toast({
        title: "Error Deleting Applicant",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for activating an applicant
 */
export function useActivateApplicant() {
  const { toast } = useToast();
  
  return useApiMutation<ActivateApplicantResponse, { pathParams: { applicantId: string } }>(activateApplicant, {
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Applicant activated successfully",
        variant: "default",
      });
    },
    onError: (message) => {
      toast({
        title: "Error Activating Applicant",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Hook for fetching list values for dropdowns
 */
export function useListValues() {
  return useApiMutation<ApiSelectOption[], string>(getListValuesById, {
    onError: (message) => {
      // Silently handle dropdown loading errors - don't show toast for these
      if (process.env.NODE_ENV === 'development') {
        console.warn('Failed to load dropdown values:', message);
      }
    },
  });
}

/**
 * Hook for fetching document tree
 */
export function useDocumentTree() {
  const { toast } = useToast();
  
  return useApiMutation<DocumentTreeResponse, string>(getDocumentTree, {
    onError: (message) => {
      toast({
        title: "Error Loading Document Categories",
        description: message,
        variant: "destructive",
      });
    },
  });
}

/**
 * Memoized function to preload commonly used list values
 */
export function usePreloadListValues() {
  const { execute: loadListValues } = useListValues();
  
  return useCallback(async (listIds: string[]) => {
    const promises = listIds.map(listId => 
      loadListValues(listId, `preload-${listId}`)
    );
    
    await Promise.allSettled(promises);
  }, [loadListValues]);
}

/**
 * Hook for cache management operations
 */
export function useCacheManagement() {
  return {
    clearApplicantCache: useCallback(() => {
      clearApplicantCache();
    }, []),
    
    clearDropdownCache: useCallback(() => {
      clearDropdownCache();
    }, []),
    
    clearAllCache: useCallback(() => {
      clearApplicantCache();
      clearDropdownCache();
    }, [])
  };
}

/**
 * Hook to access list values from AuthContext
 * This provides easy access to the globally loaded list values
 */
export function useListValuesFromContext() {
  const { listValues, isListValuesLoading } = useAuth();
  
  return {
    listValues,
    isLoading: isListValuesLoading,
    /**
     * Filter list values by a specific criteria
     */
    filterListValues: (predicate: (item: GlobalListValueDTO) => boolean) => {
      return listValues.filter(predicate);
    },
    /**
     * Find a specific list value by ID
     */
    findListValueById: (id: string) => {
      return listValues.find(item => item.id === id);
    },
    /**
     * Get list values as API select options format
     */
    getAsSelectOptions: () => {
      return listValues.map(item => ({
        value: item.id,
        key: item.name, // Changed from listValue to name
      }));
    }
  };
} 