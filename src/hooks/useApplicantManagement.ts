import React, { useCallback, useState } from 'react';

import { ApplicantFormData } from '@/components/applicants/ApplicantFormWizard';
import { Applicant } from '@/types';
import { generateUUID } from '@/lib/utils';
export interface UseApplicantManagementReturn {
  applicants: Applicant[];
  addApplicant: (formData: ApplicantFormData) => Promise<void>;
  updateApplicant: (id: string, formData: ApplicantFormData) => Promise<void>;
  deleteApplicant: (id: string) => void;
  getApplicantById: (id: string) => Applicant | undefined;
}

// Helper function to convert form data to applicant
const convertFormDataToApplicant = (formData: ApplicantFormData, existingId?: string): Applicant => {
  const id = existingId || generateUUID();
  const personalInfo = formData.personalInfo;
  const professionalInfo = formData.professionalInfo;

  // Combine personal and professional info back into personalDetails for backward compatibility
  const personalDetails = {
    ...personalInfo,
    ...professionalInfo,
    // Fix type issues by ensuring arrays are properly handled
    applicantGroup: Array.isArray(professionalInfo.applicantGroup) 
      ? professionalInfo.applicantGroup 
      : professionalInfo.applicantGroup ? [professionalInfo.applicantGroup] : undefined,
    technology: Array.isArray(professionalInfo.technology) 
      ? professionalInfo.technology 
      : professionalInfo.technology ? [professionalInfo.technology] : undefined,
  };

  return {
    id,
    personalDetails,
    documents: formData.documents,
    workExperience: formData.workExperience,
    educationDetails: formData.educationDetails,
    certifications: formData.certifications,
    employerDetails: formData.employerDetails,
    languages: formData.languages,
    // Legacy fields for backward compatibility
    firstName: personalInfo.firstName ?? '',
    lastName: personalInfo.lastName ?? '',
    email: personalInfo.email ?? '',
    phone: personalInfo.homePhone ?? personalInfo.mobilePhone ?? '',
    position: professionalInfo.jobTitle ?? 'Not specified',
    department: 'General',
    applicationDate: new Date().toISOString().split('T')[0] as string,
    status: 'applied' as const,
    experience: parseInt(professionalInfo.experienceYears ?? '0') || 0,
    skills: professionalInfo.skills ?? [],
    avatar: undefined,
  };
};

// Helper function to get initial applicants from localStorage or mock data
const getInitialApplicants = (): Applicant[] => {
  try {
    const stored = localStorage.getItem('applicants');
    if (stored) {
      return JSON.parse(stored);
    }
  } catch (error) {
    console.error('Error loading applicants from localStorage:', error);
  }

  // Return sample data if no stored data
  return [
    {
      id: '1',
      personalDetails: {
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        homePhone: '+****************',
        jobTitle: 'Senior Frontend Developer',
        experienceYears: '5',
        skills: ['React', 'TypeScript', 'Node.js', 'AWS'],
      },
      documents: [],
      workExperience: [],
      educationDetails: [],
      certifications: [{
        id: '1',
        certification: '',
        yearCompleted: '',
        comments: '',
      }],
      employerDetails: {
        isNew: true,
        addFromExistingVendor: false,
      },
      languages: [],
      firstName: 'Sarah',
      lastName: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Senior Frontend Developer',
      department: 'Engineering',
      applicationDate: '2024-01-15',
      status: 'interview' as const,
      experience: 5,
      skills: ['React', 'TypeScript', 'Node.js', 'AWS'],
    },
    {
      id: '2',
      personalDetails: {
        firstName: 'Michael',
        lastName: 'Chen',
        email: '<EMAIL>',
        homePhone: '+****************',
        jobTitle: 'Product Manager',
        experienceYears: '3',
        skills: ['Product Strategy', 'Analytics', 'Agile'],
      },
      documents: [],
      workExperience: [],
      educationDetails: [],
      certifications: [{
        id: '2',
        certification: '',
        yearCompleted: '',
        comments: '',
      }],
      employerDetails: {
        isNew: true,
        addFromExistingVendor: false,
      },
      languages: [],
      firstName: 'Michael',
      lastName: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      position: 'Product Manager',
      department: 'Product',
      applicationDate: '2024-01-20',
      status: 'offer' as const,
      experience: 3,
      skills: ['Product Strategy', 'Analytics', 'Agile'],
    }
  ];
};

// Helper function to save applicants to localStorage
const saveApplicantsToStorage = (applicants: Applicant[]) => {
  try {
    localStorage.setItem('applicants', JSON.stringify(applicants));
  } catch (error) {
    console.error('Error saving applicants to localStorage:', error);
  }
};

export function useApplicantManagement(): UseApplicantManagementReturn {
  const [applicants, setApplicants] = useState<Applicant[]>(() => getInitialApplicants());

  const addApplicant = useCallback(async (formData: ApplicantFormData) => {
    // Simulate potential async operations (API calls, validation, etc.)
    return new Promise<void>((resolve, reject) => {
      try {
        const newApplicant = convertFormDataToApplicant(formData);
        
        setApplicants(prev => {
          const updated = [...prev, newApplicant];
          saveApplicantsToStorage(updated);
          return updated;
        });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }, []);

  const updateApplicant = useCallback(async (id: string, formData: ApplicantFormData) => {
    // Simulate potential async operations (API calls, validation, etc.)
    return new Promise<void>((resolve, reject) => {
      try {
        setApplicants(prev => {
          const updated = prev.map(applicant =>
            applicant.id === id
              ? convertFormDataToApplicant(formData, id)
              : applicant
          );
          saveApplicantsToStorage(updated);
          return updated;
        });
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }, []);

  const deleteApplicant = useCallback((id: string) => {
    setApplicants(prev => {
      const updated = prev.filter(applicant => applicant.id !== id);
      saveApplicantsToStorage(updated);
      return updated;
    });
  }, []);

  const getApplicantById = useCallback((id: string): Applicant | undefined => {
    return applicants.find(applicant => applicant.id === id);
  }, [applicants]);

  return {
    applicants,
    addApplicant,
    updateApplicant,
    deleteApplicant,
    getApplicantById,
  };
}
