import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useApplicantManagement } from '../useApplicantManagement'
import { ApplicantFormData } from '@/components/applicants/ApplicantFormWizard'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock console.error to avoid noise in tests
const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

describe('useApplicantManagement - Error Handling', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
    localStorageMock.setItem.mockImplementation(() => {})
  })

  afterEach(() => {
    consoleErrorSpy.mockClear()
  })

  describe('localStorage Error Handling', () => {
    it('should handle localStorage.setItem errors gracefully when adding applicant', () => {
      // Arrange: Mock localStorage.setItem to throw an error
      const storageError = new Error('Storage quota exceeded')
      localStorageMock.setItem.mockImplementation(() => {
        throw storageError
      })

      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length

      const mockFormData: ApplicantFormData = {
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          middleName: '',
          nickName: '',
          email: '<EMAIL>',
          alternateEmail: '',
          homePhone: '+1234567890',
          mobilePhone: '',
          workPhone: '',
          otherPhone: '',
          dateOfBirth: '1990-01-01',
          ssn: '***********',
          skypeId: '',
          address: '123 Main St, Anytown, CA 12345, USA'
        },
        professionalInfo: {
          position: 'Software Engineer',
          company: 'Tech Corp',
          yearsOfExperience: 5,
          expectedSalary: 80000,
          availability: 'immediate',
          workAuthorization: 'authorized',
          willingToRelocate: true,
          preferredWorkLocation: 'remote'
        },
        documents: [],
        workExperience: [],
        educationDetails: [],
        certifications: [],
        employerDetails: {
          contactPerson: '',
          contactEmail: '',
          contactPhone: '',
          relationship: '',
          isActive: true
        },
        languages: []
      }

      // Act: Try to add an applicant
      act(() => {
        result.current.addApplicant(mockFormData)
      })

      // Assert: Function should not throw, but should log error
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error saving applicants to localStorage:',
        storageError
      )
      
      // The applicant should still be added to state despite storage error
      expect(result.current.applicants).toHaveLength(initialCount + 1)
      const addedApplicant = result.current.applicants[initialCount]
      expect(addedApplicant?.firstName).toBe('John')
    })

    it('should handle localStorage.setItem errors gracefully when updating applicant', () => {
      // Arrange: Mock localStorage.setItem to throw an error
      const storageError = new Error('Storage not available')
      localStorageMock.setItem.mockImplementation(() => {
        throw storageError
      })

      const { result } = renderHook(() => useApplicantManagement())
      const existingApplicant = result.current.applicants[0]
      
      if (!existingApplicant) {
        throw new Error('No existing applicant found for test')
      }
      
      const updatedFormData: ApplicantFormData = {
        personalInfo: {
          firstName: 'Jane',
          lastName: 'Smith',
          middleName: '',
          nickName: '',
          email: '<EMAIL>',
          alternateEmail: '',
          homePhone: '+1234567890',
          mobilePhone: '',
          workPhone: '',
          otherPhone: '',
          dateOfBirth: '1990-01-01',
          ssn: '***********',
          skypeId: '',
          address: '123 Main St, Anytown, CA 12345, USA'
        },
        professionalInfo: {
          position: 'Senior Software Engineer',
          company: 'Tech Corp',
          yearsOfExperience: 7,
          expectedSalary: 100000,
          availability: 'immediate',
          workAuthorization: 'authorized',
          willingToRelocate: true,
          preferredWorkLocation: 'remote'
        },
        documents: [],
        workExperience: [],
        educationDetails: [],
        certifications: [],
        employerDetails: {
          contactPerson: '',
          contactEmail: '',
          contactPhone: '',
          relationship: '',
          isActive: true
        },
        languages: []
      }

      // Act: Try to update an applicant
      act(() => {
        result.current.updateApplicant(existingApplicant.id, updatedFormData)
      })

      // Assert: Function should not throw, but should log error
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error saving applicants to localStorage:',
        storageError
      )
      
      // The applicant should still be updated in state despite storage error
      const updatedApplicant = result.current.getApplicantById(existingApplicant.id)
      expect(updatedApplicant?.firstName).toBe('Jane')
    })

    it('should handle localStorage.setItem errors gracefully when deleting applicant', () => {
      // Arrange: Mock localStorage.setItem to throw an error
      const storageError = new Error('Storage quota exceeded')
      localStorageMock.setItem.mockImplementation(() => {
        throw storageError
      })

      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length
      const applicantToDelete = result.current.applicants[0]

      if (!applicantToDelete) {
        throw new Error('No applicant found for deletion test')
      }

      // Act: Try to delete an applicant
      act(() => {
        result.current.deleteApplicant(applicantToDelete.id)
      })

      // Assert: Function should not throw, but should log error
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Error saving applicants to localStorage:',
        storageError
      )
      
      // The applicant should still be deleted from state despite storage error
      expect(result.current.applicants).toHaveLength(initialCount - 1)
      expect(result.current.getApplicantById(applicantToDelete.id)).toBeUndefined()
    })
  })

  describe('Data Validation Error Handling', () => {
    it('should handle invalid form data gracefully when adding applicant', () => {
      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length

      // Arrange: Create invalid form data with missing required fields
      const invalidFormData: ApplicantFormData = {
        personalInfo: {
          firstName: '', // Empty required field
          lastName: '',
          middleName: '',
          nickName: '',
          email: 'invalid-email', // Invalid email format
          alternateEmail: '',
          homePhone: 'invalid-phone', // Invalid phone format
          mobilePhone: '',
          workPhone: '',
          otherPhone: '',
          dateOfBirth: 'invalid-date', // Invalid date format
          ssn: 'invalid-ssn', // Invalid SSN format
          skypeId: '',
          address: 'invalid address'
        },
        professionalInfo: {
          position: '',
          company: '',
          yearsOfExperience: -1, // Invalid negative experience
          expectedSalary: -1000, // Invalid negative salary
          availability: 'immediate',
          workAuthorization: 'authorized',
          willingToRelocate: true,
          preferredWorkLocation: ''
        },
        documents: [],
        workExperience: [],
        educationDetails: [],
        certifications: [],
        employerDetails: {
          contactPerson: '',
          contactEmail: 'invalid-email',
          contactPhone: 'invalid-phone',
          relationship: '',
          isActive: true
        },
        languages: []
      }

      // Act: Try to add applicant with invalid data
      act(() => {
        result.current.addApplicant(invalidFormData)
      })

      // Assert: Should still add the applicant (validation happens at form level, not hook level)
      expect(result.current.applicants).toHaveLength(initialCount + 1)
      
      // The applicant should be created with the provided data (even if invalid)
      const addedApplicant = result.current.applicants[initialCount]
      expect(addedApplicant?.firstName).toBe('')
      expect(addedApplicant?.email).toBe('invalid-email')
    })

    it('should handle non-existent applicant ID gracefully when updating', () => {
      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length

      const validFormData: ApplicantFormData = {
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          middleName: '',
          nickName: '',
          email: '<EMAIL>',
          alternateEmail: '',
          homePhone: '+1234567890',
          mobilePhone: '',
          workPhone: '',
          otherPhone: '',
          dateOfBirth: '1990-01-01',
          ssn: '***********',
          skypeId: '',
          address: '123 Main St, Anytown, CA 12345, USA'
        },
        professionalInfo: {
          position: 'Software Engineer',
          company: 'Tech Corp',
          yearsOfExperience: 5,
          expectedSalary: 80000,
          availability: 'immediate',
          workAuthorization: 'authorized',
          willingToRelocate: true,
          preferredWorkLocation: 'remote'
        },
        documents: [],
        workExperience: [],
        educationDetails: [],
        certifications: [],
        employerDetails: {
          contactPerson: '',
          contactEmail: '',
          contactPhone: '',
          relationship: '',
          isActive: true
        },
        languages: []
      }

      // Act: Try to update non-existent applicant
      act(() => {
        result.current.updateApplicant('non-existent-id', validFormData)
      })

      // Assert: Should not throw error and should not change applicant count
      expect(result.current.applicants).toHaveLength(initialCount)
      
      // No applicant should be found with the non-existent ID
      expect(result.current.getApplicantById('non-existent-id')).toBeUndefined()
    })

    it('should handle non-existent applicant ID gracefully when deleting', () => {
      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length

      // Act: Try to delete non-existent applicant
      act(() => {
        result.current.deleteApplicant('non-existent-id')
      })

      // Assert: Should not throw error and should not change applicant count
      expect(result.current.applicants).toHaveLength(initialCount)
    })


  })

  describe('Edge Case Error Handling', () => {
    it('should handle concurrent operations gracefully', () => {
      const { result } = renderHook(() => useApplicantManagement())
      const initialCount = result.current.applicants.length

      const formData: ApplicantFormData = {
        personalInfo: {
          firstName: 'John',
          lastName: 'Doe',
          middleName: '',
          nickName: '',
          email: '<EMAIL>',
          alternateEmail: '',
          homePhone: '+1234567890',
          mobilePhone: '',
          workPhone: '',
          otherPhone: '',
          dateOfBirth: '1990-01-01',
          ssn: '***********',
          skypeId: '',
          address: '123 Main St, Anytown, CA 12345, USA'
        },
        professionalInfo: {
          position: 'Software Engineer',
          company: 'Tech Corp',
          yearsOfExperience: 5,
          expectedSalary: 80000,
          availability: 'immediate',
          workAuthorization: 'authorized',
          willingToRelocate: true,
          preferredWorkLocation: 'remote'
        },
        documents: [],
        workExperience: [],
        educationDetails: [],
        certifications: [],
        employerDetails: {
          contactPerson: '',
          contactEmail: '',
          contactPhone: '',
          relationship: '',
          isActive: true
        },
        languages: []
      }

      // Act: Perform multiple operations simultaneously
      act(() => {
        result.current.addApplicant(formData)
        result.current.addApplicant({ ...formData, personalInfo: { ...formData.personalInfo, firstName: 'Jane' } })
        const firstApplicant = result.current.applicants[0]
        if (firstApplicant) {
          result.current.deleteApplicant(firstApplicant.id)
        }
      })

      // Assert: Should handle concurrent operations without data corruption
      expect(result.current.applicants.length).toBeGreaterThanOrEqual(initialCount - 1)
      expect(result.current.applicants.length).toBeLessThanOrEqual(initialCount + 2)
    })
  })
})
