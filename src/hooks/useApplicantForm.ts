import { useState, useCallback, useEffect } from 'react';
import { ApplicantFormData } from '@/components/applicants/ApplicantFormWizard';
import { Applicant } from '@/types';
import { generateUUID } from "@/lib/utils";

interface ValidationErrors {
  [key: string]: string[];
}

interface FormValidation {
  isValid: boolean;
  errors: ValidationErrors;
}

export interface UseApplicantFormReturn {
  formData: ApplicantFormData;
  updateFormData: <K extends keyof ApplicantFormData>(
    section: K,
    data: ApplicantFormData[K]
  ) => void;
  resetForm: (applicant?: Applicant | null) => void;
  validateForm: () => FormValidation;
  validateSection: (section: keyof ApplicantFormData) => FormValidation;
  isDirty: boolean;
  hasUnsavedChanges: boolean;
}

const getInitialFormData = (applicant?: Applicant | null): ApplicantFormData => {
  if (applicant) {
    const personalDetails = applicant.personalDetails ?? {
      firstName: applicant.firstName ?? '',
      lastName: applicant.lastName ?? '',
      email: applicant.email ?? '',
      homePhone: applicant.phone ?? '',
    };

    return {
      personalInfo: {
        firstName: personalDetails.firstName ?? '',
        lastName: personalDetails.lastName ?? '',
        middleName: personalDetails.middleName ?? '',
        nickName: personalDetails.nickName ?? '',
        email: personalDetails.email ?? '',
        alternateEmail: personalDetails.alternateEmail ?? '',
        homePhone: personalDetails.homePhone ?? '',
        mobilePhone: personalDetails.mobilePhone ?? '',
        workPhone: personalDetails.workPhone ?? '',
        otherPhone: personalDetails.otherPhone ?? '',
        dateOfBirth: personalDetails.dateOfBirth ?? '',
        ssn: personalDetails.ssn ?? '',
        skypeId: personalDetails.skypeId ?? '',
        linkedinProfileUrl: personalDetails.linkedinProfileUrl ?? '',
        facebookProfileUrl: personalDetails.facebookProfileUrl ?? '',
        twitterProfileUrl: personalDetails.twitterProfileUrl ?? '',
        videoReference: personalDetails.videoReference ?? '',
        workAuthorization: personalDetails.workAuthorization ?? '',
        workAuthorizationExpiry: personalDetails.workAuthorizationExpiry ?? '',
        clearance: personalDetails.clearance ?? undefined,
        address: personalDetails.address ?? '',
        city: personalDetails.city ?? '',
        country: personalDetails.country ?? '',
        state: personalDetails.state ?? '',
        zipCode: personalDetails.zipCode ?? '',
      },
      professionalInfo: {
        source: personalDetails.source ?? '',
        referredBy: personalDetails.referredBy ?? '',
        experienceYears: personalDetails.experienceYears ?? '',
        experienceMonths: personalDetails.experienceMonths ?? '',
        jobTitle: personalDetails.jobTitle ?? '',
        expectedPayCurrency: personalDetails.expectedPayCurrency ?? '',
        expectedPayAmount: personalDetails.expectedPayAmount ?? '',
        currentCtcCurrency: personalDetails.currentCtcCurrency ?? '',
        currentCtcAmount: personalDetails.currentCtcAmount ?? '',
        skills: personalDetails.skills ?? [],
        primarySkills: personalDetails.primarySkills ?? [],
        technology: personalDetails.technology ?? [],
        industry: personalDetails.industry ?? [],
        function: personalDetails.function ?? [],
        taxTerms: personalDetails.taxTerms ?? '',
        noticePeriod: personalDetails.noticePeriod ?? '',
        currentCompany: personalDetails.currentCompany ?? '',
        applicantStatus: personalDetails.applicantStatus ?? '',
        applicantGroup: personalDetails.applicantGroup ?? [],
        ownership: personalDetails.ownership ?? '',
        relocation: personalDetails.relocation ?? undefined,
        additionalComments: personalDetails.additionalComments ?? '',
        panCardNumber: personalDetails.panCardNumber ?? '',
        aadharNumber: personalDetails.aadharNumber ?? '',
        gpa: personalDetails.gpa ?? '',
        gender: personalDetails.gender ?? '',
        raceEthnicity: personalDetails.raceEthnicity ?? '',
        veteranStatus: personalDetails.veteranStatus ?? '',
        veteranType: personalDetails.veteranType ?? '',
        disability: personalDetails.disability ?? '',
      },
      documents: applicant.documents ?? [],
      workExperience: applicant.workExperience ?? [],
      educationDetails: applicant.educationDetails ?? [],
      certifications: applicant.certifications ?? [{
        id: generateUUID(),
        certification: '',
        yearCompleted: '',
        comments: '',
      }],
      employerDetails: applicant.employerDetails ?? {
        isNew: true,
        addFromExistingVendor: false,
      },
      languages: applicant.languages ?? [],
    };
  }

  return {
    personalInfo: {
      firstName: '',
      lastName: '',
      email: '',
    },
    professionalInfo: {},
    documents: [],
    workExperience: [],
    educationDetails: [],
    certifications: [{
      id: generateUUID(),
      certification: '',
      yearCompleted: '',
      comments: '',
    }],
    employerDetails: {
      isNew: true,
      addFromExistingVendor: false,
    },
    languages: [],
  };
};

const validatePersonalDetails = (data: ApplicantFormData['personalInfo']): string[] => {
  const errors: string[] = [];
  
  if (!data.firstName?.trim()) {
    errors.push('First name is required');
  }
  
  if (!data.lastName?.trim()) {
    errors.push('Last name is required');
  }
  
  if (!data.email?.trim()) {
    errors.push('Email is required');
  } else if (!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(data.email)) {
    errors.push('Please enter a valid email address');
  }
  
  return errors;
};

const validateWorkExperience = (data: ApplicantFormData['workExperience']): string[] => {
  const errors: string[] = [];
  
  data.forEach((exp, index) => {
    if (!exp.companyName?.trim()) {
      errors.push(`Work experience ${index + 1}: Company name is required`);
    }
    if (!exp.jobTitle?.trim()) {
      errors.push(`Work experience ${index + 1}: Job title is required`);
    }
    if (!exp.startDate) {
      errors.push(`Work experience ${index + 1}: Start date is required`);
    }
  });
  
  return errors;
};

const validateEducationDetails = (data: ApplicantFormData['educationDetails']): string[] => {
  const errors: string[] = [];
  
  data.forEach((edu, index) => {
    if (!edu.schoolName?.trim()) {
      errors.push(`Education ${index + 1}: School name is required`);
    }
    if (!edu.degree?.trim()) {
      errors.push(`Education ${index + 1}: Degree is required`);
    }
  });
  
  return errors;
};

const validateCertifications = (data: ApplicantFormData['certifications']): string[] => {
  const errors: string[] = [];
  
  data.forEach((cert, index) => {
    if (!cert.certificationName?.trim()) {
      errors.push(`Certification ${index + 1}: Certification name is required`);
    }
    if (!cert.issuingOrganization?.trim()) {
      errors.push(`Certification ${index + 1}: Issuing organization is required`);
    }
  });
  
  return errors;
};

const validateLanguages = (data: ApplicantFormData['languages']): string[] => {
  const errors: string[] = [];
  
  data.forEach((lang, index) => {
    if (!lang.language?.trim()) {
      errors.push(`Language ${index + 1}: Language is required`);
    }
    if (!lang.proficiency.read && !lang.proficiency.speak && !lang.proficiency.write) {
      errors.push(`Language ${index + 1}: At least one proficiency level is required`);
    }
  });
  
  return errors;
};

export function useApplicantForm(initialApplicant?: Applicant | null): UseApplicantFormReturn {
  const [formData, setFormData] = useState<ApplicantFormData>(() =>
    getInitialFormData(initialApplicant)
  );
  const [initialData, setInitialData] = useState<ApplicantFormData>(() =>
    getInitialFormData(initialApplicant)
  );
  const [isDirty, setIsDirty] = useState(false);

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(initialData);
    setIsDirty(hasChanges);
  }, [formData, initialData]);

  const updateFormData = useCallback(<K extends keyof ApplicantFormData>(
    section: K,
    data: ApplicantFormData[K]
  ) => {
    setFormData(prev => ({
      ...prev,
      [section]: data,
    }));
  }, []);

  const resetForm = useCallback((applicant?: Applicant | null) => {
    const newData = getInitialFormData(applicant);
    setFormData(newData);
    setInitialData(newData);
    setIsDirty(false);
  }, []);

  const validateSection = useCallback((section: keyof ApplicantFormData): FormValidation => {
    const errors: ValidationErrors = {};
    
    switch (section) {
      case 'personalDetails': {
        const personalErrors = validatePersonalDetails(formData.personalDetails);
        if (personalErrors.length > 0) {
          errors.personalDetails = personalErrors;
        }
        break;
      }
      case 'workExperience': {
        const workErrors = validateWorkExperience(formData.workExperience);
        if (workErrors.length > 0) {
          errors.workExperience = workErrors;
        }
        break;
      }
      case 'educationDetails': {
        const eduErrors = validateEducationDetails(formData.educationDetails);
        if (eduErrors.length > 0) {
          errors.educationDetails = eduErrors;
        }
        break;
      }
      case 'certifications': {
        const certErrors = validateCertifications(formData.certifications);
        if (certErrors.length > 0) {
          errors.certifications = certErrors;
        }
        break;
      }
      case 'languages': {
        const langErrors = validateLanguages(formData.languages);
        if (langErrors.length > 0) {
          errors.languages = langErrors;
        }
        break;
      }
      default:
        break;
    }
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors,
    };
  }, [formData]);

  const validateForm = useCallback((): FormValidation => {
    const allErrors: ValidationErrors = {};
    
    // Validate all sections
    const sections: (keyof ApplicantFormData)[] = [
      'personalInfo',
      'professionalInfo',
      'workExperience',
      'educationDetails',
      'certifications',
      'languages'
    ];
    
    sections.forEach(section => {
      const validation = validateSection(section);
      if (!validation.isValid) {
        Object.assign(allErrors, validation.errors);
      }
    });
    
    return {
      isValid: Object.keys(allErrors).length === 0,
      errors: allErrors,
    };
  }, [validateSection]);

  return {
    formData,
    updateFormData,
    resetForm,
    validateForm,
    validateSection,
    isDirty,
    hasUnsavedChanges: isDirty,
  };
}
