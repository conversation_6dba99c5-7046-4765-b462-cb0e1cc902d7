import { useState, useCallback } from 'react';
import { OperationType, DialogType, ErrorType } from '@/components/applicants/ui';

interface DialogState {
  open: boolean;
  type: DialogType | null;
  operation: OperationType | null;
  errorType?: ErrorType;
  errorMessage?: string;
}

interface UseApplicantOperationDialogReturn {
  dialogState: DialogState;
  showSuccessDialog: (operation: OperationType) => void;
  showFailureDialog: (
    operation: OperationType, 
    errorType: ErrorType, 
    errorMessage?: string
  ) => void;
  closeDialog: () => void;
  handleRetry: (retryFn: () => void) => () => void;
}

const initialState: DialogState = {
  open: false,
  type: null,
  operation: null,
  errorType: undefined,
  errorMessage: undefined,
};

export function useApplicantOperationDialog(): UseApplicantOperationDialogReturn {
  const [dialogState, setDialogState] = useState<DialogState>(initialState);

  const showSuccessDialog = useCallback((operation: OperationType) => {
    setDialogState({
      open: true,
      type: 'success',
      operation,
      errorType: undefined,
      errorMessage: undefined,
    });
  }, []);

  const showFailureDialog = useCallback((
    operation: OperationType,
    errorType: ErrorType,
    errorMessage?: string
  ) => {
    setDialogState({
      open: true,
      type: 'failure',
      operation,
      errorType,
      errorMessage,
    });
  }, []);

  const closeDialog = useCallback(() => {
    setDialogState(initialState);
  }, []);

  const handleRetry = useCallback((retryFn: () => void) => {
    return () => {
      closeDialog();
      retryFn();
    };
  }, [closeDialog]);

  return {
    dialogState,
    showSuccessDialog,
    showFailureDialog,
    closeDialog,
    handleRetry,
  };
}

// Helper function to determine error type from error object
export function getErrorType(error: unknown): { errorType: ErrorType; errorMessage?: string } {
  if (!error) {
    return { errorType: 'unknown' };
  }

  // Handle different error types
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    
    // Network errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return { errorType: 'network', errorMessage: error.message };
    }
    
    // Validation errors
    if (message.includes('validation') || message.includes('required') || message.includes('invalid')) {
      return { errorType: 'validation', errorMessage: error.message };
    }
    
    // Server errors (HTTP status codes)
    if (message.includes('500') || message.includes('server') || message.includes('internal')) {
      return { errorType: 'server', errorMessage: error.message };
    }
    
    return { errorType: 'unknown', errorMessage: error.message };
  }

  // Handle HTTP response errors
  if (typeof error === 'object' && error !== null) {
    const errorObj = error as Record<string, unknown>;
    
    if (errorObj.status) {
      const status = Number(errorObj.status);
      
      if (status >= 400 && status < 500) {
        return { 
          errorType: 'validation', 
          errorMessage: errorObj.message as string || 'Validation error occurred' 
        };
      }
      
      if (status >= 500) {
        return { 
          errorType: 'server', 
          errorMessage: errorObj.message as string || 'Server error occurred' 
        };
      }
    }
    
    if (errorObj.message) {
      return { errorType: 'unknown', errorMessage: errorObj.message as string };
    }
  }

  return { errorType: 'unknown' };
} 