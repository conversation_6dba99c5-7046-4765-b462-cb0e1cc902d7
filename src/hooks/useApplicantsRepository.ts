/**
 * Repository-based hooks for applicant management
 * 
 * This module provides React hooks that use the repository pattern
 * instead of direct API calls, enabling better testing and decoupling.
 */

import { useMemo } from 'react';
import { useToast } from '@/hooks/use-toast';
import { config, isFeatureEnabled } from '@/config/appConfig';
import { ApplicantRepository } from '@/repositories/ApplicantRepository';
import { ApiApplicantRepository } from '@/repositories/ApiApplicantRepository';
import { MockApplicantRepository } from '@/repositories/MockApplicantRepository';
import {
  ApplicantListParams,
  GetAllApplicantsResponse,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  PatchApplicantFormData,
  PatchApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  ApiSelectOption,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
} from '@/types/applicants';
import { ApiError } from '@/lib/apiConfig';

/**
 * Configuration for repository creation
 */
interface UseRepositoryConfig {
  enableMocking?: boolean;
  enableCaching?: boolean;
  enableRetry?: boolean;
}

/**
 * Hook that provides the appropriate repository instance based on configuration
 */
export function useApplicantRepository(customConfig?: UseRepositoryConfig): ApplicantRepository {
  return useMemo(() => {
    const shouldUseMock = customConfig?.enableMocking ?? isFeatureEnabled('enableMocking');
    
    const repositoryConfig = {
      enableCaching: customConfig?.enableCaching ?? config.performance.enableCaching,
      enableRetry: customConfig?.enableRetry ?? true,
      maxRetries: config.api.maxRetries,
      retryDelay: config.api.retryDelay,
      cacheTimeout: config.performance.cacheTimeout,
    };

    if (shouldUseMock) {
      return new MockApplicantRepository(repositoryConfig);
    } else {
      return new ApiApplicantRepository(repositoryConfig);
    }
  }, [customConfig]);
}

/**
 * Hook for listing applicants with enhanced error handling and toast notifications
 */
export function useListApplicants() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const listApplicants = async (
    params?: { query?: ApplicantListParams }
  ): Promise<GetAllApplicantsResponse | undefined> => {
    return repository.listApplicants(params, (error: ApiError) => {
      toast({
        title: "Error Loading Applicants",
        description: error.message || "Failed to load applicant list",
        variant: "destructive",
      });
    });
  };

  return { listApplicants };
}

/**
 * Hook for getting a single applicant
 */
export function useGetApplicant() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const getApplicant = async (
    applicantId: string
  ): Promise<ApplicantDetailResponse | undefined> => {
    return repository.getApplicant(
      { pathParams: { applicantId } },
      (error: ApiError) => {
        toast({
          title: "Error Loading Applicant",
          description: error.message || "Failed to load applicant details",
          variant: "destructive",
        });
      }
    );
  };

  return { getApplicant };
}

/**
 * Hook for creating a new applicant
 */
export function useCreateApplicant() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const createApplicant = async (
    applicantData: CreateApplicantRequest
  ): Promise<CreateApplicantResponse | undefined> => {
    const result = await repository.createApplicant(
      { body: applicantData },
      (error: ApiError) => {
        toast({
          title: "Error Creating Applicant",
          description: error.message || "Failed to create applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant created successfully",
        variant: "default",
      });
    }

    return result;
  };

  return { createApplicant };
}

/**
 * Hook for creating an applicant with files
 */
export function useCreateApplicantWithFiles() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const createApplicantWithFiles = async (
    params: CreateApplicantWithFilesRequest
  ): Promise<CreateApplicantResponse | undefined> => {
    const result = await repository.createApplicantWithFiles(params, (error: ApiError) => {
      toast({
        title: "Error Creating Applicant",
        description: error.message || "Failed to create applicant with files",
        variant: "destructive",
      });
    });

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant and files uploaded successfully",
        variant: "default",
      });
    }

    return result;
  };

  return { createApplicantWithFiles };
}

/**
 * Hook for updating an applicant
 */
export function useUpdateApplicant() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const updateApplicant = async (
    applicantId: string,
    patchData: PatchApplicantFormData
  ): Promise<PatchApplicantResponse | undefined> => {
    const result = await repository.updateApplicant(
      { pathParams: { applicantId }, body: patchData },
      (error: ApiError) => {
        toast({
          title: "Error Updating Applicant",
          description: error.message || "Failed to update applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant updated successfully",
        variant: "default",
      });
    }

    return result;
  };

  return { updateApplicant };
}

/**
 * Hook for deleting an applicant
 */
export function useDeleteApplicant() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const deleteApplicant = async (
    applicantId: string
  ): Promise<DeleteApplicantResponse | undefined> => {
    const result = await repository.deleteApplicant(
      { pathParams: { applicantId } },
      (error: ApiError) => {
        toast({
          title: "Error Deleting Applicant",
          description: error.message || "Failed to delete applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant deleted successfully",
        variant: "default",
      });
    }

    return result;
  };

  return { deleteApplicant };
}

/**
 * Hook for activating an applicant
 */
export function useActivateApplicant() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const activateApplicant = async (
    applicantId: string
  ): Promise<ActivateApplicantResponse | undefined> => {
    const result = await repository.activateApplicant(
      { pathParams: { applicantId } },
      (error: ApiError) => {
        toast({
          title: "Error Activating Applicant",
          description: error.message || "Failed to activate applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant activated successfully",
        variant: "default",
      });
    }

    return result;
  };

  return { activateApplicant };
}

/**
 * Hook for getting list values
 */
export function useGetListValues() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const getListValues = async (
    listNameId: string
  ): Promise<ApiSelectOption[] | undefined> => {
    return repository.getListValuesById(listNameId, (error: ApiError) => {
      toast({
        title: "Error Loading List Values",
        description: error.message || "Failed to load list values",
        variant: "destructive",
      });
    });
  };

  return { getListValues };
}

/**
 * Hook for getting document tree
 */
export function useGetDocumentTree() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const getDocumentTree = async (
    resourceId: string
  ): Promise<DocumentTreeResponse | undefined> => {
    return repository.getDocumentTree(resourceId, (error: ApiError) => {
      toast({
        title: "Error Loading Document Tree",
        description: error.message || "Failed to load document tree",
        variant: "destructive",
      });
    });
  };

  return { getDocumentTree };
}

/**
 * Hook for getting all list values
 */
export function useGetAllListValues() {
  const { toast } = useToast();
  const repository = useApplicantRepository();

  const getAllListValues = async (): Promise<GetAllGlobalListValuesDTO | undefined> => {
    return repository.getAllListValues((error: ApiError) => {
      toast({
        title: "Error Loading Global List Values",
        description: error.message || "Failed to load global list values",
        variant: "destructive",
      });
    });
  };

  return { getAllListValues };
}

/**
 * Comprehensive hook that provides all applicant operations
 */
export function useApplicantOperations() {
  const { listApplicants } = useListApplicants();
  const { getApplicant } = useGetApplicant();
  const { createApplicant } = useCreateApplicant();
  const { createApplicantWithFiles } = useCreateApplicantWithFiles();
  const { updateApplicant } = useUpdateApplicant();
  const { deleteApplicant } = useDeleteApplicant();
  const { activateApplicant } = useActivateApplicant();
  const { getListValues } = useGetListValues();
  const { getDocumentTree } = useGetDocumentTree();
  const { getAllListValues } = useGetAllListValues();

  return {
    listApplicants,
    getApplicant,
    createApplicant,
    createApplicantWithFiles,
    updateApplicant,
    deleteApplicant,
    activateApplicant,
    getListValues,
    getDocumentTree,
    getAllListValues,
  };
}

/**
 * Hook for repository configuration and management
 */
export function useRepositoryManager() {
  const repository = useApplicantRepository();

  return {
    repository,
    // Type guard to check if repository is API-based
    isApiRepository: () => repository instanceof ApiApplicantRepository,
    // Type guard to check if repository is mock-based
    isMockRepository: () => repository instanceof MockApplicantRepository,
    // Get cache statistics if available
    getCacheStats: () => {
      if (repository instanceof ApiApplicantRepository) {
        return repository.getCacheStats();
      }
      return null;
    },
    // Clear cache if available
    clearCache: () => {
      if (repository instanceof ApiApplicantRepository) {
        repository.clearCache();
      }
    },
  };
} 