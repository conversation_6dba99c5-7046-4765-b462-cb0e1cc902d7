import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Settings,
  Eye,
  BarChart3,
  Users,
  Calendar,
  CheckCircle,
  ArrowRight,
  Sparkles,
} from 'lucide-react';

interface DemoScreen {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  features: string[];
  status: 'completed' | 'in-progress' | 'planned';
}

const FormBuilderDemo: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<string>('dashboard');

  const demoScreens: DemoScreen[] = [
    {
      id: 'dashboard',
      title: 'Form Builder Dashboard',
      description: 'Central hub for managing all your forms with analytics and quick actions',
      icon: FileText,
      features: [
        'Form overview with stats',
        'Search and filter forms',
        'Quick actions (edit, duplicate, share)',
        'Form status management',
        'Usage analytics',
      ],
      status: 'completed',
    },
    {
      id: 'templates',
      title: 'Template Gallery',
      description: 'Pre-built form templates for common HR scenarios',
      icon: Sparkles,
      features: [
        'Categorized templates',
        'Template preview',
        'Usage statistics',
        'One-click template usage',
        'Custom template creation',
      ],
      status: 'completed',
    },
    {
      id: 'builder',
      title: 'Visual Form Builder',
      description: 'Drag-and-drop interface for creating custom forms',
      icon: Settings,
      features: [
        'Drag-and-drop field library',
        'Live form preview',
        'Field property configuration',
        'Form settings management',
        'Real-time validation',
      ],
      status: 'completed',
    },
    {
      id: 'preview',
      title: 'Public Form View',
      description: 'Candidate-facing form with professional design',
      icon: Eye,
      features: [
        'Mobile-responsive design',
        'Progress tracking',
        'Field validation',
        'File upload support',
        'Thank you page',
      ],
      status: 'completed',
    },
    {
      id: 'responses',
      title: 'Response Management',
      description: 'Comprehensive response tracking and analytics',
      icon: BarChart3,
      features: [
        'Response overview dashboard',
        'Detailed response viewer',
        'Export capabilities',
        'Search and filtering',
        'Response analytics',
      ],
      status: 'completed',
    },
  ];

  const getStatusBadge = (status: DemoScreen['status']) => {
    const variants = {
      completed: { variant: 'default' as const, label: 'Completed', color: 'text-green-600' },
      'in-progress': { variant: 'secondary' as const, label: 'In Progress', color: 'text-yellow-600' },
      planned: { variant: 'outline' as const, label: 'Planned', color: 'text-gray-600' },
    };

    const config = variants[status];
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const getStatusIcon = (status: DemoScreen['status']) => {
    if (status === 'completed') {
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
            <FileText className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Form Builder Demo</h1>
            <p className="text-muted-foreground">
              Interactive showcase of all form builder screens and features
            </p>
          </div>
        </div>

        <div className="flex items-center justify-center gap-4">
          <Badge variant="secondary" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            5 Screens Completed
          </Badge>
          <Badge variant="outline">
            Mock Data Ready
          </Badge>
          <Badge variant="outline">
            Business Review Ready
          </Badge>
        </div>
      </div>

      {/* Demo Navigation */}
      <Tabs value={activeDemo} onValueChange={setActiveDemo} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          {demoScreens.map((screen) => {
            const Icon = screen.icon;
            return (
              <TabsTrigger key={screen.id} value={screen.id} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span className="hidden sm:inline">{screen.title.split(' ')[0]}</span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {demoScreens.map((screen) => {
          const Icon = screen.icon;
          return (
            <TabsContent key={screen.id} value={screen.id} className="mt-6">
              <Card>
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center gap-4">
                      <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="flex items-center gap-3">
                          {screen.title}
                          {getStatusIcon(screen.status)}
                        </CardTitle>
                        <p className="text-muted-foreground mt-1">
                          {screen.description}
                        </p>
                      </div>
                    </div>
                    {getStatusBadge(screen.status)}
                  </div>
                </CardHeader>

                <CardContent className="space-y-6">
                  {/* Features List */}
                  <div>
                    <h4 className="font-semibold mb-3">Key Features</h4>
                    <div className="grid gap-2 md:grid-cols-2">
                      {screen.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                          <span className="text-sm">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Demo Actions */}
                  <div className="flex gap-3 pt-4 border-t">
                    <Button 
                      onClick={() => {
                        // In real app: navigate to the actual screen
                        window.open(`/form-builder${screen.id === 'dashboard' ? '' : `/${screen.id}`}`, '_blank');
                      }}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      View Live Demo
                    </Button>
                    
                    <Button variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      View Code
                    </Button>

                    {screen.id !== 'responses' && (
                      <Button 
                        variant="ghost"
                        onClick={() => {
                          const nextIndex = demoScreens.findIndex(s => s.id === screen.id) + 1;
                          if (nextIndex < demoScreens.length) {
                            setActiveDemo(demoScreens[nextIndex].id);
                          }
                        }}
                      >
                        Next Screen
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          );
        })}
      </Tabs>

      {/* Implementation Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Implementation Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            <div>
              <h4 className="font-semibold mb-3">What's Included</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Complete UI screens with mock data
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  TypeScript interfaces and types
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Responsive design with shadcn/ui
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Form validation and error handling
                </li>
                <li className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Navigation integration
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Next Steps</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  Business team review and feedback
                </li>
                <li className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  Scope finalization and prioritization
                </li>
                <li className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  Backend API design and implementation
                </li>
                <li className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  Database schema design
                </li>
                <li className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  Integration with existing systems
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FormBuilderDemo;
