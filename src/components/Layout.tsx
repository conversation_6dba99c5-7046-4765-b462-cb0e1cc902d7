import { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import NotificationCenter from '@/components/NotificationCenter';
import { LEFT_MENU_CONFIGURATION } from '@/config/LeftMenuConfiguration';
import * as LucideIcons from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import {
  Menu,
  LogOut,
  User,
  ChevronDown,
  Building2,
  Settings as SettingsIcon
} from 'lucide-react';

// Dynamic icon loading function
const getDynamicIcon = (iconName: string): LucideIcon => {
  // Get the icon from Lucide React dynamically
  const IconComponent = (LucideIcons as Record<string, LucideIcon>)[iconName];

  // Return the icon component or a fallback
  return IconComponent || LucideIcons.BarChart3;
};

// Convert menu configuration to navigation format
const getNavigationFromConfig = () => {
  return LEFT_MENU_CONFIGURATION.map(item => ({
    name: item.title,
    href: item.path,
    icon: getDynamicIcon(item.icon)
  }));
};

// Base navigation items from file-based configuration
const baseNavigation = getNavigationFromConfig();

// Client-specific navigation items (exclude vendor-only routes)
const clientNavigation = baseNavigation.filter(item =>
  item.href !== '/vendor-jobs' // Vendor Jobs is only for vendor organizations
);

// Vendor-specific navigation items (exclude client-only routes)
const vendorNavigation = baseNavigation.filter(item =>
  item.href !== '/vendors' && // Vendor Management is only for client organizations
  item.href !== '/vendor-submissions' // Vendor Submissions is only for client organizations
);

export default function Layout() {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const {
    user,
    organization,
    logout,
    isClientOrganization,
    isVendorOrganization
  } = useAuth();

  // Get navigation based on organization type and user role
  const getNavigation = () => {
    let nav = baseNavigation;

    if (isClientOrganization()) {
      nav = clientNavigation;
    } else if (isVendorOrganization()) {
      nav = vendorNavigation;
    }

    // Filter out super admin items for non-super admin users
    if (user?.role !== 'super_admin') {
      nav = nav.filter(item => item.href !== '/super-admin');
    }

    return nav;
  };

  const navigation = getNavigation();

  const handleLogout = async () => {
    try {
      await logout();
      // Redirect will be handled by the auth context
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      <div className="flex h-16 shrink-0 items-center px-6 border-b">
        <Building2 className="h-8 w-8 text-blue-600" />
        <span className="ml-2 text-xl font-bold">TalentFlow</span>
      </div>

      {/* User Profile Section */}
      <div className="px-4 py-3 border-b bg-gray-50">
        <div className="flex items-center space-x-3">
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.avatar} alt={`${user?.firstName} ${user?.lastName}`} />
            <AvatarFallback className="text-xs">
              {user?.firstName?.[0]}{user?.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.firstName} {user?.lastName}
            </p>
            <p className="text-xs text-gray-500 truncate">
              {user?.role} at {organization?.name}
            </p>
          </div>
        </div>
      </div>
      <nav className="flex-1 space-y-1 px-4 py-4">
        {navigation.map((item) => {
          const Icon = item.icon;
          const isActive = location.pathname === item.href;
          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                'group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors',
                isActive
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
              )}
              onClick={() => setSidebarOpen(false)}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar */}
      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            className="fixed top-4 left-4 z-40 lg:hidden"
            size="icon"
          >
            <Menu className="h-6 w-6" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-white border-r">
          <SidebarContent />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <div className="flex justify-end items-center h-16">
              {/* Mobile menu button */}
              <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="lg:hidden absolute left-4">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="w-64 p-0">
                  {/* Mobile sidebar content */}
                  <div className="flex flex-col h-full">
                    <div className="flex items-center px-4 py-3 border-b">
                      <Building2 className="h-8 w-8 text-blue-600" />
                      <span className="ml-2 text-lg font-semibold">ATS Platform</span>
                    </div>

                    {/* User Profile Section */}
                    <div className="px-4 py-3 border-b bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user?.avatar} alt={`${user?.firstName} ${user?.lastName}`} />
                          <AvatarFallback className="text-xs">
                            {user?.firstName?.[0]}{user?.lastName?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {user?.firstName} {user?.lastName}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {user?.role} at {organization?.name}
                          </p>
                        </div>
                      </div>
                    </div>

                    <nav className="flex-1 space-y-1 px-4 py-4">
                      {navigation.map((item) => {
                        const Icon = item.icon;
                        return (
                          <Link
                            key={item.name}
                            to={item.href}
                            className={cn(
                              location.pathname === item.href
                                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                                : 'text-gray-700 hover:bg-gray-50',
                              'group flex items-center px-2 py-2 text-sm font-medium rounded-md'
                            )}
                            onClick={() => setSidebarOpen(false)}
                          >
                            <Icon className="mr-3 h-5 w-5" />
                            {item.name}
                          </Link>
                        );
                      })}
                    </nav>
                  </div>
                </SheetContent>
              </Sheet>

              {/* Right side - Notifications and User Menu */}
              <div className="flex items-center space-x-4">
                <NotificationCenter />

                {/* User Menu */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="flex items-center space-x-2 px-2 py-1 h-auto">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user?.avatar} alt={`${user?.firstName} ${user?.lastName}`} />
                        <AvatarFallback className="text-xs">
                          {user?.firstName?.[0]}{user?.lastName?.[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div className="hidden sm:block text-left">
                        <p className="text-sm font-medium text-gray-900">
                          {user?.firstName} {user?.lastName}
                        </p>
                        <p className="text-xs text-gray-500">
                          {organization?.name}
                        </p>
                      </div>
                      <ChevronDown className="h-4 w-4 text-gray-400 hidden sm:block" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium">{user?.firstName} {user?.lastName}</p>
                        <p className="text-xs text-gray-500">{user?.email}</p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link to="/profile" className="flex items-center">
                        <User className="mr-2 h-4 w-4" />
                        My Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/settings" className="flex items-center">
                        <SettingsIcon className="mr-2 h-4 w-4" />
                        Organization Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout} className="text-red-600">
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </header>

        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
}