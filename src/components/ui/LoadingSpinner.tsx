import { cn } from "@/lib/utils";
import { Loader2 } from "lucide-react";

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg";
  className?: string;
  message?: string;
  // Accessibility props
  ariaLabel?: string;
  role?: string;
}

export function LoadingSpinner({ 
  size = "md", 
  className,
  message,
  ariaLabel = "Loading",
  role = "status"
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8"
  };

  return (
    <div 
      className={cn("flex items-center justify-center gap-2", className)}
      role={role}
      aria-label={ariaLabel}
      aria-live="polite"
    >
      <Loader2 
        className={cn("animate-spin text-primary", sizeClasses[size])} 
        aria-hidden="true"
      />
      {message && (
        <span className="text-sm text-muted-foreground" aria-live="polite">
          {message}
        </span>
      )}
      {/* Screen reader only text */}
      <span className="sr-only">
        {message || ariaLabel}
      </span>
    </div>
  );
}

interface LoadingOverlayProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
  // Accessibility props
  ariaLabel?: string;
}

export function LoadingOverlay({ 
  isLoading, 
  message, 
  children,
  ariaLabel = "Content is loading"
}: LoadingOverlayProps) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div 
          className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-10"
          role="dialog"
          aria-modal="true"
          aria-label={ariaLabel}
          aria-busy="true"
        >
          <LoadingSpinner 
            size="lg" 
            message={message} 
            ariaLabel={ariaLabel}
          />
        </div>
      )}
    </div>
  );
}

interface LoadingStateProps {
  loading: boolean;
  error?: string | null;
  children: React.ReactNode;
  loadingMessage?: string;
  errorTitle?: string;
  onRetry?: () => void;
  // Accessibility props
  loadingAriaLabel?: string;
  errorAriaLabel?: string;
}

export function LoadingState({
  loading,
  error,
  children,
  loadingMessage,
  errorTitle = "Something went wrong",
  onRetry,
  loadingAriaLabel = "Loading content",
  errorAriaLabel = "Error occurred"
}: LoadingStateProps) {
  if (loading) {
    return (
      <div 
        className="flex items-center justify-center p-4 sm:p-6 md:p-8"
        role="status"
        aria-label={loadingAriaLabel}
      >
        <LoadingSpinner 
          size="lg" 
          message={loadingMessage} 
          ariaLabel={loadingAriaLabel}
        />
      </div>
    );
  }

  if (error) {
    return (
      <div 
        className="flex flex-col items-center justify-center p-4 sm:p-6 md:p-8 text-center max-w-md mx-auto"
        role="alert"
        aria-label={errorAriaLabel}
      >
        <div className="text-destructive mb-2 text-base sm:text-lg font-medium">
          {errorTitle}
        </div>
        <div className="text-sm text-muted-foreground mb-4 leading-relaxed">
          {error}
        </div>
        {onRetry && (
          <button
            onClick={onRetry}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
            aria-label="Retry the failed operation"
          >
            Try Again
          </button>
        )}
      </div>
    );
  }

  return <>{children}</>;
} 