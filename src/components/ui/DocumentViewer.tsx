import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useToast } from '@/hooks/use-toast';
import { getFileByLocation } from '@/api/applicantsApi';
import { Maximize2, Minimize2, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DocumentViewerProps {
  isOpen: boolean;
  onClose: () => void;
  fileLocation?: string;
  fileName?: string;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({
  isOpen,
  onClose,
  fileLocation,
  fileName
}) => {
  const { toast } = useToast();
  const [fileContent, setFileContent] = useState<string | null>(null);
  const [fileType, setFileType] = useState<string | null>(null);
  const [isLarge, setIsLarge] = useState(false);
  const [docxContent, setDocxContent] = useState<string | null>(null);
  const [xlsxContent, setXlsxContent] = useState<Record<string, unknown>[] | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFileContent = async () => {
      if (!fileLocation) return;

      try {
        setLoading(true);
        const response = await getFileByLocation(
          fileLocation,
          (error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to load document",
              variant: "destructive",
            });
          }
        );

        if (!response) return;

        const fileData = response.data;
        const extractedFileName = fileName || fileLocation.split("/").pop() || 'document';
        const fileExtension = extractedFileName.split(".").pop()?.toLowerCase() || '';

        let inferredFileType: string;
        switch (fileExtension) {
          case 'pdf':
            inferredFileType = 'application/pdf';
            break;
          case 'docx':
            inferredFileType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            break;
          case 'xlsx':
          case 'xls':
            inferredFileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            break;
          case 'csv':
            inferredFileType = 'text/csv';
            break;
          case 'jpg':
          case 'jpeg':
          case 'png':
          case 'gif':
          case 'bmp':
          case 'webp':
            inferredFileType = `image/${fileExtension}`;
            break;
          default:
            inferredFileType = 'unknown';
        }

        setFileType(inferredFileType);

        if (inferredFileType === 'application/pdf') {
          // For PDF, create blob URL
          const binaryString = atob(fileData);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          const blob = new Blob([bytes], { type: 'application/pdf' });
          const pdfUrl = URL.createObjectURL(blob);
          setFileContent(pdfUrl);
        } else if (inferredFileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
          // For DOCX, we'd need mammoth library - for now show a message
          setDocxContent('<div class="p-4 text-center">DOCX files require additional processing. Please download to view.</div>');
        } else if (inferredFileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || inferredFileType === 'text/csv') {
          // For Excel/CSV, we'd need xlsx library - for now show a message
          setXlsxContent([{ message: 'Excel/CSV files require additional processing. Please download to view.' }]);
        } else if (inferredFileType.startsWith('image/')) {
          // For images, set as base64 data URL
          setFileContent(fileData);
        } else {
          // For other file types
          setFileContent(fileData);
        }
      } catch (error) {
        console.error("Error fetching file content:", error);
        toast({
          title: "Error",
          description: "Failed to load document content",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    if (isOpen && fileLocation) {
      fetchFileContent();
    }
  }, [isOpen, fileLocation, fileName, toast]);

  const renderFileContent = () => {
    if (loading) {
      return (
        <div className="flex justify-center items-center w-full h-full min-h-[300px]">
          <LoadingSpinner />
        </div>
      );
    }

    if (!fileType) {
      return <div className="p-4 text-center text-gray-500">File type not determined.</div>;
    }

    if (fileType.startsWith('image/')) {
      const imageUrl = `data:${fileType};base64,${fileContent}`;
      return (
        <img 
          src={imageUrl} 
          alt="Document" 
          className="w-full h-full object-contain"
        />
      );
    } else if (fileType === 'application/pdf') {
      return (
        <iframe
          src={fileContent || ''}
          className="w-full h-full border-0"
          title="PDF Viewer"
        />
      );
    } else if (fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return (
        <div
          dangerouslySetInnerHTML={{ __html: docxContent || '' }}
          className="w-full h-full overflow-y-auto p-4"
        />
      );
    } else if (fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || fileType === 'text/csv') {
      return (
        <div className="w-full h-full overflow-auto p-4">
          <div className="text-center text-gray-500">
            {(xlsxContent?.[0]?.message as string) || 'Spreadsheet content would be displayed here'}
          </div>
        </div>
      );
    } else {
      return (
        <div className="p-4 text-center text-gray-500">
          Unsupported file type: {fileType}
        </div>
      );
    }
  };

  const handleClose = () => {
    // Clean up blob URLs to prevent memory leaks
    if (fileContent && fileContent.startsWith('blob:')) {
      URL.revokeObjectURL(fileContent);
    }
    setFileContent(null);
    setFileType(null);
    setDocxContent(null);
    setXlsxContent(null);
    setLoading(true);
    onClose();
  };

  const toggleSize = () => {
    setIsLarge(prev => !prev);
  };

  const displayFileName = fileName || fileLocation?.split("/").pop() || 'Document';

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent 
        className={cn(
          "p-0 gap-0 [&>button]:hidden flex flex-col",
          isLarge ? "max-w-6xl w-[90vw] h-[90vh]" : "max-w-4xl w-[80vw] h-[70vh]"
        )}
      >
        <DialogHeader className="px-4 py-2 pb-0 border-b flex-row items-center justify-between space-y-0 min-h-0 h-12 flex-shrink-0">
          <DialogTitle className="text-base font-semibold truncate flex-1 pr-4">
            {displayFileName}
          </DialogTitle>
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleSize}
              className="h-6 w-6 p-0 hover:bg-gray-100"
              title={isLarge ? "Minimize" : "Maximize"}
            >
              {isLarge ? (
                <Minimize2 className="h-3 w-3" />
              ) : (
                <Maximize2 className="h-3 w-3" />
              )}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-6 w-6 p-0 hover:bg-gray-100"
              title="Close"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </DialogHeader>
        
        <div className="flex-1 min-h-0 relative overflow-hidden">
          {renderFileContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentViewer; 