/**
 * Lazy Load Fallback Components
 * 
 * Provides loading states for lazy-loaded components with different
 * contexts and visual appearances.
 */

import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface LazyLoadFallbackProps {
  variant?: 'default' | 'card' | 'form' | 'minimal' | 'wizard';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  message?: string;
}

/**
 * Main lazy load fallback component
 */
export function LazyLoadFallback({ 
  variant = 'default', 
  size = 'md',
  className,
  message = 'Loading...'
}: LazyLoadFallbackProps) {
  const baseClasses = "flex items-center justify-center";
  
  const sizeClasses = {
    sm: "h-20",
    md: "h-32", 
    lg: "h-48",
    xl: "h-64"
  };

  switch (variant) {
    case 'card':
      return <CardFallbackInternal size={size} className={className} message={message} />;
    case 'form':
      return <FormFallbackInternal size={size} className={className} message={message} />;
    case 'wizard':
      return <WizardFallbackInternal size={size} className={className} message={message} />;
    case 'minimal':
      return <MinimalFallbackInternal size={size} className={className} message={message} />;
    default:
      return (
        <div className={cn(baseClasses, sizeClasses[size], className)}>
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
            <p className="text-sm text-muted-foreground">{message}</p>
          </div>
        </div>
      );
  }
}

/**
 * Card variant for components that render as cards
 */
function CardFallbackInternal({ size, className, message }: Omit<LazyLoadFallbackProps, 'variant'>) {
  const heightClasses = {
    sm: "h-24",
    md: "h-32",
    lg: "h-48", 
    xl: "h-64"
  };

  return (
    <Card className={cn(heightClasses[size || 'md'], className)}>
      <CardHeader>
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-center h-full">
          <div className="flex flex-col items-center space-y-2">
            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            <p className="text-xs text-muted-foreground">{message}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Form variant for form components
 */
function FormFallbackInternal({ size, className, message }: Omit<LazyLoadFallbackProps, 'variant'>) {
  const heightClasses = {
    sm: "h-32",
    md: "h-48",
    lg: "h-64",
    xl: "h-80"
  };

  return (
    <div className={cn("space-y-4 p-4", heightClasses[size || 'md'], className)}>
      {/* Form field skeletons */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-10 w-full" />
      </div>
      <div className="space-y-2">
        <Skeleton className="h-4 w-28" />
        <Skeleton className="h-20 w-full" />
      </div>
      
      {/* Loading indicator */}
      <div className="flex items-center justify-center pt-4">
        <div className="flex flex-col items-center space-y-2">
          <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
          <p className="text-xs text-muted-foreground">{message}</p>
        </div>
      </div>
    </div>
  );
}

/**
 * Wizard variant for wizard step components
 */
function WizardFallbackInternal({ size, className, message }: Omit<LazyLoadFallbackProps, 'variant'>) {
  const heightClasses = {
    sm: "h-40",
    md: "h-56",
    lg: "h-72",
    xl: "h-96"
  };

  return (
    <div className={cn("p-6", heightClasses[size || 'md'], className)}>
      {/* Wizard step skeleton */}
      <div className="space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-4 w-28" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
      
      {/* Loading indicator */}
      <div className="flex items-center justify-center pt-6">
        <div className="flex flex-col items-center space-y-2">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <p className="text-sm text-muted-foreground">{message}</p>
        </div>
      </div>
    </div>
  );
}

/**
 * Minimal variant for simple components
 */
function MinimalFallbackInternal({ size, className, message }: Omit<LazyLoadFallbackProps, 'variant'>) {
  const sizeClasses = {
    sm: "h-8",
    md: "h-12",
    lg: "h-16", 
    xl: "h-20"
  };

  return (
    <div className={cn("flex items-center justify-center", sizeClasses[size || 'md'], className)}>
      <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
      {message && (
        <span className="ml-2 text-sm text-muted-foreground">{message}</span>
      )}
    </div>
  );
}

/**
 * Pre-configured fallbacks for common use cases
 */
export const FormFallback = () => <LazyLoadFallback variant="form" message="Loading form..." />;
export const WizardFallback = () => <LazyLoadFallback variant="wizard" message="Loading step..." />;
export const CardFallback = () => <LazyLoadFallback variant="card" message="Loading..." />;
export const MinimalFallback = () => <LazyLoadFallback variant="minimal" />;

/**
 * Error boundary fallback for lazy loading failures
 */
interface LazyLoadErrorFallbackProps {
  error?: Error;
  resetError?: () => void;
  componentName?: string;
}

export function LazyLoadErrorFallback({ 
  error, 
  resetError, 
  componentName = 'component' 
}: LazyLoadErrorFallbackProps) {
  return (
    <Card className="border-destructive">
      <CardContent className="p-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="rounded-full bg-destructive/10 p-3">
            <svg
              className="h-6 w-6 text-destructive"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="font-medium">Failed to load {componentName}</h3>
            <p className="text-sm text-muted-foreground mt-1">
              {error?.message || 'An unexpected error occurred while loading this component.'}
            </p>
          </div>
          {resetError && (
            <button
              onClick={resetError}
              className="text-sm text-primary hover:underline"
            >
              Try again
            </button>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 