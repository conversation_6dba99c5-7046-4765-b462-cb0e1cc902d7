import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Check, ChevronDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface SelectOption {
  value: string;
  label: string;
}

export interface MultiSelectAutocompleteProps {
  options: SelectOption[];
  value?: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  emptyMessage?: string;
  disabled?: boolean;
  className?: string;
  id?: string;
  maxSelectedDisplay?: number;
  'aria-label'?: string;
  'aria-describedby'?: string;
}

/**
 * MultiSelectAutocomplete - A searchable multi-selection dropdown component
 * 
 * Features:
 * - Real-time search/filter functionality
 * - Multiple selection with removable tags/badges
 * - Keyboard navigation (arrow keys, enter, escape)
 * - Accessibility support with ARIA labels
 * - Controlled component pattern
 * - Configurable max display count for selected items
 * - Loading and error states support
 */
export const MultiSelectAutocomplete: React.FC<MultiSelectAutocompleteProps> = ({
  options,
  value = [],
  onChange,
  placeholder = "Select options...",
  searchPlaceholder = "Search options...",
  emptyMessage = "No options found",
  disabled = false,
  className,
  id,
  maxSelectedDisplay = 3,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
}) => {
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState('');
  const [showAllSelected, setShowAllSelected] = useState(false);
  const triggerRef = useRef<HTMLButtonElement>(null);

  // Find selected options
  const selectedOptions = options.filter(option => value.includes(option.value));

  // Filter options based on search
  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchValue.toLowerCase()) ||
    option.value.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Handle option selection/deselection
  const handleSelect = (selectedValue: string) => {
    const newValue = value.includes(selectedValue)
      ? value.filter(v => v !== selectedValue) // Remove if already selected
      : [...value, selectedValue]; // Add if not selected
    
    onChange(newValue);
  };

  // Handle removing a selected option
  const handleRemove = (valueToRemove: string, e?: React.MouseEvent) => {
    e?.stopPropagation();
    const newValue = value.filter(v => v !== valueToRemove);
    onChange(newValue);
  };

  // Handle clear all selections
  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    onChange([]);
  };

  // Reset search and collapse view when popover closes
  useEffect(() => {
    if (!open) {
      setSearchValue('');
      setShowAllSelected(false);
    }
  }, [open]);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setOpen(false);
      triggerRef.current?.focus();
    }
  };

  // Render selected items display
  const renderSelectedItems = () => {
    if (selectedOptions.length === 0) {
      return <span className="text-muted-foreground">{placeholder}</span>;
    }

    const shouldShowAll = showAllSelected || selectedOptions.length <= maxSelectedDisplay;
    const displayItems = shouldShowAll ? selectedOptions : selectedOptions.slice(0, maxSelectedDisplay);
    const remainingCount = selectedOptions.length - maxSelectedDisplay;

    return (
      <div className="flex flex-wrap gap-1 max-w-full">
        {displayItems.map((option) => (
          <Badge
            key={option.value}
            variant="secondary"
            className="text-xs px-2 py-1 max-w-[120px] flex items-center gap-1"
          >
            <span className="truncate">{option.label}</span>
            {!disabled && (
              <button
                type="button"
                onClick={(e) => handleRemove(option.value, e)}
                className="flex items-center justify-center p-0.5 rounded hover:bg-muted ml-1"
                tabIndex={-1}
              >
                <X className="h-3 w-3 hover:text-destructive" />
              </button>
            )}
          </Badge>
        ))}
        {remainingCount > 0 && !showAllSelected && (
          <Badge
            variant="outline"
            className="text-xs px-2 py-1 cursor-pointer hover:bg-accent"
            onClick={(e) => {
              e.stopPropagation();
              setShowAllSelected(true);
            }}
          >
            +{remainingCount} more
          </Badge>
        )}
        {showAllSelected && selectedOptions.length > maxSelectedDisplay && (
          <Badge
            variant="outline"
            className="text-xs px-2 py-1 cursor-pointer hover:bg-accent"
            onClick={(e) => {
              e.stopPropagation();
              setShowAllSelected(false);
            }}
          >
            Show less
          </Badge>
        )}
      </div>
    );
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          ref={triggerRef}
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label={ariaLabel}
          aria-describedby={ariaDescribedBy}
          disabled={disabled}
          className={cn(
            "w-full justify-between min-h-[40px] h-auto py-2",
            selectedOptions.length === 0 && "text-muted-foreground",
            className
          )}
          id={id}
        >
          <div className="flex-1 text-left overflow-hidden">
            {renderSelectedItems()}
          </div>
          <div className="flex items-center gap-1 ml-2">
            {selectedOptions.length > 0 && !disabled && (
              <button
                type="button"
                onClick={handleClearAll}
                className="flex items-center justify-center p-0.5 rounded hover:bg-muted"
                tabIndex={-1}
              >
                <X className="h-4 w-4 opacity-50 hover:opacity-100" />
              </button>
            )}
            <ChevronDown className="h-4 w-4 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0" align="start">
        <Command onKeyDown={handleKeyDown}>
          <CommandInput
            placeholder={searchPlaceholder}
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <CommandList>
            <CommandEmpty>{emptyMessage}</CommandEmpty>
            <CommandGroup>
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => handleSelect(option.value)}
                  className="cursor-pointer"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value.includes(option.value) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  <span className="truncate">{option.label}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

// FormField wrapper for better form integration
export interface MultiSelectAutocompleteFieldProps extends Omit<MultiSelectAutocompleteProps, 'id' | 'aria-label' | 'aria-describedby'> {
  id: string;
  label: string;
  required?: boolean;
  error?: string;
  description?: string;
  className?: string;
}

/**
 * FormField wrapper for MultiSelectAutocomplete
 * Provides consistent form field styling and validation display
 */
export const MultiSelectAutocompleteField: React.FC<MultiSelectAutocompleteFieldProps> = ({
  id,
  label,
  required = false,
  error,
  description,
  className,
  ...autocompleteProps
}) => {
  return (
    <div className={cn("space-y-2", className)}>
      <label
        htmlFor={id}
        className={cn(
          "text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",
          required && "after:content-['*'] after:ml-0.5 after:text-red-500"
        )}
      >
        {label}
      </label>
      <MultiSelectAutocomplete
        id={id}
        aria-label={label}
        aria-describedby={error ? `${id}-error` : description ? `${id}-description` : undefined}
        className={cn(error && "border-red-500")}
        {...autocompleteProps}
      />
      {description && !error && (
        <p id={`${id}-description`} className="text-sm text-muted-foreground">
          {description}
        </p>
      )}
      {error && (
        <p id={`${id}-error`} className="text-sm text-red-500">
          {error}
        </p>
      )}
    </div>
  );
};

export default MultiSelectAutocomplete;
