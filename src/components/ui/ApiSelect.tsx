import React, { useEffect, useState } from 'react';
import { Loader2 } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';
import { getListValuesById } from '@/api/applicantsApi';
import { ApiSelectOption } from '@/types/applicants';
import { useToast } from '@/hooks/use-toast';
import { announceToScreenReader } from '@/lib/accessibility';

interface ApiSelectProps {
  listNameId: string;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  disabled?: boolean;
  className?: string;
  // Accessibility props
  ariaLabel?: string;
  ariaDescribedBy?: string;
  id?: string;
  error?: string;
}

export const ApiSelect = React.forwardRef<
  React.ElementRef<typeof Select>,
  ApiSelectProps
>(({ 
  listNameId, 
  placeholder = "Select an option...", 
  value, 
  onValueChange, 
  disabled = false,
  className,
  ariaLabel,
  ariaDescribedBy,
  id,
  error,
  ...props 
}, ref) => {
  const [options, setOptions] = useState<ApiSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingAnnounced, setLoadingAnnounced] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchOptions = async () => {
      if (!listNameId) return;
      
      setLoading(true);
      setLoadingAnnounced(false);
      
      // Announce loading state to screen readers
      if (!loadingAnnounced) {
        announceToScreenReader('Loading options...', 'polite');
        setLoadingAnnounced(true);
      }
      
      const result = await getListValuesById(
        listNameId,
        (error) => {
          // Announce error to screen readers
          announceToScreenReader(
            `Error loading options: ${error.message || "Failed to load options"}`, 
            'assertive', 
            3000
          );
          
          toast({
            title: "Error",
            description: error.message || "Failed to load options",
            variant: "destructive",
          });
        }
      );

      if (result) {
        setOptions(result);
        
        // Announce successful loading to screen readers
        announceToScreenReader(`${result.length} options loaded`, 'polite');
      }
      
      setLoading(false);
    };

    fetchOptions();
  }, [listNameId, toast, loadingAnnounced]);

  // Generate IDs for accessibility
  const selectId = id || `api-select-${listNameId}`;
  const errorId = error ? `${selectId}-error` : undefined;
  const loadingId = loading ? `${selectId}-loading` : undefined;
  
  return (
    <div className="space-y-1">
      <Select 
        value={value} 
        onValueChange={onValueChange} 
        disabled={disabled || loading}
        {...props}
      >
        <SelectTrigger 
          className={className}
          id={selectId}
          aria-label={ariaLabel}
          aria-describedby={[ariaDescribedBy, errorId, loadingId].filter(Boolean).join(' ') || undefined}
          aria-invalid={error ? 'true' : undefined}
          aria-busy={loading}
        >
          {loading ? (
            <div className="flex items-center" id={loadingId}>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
              <span>Loading options...</span>
            </div>
          ) : (
            <SelectValue placeholder={placeholder} />
          )}
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem 
              key={option.value} 
              value={option.value}
              aria-describedby={`${selectId}-option-${option.value}`}
            >
              {option.key}
            </SelectItem>
          ))}
          {options.length === 0 && !loading && (
            <SelectItem value="" disabled aria-label="No options available">
              No options available
            </SelectItem>
          )}
        </SelectContent>
      </Select>
      
      {/* Error message for screen readers */}
      {error && (
        <div 
          id={errorId}
          className="text-sm text-destructive"
          role="alert"
          aria-live="polite"
        >
          {error}
        </div>
      )}
      
      {/* Screen reader only loading status */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {loading && "Loading options, please wait..."}
        {!loading && options.length > 0 && `${options.length} options available`}
        {!loading && options.length === 0 && "No options available"}
      </div>
    </div>
  );
});

ApiSelect.displayName = "ApiSelect"; 