import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { mockApplicants } from '@/data/mockData';
import { Interview } from '@/types';
import { format, parseISO } from 'date-fns';
import { Star, FileText } from 'lucide-react';

interface InterviewFeedbackDialogProps {
  readonly isOpen: boolean;
  readonly onClose: () => void;
  readonly selectedInterview: Interview | null;
  readonly feedback: {
    rating: number;
    notes: string;
    recommendation: string;
    strengths: string;
    concerns: string;
    nextSteps: string;
  };
  readonly onFeedbackChange: (field: string, value: string | number) => void;
  readonly onSubmitFeedback: () => void;
}

const formatInterviewTime = (interview: Interview) => {
  try {
    const dateTime = parseISO(`${interview.date}T${interview.time}`);
    return format(dateTime, 'MMM d, yyyy h:mm a');
  } catch {
    return `${interview.date} ${interview.time}`;
  }
};

export default function InterviewFeedbackDialog({
  isOpen,
  onClose,
  selectedInterview,
  feedback,
  onFeedbackChange,
  onSubmitFeedback
}: InterviewFeedbackDialogProps) {
  if (!selectedInterview) return null;

  const candidate = mockApplicants.find(c => c.id === selectedInterview.candidateId);

  const renderStarRating = () => {
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onFeedbackChange('rating', star)}
            className={`p-1 rounded ${
              star <= feedback.rating
                ? 'text-yellow-400 hover:text-yellow-500'
                : 'text-gray-300 hover:text-gray-400'
            }`}
          >
            <Star className="h-6 w-6 fill-current" />
          </button>
        ))}
        <span className="ml-2 text-sm text-gray-600">
          {feedback.rating > 0 ? `${feedback.rating}/5` : 'No rating'}
        </span>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Interview Feedback</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Interview Details */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-semibold text-lg mb-2">
              {candidate?.firstName} {candidate?.lastName}
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div>
                <span className="font-medium">Position:</span> {candidate?.position}
              </div>
              <div>
                <span className="font-medium">Interview Date:</span> {formatInterviewTime(selectedInterview)}
              </div>
              <div>
                <span className="font-medium">Type:</span> {selectedInterview.type}
              </div>
              <div>
                <span className="font-medium">Interviewer:</span> {selectedInterview.interviewer}
              </div>
            </div>
          </div>

          {/* Rating */}
          <div>
            <Label className="text-base font-medium">Overall Rating</Label>
            <div className="mt-2">
              {renderStarRating()}
            </div>
          </div>

          {/* Recommendation */}
          <div>
            <Label htmlFor="recommendation">Recommendation</Label>
            <Select value={feedback.recommendation} onValueChange={(value) => onFeedbackChange('recommendation', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select recommendation" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="strong-hire">Strong Hire</SelectItem>
                <SelectItem value="hire">Hire</SelectItem>
                <SelectItem value="no-hire">No Hire</SelectItem>
                <SelectItem value="strong-no-hire">Strong No Hire</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Strengths */}
          <div>
            <Label htmlFor="strengths">Key Strengths</Label>
            <Textarea
              id="strengths"
              placeholder="What did the candidate do well?"
              value={feedback.strengths}
              onChange={(e) => onFeedbackChange('strengths', e.target.value)}
              rows={3}
            />
          </div>

          {/* Concerns */}
          <div>
            <Label htmlFor="concerns">Areas of Concern</Label>
            <Textarea
              id="concerns"
              placeholder="What areas need improvement or raised concerns?"
              value={feedback.concerns}
              onChange={(e) => onFeedbackChange('concerns', e.target.value)}
              rows={3}
            />
          </div>

          {/* Detailed Notes */}
          <div>
            <Label htmlFor="notes">Detailed Notes</Label>
            <Textarea
              id="notes"
              placeholder="Provide detailed feedback about the interview..."
              value={feedback.notes}
              onChange={(e) => onFeedbackChange('notes', e.target.value)}
              rows={4}
            />
          </div>

          {/* Next Steps */}
          <div>
            <Label htmlFor="nextSteps">Recommended Next Steps</Label>
            <Textarea
              id="nextSteps"
              placeholder="What should happen next in the hiring process?"
              value={feedback.nextSteps}
              onChange={(e) => onFeedbackChange('nextSteps', e.target.value)}
              rows={2}
            />
          </div>

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={onSubmitFeedback}>
              <FileText className="mr-2 h-4 w-4" />
              Submit Feedback
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
