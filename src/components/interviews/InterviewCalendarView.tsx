import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { mockApplicants } from '@/data/mockData';
import { Interview } from '@/types';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, parseISO } from 'date-fns';
import { CalendarIcon, ChevronLeft, ChevronRight } from 'lucide-react';

interface InterviewCalendarViewProps {
  readonly interviews: Interview[];
  readonly selectedDate: Date | undefined;
  readonly onDateSelect: (date: Date | undefined) => void;
  readonly onInterviewClick?: (interview: Interview) => void;
}

const formatInterviewTime = (interview: Interview) => {
  try {
    const dateTime = parseISO(`${interview.date}T${interview.time}`);
    return format(dateTime, 'h:mm a');
  } catch {
    return interview.time;
  }
};

export default function InterviewCalendarView({ 
  interviews,
  selectedDate,
  onDateSelect,
  onInterviewClick
}: InterviewCalendarViewProps) {
  const currentWeek = selectedDate || new Date();
  const weekStart = startOfWeek(currentWeek);
  const weekEnd = endOfWeek(currentWeek);
  const calendarData = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = addDays(currentWeek, direction === 'prev' ? -7 : 7);
    onDateSelect(newDate);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <CalendarIcon className="mr-2 h-5 w-5" />
            Interview Calendar
          </span>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={() => navigateWeek('prev')}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm font-medium min-w-[120px] text-center">
              {format(weekStart, 'MMM d')} - {format(weekEnd, 'MMM d, yyyy')}
            </span>
            <Button variant="outline" size="sm" onClick={() => navigateWeek('next')}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-7 gap-4">
          {calendarData.map((day) => {
            const dayInterviews = interviews.filter(interview => {
              try {
                const interviewDate = parseISO(interview.date);
                return isSameDay(interviewDate, day);
              } catch {
                return false;
              }
            });

            return (
              <div key={day.toISOString()} className="min-h-[120px] border rounded-lg p-2">
                <div className="font-medium text-sm mb-2">
                  {format(day, 'EEE d')}
                </div>
                <div className="space-y-1">
                  {dayInterviews.map((interview) => {
                    const candidate = mockApplicants.find(c => c.id === interview.candidateId);
                    return (
                      <div 
                        key={interview.id} 
                        className="text-xs p-1 bg-blue-100 rounded border-l-2 border-blue-500 cursor-pointer hover:bg-blue-200 transition-colors"
                        onClick={() => onInterviewClick?.(interview)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            onInterviewClick?.(interview);
                          }
                        }}
                        role="button"
                        tabIndex={0}
                        aria-label={`Interview at ${formatInterviewTime(interview)} with ${candidate?.firstName} ${candidate?.lastName}`}
                      >
                        <div className="font-medium">{formatInterviewTime(interview)}</div>
                        <div className="truncate">{candidate?.firstName} {candidate?.lastName}</div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}
