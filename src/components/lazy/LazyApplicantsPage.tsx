/**
 * Lazy Applicants Page Wrapper
 * 
 * This component provides route-level code splitting for the applicants module
 * with error boundaries and intelligent preloading strategies.
 */

import React, { Suspense, lazy, useEffect } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { LazyLoadFallback, LazyLoadErrorFallback } from '@/components/ui/LazyLoadFallback';
import { config } from '@/config/appConfig';

// Lazy load the main applicants page
const ApplicantsPage = lazy(() => 
  import('@/pages/applicants').then(module => ({
    default: module.default
  }))
);

// Lazy load related components for preloading
const preloadableComponents = {
  ApplicantFormWizard: () => import('@/components/applicants/ApplicantFormWizard'),
  ApplicantViewWizard: () => import('@/components/applicants/ApplicantViewWizard'),
  PersonalInfoForm: () => import('@/components/applicants/forms/PersonalInfoForm'),
  ProfessionalInfoForm: () => import('@/components/applicants/forms/ProfessionalInfoForm'),
  DocumentsForm: () => import('@/components/applicants/forms/DocumentsForm'),
};

/**
 * Preload components based on strategy
 */
async function preloadComponents(strategy: string) {
  if (strategy === 'aggressive') {
    // Preload all components immediately
    const preloadPromises = Object.values(preloadableComponents).map(importFn => 
      importFn().catch(error => console.warn('Preload failed:', error))
    );
    await Promise.allSettled(preloadPromises);
    console.log('[LazyApplicantsPage] Aggressive preloading completed');
  } else if (strategy === 'user-interaction') {
    // Preload on user interaction (mouse enter, etc.)
    const preloadOnInteraction = () => {
      Promise.all([
        preloadableComponents.ApplicantFormWizard(),
        preloadableComponents.ApplicantViewWizard(),
      ]).catch(error => console.warn('Interaction preload failed:', error));
    };

    // Add interaction listeners
    document.addEventListener('mouseenter', preloadOnInteraction, { once: true });
    document.addEventListener('keydown', preloadOnInteraction, { once: true });
  }
  // 'lazy' strategy - no preloading, load on demand
}

interface LazyApplicantsPageProps {
  // Props that the applicants page might need
  className?: string;
  [key: string]: unknown;
}

export default function LazyApplicantsPage(props: LazyApplicantsPageProps) {
  // Preload based on configuration
  useEffect(() => {
    if (config.performance.enableCodeSplitting) {
      preloadComponents(config.performance.preloadStrategy);
    }
  }, []);

  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <LazyLoadErrorFallback
          error={error}
          resetError={resetErrorBoundary}
          componentName="Applicants Page"
        />
      )}
      onError={(error, errorInfo) => {
        console.error('Applicants page error:', error, errorInfo);
        // In production, you might want to send this to an error reporting service
      }}
    >
      <Suspense
        fallback={
          <LazyLoadFallback
            variant="card"
            size="xl"
            message="Loading applicants module..."
          />
        }
      >
        <ApplicantsPage {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}

/**
 * Higher-order component for route-level lazy loading
 */
export function withLazyLoading<T extends Record<string, unknown>>(
  importFn: () => Promise<{ default: React.ComponentType<T> }>,
  options: {
    fallbackVariant?: 'default' | 'card' | 'form' | 'minimal' | 'wizard';
    fallbackSize?: 'sm' | 'md' | 'lg' | 'xl';
    fallbackMessage?: string;
    errorComponentName?: string;
    preloadComponents?: Record<string, () => Promise<unknown>>;
    preloadStrategy?: 'aggressive' | 'lazy' | 'user-interaction';
  } = {}
) {
  const LazyComponent = lazy(importFn);

  return function LazyWrapper(props: T) {
    const {
      fallbackVariant = 'default',
      fallbackSize = 'lg',
      fallbackMessage = 'Loading...',
      errorComponentName = 'component',
      preloadComponents = {},
      preloadStrategy = config.performance.preloadStrategy,
    } = options;

    // Preload related components
    useEffect(() => {
      if (config.performance.enableCodeSplitting && Object.keys(preloadComponents).length > 0) {
        preloadComponents(preloadStrategy);
      }
    }, [preloadStrategy]);

    return (
      <ErrorBoundary
        FallbackComponent={({ error, resetErrorBoundary }) => (
          <LazyLoadErrorFallback
            error={error}
            resetError={resetErrorBoundary}
            componentName={errorComponentName}
          />
        )}
      >
        <Suspense
          fallback={
            <LazyLoadFallback
              variant={fallbackVariant}
              size={fallbackSize}
              message={fallbackMessage}
            />
          }
        >
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
}

/**
 * Preload specific applicant components
 */
export async function preloadApplicantComponents(components: (keyof typeof preloadableComponents)[]) {
  const preloadPromises = components.map(componentName => {
    const importFn = preloadableComponents[componentName];
    return importFn().catch(error => 
      console.warn(`Failed to preload ${componentName}:`, error)
    );
  });

  await Promise.allSettled(preloadPromises);
  console.log(`[LazyApplicantsPage] Preloaded ${components.length} components`);
}

/**
 * Get chunk loading statistics
 */
export function getChunkLoadingStats() {
  // This would integrate with webpack chunk loading stats in a real implementation
  return {
    chunksLoaded: 0, // Would be populated by webpack
    chunksFailed: 0,
    totalChunks: 0,
    loadingTime: 0,
  };
} 