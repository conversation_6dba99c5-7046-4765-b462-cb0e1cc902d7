import React from 'react';
import { SingleSelectAutocompleteField, SelectOption } from '@/components/ui/SingleSelectAutocomplete';
import { FormFieldProps } from './FormField';
import { ApiSelectOption } from '@/types/applicants';

export interface StaticApiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  options: ApiSelectOption[]; // Uses API format: {value, key}
  disabled?: boolean;
}

/**
 * Static API select input component with search functionality
 * Uses static options in API format (value, key) instead of fetching from API
 */
export const StaticApiSelectInput: React.FC<StaticApiSelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder = 'Select an option',
  options,
  disabled = false,
}) => {
  // Convert ApiSelectOption to SelectOption for the autocomplete component
  const selectOptions: SelectOption[] = options.map(apiOption => ({
    value: apiOption.value,
    label: apiOption.key, // Convert key to label for the component
  }));

  return (
    <div className="space-y-2">
      <SingleSelectAutocompleteField
        id={id}
        label={label}
        required={required}
        error={error}
        className={className}
        description={description}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        options={selectOptions}
        disabled={disabled}
        searchPlaceholder="Search options..."
        emptyMessage="No options found"
      />
    </div>
  );
};

export default StaticApiSelectInput; 