import React, { useEffect, useState } from 'react';
import { SingleSelectAutocompleteField, SelectOption } from '@/components/ui/SingleSelectAutocomplete';
import { FormFieldProps } from './FormField';
import { getDocumentTree } from '@/api/applicantsApi';
import { DocumentTreeSelectOption } from '@/types/applicants';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export interface DocumentTreeSelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  resourceId: string; // UUID for the document tree resource
  disabled?: boolean;
}

/**
 * Document tree select input component with search functionality
 * Fetches document subcategories from the tree API endpoint
 */
export const DocumentTreeSelectInput: React.FC<DocumentTreeSelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder = 'Select document type',
  resourceId,
  disabled = false,
}) => {
  const [treeOptions, setTreeOptions] = useState<DocumentTreeSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchTreeOptions = async () => {
      if (!resourceId) return;
      
      setLoading(true);
      
      const result = await getDocumentTree(
        resourceId,
        (error) => {
          toast({
            title: "Error",
            description: error.message || "Failed to load document types",
            variant: "destructive",
          });
        }
      );

      if (result && result.children) {
        // Convert children to select options (id -> value, name -> key)
        const options: DocumentTreeSelectOption[] = result.children
          .filter(child => child.isActive) // Only active subcategories
          .map(child => ({
            value: child.id,
            key: child.name,
          }));
        
        setTreeOptions(options);
      }
      
      setLoading(false);
    };

    fetchTreeOptions();
  }, [resourceId, toast]);

  // Convert DocumentTreeSelectOption to SelectOption for the autocomplete component
  const options: SelectOption[] = treeOptions.map(treeOption => ({
    value: treeOption.value,
    label: treeOption.key, // Convert key to label for the autocomplete component
  }));

  // Show loading state in placeholder
  const effectivePlaceholder = loading ? 'Loading document types...' : placeholder;
  const isDisabled = disabled || loading;

  return (
    <div className="space-y-2">
      <SingleSelectAutocompleteField
        id={id}
        label={label}
        required={required}
        error={error}
        className={className}
        description={description}
        value={value}
        onChange={onChange}
        placeholder={effectivePlaceholder}
        options={options}
        disabled={isDisabled}
        searchPlaceholder="Search document types..."
        emptyMessage={loading ? "Loading..." : "No document types found"}
      />
      {loading && (
        <div className="flex items-center text-sm text-gray-500">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Loading document types from server...
        </div>
      )}
    </div>
  );
};

export default DocumentTreeSelectInput; 