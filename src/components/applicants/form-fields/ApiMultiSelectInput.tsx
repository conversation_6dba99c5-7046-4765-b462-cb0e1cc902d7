import React, { useEffect, useState } from 'react';
import { MultiSelectInput } from './MultiSelectInput';
import { FormFieldProps } from './FormField';
import { getListValuesById } from '@/api/applicantsApi';
import { ApiSelectOption } from '@/types/applicants';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export interface ApiMultiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  id: string;
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  listNameId: string;
  placeholder?: string;
  description?: string;
  maxSelections?: number;
  disabled?: boolean;
  className?: string;
}

export const ApiMultiSelectInput: React.FC<ApiMultiSelectInputProps> = ({
  id,
  label,
  value,
  onChange,
  listNameId,
  placeholder = "Select options...",
  description,
  maxSelections,
  disabled = false,
  className,
  error,
  ...props
}) => {
  const [apiOptions, setApiOptions] = useState<ApiSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchOptions = async () => {
      if (!listNameId) return;
      
      setLoading(true);
      try {
        const result = await getListValuesById(
          listNameId,
          (error) => {
            console.warn(`Failed to load options for ${label}:`, error.message);
            toast({
              title: "Dropdown Error",
              description: `Failed to load ${label} options. Using cached data if available.`,
              variant: "default",
            });
          }
        );

        if (result) {
          setApiOptions(result);
        }
      } catch (error) {
        console.error(`Error loading options for ${label}:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchOptions();
  }, [listNameId, label, toast]);

  // Convert ApiSelectOption to SelectOption for the MultiSelectInput component
  const options = apiOptions.map(apiOption => ({
    value: apiOption.value,
    label: apiOption.key, // Convert key to label for compatibility
  }));

  const isDisabled = disabled || loading;

  return (
    <div className="space-y-2">
      <MultiSelectInput
        id={id}
        label={label}
        value={value}
        onChange={onChange}
        options={options}
        placeholder={loading ? "Loading options..." : placeholder}
        description={description}
        maxSelections={maxSelections}
        disabled={isDisabled}
        className={className}
        error={error}
        {...props}
      />
      {loading && (
        <div className="flex items-center text-sm text-gray-500">
          <Loader2 className="mr-2 h-3 w-3 animate-spin" />
          Loading {label.toLowerCase()} options...
        </div>
      )}
    </div>
  );
};

export default ApiMultiSelectInput; 