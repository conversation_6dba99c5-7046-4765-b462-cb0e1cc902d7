import React from 'react';
import { MultiSelectAutocompleteField, SelectOption as AutocompleteSelectOption } from '@/components/ui/MultiSelectAutocomplete';
import { FormFieldProps } from './FormField';

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface MultiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  options: SelectOption[];
  disabled?: boolean;
  maxSelections?: number;
}

/**
 * Multi-select input component with search functionality
 * Now uses MultiSelectAutocomplete for enhanced user experience
 */
export const MultiSelectInput: React.FC<MultiSelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value = [],
  onChange,
  placeholder = 'Select options',
  options,
  disabled = false,
  maxSelections,
}) => {
  // Convert SelectOption[] to AutocompleteSelectOption[] format
  const autocompleteOptions: AutocompleteSelectOption[] = options.map(option => ({
    value: option.value,
    label: option.label,
  }));

  // Handle max selections by filtering available options
  const handleChange = (newValue: string[]) => {
    if (maxSelections && newValue.length > maxSelections) {
      // Don't allow more than max selections
      return;
    }
    onChange(newValue);
  };

  return (
    <div className="space-y-2">
      <MultiSelectAutocompleteField
        id={id}
        label={label}
        required={required}
        error={error}
        className={className}
        description={description}
        value={value}
        onChange={handleChange}
        placeholder={placeholder}
        options={autocompleteOptions}
        disabled={disabled}
        searchPlaceholder="Search options..."
        emptyMessage="No options found"
        maxSelectedDisplay={3}
      />

      {/* Show message when max selections reached */}
      {maxSelections && value.length >= maxSelections && (
        <p className="text-sm text-muted-foreground">
          Maximum {maxSelections} selections reached
        </p>
      )}
    </div>
  );
};

export default MultiSelectInput;
