import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FormField, FormFieldProps } from './FormField';
import { cn } from '@/lib/utils';

export interface CountryCode {
  code: string;
  label: string;
  flag?: string;
}

export interface PhoneInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  countryCode: string;
  onCountryCodeChange: (code: string) => void;
  placeholder?: string;
  disabled?: boolean;
  countryCodes?: CountryCode[];
}

const DEFAULT_COUNTRY_CODES: CountryCode[] = [
  { code: '+1', label: 'US (+1)', flag: '🇺🇸' },
  { code: '+91', label: 'IN (+91)', flag: '🇮🇳' },
  { code: '+44', label: 'UK (+44)', flag: '🇬🇧' },
  { code: '+61', label: 'AU (+61)', flag: '🇦🇺' },
  { code: '+49', label: 'DE (+49)', flag: '🇩🇪' },
];

/**
 * Reusable phone input component with country code selector
 * Combines country code dropdown with phone number input
 */
export const PhoneInput: React.FC<PhoneInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  countryCode,
  onCountryCodeChange,
  placeholder = 'Enter phone number',
  disabled = false,
  countryCodes = DEFAULT_COUNTRY_CODES,
}) => {
  return (
    <FormField
      id={id}
      label={label}
      required={required}
      error={error}
      className={className}
      description={description}
    >
      <div className="flex">
        <Select
          value={countryCode}
          onValueChange={onCountryCodeChange}
          disabled={disabled}
        >
          <SelectTrigger className="w-20 rounded-r-none border-r-0">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {countryCodes.map((country) => (
              <SelectItem key={country.code} value={country.code}>
                <span className="flex items-center gap-2">
                  {country.flag && <span>{country.flag}</span>}
                  {country.code}
                </span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Input
          id={id}
          type="tel"
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className={cn(
            'flex-1 rounded-l-none',
            error && 'border-red-500'
          )}
          disabled={disabled}
          aria-invalid={!!error}
          aria-describedby={error ? `${id}-error` : undefined}
        />
      </div>
    </FormField>
  );
};

export default PhoneInput;
