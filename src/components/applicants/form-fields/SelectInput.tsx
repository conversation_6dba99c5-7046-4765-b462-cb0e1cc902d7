import React from 'react';
import { SingleSelectAutocompleteField, SelectOption } from '@/components/ui/SingleSelectAutocomplete';
import { FormFieldProps } from './FormField';

// Re-export SelectOption from SingleSelectAutocomplete for backward compatibility
export type { SelectOption };

export interface SelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  options: SelectOption[];
  disabled?: boolean;
}

/**
 * Reusable select input component with search functionality
 * Now uses SingleSelectAutocomplete for enhanced user experience
 */
export const SelectInput: React.FC<SelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder = 'Select an option',
  options,
  disabled = false,
}) => {
  return (
    <SingleSelectAutocompleteField
      id={id}
      label={label}
      required={required}
      error={error}
      className={className}
      description={description}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      options={options}
      disabled={disabled}
      searchPlaceholder="Search options..."
      emptyMessage="No options found"
    />
  );
};

export default SelectInput;
