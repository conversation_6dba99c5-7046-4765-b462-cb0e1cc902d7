import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { FormField, FormFieldProps } from './FormField';
import { cn } from '@/lib/utils';

export interface TextAreaInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  rows?: number;
  maxLength?: number;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

/**
 * Reusable textarea input component with consistent styling and validation
 * Wraps shadcn/ui Textarea with FormField for standardized form structure
 */
export const TextAreaInput: React.FC<TextAreaInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder,
  disabled = false,
  rows = 3,
  maxLength,
  resize = 'vertical',
}) => {
  return (
    <FormField
      id={id}
      label={label}
      required={required}
      error={error}
      className={className}
      description={description}
    >
      <Textarea
        id={id}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(
          error && 'border-red-500',
          resize === 'none' && 'resize-none',
          resize === 'vertical' && 'resize-y',
          resize === 'horizontal' && 'resize-x'
        )}
        disabled={disabled}
        rows={rows}
        maxLength={maxLength}
        aria-invalid={!!error}
        aria-describedby={error ? `${id}-error` : undefined}
      />
      {maxLength && (
        <div className="text-xs text-gray-500 text-right mt-1">
          {value.length}/{maxLength}
        </div>
      )}
    </FormField>
  );
};

export default TextAreaInput;
