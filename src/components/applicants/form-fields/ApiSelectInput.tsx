import React, { useEffect, useState } from 'react';
import { SingleSelectAutocompleteField, SelectOption } from '@/components/ui/SingleSelectAutocomplete';
import { FormFieldProps } from './FormField';
import { getListValuesById } from '@/api/applicantsApi';
import { ApiSelectOption } from '@/types/applicants';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { announceToScreenReader, AriaUtils } from '@/lib/accessibility';

export interface ApiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  listNameId: string; // UUID for the list to fetch from API
  disabled?: boolean;
  // Accessibility props
  ariaLabel?: string;
  ariaDescribedBy?: string;
}

/**
 * API-driven select input component with search functionality
 * Fetches options from the list values API endpoint
 */
export const ApiSelectInput: React.FC<ApiSelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder = 'Select an option',
  listNameId,
  disabled = false,
  ariaLabel,
  ariaDescribedBy,
}) => {
  const [apiOptions, setApiOptions] = useState<ApiSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    const fetchOptions = async () => {
      if (!listNameId) return;
      
      setLoading(true);
      
      const result = await getListValuesById(
        listNameId,
        (error) => {
          announceToScreenReader(
            `Error loading options: ${error.message || "Failed to load options"}`, 
            'assertive'
          );
          toast({
            title: "Error",
            description: error.message || "Failed to load options",
            variant: "destructive",
          });
        }
      );

      if (result) {
        setApiOptions(result);
        announceToScreenReader(`${result.length} options loaded`, 'polite');
      }
      
      setLoading(false);
    };

    fetchOptions();
  }, [listNameId, toast]);

  // Convert ApiSelectOption to SelectOption for the autocomplete component
  const options: SelectOption[] = apiOptions.map(apiOption => ({
    value: apiOption.value,
    label: apiOption.key, // Convert key to label for the component
  }));

  // Show loading state in placeholder
  const effectivePlaceholder = loading ? 'Loading options...' : placeholder;
  const isDisabled = disabled || loading;
  
  // Generate ID for the field
  const fieldId = id || AriaUtils.generateId('api-select');
  const loadingId = loading ? `${fieldId}-loading` : undefined;

  return (
    <div className="space-y-2">
      <SingleSelectAutocompleteField
        id={fieldId}
        label={label}
        required={required}
        error={error}
        className={className}
        description={description}
        value={value}
        onChange={onChange}
        placeholder={effectivePlaceholder}
        options={options}
        disabled={isDisabled}
        searchPlaceholder="Search options..."
        emptyMessage={loading ? "Loading..." : "No options found"}
      />
      {loading && (
        <div 
          id={loadingId}
          className="flex items-center text-sm text-muted-foreground"
          aria-live="polite"
        >
          <Loader2 className="mr-2 h-4 w-4 animate-spin" aria-hidden="true" />
          Loading options from server...
        </div>
      )}
      
      {/* Screen reader status updates */}
      <div className="sr-only" aria-live="polite" aria-atomic="true">
        {loading && "Loading options, please wait..."}
        {!loading && options.length > 0 && `${options.length} options available`}
        {!loading && options.length === 0 && "No options available"}
      </div>
    </div>
  );
};

export default ApiSelectInput; 