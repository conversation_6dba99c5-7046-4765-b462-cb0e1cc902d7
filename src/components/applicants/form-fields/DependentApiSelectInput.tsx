import React, { useEffect, useState } from 'react';
import { SingleSelectAutocompleteField, SelectOption } from '@/components/ui/SingleSelectAutocomplete';
import { FormFieldProps } from './FormField';
import { getListValuesById } from '@/api/applicantsApi';
import { ApiSelectOption } from '@/types/applicants';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

export interface DependentApiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string | undefined) => void;
  placeholder?: string;
  parentValue?: string; // The selected value from parent dropdown (e.g., country ID)
  fallbackListNameId?: string; // Fallback list ID if no parent value is selected
  disabled?: boolean;
  emptyParentMessage?: string; // Message to show when no parent is selected
}

/**
 * Dependent API-driven select input component
 * Uses the parentValue as the listNameId for the API call
 * Perfect for cascading dropdowns like Country -> State
 */
export const DependentApiSelectInput: React.FC<DependentApiSelectInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder = 'Select an option',
  parentValue,
  fallbackListNameId,
  disabled = false,
  emptyParentMessage = 'Please select parent option first',
}) => {
  const [apiOptions, setApiOptions] = useState<ApiSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Determine which listNameId to use
  const listNameId = parentValue || fallbackListNameId;

  useEffect(() => {
    const fetchOptions = async () => {
      // Clear previous options when parent changes
      setApiOptions([]);
      
      // If no listNameId available, don't fetch
      if (!listNameId) {
        return;
      }
      
      setLoading(true);
      
      const result = await getListValuesById(
        listNameId,
        (error) => {
          toast({
            title: "Error",
            description: error.message || "Failed to load options",
            variant: "destructive",
          });
        }
      );

      if (result) {
        setApiOptions(result);
      }
      
      setLoading(false);
    };

    fetchOptions();
  }, [listNameId, toast]);

  // Clear value when parent changes and current value is not in new options
  useEffect(() => {
    if (value && apiOptions.length > 0) {
      const valueExists = apiOptions.some(option => option.value === value);
      if (!valueExists) {
        onChange(undefined); // Clear the value if it's not valid for new options
      }
    }
  }, [apiOptions, value, onChange]);

  // Convert ApiSelectOption to SelectOption for the autocomplete component
  const options: SelectOption[] = apiOptions.map(apiOption => ({
    value: apiOption.value,
    label: apiOption.key, // Convert key to label for the component
  }));

  // Determine the effective placeholder and disabled state
  const isDisabled = disabled || loading || !listNameId;
  let effectivePlaceholder = placeholder;
  
  if (!listNameId && !loading) {
    effectivePlaceholder = emptyParentMessage;
  } else if (loading) {
    effectivePlaceholder = 'Loading options...';
  }

  return (
    <div className="space-y-2">
      <SingleSelectAutocompleteField
        id={id}
        label={label}
        required={required}
        error={error}
        className={className}
        description={description}
        value={value}
        onChange={onChange}
        placeholder={effectivePlaceholder}
        options={options}
        disabled={isDisabled}
        searchPlaceholder="Search options..."
        emptyMessage={!listNameId ? emptyParentMessage : loading ? "Loading..." : "No options found"}
      />
      {loading && listNameId && (
        <div className="flex items-center text-sm text-gray-500">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Loading options based on selection...
        </div>
      )}
      {!listNameId && !loading && (
        <div className="text-sm text-gray-500">
          {emptyParentMessage}
        </div>
      )}
    </div>
  );
};

export default DependentApiSelectInput; 