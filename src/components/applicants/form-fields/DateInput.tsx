import React from 'react';
import { Input } from '@/components/ui/input';
import { FormField, FormFieldProps } from './FormField';
import { cn } from '@/lib/utils';

export interface DateInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  min?: string;
  max?: string;
}

/**
 * Reusable date input component with consistent styling and validation
 * Wraps HTML date input with FormField for standardized form structure
 */
export const DateInput: React.FC<DateInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  disabled = false,
  min,
  max,
}) => {
  return (
    <FormField
      id={id}
      label={label}
      required={required}
      error={error}
      className={className}
      description={description}
    >
      <Input
        id={id}
        type="date"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(error && 'border-red-500')}
        disabled={disabled}
        min={min}
        max={max}
        aria-invalid={!!error}
        aria-describedby={error ? `${id}-error` : undefined}
      />
    </FormField>
  );
};

export default DateInput;
