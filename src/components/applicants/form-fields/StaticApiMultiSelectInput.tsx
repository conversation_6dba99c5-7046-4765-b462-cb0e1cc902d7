import React from 'react';
import { MultiSelectInput } from './MultiSelectInput';
import { FormFieldProps } from './FormField';
import { ApiSelectOption } from '@/types/applicants';

export interface StaticApiMultiSelectInputProps extends Omit<FormFieldProps, 'children'> {
  id: string;
  label: string;
  value: string[];
  onChange: (value: string[]) => void;
  options: ApiSelectOption[]; // Uses API format: {value, key}
  placeholder?: string;
  description?: string;
  maxSelections?: number;
  disabled?: boolean;
  className?: string;
}

/**
 * Static API multi-select input component with search functionality
 * Uses static options in API format (value, key) instead of fetching from API
 */
export const StaticApiMultiSelectInput: React.FC<StaticApiMultiSelectInputProps> = ({
  id,
  label,
  value,
  onChange,
  options,
  placeholder = "Select options...",
  description,
  maxSelections,
  disabled = false,
  className,
  error,
  ...props
}) => {
  // Convert ApiSelectOption to SelectOption for the MultiSelectInput component
  const selectOptions = options.map(apiOption => ({
    value: apiOption.value,
    label: apiOption.key, // Convert key to label for compatibility
  }));

  return (
    <div className="space-y-2">
      <MultiSelectInput
        id={id}
        label={label}
        value={value}
        onChange={onChange}
        options={selectOptions}
        placeholder={placeholder}
        description={description}
        maxSelections={maxSelections}
        disabled={disabled}
        className={className}
        error={error}
        {...props}
      />
    </div>
  );
};

export default StaticApiMultiSelectInput; 