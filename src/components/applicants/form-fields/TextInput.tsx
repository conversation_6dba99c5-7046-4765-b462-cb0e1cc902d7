import React from 'react';
import { Input } from '@/components/ui/input';
import { Form<PERSON>ield, FormFieldProps } from './FormField';
import { cn } from '@/lib/utils';

export interface TextInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password';
  disabled?: boolean;
  maxLength?: number;
  autoComplete?: string;
}

/**
 * Reusable text input component with consistent styling and validation
 * Wraps shadcn/ui Input with FormField for standardized form structure
 */
export const TextInput: React.FC<TextInputProps> = ({
  id,
  label,
  required = false,
  error,
  className,
  description,
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  maxLength,
  autoComplete,
}) => {
  return (
    <FormField
      id={id}
      label={label}
      required={required}
      error={error}
      className={className}
      description={description}
    >
      <Input
        id={id}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(error && 'border-red-500')}
        disabled={disabled}
        maxLength={maxLength}
        autoComplete={autoComplete}
        aria-invalid={!!error}
        aria-describedby={error ? `${id}-error` : undefined}
      />
    </FormField>
  );
};

export default TextInput;
