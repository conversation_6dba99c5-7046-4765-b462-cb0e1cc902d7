import { useState, useCallback } from 'react';

export interface UseFormInteractionReturn {
  hasInteracted: boolean;
  markAsInteracted: () => void;
  reset: () => void;
  shouldShowValidation: (attemptedSubmit?: boolean) => boolean;
}

/**
 * Hook to track form interaction state for validation display timing
 * Prevents premature validation indicators before user interaction
 */
export const useFormInteraction = (): UseFormInteractionReturn => {
  const [hasInteracted, setHasInteracted] = useState(false);

  const markAsInteracted = useCallback(() => {
    if (!hasInteracted) {
      setHasInteracted(true);
    }
  }, [hasInteracted]);

  const reset = useCallback(() => {
    setHasInteracted(false);
  }, []);

  const shouldShowValidation = useCallback((attemptedSubmit: boolean = false) => {
    return hasInteracted || attemptedSubmit;
  }, [hasInteracted]);

  return {
    hasInteracted,
    markAsInteracted,
    reset,
    shouldShowValidation,
  };
};

export default useFormInteraction;
