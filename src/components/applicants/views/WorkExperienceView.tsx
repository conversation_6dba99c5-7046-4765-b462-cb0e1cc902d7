import React from 'react';
import { Applicant } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Briefcase, Calendar, MapPin } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { mapUuidToName, mapUuidsToNames } from '@/lib/utils';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
interface WorkExperienceViewProps {
  applicant: Applicant;
}

const WorkExperienceView: React.FC<WorkExperienceViewProps> = ({ applicant }) => {
  const { listValues } = useAuth();

  const workExperience = (applicant.workExperience ?? []).filter(exp => exp.isActive !== false);

  // Helper component for disabled select inputs
  const DisabledSelectInput: React.FC<{ 
    id: string; 
    label: string; 
    value?: string; 
  }> = ({ id, label, value }) => {
    const mappedValue = mapUuidToName(value, listValues);
    
    // Debug logging for UUID mapping issues
    if (value && value !== mappedValue && process.env.NODE_ENV === 'development') {
      console.log(`UUID Mapping for ${label}:`, {
        originalValue: value,
        mappedValue: mappedValue,
        listValuesCount: listValues.length,
        foundMatch: listValues.find(item => item.id === value)
      });
    }
    
    return (
      <div className={label ? "space-y-2" : ""}>
        {label && <Label htmlFor={id}>{label}</Label>}
        <Input
          id={id}
          value={mappedValue}
          disabled
          className="bg-gray-50"
          placeholder={mappedValue ? undefined : "Not provided"}
        />
      </div>
    );
  };

  const ViewField: React.FC<{ label: string; value?: string | number; className?: string }> = ({ 
    label, 
    value, 
    className = "" 
  }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center">
        {value ?? 'Not provided'}
      </div>
    </div>
  );

  const ViewSelectField: React.FC<{ label: string; value?: string; className?: string }> = ({ 
    label, 
    value, 
    className = "" 
  }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center">
        {mapUuidToName(value, listValues)}
      </div>
    </div>
  );

  const ViewArrayField: React.FC<{ label: string; values?: string[]; className?: string }> = ({ 
    label, 
    values, 
    className = "" 
  }) => {
    const mappedValues = mapUuidsToNames(values, listValues);
    
    return (
      <div className={`space-y-1 ${className}`}>
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <div className="bg-gray-50 px-3 py-2 rounded-md min-h-[36px]">
          {mappedValues && mappedValues.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {mappedValues.map((value, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {value}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-sm text-gray-900">Not provided</span>
          )}
        </div>
      </div>
    );
  };

  const formatDateRange = (startDate?: string, endDate?: string) => {
    const start = startDate ? new Date(startDate).toLocaleDateString() : 'Unknown';
    const end = endDate ? new Date(endDate).toLocaleDateString() : 'Present';
    return `${start} - ${end}`;
  };

  const calculateDuration = (startDate?: string, endDate?: string) => {
    if (!startDate) return 'Unknown duration';
    
    const start = new Date(startDate);
    const end = endDate ? new Date(endDate) : new Date();
    
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const years = Math.floor(diffDays / 365);
    const months = Math.floor((diffDays % 365) / 30);
    
    if (years > 0) {
      return `${years} year${years > 1 ? 's' : ''} ${months} month${months > 1 ? 's' : ''}`;
    } else {
      return `${months} month${months > 1 ? 's' : ''}`;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Work Experience</h3>
        
        {workExperience.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Briefcase className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No work experience added</p>
          </div>
        ) : (
          <div className="space-y-6">
            {workExperience.map((experience, index) => (
              <div key={index} className="border rounded-lg p-6 bg-white">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <Briefcase className="h-6 w-6 text-blue-600 mt-1" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">
                        {experience.jobTitle ?? 'Job Title Not Specified'}
                      </h4>
                      <p className="text-md text-gray-700 font-medium">
                        {experience.companyName ?? 'Company Not Specified'}
                      </p>
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {formatDateRange(experience.startDate, experience.endDate)}
                        </div>
                        <div className="flex items-center gap-1">
                          <span>Duration: {calculateDuration(experience.startDate, experience.endDate)}</span>
                        </div>
                      </div>
                      {experience.location && (
                        <div className="flex items-center gap-1 mt-1 text-sm text-gray-600">
                          <MapPin className="h-4 w-4" />
                          {experience.location}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* {experience.isCurrentJob && (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      Current Position
                    </Badge>
                  )} */}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <DisabledSelectInput
                    id={`employeeType-${index}`}
                    label="Employee Type"
                    value={experience.employeeType}
                  />
                  {/* <ViewSelectField 
                    label="Department" 
                    value={experience.department} 
                  />
                  
                  <ViewField 
                    label="Salary" 
                    value={experience.salary} 
                  />
                  <ViewSelectField 
                    label="Currency" 
                    value={experience.currency} 
                  /> */}
                  
                  {/* <ViewField 
                    label="Manager Name" 
                    value={experience.managerName} 
                  />
                  <ViewField 
                    label="Manager Contact" 
                    value={experience.managerContact} 
                  /> */}
                  {experience.responsibilities && (
                  <div className="mb-4">
                    <ViewField 
                      label="Key Responsibilities" 
                      value={experience.responsibilities} 
                    />
                  </div>
                )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default WorkExperienceView;
