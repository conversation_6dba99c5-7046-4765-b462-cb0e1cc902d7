import React from "react";
import { Applicant } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Building, Phone, User } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { mapUuidToName } from "@/lib/utils";
import { vendorContactOptions } from "@/utils/applicants/formOptions";

interface EmployerDetailsViewProps {
  applicant: Applicant;
}

const EmployerDetailsView: React.FC<EmployerDetailsViewProps> = ({
  applicant,
}) => {
  const { listValues } = useAuth();
  const employerDetails = applicant.employerDetails;

  // Helper function to map vendor contact UUID to display value
  const mapVendorContactToName = (uuid: string | undefined): string => {
    if (!uuid) return 'Not provided';
    const option = vendorContactOptions.find(opt => opt.value === uuid);
    return option ? option.key : uuid; // Fallback to UUID if not found
  };

  const ViewField: React.FC<{
    label: string;
    value?: string | number | boolean;
    className?: string;
  }> = ({ label, value, className = "" }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center">
        {value !== undefined && value !== null
          ? value.toString()
          : "Not provided"}
      </div>
    </div>
  );

  const ViewSelectField: React.FC<{
    label: string;
    value?: string | boolean;
    className?: string;
  }> = ({ label, value, className = "" }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center">
        {typeof value === "boolean"
          ? value
            ? "Yes"
            : "No"
          : mapUuidToName(value as string, listValues)}
      </div>
    </div>
  );

  if (!employerDetails) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Employer Details
          </h3>
          <div className="text-center py-8 text-gray-500">
            <Building className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No employer details provided</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Employer Details
        </h3>

        <div className="border rounded-lg p-6 bg-white">
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <Building className="h-6 w-6 text-blue-600 mt-1" />
              </div>
              <div>
                <h4 className="text-lg font-semibold text-gray-900">
                  {employerDetails.employerName ??
                    "Employer Name Not Specified"}
                </h4>
                {employerDetails.isNew !== undefined && (
                  <div className="mt-2">
                    <Badge
                      variant={employerDetails.isNew ? "default" : "secondary"}
                    >
                      {employerDetails.isNew
                        ? "New Employer"
                        : "Existing Employer"}
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {employerDetails.status !== undefined && (
              <Badge variant={employerDetails.status ? "default" : "secondary"}>
                {employerDetails.status ? "Active" : "Inactive"}
              </Badge>
            )}
          </div>

          {/* Employer Information */}
          <div className="mb-6">
            <h5 className="text-md font-medium text-gray-800 mb-3">
              Employer Information
            </h5>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {!employerDetails.vendorContact && (
                <ViewField
                  label="Employer Name"
                  value={employerDetails.employerName}
                />
              )}
              <ViewSelectField
                label="Add from Existing Vendor"
                value={employerDetails.addFromExistingVendor ? "Yes" : "No"}
              />

              {employerDetails.vendorContact && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ViewField
                    label="Vendor Contact"
                    value={mapVendorContactToName(employerDetails.vendorContact)}
                    className="md:col-span-2"
                  />
                </div>
              )}
            </div>
          </div>
          {!employerDetails.vendorContact && (
            <>
              {/* Contact Person Details */}
              <div className="mb-6">
                <h5 className="text-md font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Contact Person Details
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ViewField
                    label="First Name"
                    value={employerDetails.firstName}
                  />
                  <ViewField
                    label="Last Name"
                    value={employerDetails.lastName}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="mb-6">
                <h5 className="text-md font-medium text-gray-800 mb-3 flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Contact Information
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ViewField label="Email ID" value={employerDetails.emailId} />
                  <ViewField
                    label="Mobile Number"
                    value={employerDetails.mobileNumber}
                  />
                  <ViewField
                    label="Office Number"
                    value={employerDetails.officeNumber}
                  />
                </div>
              </div>

              {/* Status Information */}
              <div className="pt-4 border-t mt-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      Status:
                    </span>
                    <Badge
                      variant={employerDetails.status ? "default" : "secondary"}
                    >
                      {employerDetails.status ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      Type:
                    </span>
                    <Badge
                      variant={employerDetails.isNew ? "default" : "outline"}
                    >
                      {employerDetails.isNew
                        ? "New Employer"
                        : "Existing Employer"}
                    </Badge>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmployerDetailsView;
