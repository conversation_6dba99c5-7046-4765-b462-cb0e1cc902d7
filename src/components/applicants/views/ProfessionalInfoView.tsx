import React, { useState } from "react";
import { Applicant } from "@/types";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { mapUuidToName, mapUuidsToNames } from "@/lib/utils";
import {
  functionOptions,
  ownershipOptions,
} from "@/utils/applicants/formOptions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  ChevronDown,
  ChevronUp,
  Briefcase,
  DollarSign,
  Award,
  Settings,
  Users,
} from "lucide-react";

interface ProfessionalInfoViewProps {
  applicant: Applicant;
}

const ProfessionalInfoView: React.FC<ProfessionalInfoViewProps> = ({
  applicant,
}) => {
  const { listValues } = useAuth();

  // Use transformed data structure (personalDetails contains all the flattened data)
  const personalDetails = applicant.personalDetails || {};

  // State for collapsible sections - matching ProfessionalInfoForm exactly
  const [openSections, setOpenSections] = useState({
    experience: true,
    compensation: false,
    skills: false,
    preferences: false,
    status: false,
    eeo: false,
  });

  const toggleSection = (section: keyof typeof openSections) => {
    try {
      setOpenSections((prev) => ({
        ...prev,
        [section]: !prev[section],
      }));
    } catch (error) {
      console.error("Error toggling section:", error, { section });
    }
  };

  // Helper component for disabled text inputs
  const DisabledTextInput: React.FC<{
    id: string;
    label: string;
    value?: string | number;
    required?: boolean;
  }> = ({ id, label, value, required }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input id={id} value={value || ""} disabled className="bg-gray-50" />
    </div>
  );
  const ownershipValue = personalDetails.ownership;
  const ownershipDisplay =
    ownershipOptions.find((opt) => opt.value === ownershipValue)?.key ||
    "Unknown";
  // Helper component for disabled select inputs
  const DisabledSelectInput: React.FC<{
    id: string;
    label: string;
    value?: string;
  }> = ({ id, label, value }) => {
    const mappedValue = mapUuidToName(value, listValues);

    return (
      <div className={label ? "space-y-2" : ""}>
        {label && <Label htmlFor={id}>{label}</Label>}
        <Input
          id={id}
          value={mappedValue}
          disabled
          className="bg-gray-50"
          placeholder={mappedValue ? undefined : "Not provided"}
        />
      </div>
    );
  };

  // Helper component for disabled array fields (skills, etc.)
  const DisabledArrayField: React.FC<{
    id: string;
    label: string;
    values?: string[];
    options?: { value: string; key: string }[];
  }> = ({ id, label, values, options }) => {
    let mappedValues: string[] = [];
    try {
      if (options) {
        mappedValues = (values || []).map(
          (uuid) => options.find((opt) => opt.value === uuid)?.key || "Unknown"
        );
      } else {
        mappedValues = mapUuidsToNames(values, listValues);
      }
    } catch (error) {
      console.error("Error mapping UUIDs to names:", error, {
        values,
        options,
      });
      mappedValues = [];
    }
    return (
      <div className="space-y-2">
        <Label htmlFor={id}>{label}</Label>
        <div className="bg-gray-50 px-3 py-2 rounded-md min-h-[40px] border border-gray-300">
          {mappedValues && mappedValues.length > 0 ? (
            <div className="flex flex-wrap gap-1">
              {mappedValues.map((value, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {value || "Unknown"}
                </Badge>
              ))}
            </div>
          ) : (
            <span className="text-gray-500 text-sm">Not provided</span>
          )}
        </div>
      </div>
    );
  };

  // Helper for experience display (matching form structure)
  const ExperienceDisplay: React.FC = () => (
    <div className="space-y-2">
      <Label>Experience</Label>
      <div className="flex flex-col gap-4 lg:flex-col xl:flex-row">
        <div className="flex flex-row items-center gap-2">
          <Input
            value={mapUuidToName(personalDetails.experienceYears, listValues) || ""}
            disabled
            className="bg-gray-50 w-36 lg:w-28"
          />
          <span className="text-sm text-black-500 font-bold">Years</span>
        </div>
        <div className="flex flex-row items-center gap-2">
          <Input
            value={mapUuidToName(personalDetails.experienceMonths, listValues) || ""}
            disabled
            className="bg-gray-50 w-36 lg:w-28"
          />
          <span className="text-sm text-black-500 font-bold">Months</span>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <Briefcase className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          Professional Information
        </h3>
      </div>

      {/* Experience & Job Details - Always Open (matching ProfessionalInfoForm) */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Experience & Job Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Experience */}
            <ExperienceDisplay />

            {/* Job Title */}
            <DisabledTextInput
              id="jobTitle"
              label="Job Title"
              value={applicant.position}
            />
          </div>
        </CardContent>
      </Card>

      {/* Compensation & Terms - Collapsible */}
      <Card className="border-l-4 border-l-green-500">
        <Collapsible
          open={openSections.compensation}
          onOpenChange={() => toggleSection("compensation")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Compensation & Terms
                </div>
                {openSections.compensation ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Expected Pay */}
                <div className="space-y-2">
                  <Label>Expected Pay</Label>
                  <div className="flex gap-2">
                    <DisabledSelectInput
                      id="expectedPayCurrency"
                      label=""
                      value={personalDetails.expectedPayCurrency}
                    />
                    <DisabledSelectInput
                      id="expectedPayAmount"
                      label=""
                      value={personalDetails.expectedPayAmount}
                    />
                  </div>
                </div>

                {/* Current CTC */}
                <div className="space-y-2">
                  <Label>Current CTC</Label>
                  <div className="flex gap-2">
                    <DisabledSelectInput
                      id="currentCtcCurrency"
                      label=""
                      value={personalDetails.currentCtcCurrency}
                    />
                    <DisabledSelectInput
                      id="currentCtcAmount"
                      label=""
                      value={personalDetails.currentCtcAmount}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Skills & Technology - Collapsible */}
      <Card className="border-l-4 border-l-cyan-500">
        <Collapsible
          open={openSections.skills}
          onOpenChange={() => toggleSection("skills")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  Skills & Technology
                </div>
                {openSections.skills ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              {(() => {
                try {
                  return (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {/* Skills */}
                      <DisabledArrayField
                        id="skills"
                        label="Skills"
                        values={
                          Array.isArray(personalDetails.skills)
                            ? personalDetails.skills
                            : []
                        }
                      />

                      {/* Primary Skills */}
                      <DisabledArrayField
                        id="primarySkills"
                        label="Primary Skills"
                        values={
                          Array.isArray(personalDetails.primarySkills)
                            ? personalDetails.primarySkills
                            : []
                        }
                      />

                      {/* Technology */}
                      <DisabledArrayField
                        id="technology"
                        label="Technology"
                        values={
                          Array.isArray(personalDetails.technology)
                            ? personalDetails.technology
                            : []
                        }
                      />

                      {/* Industry */}
                      <DisabledArrayField
                        id="industry"
                        label="Industry"
                        values={
                          Array.isArray(personalDetails.industry)
                            ? personalDetails.industry
                            : []
                        }
                        options={functionOptions}
                      />

                      {/* Function */}
                      <DisabledArrayField
                        id="function"
                        label="Function"
                        values={
                          Array.isArray(personalDetails.function)
                            ? personalDetails.function
                            : []
                        }
                        options={functionOptions}
                      />
                    </div>
                  );
                } catch (error) {
                  console.error(
                    "Error rendering Skills & Technology section:",
                    error
                  );
                  return (
                    <div className="text-red-500 p-4">
                      Error loading Skills & Technology section. Please check
                      console for details.
                    </div>
                  );
                }
              })()}
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Preferences & Details - Collapsible */}
      <Card className="border-l-4 border-l-orange-500">
        <Collapsible
          open={openSections.preferences}
          onOpenChange={() => toggleSection("preferences")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Preferences & Details
                </div>
                {openSections.preferences ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Current Company */}
                <DisabledTextInput
                  id="currentCompany"
                  label="Current Company"
                  value={personalDetails.currentCompany}
                />

                {/* Source */}
                <DisabledSelectInput
                  id="source"
                  label="Source"
                  value={personalDetails.source}
                />

                {/* Referred By */}
                <DisabledTextInput
                  id="referredBy"
                  label="Referred By"
                  value={personalDetails.referredBy}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Status & Management - Collapsible */}
      <Card className="border-l-4 border-l-red-500">
        <Collapsible
          open={openSections.status}
          onOpenChange={() => toggleSection("status")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Status & Management
                </div>
                {openSections.status ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Applicant Status */}
                <DisabledSelectInput
                  id="applicantStatus"
                  label="Applicant Status"
                  value={personalDetails.applicantStatus}
                />

                {/* Ownership */}
                <div className="space-y-2">
                  <Label htmlFor="ownership">Ownership</Label>
                  <Input id="ownership" disabled value={ownershipDisplay} />
                </div>

                {/* Relocation */}
                <DisabledTextInput
                  id="relocation"
                  label="Relocation"
                  value={personalDetails.relocation}
                />

                {/* PAN Card Number */}
                <DisabledTextInput
                  id="panCardNumber"
                  label="PAN Card Number"
                  value={personalDetails.panCardNumber}
                />

                {/* Aadhar Number */}
                <DisabledTextInput
                  id="aadharNumber"
                  label="Aadhar Number"
                  value={
                    personalDetails.aadharNumber
                      ? "****-****-" +
                        personalDetails.aadharNumber.slice(-4)
                      : undefined
                  }
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      <Card className="border-l-4 border-l-purple-500">
        <Collapsible
          open={openSections.status}
          onOpenChange={() => toggleSection("eeo")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  EEO Information
                </div>
                {openSections.eeo ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Gender */}
                <DisabledSelectInput
                  id="gender"
                  label="Gender"
                  value={personalDetails.gender}
                />

                {/* Race/Ethnicity */}
                <DisabledSelectInput
                  id="raceEthnicity"
                  label="Race/Ethnicity"
                  value={personalDetails.raceEthnicity}
                />

                {/* Veteran Status */}
                <DisabledSelectInput
                  id="veteranStatus"
                  label="Veteran Status"
                  value={personalDetails.veteranStatus}
                />

                {/* Veteran Type */}
                <DisabledSelectInput
                  id="veteranType"
                  label="Veteran Type"
                  value={personalDetails.veteranType}
                />

                {/* Disability */}
                <DisabledSelectInput
                  id="disability"
                  label="Disability"
                  value={personalDetails.disability}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
};

export default ProfessionalInfoView;
