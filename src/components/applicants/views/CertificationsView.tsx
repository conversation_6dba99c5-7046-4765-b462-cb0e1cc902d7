import React from 'react';
import { Applicant } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Award, Calendar, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { mapUuidToName } from '@/lib/utils';

interface CertificationsViewProps {
  applicant: Applicant;
}

const CertificationsView: React.FC<CertificationsViewProps> = ({ applicant }) => {

  const certifications = (applicant.certifications ?? []).filter(cert => cert.isActive !== false);

  const ViewField: React.FC<{ label: string; value?: string | number; className?: string }> = ({ 
    label, 
    value, 
    className = "" 
  }) => (
    <div className={`space-y-1 ${className}`}>
      <span id={`${label.replace(/\s+/g, '-').toLowerCase()}-label`} className="text-sm font-medium text-gray-700">{label}</span>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center" aria-labelledby={`${label.replace(/\s+/g, '-').toLowerCase()}-label`}>
        {value ?? 'Not provided'}
      </div>
    </div>
  );

  const ViewSelectField: React.FC<{ label: string; value?: string; className?: string }> = ({ 
    label, 
    value, 
    className = "" 
  }) => (
    <div className={`space-y-1 ${className}`}>
      <label className="text-sm font-medium text-gray-700">{label}</label>
      <div className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md min-h-[36px] flex items-center">
        {mapUuidToName(value, listValues)}
      </div>
    </div>
  );

  const getExpiryStatus = (expiryDate?: string) => {
    if (!expiryDate) return null;
    
    const expiry = new Date(expiryDate);
    const now = new Date();
    const diffTime = expiry.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return { status: 'expired', message: 'Expired', variant: 'destructive' as const };
    } else if (diffDays <= 30) {
      return { status: 'expiring', message: `Expires in ${diffDays} days`, variant: 'secondary' as const };
    } else {
      return { status: 'valid', message: 'Valid', variant: 'default' as const };
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Certifications</h3>
        
        {certifications.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Award className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <p>No certifications added</p>
          </div>
        ) : (
          <div className="space-y-6">
            {certifications.map((certification, index) => {
              const expiryStatus = getExpiryStatus(certification.expiryDate);
              
              return (
                <div key={index} className="border rounded-lg p-6 bg-white">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <Award className="h-6 w-6 text-blue-600 mt-1" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">
                          {certification.certification ?? 'Certification Not Specified'}
                        </h4>
                        {certification.issuingOrganization && (
                          <p className="text-md text-gray-700 font-medium">
                            {certification.issuingOrganization}
                          </p>
                        )}
                        <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
                          {certification.issueDate && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              Issued: {new Date(certification.issueDate).toLocaleDateString()}
                            </div>
                          )}
                          {certification.yearCompleted && !certification.issueDate && (
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4" />
                              Completed: {certification.yearCompleted}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex flex-col gap-2 items-end">
                      {expiryStatus && (
                        <Badge variant={expiryStatus.variant}>
                          {expiryStatus.message}
                        </Badge>
                      )}
                      {certification.credentialId && (
                        <span className="text-xs text-gray-500">
                          ID: {certification.credentialId}
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    {certification.certificationLevel && (
                      <ViewSelectField 
                        label="Certification Level" 
                        value={certification.certificationLevel} 
                      />
                    )}
                    
                    {certification.score && (
                      <ViewField 
                        label="Score" 
                        value={certification.score} 
                      />
                    )}
                    
                    {certification.validityPeriod && (
                      <ViewField 
                        label="Validity Period" 
                        value={certification.validityPeriod} 
                      />
                    )}
                    
                    {certification.expiryDate && (
                      <ViewField 
                        label="Expiry Date" 
                        value={new Date(certification.expiryDate).toLocaleDateString()} 
                      />
                    )}
                    
                    {certification.renewalDate && (
                      <ViewField 
                        label="Renewal Date" 
                        value={new Date(certification.renewalDate).toLocaleDateString()} 
                      />
                    )}
                    
                    {certification.credentialId && (
                      <ViewField 
                        label="Credential ID" 
                        value={certification.credentialId} 
                      />
                    )}
                  </div>

                  {certification.skills && certification.skills.length > 0 && (
                    <div className="mb-4">
                      <span id="skills-covered-label" className="text-sm font-medium text-gray-700 mb-1 block">Skills Covered</span>
                      <div className="bg-gray-50 px-3 py-2 rounded-md min-h-[36px]" aria-labelledby="skills-covered-label">
                        <div className="flex flex-wrap gap-1">
                          {certification.skills.map((skill, skillIndex) => (
                            <Badge key={skillIndex} variant="secondary" className="text-xs">
                              {skill}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {certification.description && (
                    <div className="mb-4">
                      <ViewField 
                        label="Description" 
                        value={certification.description} 
                      />
                    </div>
                  )}

                  {certification.comments && (
                    <div className="mb-4">
                      <ViewField 
                        label="Comments" 
                        value={certification.comments} 
                      />
                    </div>
                  )}

                  {(certification.credentialUrl || certification.verificationUrl) && (
                    <div className="flex gap-2 mb-4">
                      {certification.credentialUrl && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex items-center gap-2"
                          onClick={() => window.open(certification.credentialUrl, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                          View Credential
                        </Button>
                      )}
                      {certification.verificationUrl && (
                        <Button
                          size="sm"
                          variant="outline"
                          className="flex items-center gap-2"
                          onClick={() => window.open(certification.verificationUrl, '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                          Verify
                        </Button>
                      )}
                    </div>
                  )}

                  {certification.isVerified !== undefined && (
                    <div className="pt-4 border-t">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium text-gray-700">Verification Status:</span>
                        <Badge variant={certification.isVerified ? "default" : "secondary"}>
                          {certification.isVerified ? "Verified" : "Not Verified"}
                        </Badge>
                      </div>
                      {certification.verificationDate && (
                        <div className="mt-2">
                          <span className="text-sm text-gray-600">
                            Verified on: {new Date(certification.verificationDate).toLocaleDateString()}
                          </span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default CertificationsView;
