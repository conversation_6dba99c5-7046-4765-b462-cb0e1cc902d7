import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from '@/contexts/AuthContext';
import { Applicant } from '@/types';
import { DocumentTreeSelectOption } from '@/types/applicants';
import { FileText, Eye } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { getDocumentTree } from '@/api/applicantsApi';
import { LIST_IDS } from '@/constants/listIds';
import { useToast } from '@/hooks/use-toast';
import DocumentViewer from '@/components/ui/DocumentViewer';

interface DocumentsViewProps {
  applicant: Applicant;
}

const DocumentsView: React.FC<DocumentsViewProps> = ({ applicant }) => {
  const { listValues } = useAuth();
  const { toast } = useToast();
  const [treeOptions, setTreeOptions] = useState<DocumentTreeSelectOption[]>([]);
  const [isLoadingTreeOptions, setIsLoadingTreeOptions] = useState(false);
  const [viewerState, setViewerState] = useState<{
    isOpen: boolean;
    fileLocation?: string;
    fileName?: string;
  }>({
    isOpen: false,
    fileLocation: undefined,
    fileName: undefined,
  });
  const documents = applicant.documents ?? [];

  // Fetch document tree options on component mount
  useEffect(() => {
    const fetchTreeOptions = async () => {
      setIsLoadingTreeOptions(true);
      
      const result = await getDocumentTree(
        LIST_IDS.DOCUMENT_TREE_RESOURCE,
        (error) => {
          toast({
            title: "Error",
            description: error.message || "Failed to load document types",
            variant: "destructive",
          });
        }
      );

      if (result && result.children) {
        // Convert children to select options (id -> value, name -> key)
        const options: DocumentTreeSelectOption[] = result.children
          .filter(child => child.isActive) // Only active subcategories
          .map(child => ({
            value: child.id,
            key: child.name,
          }));
        
        setTreeOptions(options);
      }
      
      setIsLoadingTreeOptions(false);
    };

    fetchTreeOptions();
  }, [toast]);

  // Helper function to map UUID to name using tree options
  const mapUuidToNameFromTree = (uuid?: string): string => {
    if (!uuid) return "Unknown";
    const option = treeOptions.find(option => option.value === uuid);
    return option ? option.key : uuid;
  };

  // Helper function to extract filename from file path (part after underscore)
  const getFileName = (document: NonNullable<Applicant['documents']>[0]): string => {
    let fileName = '';
    
    // First try to use fileName if available
    if (document.fileName) {
      fileName = document.fileName;
    }
    // If no fileName, extract from filePath
    else if (document.filePath) {
      const pathParts = document.filePath.split('/');
      fileName = pathParts[pathParts.length - 1] || 'Unknown file';
    }
    // Fallback to title if available
    else if (document.title) {
      fileName = document.title;
    }
    // Final fallback
    else {
      fileName = 'Unknown file';
    }

    // Extract part after underscore if it exists
    const underscoreIndex = fileName.indexOf('_');
    if (underscoreIndex !== -1 && underscoreIndex < fileName.length - 1) {
      return fileName.substring(underscoreIndex + 1);
    }
    
    return fileName;
  };

  // Handle document preview/view action
  const handleViewDocument = (document: NonNullable<Applicant['documents']>[0], index: number) => {
    if (!document.filePath) {
      toast({
        title: "Error",
        description: "Document file path not available",
        variant: "destructive",
      });
      return;
    }

    const fileName = getFileName(document);
    
    setViewerState({
      isOpen: true,
      fileLocation: document.filePath,
      fileName: fileName,
    });
  };

  // Close document viewer
  const handleCloseViewer = () => {
    setViewerState({
      isOpen: false,
      fileLocation: undefined,
      fileName: undefined,
    });
  };

  // Helper component for disabled text inputs
  const DisabledTextInput: React.FC<{ 
    id: string; 
    label: string; 
    value?: string; 
  }> = ({ id, label, value }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>{label}</Label>
      <Input
        id={id}
        value={value || ""}
        disabled
        className="bg-gray-50"
      />
    </div>
  );

  // Helper component for disabled select inputs
  const DisabledSelectInput: React.FC<{ 
      id: string; 
      label: string; 
      value?: string; 
    }> = ({ id, label, value }) => (
      <div className="space-y-2">
        <Label htmlFor={id}>{label}</Label>
        <Input
          id={id}
          value={mapUuidToNameFromTree(value)}
          disabled
          className="bg-gray-50"
        />
      </div>
    );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <FileText className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Documents</h3>
      </div>
      
      {documents.length === 0 ? (
        <Card className="border-l-4 border-l-gray-500">
          <CardContent className="py-8">
            <div className="text-center text-gray-500">
              <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>No documents uploaded</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {documents.map((document, index) => {
            const fileName = getFileName(document);
            
            return (
              <Card key={index} className="border-l-4 border-l-blue-500">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base font-medium flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span className="truncate">Document {index + 1}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDocument(document, index)}
                        className="h-8 w-8 p-0"
                        title="View document"
                        disabled={!document.filePath}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <DisabledSelectInput
                      id={`type-${index}`}
                      label="Document Type" 
                      value={document.subcategory}  
                    />
                    
                    <DisabledTextInput 
                      id={`title-${index}`}
                      label="Title" 
                      value={document.title} 
                    />
                    
                    <DisabledTextInput 
                      id={`comments-${index}`}
                      label="Comments" 
                      value={document.comments} 
                    />
                  </div>
                  
                  {/* File name display below the form fields */}
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">File:</span>
                      <span className="text-sm text-gray-600">{fileName}</span>
                      {!document.filePath && (
                        <span className="text-xs text-red-500 ml-2">(File not available)</span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Document Viewer Dialog */}
      <DocumentViewer
        isOpen={viewerState.isOpen}
        onClose={handleCloseViewer}
        fileLocation={viewerState.fileLocation}
        fileName={viewerState.fileName}
      />
    </div>
  );
};

export default DocumentsView;
