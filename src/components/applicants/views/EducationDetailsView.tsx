import React from 'react';
import { Applicant } from '@/types';
import { Badge } from '@/components/ui/badge';
import { GraduationCap, MapPin, Calendar } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { mapUuidToName } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

// Extended interface to handle API response structure
interface ApiEducationDetails {
  id?: string;
  schoolName?: string;
  degree?: string;
  yearCompleted?: string;
  majorStudy?: string;
  minorStudy?: string;
  gpa?: string;
  country?: string;
  state?: string;
  city?: string;
  isHigherEducation?: boolean;
}

interface EducationDetailsViewProps {
  applicant: Applicant;
}

const EducationDetailsView: React.FC<EducationDetailsViewProps> = ({ applicant }) => {
  const { listValues } = useAuth();

  const educationDetails = (applicant.educationDetails ?? []).filter(edu => edu.isActive !== false);


  // Helper component for disabled text inputs
  const DisabledTextInput: React.FC<{ 
    id: string; 
    label: string; 
    value?: string | number; 
  }> = ({ id, label, value }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>{label}</Label>
      <Input
        id={id}
        value={value || ""}
        disabled
        className="bg-gray-50"
      />
    </div>
  );

  // Helper component for disabled select inputs
  const DisabledSelectInput: React.FC<{ 
    id: string; 
    label: string; 
    value?: string; 
  }> = ({ id, label, value }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>{label}</Label>
      <Input
        id={id}
        value={mapUuidToName(value, listValues)}
        disabled
        className="bg-gray-50"
      />
    </div>
  );

  const getEducationLevel = (degree?: string) => {
    if (!degree) return 'Unknown Level';
    const lowerDegree = degree.toLowerCase();
    if (lowerDegree.includes('phd') || lowerDegree.includes('doctorate')) return 'Doctorate';
    if (lowerDegree.includes('master') || lowerDegree.includes('mba') || lowerDegree.includes('ms') || lowerDegree.includes('ma')) return 'Masters';
    if (lowerDegree.includes('bachelor') || lowerDegree.includes('bs') || lowerDegree.includes('ba') || lowerDegree.includes('be')) return 'Bachelors';
    if (lowerDegree.includes('associate')) return 'Associate';
    if (lowerDegree.includes('diploma')) return 'Diploma';
    return 'Other';
  };

  const getGradeDisplay = (education: { gpa?: string; percentage?: string; grade?: string }) => {
    if (education.gpa) return `GPA: ${education.gpa}`;
    if (education.percentage) return `${education.percentage}%`;
    if (education.grade) return education.grade;
    return 'Not provided';
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <GraduationCap className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Education Details</h3>
      </div>
      
      {educationDetails.length === 0 ? (
        <Card className="border-l-4 border-l-gray-500">
          <CardContent className="py-8">
            <div className="text-center text-gray-500">
              <GraduationCap className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <p>No education details added</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {educationDetails.map((education, index) => (
            <Card key={index} className="border-l-4 border-l-blue-500">
              <CardHeader className="pb-3">
                <CardTitle className="text-base font-medium flex items-center gap-2">
                  <GraduationCap className="h-4 w-4" />
                  Education {index + 1} - {getEducationLevel(education.degree)}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <DisabledTextInput
                    id={`schoolName-${index}`}
                    label="School Name"
                    value={education.schoolName}
                  />

                  <DisabledSelectInput
                    id={`degree-${index}`}
                    label="Degree"
                    value={education.degree}
                  />

                  <DisabledTextInput
                    id={`yearCompleted-${index}`}
                    label="Year Completed"
                    value={education.yearCompleted}
                  />

                  <DisabledTextInput
                    id={`majorStudy-${index}`}
                    label="Major Study"
                    value={education.majorStudy}
                  />

                  <DisabledTextInput
                    id={`minorStudy-${index}`}
                    label="Minor Study"
                    value={education.minorStudy}
                  />

                  <DisabledTextInput
                    id={`gpa-${index}`}
                    label="GPA"
                    value={education.gpa}
                  />

                  <DisabledSelectInput
                    id={`country-${index}`}
                    label="Country"
                    value={education.country}
                  />

                  <DisabledSelectInput
                    id={`state-${index}`}
                    label="State"
                    value={education.state}
                  />

                  <DisabledTextInput
                    id={`city-${index}`}
                    label="City"
                    value={education.city}
                  />
                </div>

                {/* Status Information */}
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Higher Education status from API response */}
                    {typeof (education as ApiEducationDetails).isHigherEducation === 'boolean' && (
                      <div className="flex items-center gap-2">
                        <Label>Higher Education:</Label>
                        <Badge variant={(education as ApiEducationDetails).isHigherEducation ? "default" : "secondary"}>
                          {(education as ApiEducationDetails).isHigherEducation ? "Yes" : "No"}
                        </Badge>

                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default EducationDetailsView;
