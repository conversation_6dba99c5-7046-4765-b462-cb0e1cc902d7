import React, { useState } from "react";
import { Applicant } from "@/types";
import { useAuth } from "@/contexts/AuthContext";
import { mapUuidToName } from "@/lib/utils";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  MapPin,
  Globe,
  Shield,
} from "lucide-react";

interface PersonalInfoViewProps {
  applicant: Applicant;
}

const PersonalInfoView: React.FC<PersonalInfoViewProps> = ({ applicant }) => {
  const { listValues } = useAuth();

  // State for collapsible sections - matching PersonalInfoForm exactly
  const [openSections, setOpenSections] = useState({
    basic: true,
    contact: false,
    address: false,
    personal: false,
    social: false,
    work: false,
  });

  const toggleSection = (section: keyof typeof openSections) => {
    try {
      setOpenSections((prev) => ({
        ...prev,
        [section]: !prev[section],
      }));
    } catch (error) {
      console.error("Error toggling section:", error, { section });
    }
  };

  // Helper component for disabled text inputs
  const DisabledTextInput: React.FC<{
    id: string;
    label: string;
    value?: string | number;
    required?: boolean;
    type?: string;
  }> = ({ id, label, value, required, type = "text" }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      <Input
        id={id}
        type={type}
        value={value || ""}
        disabled
        className="bg-gray-50"
      />
    </div>
  );

  // Helper component for disabled select inputs
  const DisabledSelectInput: React.FC<{
    id: string;
    label: string;
    value?: string;
  }> = ({ id, label, value }) => {
    const mappedValue = mapUuidToName(value, listValues);

    // Debug logging for UUID mapping issues
    if (
      value &&
      value !== mappedValue &&
      process.env.NODE_ENV === "development"
    ) {
      console.log(`UUID Mapping for ${label}:`, {
        originalValue: value,
        mappedValue: mappedValue,
        listValuesCount: listValues.length,
        foundMatch: listValues.find((item) => item.id === value),
      });
    }

    return (
      <div className={label ? "space-y-2" : ""}>
        {label && <Label htmlFor={id}>{label}</Label>}
        <Input
          id={id}
          value={mappedValue}
          disabled
          className="bg-gray-50"
          placeholder={mappedValue ? undefined : "Not provided"}
        />
      </div>
    );
  };

  // Helper component for disabled textarea
  const DisabledTextArea: React.FC<{
    id: string;
    label: string;
    value?: string;
  }> = ({ id, label, value }) => (
    <div className="space-y-2">
      <Label htmlFor={id}>{label}</Label>
      <Textarea
        id={id}
        value={value || ""}
        disabled
        className="bg-gray-50"
        rows={3}
      />
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <User className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          Personal Information
        </h3>
      </div>

      {/* Basic Information - Always Open (matching PersonalInfoForm) */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <User className="h-4 w-4" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* First Name */}
            <DisabledTextInput
              id="firstName"
              label="First Name"
              required
              value={applicant.firstName}
            />

            {/* Last Name */}
            <DisabledTextInput
              id="lastName"
              label="Last Name"
              required
              value={applicant.lastName}
            />

            {/* Middle Name */}
            <DisabledTextInput
              id="middleName"
              label="Middle Name"
              value={applicant.personalDetails.middleName}
            />

            {/* Nick Name (using preferredName from API) */}
            <DisabledTextInput
              id="nickName"
              label="Nick Name"
              value={applicant.personalDetails.nickName}
            />

            {/* Email */}
            <DisabledTextInput
              id="email"
              label="Email"
              type="email"
              required
              value={applicant.email}
            />

            {/* Alternate Email */}
            <DisabledTextInput
              id="alternateEmail"
              label="Alternate Email"
              type="email"
              value={applicant.personalDetails.alternateEmail}
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information - Collapsible */}
      <Card className="border-l-4 border-l-green-500">
        <Collapsible
          open={openSections.contact}
          onOpenChange={() => toggleSection("contact")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Contact Information
                </div>
                {openSections.contact ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Home Phone */}
                <DisabledTextInput
                  id="homePhone"
                  label="Home Phone Number"
                  value={applicant.personalDetails.homePhone}
                />

                {/* Mobile Number */}
                <DisabledTextInput
                  id="mobilePhone"
                  label="Mobile Number"
                  value={applicant.personalDetails.mobilePhone}
                />

                {/* Work Phone */}
                <DisabledTextInput
                  id="workPhone"
                  label="Work Phone Number"
                  value={applicant.personalDetails.workPhone}
                />

                {/* Other Phone */}
                <DisabledTextInput
                  id="otherPhone"
                  label="Other Phone"
                  value={applicant.personalDetails.otherPhone}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Address Information - Collapsible */}
      <Card className="border-l-4 border-l-purple-500">
        <Collapsible
          open={openSections.address}
          onOpenChange={() => toggleSection("address")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Address Information
                </div>
                {openSections.address ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* City */}
                <DisabledTextInput
                  id="city"
                  label="City"
                  value={applicant.personalDetails.city}
                />

                {/* Zip Code */}
                <DisabledTextInput
                  id="zipCode"
                  label="Zip Code"
                  value={applicant.personalDetails.zipCode}
                />

                {/* Country */}
                <DisabledSelectInput
                  id="country"
                  label="Country"
                  value={applicant.personalDetails.country}
                />

                {/* State */}
                <DisabledSelectInput
                  id="state"
                  label="State"
                  value={applicant.personalDetails.state}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Personal Details - Collapsible */}
      <Card className="border-l-4 border-l-orange-500">
        <Collapsible
          open={openSections.personal}
          onOpenChange={() => toggleSection("personal")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Personal Details
                </div>
                {openSections.personal ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Date of Birth */}
                <DisabledTextInput
                  id="dateOfBirth"
                  label="Date of Birth"
                  type="date"
                  value={applicant.personalDetails.dateOfBirth}
                />

                {/* SSN */}
                <DisabledTextInput
                  id="ssn"
                  label="SSN"
                  value={
                    applicant.personalDetails.ssn
                      ? "***-**-" + applicant.personalDetails.ssn.slice(-4)
                      : undefined
                  }
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Social Media & Online Presence - Collapsible */}
      <Card className="border-l-4 border-l-cyan-500">
        <Collapsible
          open={openSections.social}
          onOpenChange={() => toggleSection("social")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Social Media & Online Presence
                </div>
                {openSections.social ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* LinkedIn Profile URL */}
                <DisabledTextInput
                  id="linkedinProfileUrl"
                  label="LinkedIn Profile URL"
                  value={applicant.personalDetails.linkedinProfileUrl}
                />

                {/* Facebook Profile URL */}
                <DisabledTextInput
                  id="facebookProfileUrl"
                  label="Facebook Profile URL"
                  value={applicant.personalDetails.facebookProfileUrl}
                />

                {/* Twitter Profile URL */}
                <DisabledTextInput
                  id="twitterProfileUrl"
                  label="Twitter Profile URL"
                  value={applicant.personalDetails.twitterProfileUrl}
                />

                {/* Skype ID */}
                <DisabledTextInput
                  id="skypeId"
                  label="Skype ID"
                  value={applicant.personalDetails.skypeId}
                />

                {/* Video Reference */}
                <DisabledTextInput
                  id="videoReference"
                  label="Video Reference"
                  value={applicant.personalDetails.videoReference}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Work Authorization & Clearance - Collapsible */}
      <Card className="border-l-4 border-l-red-500">
        <Collapsible
          open={openSections.work}
          onOpenChange={() => toggleSection("work")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Work Authorization & Clearance
                </div>
                {openSections.work ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Work Authorization */}
                <DisabledSelectInput
                  id="workAuthorization"
                  label="Work Authorization"
                  value={applicant.personalDetails.workAuthorization}
                />

                {/* Clearance */}
                <DisabledSelectInput
                  id="clearance"
                  label="Clearance"
                  value={applicant.personalDetails.clearance ? "Yes" : "No"}
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
};

export default PersonalInfoView;
