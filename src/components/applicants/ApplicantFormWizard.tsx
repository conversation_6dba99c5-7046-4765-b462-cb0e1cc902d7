import { useState, useEffect, useRef, Suspense, lazy } from "react";
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, <PERSON>bs<PERSON>ist, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { cn, generateUUID } from "@/lib/utils";
import {
  User,
  FileText,
  Briefcase,
  GraduationCap,
  Award,
  Check,
  X,
  Building,
  Clock,
} from "lucide-react";
import { FormFallback } from "@/components/ui/LazyLoadFallback";
import { ApplicantOperationDialog } from "@/components/applicants/ui";
import { useApplicantOperationDialog, getErrorType } from "@/hooks/useApplicantOperationDialog";

// Lazy load form sections for better performance
const PersonalInfoForm = lazy(() => import("./forms/PersonalInfoForm"));
const ProfessionalInfoForm = lazy(() => import("./forms/ProfessionalInfoForm"));
const DocumentsForm = lazy(() => import("./forms/DocumentsForm"));
const WorkExperienceForm = lazy(() => import("./forms/WorkExperienceForm"));
const EducationDetailsForm = lazy(() => import("./forms/EducationDetailsForm"));
const CertificationsForm = lazy(() => import("./forms/CertificationsForm"));
const EmployerDetailsForm = lazy(() => import("./forms/EmployerDetailsForm"));

import {
  Applicant,
  PersonalInfo,
  ProfessionalInfo,
  DocumentDetails,
  WorkExperience,
  EducationDetails,
  CertificationDetails,
  EmployerDetails,
  LanguageDetails,
} from "@/types";
import { ApplicantDetailResponse } from "@/types/applicants";
import { convertApiResponseToFormData, updateApiResponseFromFormData } from "@/utils/applicants";

// Validation schemas for runtime prop validation
const ApplicantSchema = z.object({
  id: z.string().uuid().optional(),
  systemCode: z.string().optional(),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Valid email is required"),
  // Add other validation rules as needed
}).optional().nullable();

const ApplicantDetailResponseSchema = z.object({
  id: z.string().uuid("Valid UUID required for applicant ID"),
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"), 
  email: z.string().email("Valid email is required"),
  // Add other validation rules as needed
}).optional().nullable();

const ApplicantFormWizardPropsSchema = z.object({
  open: z.boolean(),
  onOpenChange: z.function(),
  applicant: ApplicantSchema,
  apiApplicant: ApplicantDetailResponseSchema,
  onSave: z.function(),
  onSaveApi: z.function().optional(),
  mode: z.enum(["add", "edit"]),
});

export interface ApplicantFormData {
  personalInfo: PersonalInfo;
  professionalInfo: ProfessionalInfo;
  documents: DocumentDetails[];
  workExperience: WorkExperience[];
  educationDetails: EducationDetails[];
  certifications: CertificationDetails[];
  employerDetails: EmployerDetails;
  languages: LanguageDetails[];
}

interface ApplicantFormWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  applicant?: Applicant | null;
  apiApplicant?: ApplicantDetailResponse | null; // New prop for API structure preservation
  onSave: (applicantData: ApplicantFormData) => void;
  onSaveApi?: (apiData: { id: string; [key: string]: unknown }, formData: ApplicantFormData) => void; // New callback for API structure
  mode: "add" | "edit";
}

const formSections = [
  {
    id: "personal-info",
    label: "Personal Info",
    icon: User,
    component: PersonalInfoForm,
  },
  {
    id: "professional-info",
    label: "Professional Info",
    icon: Briefcase,
    component: ProfessionalInfoForm,
  },
  {
    id: "documents",
    label: "Documents",
    icon: FileText,
    component: DocumentsForm,
  },
  {
    id: "education-details",
    label: "Education Details",
    icon: GraduationCap,
    component: EducationDetailsForm,
  },
  {
    id: "certifications",
    label: "Certifications",
    icon: Award,
    component: CertificationsForm,
  },
  {
    id: "employer-details",
    label: "Employer Details",
    icon: Building,
    component: EmployerDetailsForm,
  },
  {
    id: "work-experience",
    label: "Work Experience",
    icon: Clock,
    component: WorkExperienceForm,
  },
];

const getInitialFormData = (
  applicant?: Applicant | null
): ApplicantFormData => {
  if (applicant) {
    const personalDetails = applicant.personalDetails ?? {
      firstName: applicant.firstName ?? "",
      lastName: applicant.lastName ?? "",
      email: applicant.email ?? "",
      homePhone: applicant.phone ?? "",
    };

    return {
      personalInfo: {
        firstName: personalDetails.firstName ?? "",
        lastName: personalDetails.lastName ?? "",
        middleName: personalDetails.middleName ?? "",
        nickName: personalDetails.nickName ?? "",
        email: personalDetails.email ?? "",
        alternateEmail: personalDetails.alternateEmail ?? "",
        homePhone: personalDetails.homePhone ?? "",
        mobilePhone: personalDetails.mobilePhone ?? "",
        workPhone: personalDetails.workPhone ?? "",
        otherPhone: personalDetails.otherPhone ?? "",
        dateOfBirth: personalDetails.dateOfBirth ?? "",
        ssn: personalDetails.ssn ?? "",
        skypeId: personalDetails.skypeId ?? "",
        linkedinProfileUrl: personalDetails.linkedinProfileUrl ?? "",
        facebookProfileUrl: personalDetails.facebookProfileUrl ?? "",
        twitterProfileUrl: personalDetails.twitterProfileUrl ?? "",
        videoReference: personalDetails.videoReference ?? "",
        workAuthorization: personalDetails.workAuthorization ?? "",
        workAuthorizationExpiry: personalDetails.workAuthorizationExpiry ?? "",
        clearance: personalDetails.clearance ?? undefined,
        address: personalDetails.address ?? "",
        city: personalDetails.city ?? "",
        country: personalDetails.country ?? "",
        state: personalDetails.state ?? "",
        zipCode: personalDetails.zipCode ?? "",
      },
      professionalInfo: {
        source: personalDetails.source ?? "",
        referredBy: personalDetails.referredBy ?? "",
        experienceYears: personalDetails.experienceYears ?? "",
        experienceMonths: personalDetails.experienceMonths ?? "",
        jobTitle: personalDetails.jobTitle ?? "",
        expectedPayCurrency: personalDetails.expectedPayCurrency ?? "",
        expectedPayAmount: personalDetails.expectedPayAmount ?? "",
        currentCtcCurrency: personalDetails.currentCtcCurrency ?? "",
        currentCtcAmount: personalDetails.currentCtcAmount ?? "",
        skills: personalDetails.skills ?? [],
        primarySkills: personalDetails.primarySkills ?? [],
        technology: Array.isArray(personalDetails.technology)
          ? undefined
          : personalDetails.technology,
        industry: personalDetails.industry ?? [],
        function: personalDetails.function ?? [],
        taxTerms: personalDetails.taxTerms ?? "",
        noticePeriod: personalDetails.noticePeriod ?? "",
        currentCompany: personalDetails.currentCompany ?? "",
        applicantStatus: personalDetails.applicantStatus ?? "",
        applicantGroup: Array.isArray(personalDetails.applicantGroup)
          ? undefined
          : personalDetails.applicantGroup,
        ownership: personalDetails.ownership ?? "",
        relocation: personalDetails.relocation ?? undefined,
        additionalComments: personalDetails.additionalComments ?? "",
        panCardNumber: personalDetails.panCardNumber ?? "",
        aadharNumber: personalDetails.aadharNumber ?? "",
        gpa: personalDetails.gpa ?? "",
        gender: personalDetails.gender ?? "",
        raceEthnicity: personalDetails.raceEthnicity ?? "",
        veteranStatus: personalDetails.veteranStatus ?? "",
        veteranType: personalDetails.veteranType ?? "",
        disability: personalDetails.disability ?? "",
      },
      documents: applicant.documents?.filter(doc => doc.isActive === true) ?? [],
      workExperience: applicant.workExperience ?? [],
      educationDetails: applicant.educationDetails ?? [],
      certifications: applicant.certifications ?? [
        {
          id: generateUUID(),
          certification: "",
          yearCompleted: "",
          comments: "",
        },
      ],
      employerDetails: applicant.employerDetails ?? {
        isNew: true,
        addFromExistingVendor: false,
        status: true, // Default to Active
      },
      languages: applicant.languages ?? [],
    };
  }

  return {
    personalInfo: {
      firstName: "",
      lastName: "",
      email: "",
    } as PersonalInfo,
    professionalInfo: {} as ProfessionalInfo,
    documents: [],
    workExperience: [],
    educationDetails: [],
    certifications: [
      {
        id: generateUUID(),
        certification: "",
        yearCompleted: "",
        comments: "",
      },
    ],
    employerDetails: {
      isNew: true,
      addFromExistingVendor: false,
      status: true, // Default to Active
    },
    languages: [],
  };
};

export default function ApplicantFormWizard({
  open,
  onOpenChange,
  applicant,
  apiApplicant,
  onSave,
  onSaveApi,
  mode,
}: ApplicantFormWizardProps) {
  // Runtime prop validation
  useEffect(() => {
    try {
      ApplicantFormWizardPropsSchema.parse({
        open,
        onOpenChange,
        applicant,
        apiApplicant,
        onSave,
        onSaveApi,
        mode,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        console.error('ApplicantFormWizard prop validation failed:', error.errors);
        // In development, you might want to throw the error
        // In production, you might want to log it and continue with fallback behavior
        if (process.env.NODE_ENV === 'development') {
          console.warn('Props validation errors:', error.errors.map(e => `${e.path.join('.')}: ${e.message}`));
        }
      }
    }
  }, [open, onOpenChange, applicant, apiApplicant, onSave, onSaveApi, mode]);

  const [activeSection, setActiveSection] = useState("personal-info");
  const [formData, setFormData] = useState<ApplicantFormData>(() =>
    // Use API structure if available (edit mode), otherwise use legacy structure
    apiApplicant ? convertApiResponseToFormData(apiApplicant) : getInitialFormData(applicant)
  );
  const [apiFormData, setApiFormData] = useState<ApplicantDetailResponse | null>(apiApplicant || null);
  const [sectionValidation, setSectionValidation] = useState<
    Record<string, boolean>
  >({});
  const [interactedSections, setInteractedSections] = useState<
    Record<string, boolean>
  >({});
  const [attemptedSave, setAttemptedSave] = useState(false);

  // Dialog state management
  const { dialogState, showSuccessDialog, showFailureDialog, closeDialog, handleRetry } = useApplicantOperationDialog();

  // Section key mapping
  const sectionKeyMap: Record<string, keyof ApplicantFormData> = {
    "personal-info": "personalInfo",
    "professional-info": "professionalInfo",
    documents: "documents",
    "education-details": "educationDetails",
    certifications: "certifications",
    "employer-details": "employerDetails",
    "work-experience": "workExperience",
  };

  // Reset form data when applicant changes or dialog opens
  useEffect(() => {
    if (open) {
      // Use API structure if available (edit mode), otherwise use legacy structure
      const initialFormData = apiApplicant
        ? convertApiResponseToFormData(apiApplicant)
        : getInitialFormData(applicant);

      setFormData(initialFormData);
      setApiFormData(apiApplicant || null);
      setActiveSection("personal-info");
      setSectionValidation({});
      setInteractedSections({});
      setAttemptedSave(false);
    }
  }, [open, applicant, apiApplicant]);

  const updateFormData = (section: keyof ApplicantFormData, data: ApplicantFormData[keyof ApplicantFormData]) => {
    // Mark section as interacted when data changes
    const sectionId = Object.keys(sectionKeyMap).find(
      (key) => sectionKeyMap[key] === section
    );
    if (sectionId) {
      setInteractedSections((prev) => ({
        ...prev,
        [sectionId]: true,
      }));
    }

    // Update form data
    const updatedFormData = {
      ...formData,
      [section]: data,
    };
    setFormData(updatedFormData);

    // If we have API data, update it as well to maintain structure
    if (apiFormData) {
      const updatedApiData = updateApiResponseFromFormData(apiFormData, updatedFormData);
      setApiFormData(updatedApiData as ApplicantDetailResponse);
    }
  };

  const validateSection = (sectionId: string, isValid: boolean) => {
    setSectionValidation((prev) => ({
      ...prev,
      [sectionId]: isValid,
    }));
  };

  const handleSave = async () => {
    // Mark that save was attempted to show validation indicators
    setAttemptedSave(true);

    // Manually validate required fields before saving
    const personalInfoValid = validatePersonalInfo(formData.personalInfo);
    const professionalInfoValid = validateProfessionalInfo(formData.professionalInfo);

    // Update section validation with actual validation results
    setSectionValidation(prev => ({
      ...prev,
      'personal-info': personalInfoValid,
      'professional-info': professionalInfoValid,
    }));

    if (!personalInfoValid || !professionalInfoValid) {
      // Show validation error dialog
      showFailureDialog(mode, 'validation', 'Please check the form for validation errors and ensure all required fields are completed correctly.');
      
      // Find first invalid section and navigate to it
      if (!personalInfoValid) {
        setActiveSection('personal-info');
      } else if (!professionalInfoValid) {
        setActiveSection('professional-info');
      }
      return;
    }

    // All required sections are valid, proceed with save
    try {
      if (apiFormData && onSaveApi) {
        // Use API structure for edit mode
        const finalApiData = updateApiResponseFromFormData(apiFormData, formData);
        await onSaveApi(finalApiData as unknown as { id: string; [key: string]: unknown }, formData);
      } else {
        // Use legacy structure for add mode
        await onSave(formData);
      }
      
      // Show success dialog
      showSuccessDialog(mode);
      onOpenChange(false);
    } catch (error) {
      // Handle different types of errors and show appropriate dialog
      const { errorType, errorMessage } = getErrorType(error);
      showFailureDialog(mode, errorType, errorMessage);
    }
  };

  // Validation functions for required fields
  const validatePersonalInfo = (data: unknown): boolean => {
    if (!data || typeof data !== 'object') return false;
    const personalData = data as Record<string, unknown>;
    return !!(
      personalData.firstName &&
      typeof personalData.firstName === 'string' &&
      personalData.firstName.trim() &&
      personalData.lastName &&
      typeof personalData.lastName === 'string' &&
      personalData.lastName.trim() &&
      personalData.email &&
      typeof personalData.email === 'string' &&
      personalData.email.trim()
    );
  };

  const validateProfessionalInfo = (_data: unknown): boolean => {
    // Professional info has no required fields currently
    return true;
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  // Helper function to determine if validation indicators should be shown
  const shouldShowValidation = (sectionId: string) => {
    return attemptedSave || interactedSections[sectionId];
  };



  const renderActiveSection = () => {
    const section = formSections.find((s) => s.id === activeSection);
    if (!section) return null;

    const Component = section.component;
    const sectionKey = sectionKeyMap[activeSection];
    if (!sectionKey) return null;

    return (
      <Suspense fallback={<FormFallback />}>
        <Component
          data={formData[sectionKey] as never}
          onChange={(data: ApplicantFormData[keyof ApplicantFormData]) => updateFormData(sectionKey, data)}
          onValidationChange={(isValid: boolean) =>
            validateSection(activeSection, isValid)
          }
          attemptedSave={attemptedSave}
        />
      </Suspense>
    );
  };

  // Ref for scrollable tab list container (mobile)
  const tabListScrollRef = useRef<HTMLDivElement>(null);

  // Scroll to leftmost position on mount (mobile)
  useEffect(() => {
    if (tabListScrollRef.current) {
      tabListScrollRef.current.scrollLeft = 0;
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-screen h-screen max-w-none max-h-none p-0 flex flex-col">
        {/* Fixed Header */}
        <DialogHeader className="px-6 py-4 border-b bg-white z-10 flex-shrink-0">
          <DialogTitle>
            {mode === "add" ? "Add New Applicant" : "Edit Applicant"}
          </DialogTitle>
        </DialogHeader>

        {/* Desktop Layout (md and above) */}
        <div className="hidden md:flex flex-1 min-h-0">
          {/* Left Panel - Navigation */}
          <div className="w-64 border-r bg-gray-50/50">
            <ScrollArea className="h-full">
              <div className="p-4 space-y-2">
                {formSections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;
                  const isValid = sectionValidation[section.id];
                  const showValidation = shouldShowValidation(section.id);

                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2.5 text-left rounded-lg transition-colors",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-gray-100 text-gray-700"
                      )}
                    >
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <span className="flex-1 text-sm font-medium">
                        {section.label}
                      </span>
                      {showValidation && isValid === true && (
                        <Check className="h-4 w-4 text-green-600" />
                      )}
                      {showValidation && isValid === false && (
                        <X className="h-4 w-4 text-red-600" />
                      )}
                    </button>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Right Panel - Form Content */}
          <div className="flex-1 flex flex-col min-h-0">
            <ScrollArea className="flex-1">
              <div className="p-6">{renderActiveSection()}</div>
            </ScrollArea>

            {/* Footer */}
            <div className="border-t p-4 flex justify-end gap-3 bg-white flex-shrink-0">
              <Button variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button onClick={handleSave}>
                {mode === "add" ? "Add Applicant" : "Save Changes"}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Layout (below md breakpoint) */}
        <div className="md:hidden flex-1 flex flex-col min-h-0">
          <Tabs
            value={activeSection}
            onValueChange={setActiveSection}
            className="flex-1 flex flex-col min-h-0"
          >
            {/* Fixed Horizontal scrollable tabs */}
            <div className="border-b bg-gray-50/50 px-4 py-2 flex-shrink-0">
              <div className="w-full overflow-x-auto" ref={tabListScrollRef}>
                <TabsList className="flex flex-nowrap min-w-max h-auto p-1 bg-transparent gap-1 w-full">
                  {formSections.map((section) => {
                    const Icon = section.icon;
                    const isValid = sectionValidation[section.id];
                    const showValidation = shouldShowValidation(section.id);

                    return (
                      <TabsTrigger
                        key={section.id}
                        value={section.id}
                        className={cn(
                          "flex flex-col items-center gap-1 px-3 py-2 text-xs font-medium rounded-md whitespace-nowrap flex-shrink-0 min-w-[80px]",
                          "data-[state=active]:bg-primary data-[state=active]:text-primary-foreground",
                          "data-[state=inactive]:bg-white data-[state=inactive]:text-gray-700",
                          "data-[state=inactive]:hover:bg-gray-100"
                        )}
                      >
                        <Icon className="h-5 w-5 mb-1" />
                        <span className="hidden sm:inline">
                          {section.label}
                        </span>
                        <span className="sm:hidden">
                          {section.label.split(" ")[0]}
                        </span>
                        {showValidation && isValid === true && (
                          <Check className="h-3 w-3 text-green-600 mt-1" />
                        )}
                        {showValidation && isValid === false && (
                          <X className="h-3 w-3 text-red-600 mt-1" />
                        )}
                      </TabsTrigger>
                    );
                  })}
                </TabsList>
              </div>
            </div>

            {/* Scrollable Tab content */}
            <div className="flex-1 min-h-0">
              {formSections.map((section) => (
                <TabsContent
                  key={section.id}
                  value={section.id}
                  className="h-full mt-0 data-[state=inactive]:hidden"
                >
                  <ScrollArea className="h-full">
                    <div className="p-4 pb-20">{renderActiveSection()}</div>
                  </ScrollArea>
                </TabsContent>
              ))}
            </div>
          </Tabs>
          {/* </div> */}

          {/* Fixed Footer */}
          <div className="border-t p-4 flex justify-end gap-3 bg-white flex-shrink-0 z-10">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {mode === "add" ? "Add Applicant" : "Save Changes"}
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Operation Result Dialog */}
      <ApplicantOperationDialog
        open={dialogState.open}
        onOpenChange={closeDialog}
        type={dialogState.type || 'success'}
        operation={dialogState.operation || 'add'}
        errorType={dialogState.errorType}
        errorMessage={dialogState.errorMessage}
        onRetry={handleRetry(handleSave)}
      />
    </Dialog>
  );
}
