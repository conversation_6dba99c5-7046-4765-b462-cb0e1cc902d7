import { useEffect, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Switch } from "@/components/ui/switch";
import { EmployerDetails } from "@/types";
import { StaticApiSelectInput } from "@/components/applicants/form-fields";
import { vendorContactOptions } from "@/utils/applicants";

interface EmployerDetailsFormProps {
  data: EmployerDetails;
  onChange: (data: EmployerDetails) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function EmployerDetailsForm({
  data,
  onChange,
  onValidationChange,
}: EmployerDetailsFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = useState(false);

  // Ensure data is never undefined and has proper defaults
  const safeData = data ?? ({} as EmployerDetails);
  
  // Ensure at least one option is selected - default to "new" if neither is explicitly set
  const isNewSelected = safeData.isNew === true || (!safeData.isNew && !safeData.addFromExistingVendor);
  const isExistingSelected = safeData.addFromExistingVendor === true;

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Add validation rules if needed
    if (isExistingSelected && !safeData.vendorContact?.trim()) {
      newErrors.vendorContact =
        "Vendor contact is required when adding from existing vendor";
    }

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    if (hasInteracted) {
      validateForm();
    }
  }, [safeData, hasInteracted]);

  const handleChange = (
    field: keyof EmployerDetails,
    value: string | boolean
  ) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    onChange({
      ...safeData,
      [field]: value,
    });
  };

  const handleSelectionTypeChange = (value: string) => {
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    if (value === "new") {
      onChange({
        ...safeData,
        isNew: true,
        addFromExistingVendor: false,
      });
    } else if (value === "existing") {
      onChange({
        ...safeData,
        isNew: false,
        addFromExistingVendor: true,
      });
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-4">Add Employer Details</h3>

        <div className="space-y-6">
          {/* Selection Type */}
          <div className="space-y-2">
            <Label>Selection Type</Label>
            <RadioGroup
              value={isExistingSelected ? "existing" : "new"}
              onValueChange={handleSelectionTypeChange}
              className="flex gap-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="new" id="new-employer" />
                <Label htmlFor="new-employer">New</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="existing" id="existing-vendor" />
                <Label htmlFor="existing-vendor">
                  Add from existing vendor contact records
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Vendor Contact - shown when "existing" is selected */}
          {isExistingSelected && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
              <StaticApiSelectInput
                id="vendorContact"
                label="Vendor Contact"
                required={true}
                value={safeData.vendorContact ?? ""}
                onChange={(value) => handleChange("vendorContact", value ?? "")}
                options={vendorContactOptions}
                placeholder="Required"
                error={errors.vendorContact}
              />
            </div>
          )}

          {/* New Employer Fields - shown when "new" is selected */}
          {isNewSelected && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
              {/* First Name */}
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  placeholder="Enter First Name"
                  value={safeData.firstName ?? ""}
                  onChange={(e) => handleChange("firstName", e.target.value)}
                />
              </div>

              {/* Last Name */}
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  placeholder="Enter Last Name"
                  value={safeData.lastName ?? ""}
                  onChange={(e) => handleChange("lastName", e.target.value)}
                />
              </div>

              {/* Employer Name */}
              <div className="space-y-2">
                <Label htmlFor="employerName">Employer Name</Label>
                <Input
                  id="employerName"
                  placeholder="Enter Employer Name"
                  value={safeData.employerName ?? ""}
                  onChange={(e) => handleChange("employerName", e.target.value)}
                />
              </div>

              {/* Office Number */}
              <div className="space-y-2">
                <Label htmlFor="officeNumber">Office Number</Label>
                <Input
                  id="officeNumber"
                  placeholder="Enter Office Number"
                  value={safeData.officeNumber ?? ""}
                  onChange={(e) => handleChange("officeNumber", e.target.value)}
                />
              </div>

              {/* Email ID */}
              <div className="space-y-2">
                <Label htmlFor="emailId">Email ID</Label>
                <Input
                  id="emailId"
                  type="email"
                  placeholder="Enter Email ID"
                  value={safeData.emailId ?? ""}
                  onChange={(e) => handleChange("emailId", e.target.value)}
                />
              </div>

              {/* Extension */}
              {/* <div className="space-y-2">
                <Label htmlFor="extension">Extension</Label>
                <Input
                  id="extension"
                  placeholder="Enter Extension"
                  value={safeData.extension || ''}
                  onChange={(e) => handleChange('extension', e.target.value)}
                />
              </div> */}

              {/* Mobile Number */}
              <div className="space-y-2">
                <Label htmlFor="mobileNumber">Mobile Number</Label>
                <Input
                  id="mobileNumber"
                  placeholder="Enter Mobile Number"
                  value={safeData.mobileNumber ?? ""}
                  onChange={(e) => handleChange("mobileNumber", e.target.value)}
                />
              </div>

              {/* Status */}
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <div className="flex items-center space-x-3">
                  <Switch
                    id="status"
                    checked={safeData.status ?? true} // Default to Active (true)
                    onCheckedChange={(checked) =>
                      handleChange("status", checked)
                    }
                  />
                  <Label htmlFor="status" className="text-sm font-medium">
                    {safeData.status ?? true ? "Active" : "Inactive"}
                  </Label>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
