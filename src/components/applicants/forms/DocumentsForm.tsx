import { DocumentTreeSelectInput } from '@/components/applicants/form-fields';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { DocumentDetails } from '@/types';
import { LIST_IDS } from '@/constants/listIds';
import { generateUUID } from '@/lib/utils';
import { createNewDocumentEntry } from '@/utils/applicants/dataTransforms';
import { FileText, Plus, Upload, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface DocumentsFormProps {
  data: DocumentDetails[];
  onChange: (data: DocumentDetails[]) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function DocumentsForm({
  data,
  onChange,
  onValidationChange,
}: DocumentsFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({}); //NoSonar
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<number | null>(null);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    // Documents are optional, so no validation errors for now
    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    validateForm();
  }, [data]);

  // Helper function to extract filename from filePath
  const extractFilename = (filePath: string): string => {
    if (!filePath) return '';
    // Extract filename after the last slash and before any query parameters
    const parts = filePath.split('/');
    const lastPart = parts[parts.length - 1];
    if (!lastPart) return '';
    // Remove UUID prefix if it exists (pattern: UUID_filename)
    const underscoreIndex = lastPart.indexOf('_');
    if (underscoreIndex > 0 && underscoreIndex < 40) { // UUIDs are typically 36 chars + hyphens
      return lastPart.substring(underscoreIndex + 1);
    }
    return lastPart;
  };



  // Transform API documents to form format if needed
  const transformDocument = (doc: DocumentDetails | Record<string, unknown>): DocumentDetails => {
    const docData = doc as Record<string, unknown>;
    
    // Extract filename if filePath exists
    const filename = docData.filePath ? extractFilename(docData.filePath as string) : '';
    
    // Transform to ensure all required fields are present with new DocumentDetails format
    const transformed: DocumentDetails = {
      id: (docData.id as string) || generateUUID(),
      fileIndex: (docData.fileIndex as number) || 0,
      title: (docData.title as string) || '',
      category: (docData.category as string) || '',
      subcategory: (docData.subcategory as string) || '',
      comments: (docData.comments as string) || '',
      filePath: docData.filePath as string,
      fileName: filename || (docData.fileName as string),
      attachment: filename || (docData.attachment as string | File),
      file: docData.file as File,
      resumeVisibility: docData.resumeVisibility as string,
      isActive: typeof docData.isActive === 'boolean' ? docData.isActive : true, // Preserve original isActive value or default to true
    };
    

    
    return transformed;
  };

  // Transform data on the fly to ensure compatibility
  const transformedData = data.map(transformDocument);
  
  // Filter to show only active documents in the UI
  const activeDocuments = transformedData.filter(doc => doc.isActive === true);
  

  const addDocument = () => {
    // Create new document entry with proper structure:
    // - Random UUID as id
    // - filePath as undefined for new documents  
    // - subcategory will be set when user selects document type
    const newDocument = createNewDocumentEntry('', '', '', data.length);
    onChange([...data, newDocument]);
  };

  const updateDocument = (index: number, field: keyof DocumentDetails, value: string | File) => {
    const updatedDocuments = transformedData.map((doc, i) => 
      i === index ? { ...doc, [field]: value } : doc
    );
    onChange(updatedDocuments);
  };

  const handleDeleteClick = (index: number) => {
    setDocumentToDelete(index);
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (documentToDelete !== null) {
      const updatedDocuments = transformedData.map((doc, i) => 
        i === documentToDelete ? { ...doc, isActive: false } : doc
      );
      onChange(updatedDocuments);
    }
    setShowDeleteDialog(false);
    setDocumentToDelete(null);
  };

  const cancelDelete = () => {
    setShowDeleteDialog(false);
    setDocumentToDelete(null);
  };

  const handleFileUpload = (index: number, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      updateDocument(index, 'file', file); // Use the new 'file' field
      updateDocument(index, 'attachment', file); // Keep backward compatibility
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Documents</h3>
          <p className="text-sm text-gray-600">Add relevant documents and files</p>
        </div>
      </div>

      {activeDocuments.length === 0 ? (
        <Card className="p-6 text-center">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h4 className="text-lg font-medium text-gray-900 mb-2">No documents added</h4>
          <p className="text-gray-600 mb-4">
            Add documents to complete the application
          </p>
          <Button onClick={addDocument}>
            <Plus className="h-4 w-4 mr-2" />
            Add Document
          </Button>
        </Card>
      ) : (
        <div className="space-y-4">
          {activeDocuments.map((document, index) => {
            // Find the original index in transformedData for proper operations
            const originalIndex = transformedData.findIndex(doc => doc.id === document.id);
            return (
            <Card key={index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">Document {index + 1}</CardTitle>
                  <Button
                    onClick={() => handleDeleteClick(originalIndex)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Title */}
                  <div className="space-y-2">
                    <Label htmlFor={`title-${index}`}>Title</Label>
                    <Input
                      id={`title-${index}`}
                      placeholder="Enter title"
                      value={document.title}
                      onChange={(e) => updateDocument(originalIndex, 'title', e.target.value)}
                    />
                  </div>

                  {/* Type */}
                  <DocumentTreeSelectInput
                    id={`subcategory-${index}`}
                    label="Type"
                    value={document.subcategory}
                    onChange={(value) => updateDocument(originalIndex, 'subcategory', value ?? '')}
                    resourceId={LIST_IDS.DOCUMENT_TREE_RESOURCE}
                    placeholder="Select document type"
                  />
                </div>

                {/* Attachment */}
                <div className="space-y-2">
                  <Label htmlFor={`attachment-${index}`}>Attachment</Label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                    <input
                      id={`attachment-${index}`}
                      type="file"
                      className="hidden"
                      accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
                      onChange={(e) => handleFileUpload(originalIndex, e)}
                    />
                    <label
                      htmlFor={`attachment-${index}`}
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <Upload className="h-8 w-8 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-600 mb-1">
                        Drag & Drop or Select file
                      </p>
                      <p className="text-xs text-gray-500">
                        Drop files here or click{' '}
                        <span className="text-blue-600 underline">browse</span>{' '}
                        through your machine
                      </p>
                    </label>
                    {(document.file || document.attachment || document.fileName) && (
                      <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                        {(() => {
                          // Show filename if available, otherwise show file name
                          if (document.fileName) {
                            return document.fileName;
                          }
                          const file = document.file || document.attachment;
                          return typeof file === 'string' ? file : file?.name;
                        })()}
                      </div>
                    )}
                  </div>
                </div>

                {/* Resume Visibility */}
                {/* <div className="space-y-2">
                  <Label>Resume Visibility</Label>
                  <RadioGroup
                    value={document.resumeVisibility || ''}
                    onValueChange={(value) => updateDocument(index, 'resumeVisibility', value)}
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Add and Make Default" id={`resume-visibility-${index}`} />
                      <Label htmlFor={`resume-visibility-${index}`}>Add and Make Default</Label>
                    </div>
                  </RadioGroup>
                </div> */}

                {/* Comments */}
                <div className="space-y-2">
                  <Label htmlFor={`comments-${index}`}>Comments</Label>
                  <Textarea
                    id={`comments-${index}`}
                    placeholder="Add any comments"
                    value={document.comments ?? ''}
                    onChange={(e) => updateDocument(originalIndex, 'comments', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
            );
          })}

        {activeDocuments.length > 0 && (
          <div className="text-center pt-4">
            <Button onClick={addDocument} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Another Document
            </Button>
          </div>
        )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Document</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this document? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDelete}>No</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Yes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
