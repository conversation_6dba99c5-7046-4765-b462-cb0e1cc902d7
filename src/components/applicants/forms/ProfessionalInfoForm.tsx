import { useEffect, useState, useMemo } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { ProfessionalInfo, Technology } from "@/types";
import {
  StaticApiMultiSelectInput,
  ApiSelectInput,
  ApiMultiSelectInput,
  StaticApiSelectInput,
} from "@/components/applicants/form-fields";
import {
  functionOptions,
  ownershipOptions,
} from "@/utils/applicants";
import { LIST_IDS } from "@/constants/listIds";
import { ChevronDown, ChevronUp, Briefcase, DollarSign, Users, Settings, Award } from "lucide-react";

interface ProfessionalInfoFormProps {
  data: ProfessionalInfo;
  onChange: (data: ProfessionalInfo) => void;
  onValidationChange: (isValid: boolean) => void;
  attemptedSave?: boolean;
}

export default function ProfessionalInfoForm({
  data,
  onChange,
  onValidationChange,
  attemptedSave = false,
}: ProfessionalInfoFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({}); //NoSonar
  const [hasInteracted, setHasInteracted] = useState(false);

  // Ensure data is never undefined
  const safeData = useMemo(() => data ?? ({} as ProfessionalInfo), [data]);

  // State for collapsible sections
  const [openSections, setOpenSections] = useState({
    experience: true,
    compensation: false,
    skills: false,
    preferences: false,
    status: false,
    eeo: false,
  });

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Validate after user interaction OR save attempt
  useEffect(() => {
    const shouldShowValidation = hasInteracted || attemptedSave;
    if (shouldShowValidation) {
      const newErrors: Record<string, string> = {};

      // Add validation rules as needed
      // Currently no required fields in professional info

      setErrors(newErrors);
      const isValid = Object.keys(newErrors).length === 0;
      onValidationChange(isValid);
    }
  }, [safeData, hasInteracted, attemptedSave, onValidationChange]);

  const handleChange = (
    field: keyof ProfessionalInfo,
    value: string | string[]
  ) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    onChange({
      ...safeData,
      [field]: value,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <Briefcase className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Professional Information</h3>
      </div>

      {/* Experience & Basic Info - Always Open */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Briefcase className="h-4 w-4" />
            Experience & Job Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Experience */}
            <div className="space-y-2">
              <Label>Experience</Label>
              <div className="flex flex-col gap-4 lg:flex-col xl:flex-row">
                {/* Years Select + Label */}
                <div className="flex flex-row items-center gap-2">
                  <ApiSelectInput
                    id="experienceYears"
                    label=""
                    value={safeData.experienceYears ?? ""}
                    onChange={(value) => handleChange("experienceYears", value ?? "")}
                    listNameId={LIST_IDS.EXPERIENCE_YEARS}
                    placeholder="Years"
                    className="w-36 lg:w-28"
                  />
                  <span className="text-sm text-black-500 font-bold">Years</span>
                </div>

                {/* Months Select + Label */}
                <div className="flex flex-row items-center gap-2">
                  <ApiSelectInput
                    id="experienceMonths"
                    label=""
                    value={safeData.experienceMonths ?? ""}
                    onChange={(value) => handleChange("experienceMonths", value ?? "")}
                    listNameId={LIST_IDS.EXPERIENCE_MONTHS}
                    placeholder="Months"
                    className="w-36 lg:w-28"
                  />
                  <span className="text-sm text-black-500 font-bold">Months</span>
                </div>
              </div>
            </div>

            {/* Job Title */}
            <div className="space-y-2">
              <Label htmlFor="jobTitle">Job Title</Label>
              <Input
                id="jobTitle"
                placeholder="Enter Job Title"
                value={safeData.jobTitle ?? ""}
                onChange={(e) => handleChange("jobTitle", e.target.value)}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Compensation - Collapsible */}
      <Card className="border-l-4 border-l-green-500">
        <Collapsible open={openSections.compensation} onOpenChange={() => toggleSection('compensation')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Compensation & Terms
                </div>
                {openSections.compensation ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Expected Pay */}
                <div className="space-y-2">
                  <Label>Expected Pay</Label>
                  <div className="flex gap-2">
                    <ApiSelectInput
                      id="expectedPayCurrency"
                      label=""
                      value={safeData.expectedPayCurrency ?? ""}
                      onChange={(value) => handleChange("expectedPayCurrency", value ?? "")}
                      listNameId={LIST_IDS.CURRENCIES}
                      placeholder="INR"
                    />
                    <ApiSelectInput
                      id="expectedPayAmount"
                      label=""
                      value={safeData.expectedPayAmount ?? ""}
                      onChange={(value) => handleChange("expectedPayAmount", value ?? "")}
                      listNameId={LIST_IDS.SALARY_RANGES}
                      placeholder="Select Amount"
                      className="flex-1"
                    />
                  </div>
                </div>

                {/* Current CTC */}
                <div className="space-y-2">
                  <Label>Current CTC</Label>
                  <div className="flex gap-2">
                    <ApiSelectInput
                      id="currentCtcCurrency"
                      label=""
                      value={safeData.currentCtcCurrency ?? ""}
                      onChange={(value) => handleChange("currentCtcCurrency", value ?? "")}
                      listNameId={LIST_IDS.CURRENCIES}
                      placeholder="INR"
                    />
                    <ApiSelectInput
                      id="currentCtcAmount"
                      label=""
                      value={safeData.currentCtcAmount ?? ""}
                      onChange={(value) => handleChange("currentCtcAmount", value ?? "")}
                      listNameId={LIST_IDS.SALARY_RANGES}
                      placeholder="Select Amount"
                      className="flex-1"
                    />
                  </div>
                </div>

                {/* Tax Terms */}
                <ApiSelectInput
                  id="taxTerms"
                  label="Tax Terms"
                  value={safeData.taxTerms ?? ""}
                  onChange={(value) => handleChange("taxTerms", value ?? "")}
                  listNameId={LIST_IDS.TAX_TERMS}
                  placeholder="Select Tax Terms"
                />

                {/* Notice Period */}
                <ApiSelectInput
                  id="noticePeriod"
                  label="Notice Period"
                  value={safeData.noticePeriod ?? ""}
                  onChange={(value) => handleChange("noticePeriod", value ?? "")}
                  listNameId={LIST_IDS.NOTICE_PERIODS}
                  placeholder="Select Notice Period"
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Skills & Technology - Collapsible */}
      <Card className="border-l-4 border-l-purple-500">
        <Collapsible open={openSections.skills} onOpenChange={() => toggleSection('skills')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  Skills & Technology
                </div>
                {openSections.skills ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Skills */}
                <ApiMultiSelectInput
                  id="skills"
                  label="Skills"
                  value={safeData.skills ?? []}
                  onChange={(value) => handleChange("skills", value)}
                  listNameId={LIST_IDS.SKILLS}
                  placeholder="Select skills"
                  description="Select technologies and skills you have experience with"
                />

                {/* Primary Skills */}
                <ApiMultiSelectInput
                  id="primarySkills"
                  label="Primary Skills"
                  value={safeData.primarySkills ?? []}
                  onChange={(value) => handleChange("primarySkills", value)}
                  listNameId={LIST_IDS.SKILLS}
                  placeholder="Select primary skills"
                  description="Select your top 3-5 most important skills"
                  maxSelections={5}
                />

                {/* Technology */}
                <ApiSelectInput
                  id="technology"
                  label="Technology"
                  value={safeData.technology ?? ""}
                  onChange={(value) =>
                    handleChange("technology", value as Technology)
                  }
                  listNameId={LIST_IDS.TECHNOLOGIES}
                  placeholder="Select primary technology"
                />

                {/* Industry */}
                <StaticApiMultiSelectInput
                  id="industry"
                  label="Industry"
                  value={safeData.industry ?? []}
                  onChange={(value) => handleChange("industry", value)}
                  options={functionOptions}
                  placeholder="Select industries"
                />

                {/* Function */}
                <StaticApiMultiSelectInput
                  id="function"
                  label="Function"
                  value={safeData.function ?? []}
                  onChange={(value) => handleChange("function", value)}
                  options={functionOptions}
                  placeholder="Select functions"
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Preferences - Collapsible */}
      <Card className="border-l-4 border-l-orange-500">
        <Collapsible open={openSections.preferences} onOpenChange={() => toggleSection('preferences')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Preferences & Details
                </div>
                {openSections.preferences ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Current Company */}
                <div className="space-y-2">
                  <Label htmlFor="currentCompany">Current Company</Label>
                  <Input
                    id="currentCompany"
                    placeholder="Enter Current Company"
                    value={safeData.currentCompany ?? ""}
                    onChange={(e) => handleChange("currentCompany", e.target.value)}
                  />
                </div>

                {/* Source */}
                <ApiSelectInput
                  id="source"
                  label="Source"
                  value={safeData.source ?? ""}
                  onChange={(value) => handleChange("source", value ?? "")}
                  listNameId={LIST_IDS.APPLICATION_SOURCES}
                  placeholder="Select Source"
                />

                {/* Referred By */}
                <div className="space-y-2">
                  <Label htmlFor="referredBy">Referred By</Label>
                  <Input
                    id="referredBy"
                    placeholder="Enter Referred By"
                    value={safeData.referredBy ?? ""}
                    onChange={(e) => handleChange("referredBy", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Status & Management - Collapsible */}
      <Card className="border-l-4 border-l-red-500">
        <Collapsible open={openSections.status} onOpenChange={() => toggleSection('status')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Status & Management
                </div>
                {openSections.status ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Applicant Status */}
                <ApiSelectInput
                  id="applicantStatus"
                  label="Applicant Status"
                  value={safeData.applicantStatus ?? ""}
                  onChange={(value) => handleChange("applicantStatus", value ?? "")}
                  listNameId={LIST_IDS.APPLICANT_STATUS}
                  placeholder="Select Status"
                />

              <StaticApiSelectInput
                id="ownership"
                label="OwnerShip"
                required={false}
                value={safeData.ownership ?? ""}
                onChange={(value) => handleChange("ownership", value ?? "")}
                options={ownershipOptions}
                placeholder="Required"
                error={errors.vendorContact}
              />

                {/* Relocation */}
                <div className="space-y-2">
                  <Label>Relocation</Label>
                  <RadioGroup
                    value={safeData.relocation ?? ""}
                    onValueChange={(value) =>
                      handleChange("relocation", value as "Yes" | "No")
                    }
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Yes" id="relocation-yes" />
                      <Label htmlFor="relocation-yes">Yes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="No" id="relocation-no" />
                      <Label htmlFor="relocation-no">No</Label>
                    </div>
                  </RadioGroup>
                </div>

                {/* PAN Card Number */}
                <div className="space-y-2">
                  <Label htmlFor="panCardNumber">PAN Card Number</Label>
                  <Input
                    id="panCardNumber"
                    placeholder="Enter PAN Card Number"
                    value={safeData.panCardNumber ?? ""}
                    onChange={(e) => handleChange("panCardNumber", e.target.value)}
                  />
                </div>

                {/* Aadhar Number */}
                <div className="space-y-2">
                  <Label htmlFor="aadharNumber">Aadhar Number</Label>
                  <Input
                    id="aadharNumber"
                    placeholder="Enter Aadhar Number"
                    value={safeData.aadharNumber ?? ""}
                    onChange={(e) => handleChange("aadharNumber", e.target.value)}
                  />
                </div>

                {/* GPA */}
                <div className="space-y-2">
                  <Label htmlFor="gpa">GPA</Label>
                  <Input
                    id="gpa"
                    placeholder="Enter GPA"
                    value={safeData.gpa ?? ""}
                    onChange={(e) => handleChange("gpa", e.target.value)}
                  />
                </div>

                {/* Additional Comments */}
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="additionalComments">Additional Comments</Label>
                  <Textarea
                    id="additionalComments"
                    placeholder="Enter Additional Comments"
                    value={safeData.additionalComments ?? ""}
                    onChange={(e) =>
                      handleChange("additionalComments", e.target.value)
                    }
                    rows={3}
                  />
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* EEO Information - Collapsible */}
      <Card className="border-l-4 border-l-purple-500">
        <Collapsible open={openSections.eeo} onOpenChange={() => toggleSection('eeo')}>
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Award className="h-4 w-4" />
                  EEO Information
                </div>
                {openSections.eeo ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Gender */}
                <ApiSelectInput
                  id="gender"
                  label="Gender"
                  value={safeData.gender ?? ""}
                  onChange={(value) => handleChange("gender", value ?? "")}
                  listNameId={LIST_IDS.GENDERS}
                  placeholder="Select Gender"
                />

                {/* Race/Ethnicity */}
                <ApiSelectInput
                  id="raceEthnicity"
                  label="Race/Ethnicity"
                  value={safeData.raceEthnicity ?? ""}
                  onChange={(value) => handleChange("raceEthnicity", value ?? "")}
                  listNameId={LIST_IDS.RACE_ETHNICITIES}
                  placeholder="Select Race/Ethnicity"
                />

                {/* Veteran Status */}
                <ApiSelectInput
                  id="veteranStatus"
                  label="Veteran Status"
                  value={safeData.veteranStatus ?? ""}
                  onChange={(value) => handleChange("veteranStatus", value ?? "")}
                  listNameId={LIST_IDS.VETERAN_STATUS}
                  placeholder="Select Veteran Status"
                />

                {/* Veteran Type */}
                <ApiSelectInput
                  id="veteranType"
                  label="Veteran Type"
                  value={safeData.veteranType ?? ""}
                  onChange={(value) => handleChange("veteranType", value ?? "")}
                  listNameId={LIST_IDS.VETERAN_TYPES}
                  placeholder="Select Veteran Type"
                />

                {/* Disability */}
                <ApiSelectInput
                  id="disability"
                  label="Disability"
                  value={safeData.disability ?? ""}
                  onChange={(value) => handleChange("disability", value ?? "")}
                  listNameId={LIST_IDS.DISABILITIES}
                  placeholder="Select Disability"
                />
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}
