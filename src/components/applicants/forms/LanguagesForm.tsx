import { useEffect, useState } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { LanguageDetails } from '@/types';
import { Plus, X, Languages } from 'lucide-react';

interface LanguagesFormProps {
  data: LanguageDetails[];
  onChange: (data: LanguageDetails[]) => void;
  onValidationChange: (isValid: boolean) => void;
}

const availableLanguages = [
  'English',
  'Spanish',
  'French',
  'German',
  'Italian',
  'Portuguese',
  'Russian',
  'Chinese (Mandarin)',
  'Japanese',
  'Korean',
  'Arabic',
  'Hindi',
  'Bengali',
  'Telugu',
  'Tamil',
  'Gujarati',
  'Marathi',
  'Kannada',
  'Malayalam',
  'Punjabi',
  'Other'
];

export default function LanguagesForm({
  data,
  onChange,
  onValidationChange,
}: LanguagesFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = useState(false);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    data.forEach((lang, index) => {
      if (!lang.language?.trim()) {
        newErrors[`language-${index}`] = 'Language is required';
      }
      if (!lang.proficiency.read && !lang.proficiency.speak && !lang.proficiency.write) {
        newErrors[`proficiency-${index}`] = 'At least one proficiency level is required';
      }
    });

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    if (hasInteracted) {
      validateForm();
    }
  }, [data, hasInteracted]);

  const addLanguage = () => {
    const newLanguage: LanguageDetails = {
      id: Date.now().toString(),
      language: '',
      proficiency: {
        read: false,
        speak: false,
        write: false,
      },
    };
    onChange([...data, newLanguage]);
  };

  const updateLanguage = (index: number, field: keyof LanguageDetails, value: string) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    const updatedLanguages = data.map((lang, i) =>
      i === index ? { ...lang, [field]: value } : lang
    );
    onChange(updatedLanguages);
  };

  const updateProficiency = (index: number, skill: keyof LanguageDetails['proficiency'], checked: boolean) => {
    const updatedLanguages = data.map((lang, i) => 
      i === index 
        ? { 
            ...lang, 
            proficiency: { 
              ...lang.proficiency, 
              [skill]: checked 
            } 
          } 
        : lang
    );
    onChange(updatedLanguages);
  };

  const removeLanguage = (index: number) => {
    const updatedLanguages = data.filter((_, i) => i !== index);
    onChange(updatedLanguages);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Languages</h3>
        <Button onClick={addLanguage} variant="outline" size="sm">
          <Plus className="h-4 w-4 mr-2" />
          Add
        </Button>
      </div>

      {data.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Languages className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">No languages added yet</p>
            <Button onClick={addLanguage} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Language
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {data.map((language, index) => (
            <Card key={language.id || index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">Language {index + 1}</CardTitle>
                  <Button
                    onClick={() => removeLanguage(index)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Language Selection */}
                <div className="space-y-2">
                  <Label htmlFor={`language-${index}`}>
                    Language <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={language.language}
                    onValueChange={(value) => updateLanguage(index, 'language', value)}
                  >
                    <SelectTrigger className={errors[`language-${index}`] ? 'border-red-500' : ''}>
                      <SelectValue placeholder="Select Language" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableLanguages.map((lang) => (
                        <SelectItem key={lang} value={lang}>
                          {lang}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors[`language-${index}`] && (
                    <p className="text-sm text-red-500">{errors[`language-${index}`]}</p>
                  )}
                </div>

                {/* Proficiency */}
                <div className="space-y-2">
                  <Label>
                    Proficiency <span className="text-red-500">*</span>
                  </Label>
                  <div className="flex gap-6">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`read-${index}`}
                        checked={language.proficiency.read}
                        onCheckedChange={(checked) => 
                          updateProficiency(index, 'read', checked as boolean)
                        }
                      />
                      <Label htmlFor={`read-${index}`}>Read</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`speak-${index}`}
                        checked={language.proficiency.speak}
                        onCheckedChange={(checked) => 
                          updateProficiency(index, 'speak', checked as boolean)
                        }
                      />
                      <Label htmlFor={`speak-${index}`}>Speak</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`write-${index}`}
                        checked={language.proficiency.write}
                        onCheckedChange={(checked) => 
                          updateProficiency(index, 'write', checked as boolean)
                        }
                      />
                      <Label htmlFor={`write-${index}`}>Write</Label>
                    </div>
                  </div>
                  {errors[`proficiency-${index}`] && (
                    <p className="text-sm text-red-500">{errors[`proficiency-${index}`]}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
