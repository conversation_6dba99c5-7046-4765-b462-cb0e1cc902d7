import { useEffect, useMemo } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { PersonalInfo, CourtesyTitle } from "@/types";
import { SelectInput, PhoneInput, TextAreaInput } from "../form-fields";
import { FormSection } from "../ui";
import { useFormInteraction, commonValidationRules, useFormValidation } from "../hooks";
import { courtesyTitleOptions, workAuthorizationOptions, countryOptions } from "@/utils/applicants";
import { User } from "lucide-react";
import type { ValidationRules, ValidationRule } from "../hooks";
import { DateInput, TextInput } from "../form-fields";

interface PersonalInfoFormProps {
  data: PersonalInfo;
  onChange: (data: PersonalInfo) => void;
  onValidationChange: (isValid: boolean) => void;
}

/**
 * Refactored PersonalInfoForm using reusable components
 * Demonstrates the new pattern for all form components
 */
export default function PersonalInfoFormRefactored({
  data,
  onChange,
  onValidationChange,
}: PersonalInfoFormProps) {
  // Ensure data is never undefined
  const safeData = useMemo(() => data ?? ({} as PersonalInfo), [data]);

  // Form interaction tracking
  const { hasInteracted, markAsInteracted } = useFormInteraction();

  // Validation rules
  const validationRules: ValidationRules = useMemo(() => ({
    firstName: commonValidationRules.required as unknown as ValidationRule,
    lastName: commonValidationRules.required as unknown as ValidationRule,
    email: commonValidationRules.email as unknown as ValidationRule,
    homePhone: commonValidationRules.phone as unknown as ValidationRule,
    mobilePhone: commonValidationRules.phone as unknown as ValidationRule,
  }), []);

  // Form validation
  const { errors, validateAll } = useFormValidation(validationRules, hasInteracted);

  // Validate and notify parent when data changes
  useEffect(() => {
    if (hasInteracted) {
      const isValid = validateAll(safeData as unknown as Record<string, unknown>);
      onValidationChange(isValid);
    }
  }, [safeData, hasInteracted, validateAll, onValidationChange]);

  const handleChange = (field: keyof PersonalInfo, value: string | boolean) => {
    markAsInteracted();
    onChange({
      ...safeData,
      [field]: value,
    });
  };

  const handlePhoneChange = (field: keyof PersonalInfo, value: string, countryCode: string) => {
    markAsInteracted();
    onChange({
      ...safeData,
      [field]: value,
      [`${field}CountryCode`]: countryCode, // Store country code separately if needed
    });
  };

  return (
    <FormSection title="Personal Information" icon={User}>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
        {/* Courtesy Title + First Name in one row */}
        <div className="flex gap-4">
          {/* Courtesy Title - 20% */}
          <div className="flex-1 basis-1/5">
            <SelectInput
              id="courtesyTitle"
              label="Prefix"
              value={safeData.courtesyTitle ?? ""}
              onChange={(value: string | undefined) => handleChange("courtesyTitle", (value ?? "") as CourtesyTitle)}
              options={courtesyTitleOptions}
              placeholder="Select"
            />
          </div>

          {/* First Name - 80% */}
          <div className="flex-1 basis-4/5">
            <TextInput
              id="firstName"
              label="First Name"
              required
              value={safeData.firstName ?? ""}
              onChange={(value) => handleChange("firstName", value)}
              placeholder="Enter First Name"
              error={errors.firstName}
            />
          </div>
        </div>

        {/* Middle Name */}
        <TextInput
          id="middleName"
          label="Middle Name"
          value={safeData.middleName ?? ""}
          onChange={(value) => handleChange("middleName", value)}
          placeholder="Enter Middle Name"
        />

        {/* Last Name */}
        <TextInput
          id="lastName"
          label="Last Name"
          required
          value={safeData.lastName ?? ""}
          onChange={(value) => handleChange("lastName", value)}
          placeholder="Enter Last Name"
          error={errors.lastName}
        />

        {/* Nick Name */}
        <TextInput
          id="nickName"
          label="Nick Name"
          value={safeData.nickName ?? ""}
          onChange={(value) => handleChange("nickName", value)}
          placeholder="Enter Nick Name"
        />

        {/* Email */}
        <TextInput
          id="email"
          label="Email"
          type="email"
          required
          value={safeData.email ?? ""}
          onChange={(value) => handleChange("email", value)}
          placeholder="Enter Email"
          error={errors.email}
        />

        {/* Alternate Email */}
        <TextInput
          id="alternateEmail"
          label="Alternate Email"
          type="email"
          value={safeData.alternateEmail ?? ""}
          onChange={(value) => handleChange("alternateEmail", value)}
          placeholder="Enter Alternate Email"
        />

        {/* Home Phone */}
        <PhoneInput
          id="homePhone"
          label="Home Phone Number"
          value={safeData.homePhone ?? ""}
          countryCode="+91" // Could be stored in data
          onChange={(value: string) => handlePhoneChange("homePhone", value, "+91")}
          onCountryCodeChange={(code: string) => {
            // Handle country code change
            handleChange("homePhoneCountryCode" as keyof PersonalInfo, code);
          }}
          error={errors.homePhone}
        />

        {/* Mobile Phone */}
        <PhoneInput
          id="mobilePhone"
          label="Mobile Phone Number"
          value={safeData.mobilePhone ?? ""}
          countryCode="+91"
          onChange={(value: string) => handlePhoneChange("mobilePhone", value, "+91")}
          onCountryCodeChange={(code: string) => {
            handleChange("mobilePhoneCountryCode" as keyof PersonalInfo, code);
          }}
          error={errors.mobilePhone}
        />

        {/* Date of Birth */}
        <DateInput
          id="dateOfBirth"
          label="Date of Birth"
          value={safeData.dateOfBirth ?? ""}
          onChange={(value: string) => handleChange("dateOfBirth", value)}
        />

        {/* SSN */}
        <TextInput
          id="ssn"
          label="SSN"
          value={safeData.ssn ?? ""}
          onChange={(value) => handleChange("ssn", value)}
          placeholder="Enter SSN"
          maxLength={11}
        />

        {/* Work Authorization */}
        <SelectInput
          id="workAuthorization"
          label="Work Authorization"
          value={safeData.workAuthorization ?? ""}
          onChange={(value: string | undefined) => handleChange("workAuthorization", value ?? "")}
          options={workAuthorizationOptions}
          placeholder="Select Work Authorization"
        />

        {/* Country */}
        <SelectInput
          id="country"
          label="Country"
          value={safeData.country ?? ""}
          onChange={(value: string | undefined) => handleChange("country", value ?? "")}
          options={countryOptions}
          placeholder="Select Country"
        />

        {/* Address - Full Width */}
        <div className="md:col-span-2">
          <TextAreaInput
            id="address"
            label="Address"
            value={safeData.address ?? ""}
            onChange={(value) => handleChange("address", value)}
            placeholder="Enter Address"
            rows={3}
          />
        </div>

        {/* City */}
        <TextInput
          id="city"
          label="City"
          value={safeData.city ?? ""}
          onChange={(value) => handleChange("city", value)}
          placeholder="Enter City"
        />

        {/* State */}
        <TextInput
          id="state"
          label="State"
          value={safeData.state ?? ""}
          onChange={(value) => handleChange("state", value)}
          placeholder="Enter State"
        />

        {/* Zip Code */}
        <TextInput
          id="zipCode"
          label="Zip Code"
          value={safeData.zipCode ?? ""}
          onChange={(value) => handleChange("zipCode", value)}
          placeholder="Enter Zip Code"
        />

        {/* LinkedIn Profile */}
        <TextInput
          id="linkedinProfileUrl"
          label="LinkedIn Profile"
          type="url"
          value={safeData.linkedinProfileUrl ?? ""}
          onChange={(value) => handleChange("linkedinProfileUrl", value)}
          placeholder="https://linkedin.com/in/username"
        />

        {/* Clearance - Radio Group */}
        <fieldset className="space-y-2">
          <legend className="text-sm font-medium">Security Clearance</legend>
          <RadioGroup
            value={safeData.clearance ?? "No"}
            onValueChange={(value) => handleChange("clearance", value as "Yes" | "No")}
            className="flex gap-6"
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="Yes" id="clearance-yes" />
              <label htmlFor="clearance-yes" className="text-sm">Yes</label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="No" id="clearance-no" />
              <label htmlFor="clearance-no" className="text-sm">No</label>
            </div>
          </RadioGroup>
        </fieldset>
      </div>
    </FormSection>
  );
}
