import React, { useEffect, useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { WorkExperience } from '@/types';
import { Plus, X, Briefcase } from 'lucide-react';
import { ApiSelectInput } from '@/components/applicants/form-fields';
import { LIST_IDS } from '@/constants/listIds';
import { generateUUID } from '@/lib/utils';

interface WorkExperienceFormProps {
  data: WorkExperience[];
  onChange: (data: WorkExperience[]) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function WorkExperienceForm({
  data,
  onChange,
  onValidationChange,
}: WorkExperienceFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [experienceToDelete, setExperienceToDelete] = useState<number | null>(null);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Only validate active work experience entries
    const activeExperiences = data.filter(exp => exp.isActive !== false);
    activeExperiences.forEach((exp, index) => {
      if (!exp.companyName?.trim()) {
        newErrors[`companyName-${index}`] = 'Company name is required';
      }
      if (!exp.jobTitle?.trim()) {
        newErrors[`jobTitle-${index}`] = 'Job title is required';
      }
      if (!exp.startDate) {
        newErrors[`startDate-${index}`] = 'Start date is required';
      }
    });

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    if (hasInteracted) {
      validateForm();
    }
  }, [data, hasInteracted]);

  const addWorkExperience = () => {
    const newExperience: WorkExperience = {
      id: generateUUID(),
      companyName: '',
      jobTitle: '',
      startDate: '',
      endDate: '',
      responsibilities: '',
      location: '',
      employeeType: '',
      isActive: true,
    };
    onChange([...data, newExperience]);
  };

  const updateWorkExperience = (index: number, field: keyof WorkExperience, value: string) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    const updatedExperiences = data.map((exp, i) =>
      i === index ? { ...exp, [field]: value } : exp
    );
    onChange(updatedExperiences);
  };

  const handleDeleteClick = (index: number) => {
    setExperienceToDelete(index);
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (experienceToDelete !== null) {
      const updatedExperiences = data.map((exp, i) => 
        i === experienceToDelete ? { ...exp, isActive: false } : exp
      );
      onChange(updatedExperiences);
    }
    setShowDeleteDialog(false);
    setExperienceToDelete(null);
  };

  const cancelDelete = () => {
    setShowDeleteDialog(false);
    setExperienceToDelete(null);
  };

  // Filter to show only active work experience entries
  const activeExperiences = data.filter(exp => exp.isActive !== false);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Work Experience Details</h3>
      </div>

      {activeExperiences.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Briefcase className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">No work experience added yet</p>
            <Button onClick={addWorkExperience} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Work Experience
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {activeExperiences.map((experience, index) => {
            // Find the original index in data array for proper operations
            const originalIndex = data.findIndex(exp => exp.id === experience.id);
            return (
            <Card key={experience.id ?? index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">Work Experience {index + 1}</CardTitle>
                  <Button
                    onClick={() => handleDeleteClick(originalIndex)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
                  {/* Company Name */}
                  <div className="space-y-2">
                    <Label htmlFor={`companyName-${index}`}>
                      Company Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`companyName-${index}`}
                      placeholder="Enter Company Name"
                      value={experience.companyName}
                      onChange={(e) => updateWorkExperience(originalIndex, 'companyName', e.target.value)}
                      className={errors[`companyName-${index}`] ? 'border-red-500' : ''}
                    />
                    {errors[`companyName-${index}`] && (
                      <p className="text-sm text-red-500">{errors[`companyName-${index}`]}</p>
                    )}
                  </div>

                  {/* Job Title */}
                  <div className="space-y-2">
                    <Label htmlFor={`jobTitle-${index}`}>
                      Job Title <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`jobTitle-${index}`}
                      placeholder="Enter Job Title"
                      value={experience.jobTitle}
                      onChange={(e) => updateWorkExperience(originalIndex, 'jobTitle', e.target.value)}
                      className={errors[`jobTitle-${index}`] ? 'border-red-500' : ''}
                    />
                    {errors[`jobTitle-${index}`] && (
                      <p className="text-sm text-red-500">{errors[`jobTitle-${index}`]}</p>
                    )}
                  </div>

                  {/* Start Date */}
                  <div className="space-y-2">
                    <Label htmlFor={`startDate-${index}`}>
                      Start Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`startDate-${index}`}
                      type="date"
                      value={experience.startDate}
                      onChange={(e) => updateWorkExperience(originalIndex, 'startDate', e.target.value)}
                      className={errors[`startDate-${index}`] ? 'border-red-500' : ''}
                    />
                    {errors[`startDate-${index}`] && (
                      <p className="text-sm text-red-500">{errors[`startDate-${index}`]}</p>
                    )}
                  </div>

                  {/* End Date */}
                  <div className="space-y-2">
                    <Label htmlFor={`endDate-${index}`}>End Date</Label>
                    <Input
                      id={`endDate-${index}`}
                      type="date"
                      value={experience.endDate ?? ''}
                      onChange={(e) => updateWorkExperience(originalIndex, 'endDate', e.target.value)}
                    />
                  </div>

                  {/* Location */}
                  <div className="space-y-2">
                    <Label htmlFor={`location-${index}`}>Location</Label>
                    <Input
                      id={`location-${index}`}
                      placeholder="Enter Work Location"
                      value={experience.location ?? ''}
                      onChange={(e) => updateWorkExperience(originalIndex, 'location', e.target.value)}
                    />
                  </div>

                  {/* Employee Type */}
                  <ApiSelectInput
                    id={`employeeType-${index}`}
                    label="Employee Type"
                    value={experience.employeeType ?? ''}
                    onChange={(value) => updateWorkExperience(originalIndex, 'employeeType', value ?? '')}
                    listNameId={LIST_IDS.EMPLOYEE_TYPES}
                    placeholder="Select Employee Type"
                  />
                </div>

                {/* Responsibilities / Roles */}
                <div className="space-y-2">
                  <Label htmlFor={`responsibilities-${index}`}>Responsibilities / Roles</Label>
                  <Textarea
                    id={`responsibilities-${index}`}
                    placeholder="Describe your roles and responsibilities"
                    value={experience.responsibilities}
                    onChange={(e) => updateWorkExperience(originalIndex, 'responsibilities', e.target.value)}
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>
            );
          })}

        {activeExperiences.length > 0 && (
          <div className="text-center pt-4">
            <Button onClick={addWorkExperience} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Another Experience
            </Button>
          </div>
        )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Work Experience</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this work experience record? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDelete}>No</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Yes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
