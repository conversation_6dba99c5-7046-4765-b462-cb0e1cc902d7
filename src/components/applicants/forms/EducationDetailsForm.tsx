import { ApiSelectInput, DependentApiSelectInput } from "@/components/applicants/form-fields";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { EducationDetails, PersonalInfo } from "@/types";
import { LIST_IDS } from "@/constants/listIds";
import { GraduationCap, Plus, X } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { generateUUID } from "@/lib/utils";

interface EducationDetailsFormProps {
  data: EducationDetails[];
  onChange: (data: EducationDetails[]) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function EducationDetailsForm({
  data,
  onChange,
  onValidationChange,
}: EducationDetailsFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [educationToDelete, setEducationToDelete] = useState<number | null>(null);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Only validate active education entries
    const activeEducation = data.filter(edu => edu.isActive !== false);
    activeEducation.forEach((edu, index) => {
      if (!edu.schoolName?.trim()) {
        newErrors[`schoolName-${index}`] = "School name is required";
      }
      if (!edu.degree?.trim()) {
        newErrors[`degree-${index}`] = "Degree is required";
      }
    });

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    if (hasInteracted) {
      validateForm();
    }
  }, [data, hasInteracted]);

  const addEducation = () => {
    const newEducation: EducationDetails = {
      id: generateUUID(),
      schoolName: "",
      degree: "",
      yearCompleted: "",
      majorStudy: "",
      minorStudy: "",
      gpa: "",
      country: "",
      state: "",
      city: "",
      higherEducation: false,
      isActive: true,
    };
    onChange([...data, newEducation]);
  };

  const updateEducation = (
    index: number,
    field: keyof EducationDetails,
    value: string | boolean
  ) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    const updatedEducation = data.map((edu, i) =>
      i === index ? { ...edu, [field]: value } : edu
    );
    onChange(updatedEducation);
  };

  const handleDeleteClick = (index: number) => {
    setEducationToDelete(index);
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (educationToDelete !== null) {
      const updatedEducation = data.map((edu, i) => 
        i === educationToDelete ? { ...edu, isActive: false } : edu
      );
      onChange(updatedEducation);
    }
    setShowDeleteDialog(false);
    setEducationToDelete(null);
  };

  const cancelDelete = () => {
    setShowDeleteDialog(false);
    setEducationToDelete(null);
  };

  // Filter to show only active education entries
  const activeEducation = data.filter(edu => edu.isActive !== false);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Education Details</h3>
      </div>

      {activeEducation.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <GraduationCap className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">No education details added yet</p>
            <Button onClick={addEducation} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Education
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {activeEducation.map((education, index) => {
            // Find the original index in data array for proper operations
            const originalIndex = data.findIndex(edu => edu.id === education.id);
            return (
            <Card key={education.id ?? index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">
                    Education {index + 1}
                  </CardTitle>
                  <Button
                    onClick={() => handleDeleteClick(originalIndex)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* School Name */}
                  <div className="space-y-2">
                    <Label htmlFor={`schoolName-${index}`}>
                      School Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id={`schoolName-${index}`}
                      placeholder="Enter School Name"
                      value={education.schoolName}
                      onChange={(e) =>
                        updateEducation(originalIndex, "schoolName", e.target.value)
                      }
                      className={
                        errors[`schoolName-${index}`] ? "border-red-500" : ""
                      }
                    />
                    {errors[`schoolName-${index}`] && (
                      <p className="text-sm text-red-500">
                        {errors[`schoolName-${index}`]}
                      </p>
                    )}
                  </div>

                  {/* Degree */}
                  <ApiSelectInput
                    id={`degree-${index}`}
                    label="Degree"
                    required={true}
                    value={education.degree}
                    onChange={(value) => updateEducation(originalIndex, "degree", value ?? "")}
                    listNameId={LIST_IDS.DEGREES}
                    placeholder="Select Degree"
                    error={errors[`degree-${index}`]}
                  />

                  {/* Year Completed */}
                  <div className="space-y-2">
                    <Label htmlFor={`yearCompleted-${index}`}>
                      Year Completed
                    </Label>
                    <Input
                      id={`yearCompleted-${index}`}
                      placeholder="Enter Year Completed"
                      value={education.yearCompleted ?? ""}
                      onChange={(e) =>
                        updateEducation(originalIndex, "yearCompleted", e.target.value)
                      }
                    />
                  </div>

                  {/* Minor Study */}
                  <div className="space-y-2">
                    <Label htmlFor={`minorStudy-${index}`}>Minor Study</Label>
                    <Input
                      id={`minorStudy-${index}`}
                      placeholder="Enter Minor Study"
                      value={education.minorStudy ?? ""}
                      onChange={(e) =>
                        updateEducation(originalIndex, "minorStudy", e.target.value)
                      }
                    />
                  </div>

                  {/* Major Study */}
                  <div className="space-y-2">
                    <Label htmlFor={`majorStudy-${index}`}>Major Study</Label>
                    <Input
                      id={`majorStudy-${index}`}
                      placeholder="Enter Major Study"
                      value={education.majorStudy ?? ""}
                      onChange={(e) =>
                        updateEducation(originalIndex, "majorStudy", e.target.value)
                      }
                    />
                  </div>

                  {/* GPA */}
                  <div className="space-y-2">
                    <Label htmlFor={`gpa-${index}`}>GPA</Label>
                    <Input
                      id={`gpa-${index}`}
                      placeholder="Enter GPA"
                      value={education.gpa ?? ""}
                      onChange={(e) =>
                        updateEducation(originalIndex, "gpa", e.target.value)
                      }
                    />
                  </div>

                  {/* Country */}
                  <ApiSelectInput
                    id={`country-${index}`}
                    label="Country"
                    value={education.country ?? ""}
                    onChange={(value) => {
                      updateEducation(originalIndex, "country", value ?? "");
                      // Clear state when country changes
                      if (education.state) {
                        updateEducation(originalIndex, "state", "");
                      }
                    }}
                    listNameId={LIST_IDS.COUNTRIES}
                    placeholder="Select Country"
                  />

                  {/* State */}
                  <DependentApiSelectInput
                    id={`state-${index}`}
                    label="State"
                    value={education.state ?? ""}
                    onChange={(value) => updateEducation(originalIndex, "state", value ?? "")}
                    parentValue={education.country}
                    placeholder="Select State"
                    emptyParentMessage="Please select country first"
                  />

                  {/* City */}
                  <div className="space-y-2">
                    <Label htmlFor={`city-${index}`}>City</Label>
                    <Input
                      id={`city-${index}`}
                      placeholder="Enter City"
                      value={education.city ?? ""}
                      onChange={(e) =>
                        updateEducation(originalIndex, "city", e.target.value)
                      }
                    />
                  </div>

                  {/* Higher Education */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id={`higherEducation-${index}`}
                        checked={education.higherEducation ?? false}
                        onCheckedChange={(checked) =>
                          updateEducation(originalIndex, "higherEducation", checked)
                        }
                      />
                      <Label htmlFor={`higherEducation-${index}`}>
                        Higher Education
                      </Label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            );
          })}

        {activeEducation.length > 0 && (
          <div className="text-center pt-4">
            <Button onClick={addEducation} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Another Education
            </Button>
          </div>
        )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Education</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this education record? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDelete}>No</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Yes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
