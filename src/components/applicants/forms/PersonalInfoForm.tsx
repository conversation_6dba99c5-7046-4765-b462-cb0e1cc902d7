import { useEffect, useMemo, useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { PersonalInfo } from "@/types";
import {
  TextInput,
  ApiSelectInput,
  DependentApiSelectInput,
} from "@/components/applicants/form-fields";
import {
  useFormValidation,
  useFormInteraction,
  commonValidationRules,
} from "@/components/applicants/hooks";
import { LIST_IDS } from "@/constants/listIds";
import {
  ChevronDown,
  ChevronUp,
  User,
  Phone,
  MapPin,
  Globe,
  Shield,
} from "lucide-react";

interface PersonalInfoFormProps {
  data: PersonalInfo;
  onChange: (data: PersonalInfo) => void;
  onValidationChange: (isValid: boolean) => void;
  attemptedSave?: boolean;
}

export default function PersonalInfoForm({
  data,
  onChange,
  onValidationChange,
  attemptedSave = false,
}: PersonalInfoFormProps) {
  // Ensure data is never undefined
  const safeData = useMemo(() => data ?? ({} as PersonalInfo), [data]);

  // Form interaction tracking
  const { hasInteracted, markAsInteracted } = useFormInteraction();

  // Validation rules
  const validationRules = useMemo(
    () => ({
      firstName: commonValidationRules.required,
      lastName: commonValidationRules.required,
      email: {
        required: true,
        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        custom: (value: unknown) => {
          if (
            value &&
            typeof value === "string" &&
            !/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(value)
          ) {
            return "Please enter a valid email address";
          }
          return null;
        },
      },
    }),
    []
  );

  // Form validation - show validation if user has interacted OR save was attempted
  const shouldShowValidation = hasInteracted || attemptedSave;
  const { errors, validateAll } = useFormValidation(
    validationRules,
    shouldShowValidation
  );

  // Validate and notify parent when data changes or save is attempted
  useEffect(() => {
    if (shouldShowValidation) {
      const isValid = validateAll(
        safeData as unknown as Record<string, unknown>
      );
      onValidationChange(isValid);
    }
  }, [safeData, shouldShowValidation, validateAll, onValidationChange]);

  const handleChange = (field: keyof PersonalInfo, value: string) => {
    markAsInteracted();
    onChange({
      ...safeData,
      [field]: value,
    });
  };

  // State for collapsible sections
  const [openSections, setOpenSections] = useState({
    basic: true,
    contact: false,
    address: false,
    personal: false,
    social: false,
    work: false,
  });

  const toggleSection = (section: keyof typeof openSections) => {
    setOpenSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-6">
        <User className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">
          Personal Information
        </h3>
      </div>

      {/* Basic Information - Always Open */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <User className="h-4 w-4" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* First Name */}
            <TextInput
              id="firstName"
              label="First Name"
              required
              value={safeData.firstName ?? ""}
              onChange={(value) => handleChange("firstName", value)}
              placeholder="Enter First Name"
              error={errors.firstName}
            />

            {/* Last Name */}
            <TextInput
              id="lastName"
              label="Last Name"
              required
              value={safeData.lastName ?? ""}
              onChange={(value) => handleChange("lastName", value)}
              placeholder="Enter Last Name"
              error={errors.lastName}
            />

            {/* Middle Name */}
            <TextInput
              id="middleName"
              label="Middle Name"
              value={safeData.middleName ?? ""}
              onChange={(value) => handleChange("middleName", value)}
              placeholder="Enter Middle Name"
            />

            {/* Nick Name */}
            <TextInput
              id="nickName"
              label="Nick Name"
              value={safeData.nickName ?? ""}
              onChange={(value) => handleChange("nickName", value)}
              placeholder="Enter Nick Name"
            />

            {/* Email */}
            <TextInput
              id="email"
              label="Email"
              type="email"
              required
              value={safeData.email ?? ""}
              onChange={(value) => handleChange("email", value)}
              placeholder="Enter Email"
              error={errors.email}
            />

            {/* Alternate Email */}
            <TextInput
              id="alternateEmail"
              label="Alternate Email"
              type="email"
              value={safeData.alternateEmail ?? ""}
              onChange={(value) => handleChange("alternateEmail", value)}
              placeholder="Enter Alternate Email"
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information - Collapsible */}
      <Card className="border-l-4 border-l-green-500">
        <Collapsible
          open={openSections.contact}
          onOpenChange={() => toggleSection("contact")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Contact Information
                </div>
                {openSections.contact ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Home Phone */}
                <div className="space-y-2">
                  <Label htmlFor="homePhone">Home Phone Number</Label>
                  <Input
                    id="homePhone"
                    placeholder="Enter Home Phone Number"
                    value={safeData.homePhone ?? ""}
                    onChange={(e) => handleChange("homePhone", e.target.value)}
                  />
                </div>

                {/* Mobile Number */}
                <div className="space-y-2">
                  <Label htmlFor="mobilePhone">Mobile Number</Label>
                  <Input
                    id="mobilePhone"
                    placeholder="Enter Mobile Number"
                    value={safeData.mobilePhone ?? ""}
                    onChange={(e) =>
                      handleChange("mobilePhone", e.target.value)
                    }
                  />
                </div>

                {/* Work Phone */}
                <div className="space-y-2">
                  <Label htmlFor="workPhone">Work Phone Number</Label>
                  <Input
                    id="workPhone"
                    placeholder="Enter Work Phone Number"
                    value={safeData.workPhone ?? ""}
                    onChange={(e) => handleChange("workPhone", e.target.value)}
                  />
                </div>

                {/* Other Phone */}
                <div className="space-y-2">
                  <Label htmlFor="otherPhone">Other Phone</Label>
                  <Input
                    id="otherPhone"
                    placeholder="Enter Other Phone"
                    value={safeData.otherPhone ?? ""}
                    onChange={(e) => handleChange("otherPhone", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Address Information - Collapsible */}
      <Card className="border-l-4 border-l-purple-500">
        <Collapsible
          open={openSections.address}
          onOpenChange={() => toggleSection("address")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Address Information
                </div>
                {openSections.address ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* City */}
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    placeholder="Enter City"
                    value={safeData.city ?? ""}
                    onChange={(e) => handleChange("city", e.target.value)}
                  />
                </div>

                {/* Zip Code */}
                <div className="space-y-2">
                  <Label htmlFor="zipCode">Zip Code</Label>
                  <Input
                    id="zipCode"
                    placeholder="Enter Zip Code"
                    value={safeData.zipCode ?? ""}
                    onChange={(e) => handleChange("zipCode", e.target.value)}
                  />
                </div>

                {/* Country */}
                <ApiSelectInput
                  id="country"
                  label="Country"
                  value={safeData.country ?? ""}
                  onChange={(value) => {
                    handleChange("country", value ?? "");
                    // Clear state when country changes
                    if (safeData.state) {
                      handleChange("state", "");
                    }
                  }}
                  listNameId={LIST_IDS.COUNTRIES}
                  placeholder="Select Country"
                  error={errors.country}
                />

                {/* State - depends on country selection */}
                <DependentApiSelectInput
                  id="state"
                  label="State"
                  value={safeData.state ?? ""}
                  onChange={(value) => handleChange("state", value ?? "")}
                  parentValue={safeData.country} // Use country ID as listNameId
                  placeholder="Select State"
                  emptyParentMessage="Please select country first"
                  error={errors.state}
                />

                {/* Address */}
                {/* <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    placeholder="Enter Address"
                    value={safeData.address ?? ""}
                    onChange={(e) => handleChange("address", e.target.value)}
                    rows={3}
                  />
                </div> */}
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Personal Details - Collapsible */}
      <Card className="border-l-4 border-l-orange-500">
        <Collapsible
          open={openSections.personal}
          onOpenChange={() => toggleSection("personal")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Personal Details
                </div>
                {openSections.personal ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Date of Birth */}
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Date of Birth</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={safeData.dateOfBirth ?? ""}
                    onChange={(e) =>
                      handleChange("dateOfBirth", e.target.value)
                    }
                  />
                </div>

                {/* SSN */}
                <div className="space-y-2">
                  <Label htmlFor="ssn">SSN</Label>
                  <Input
                    id="ssn"
                    placeholder="Enter SSN"
                    value={safeData.ssn ?? ""}
                    onChange={(e) => handleChange("ssn", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Social Media - Collapsible */}
      <Card className="border-l-4 border-l-cyan-500">
        <Collapsible
          open={openSections.social}
          onOpenChange={() => toggleSection("social")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Social Media & Online Presence
                </div>
                {openSections.social ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* LinkedIn Profile URL */}
                <div className="space-y-2">
                  <Label htmlFor="linkedinProfileUrl">
                    LinkedIn Profile URL
                  </Label>
                  <Input
                    id="linkedinProfileUrl"
                    placeholder="Enter LinkedIn Profile URL"
                    value={safeData.linkedinProfileUrl ?? ""}
                    onChange={(e) =>
                      handleChange("linkedinProfileUrl", e.target.value)
                    }
                  />
                </div>

                {/* Facebook Profile URL */}
                <div className="space-y-2">
                  <Label htmlFor="facebookProfileUrl">
                    Facebook Profile URL
                  </Label>
                  <Input
                    id="facebookProfileUrl"
                    placeholder="Enter Facebook Profile URL"
                    value={safeData.facebookProfileUrl ?? ""}
                    onChange={(e) =>
                      handleChange("facebookProfileUrl", e.target.value)
                    }
                  />
                </div>

                {/* Twitter Profile URL */}
                <div className="space-y-2">
                  <Label htmlFor="twitterProfileUrl">Twitter Profile URL</Label>
                  <Input
                    id="twitterProfileUrl"
                    placeholder="Enter Twitter Profile URL"
                    value={safeData.twitterProfileUrl ?? ""}
                    onChange={(e) =>
                      handleChange("twitterProfileUrl", e.target.value)
                    }
                  />
                </div>

                {/* Skype ID */}
                <div className="space-y-2">
                  <Label htmlFor="skypeId">Skype ID</Label>
                  <Input
                    id="skypeId"
                    placeholder="Enter Skype ID"
                    value={safeData.skypeId ?? ""}
                    onChange={(e) => handleChange("skypeId", e.target.value)}
                  />
                </div>

                {/* Video Reference */}
                <div className="space-y-2">
                  <Label htmlFor="videoReference">Video Reference</Label>
                  <Input
                    id="videoReference"
                    placeholder="Enter Video Reference"
                    value={safeData.videoReference ?? ""}
                    onChange={(e) =>
                      handleChange("videoReference", e.target.value)
                    }
                  />
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>

      {/* Work Authorization - Collapsible */}
      <Card className="border-l-4 border-l-red-500">
        <Collapsible
          open={openSections.work}
          onOpenChange={() => toggleSection("work")}
        >
          <CollapsibleTrigger asChild>
            <CardHeader className="pb-3 cursor-pointer hover:bg-gray-50 transition-colors">
              <CardTitle className="text-base font-medium flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Work Authorization & Clearance
                </div>
                {openSections.work ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Work Authorization */}
                <ApiSelectInput
                  id="workAuthorization"
                  label="Work Authorization"
                  value={safeData.workAuthorization ?? ""}
                  onChange={(value) =>
                    handleChange("workAuthorization", value ?? "")
                  }
                  listNameId={LIST_IDS.WORK_AUTHORIZATION}
                  placeholder="Select Work Authorization"
                  error={errors.workAuthorization}
                />

                {/* Clearance */}
                <div className="space-y-2">
                  <Label>Clearance</Label>
                  <RadioGroup
                    value={safeData.clearance ?? ""}
                    onValueChange={(value) =>
                      handleChange("clearance", value as "Yes" | "No")
                    }
                    className="flex gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Yes" id="clearance-yes" />
                      <Label htmlFor="clearance-yes">Yes</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="No" id="clearance-no" />
                      <Label htmlFor="clearance-no">No</Label>
                    </div>
                  </RadioGroup>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    </div>
  );
}
