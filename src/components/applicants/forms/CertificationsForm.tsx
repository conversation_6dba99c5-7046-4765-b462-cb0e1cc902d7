import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { CertificationDetails } from "@/types";
import { Plus, X, Award } from "lucide-react";
import { useEffect, useState } from "react";
import { generateUUID } from "@/lib/utils";

interface CertificationsFormProps {
  data: CertificationDetails[];
  onChange: (data: CertificationDetails[]) => void;
  onValidationChange: (isValid: boolean) => void;
}

export default function CertificationsForm({
  data,
  onChange,
  onValidationChange,
}: CertificationsFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasInteracted, setHasInteracted] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [certificationToDelete, setCertificationToDelete] = useState<number | null>(null);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Only validate active certification entries
    const activeCertifications = data.filter(cert => cert.isActive !== false);
    activeCertifications.forEach((cert, index) => {
      if (!cert.certification?.trim()) {
        newErrors[`certification-${index}`] = "Certification is required";
      }
      if (!cert.yearCompleted?.trim()) {
        newErrors[`yearCompleted-${index}`] = "Year completed is required";
      }
    });

    setErrors(newErrors);
    const isValid = Object.keys(newErrors).length === 0;
    onValidationChange(isValid);
    return isValid;
  };

  useEffect(() => {
    // Ensure there's always at least one certification form (only consider active certifications)
    const activeCertifications = data.filter(cert => cert.isActive !== false);
    if (activeCertifications.length === 0 && data.length === 0) {
      addCertification();
    } else if (hasInteracted) {
      validateForm();
    }
  }, [data.length, hasInteracted]);

  useEffect(() => {
    if (data.length > 0 && hasInteracted) {
      validateForm();
    }
  }, [data, hasInteracted]);

  const addCertification = () => {
    const newCertification: CertificationDetails = {
      id: generateUUID(),
      certification: "",
      yearCompleted: "",
      comments: "",
      isActive: true,
    };
    onChange([...data, newCertification]);
  };

  const updateCertification = (
    index: number,
    field: keyof CertificationDetails,
    value: string
  ) => {
    // Mark as interacted on first change
    if (!hasInteracted) {
      setHasInteracted(true);
    }

    const updatedCertifications = data.map((cert, i) =>
      i === index ? { ...cert, [field]: value } : cert
    );
    onChange(updatedCertifications);
  };

  const handleDeleteClick = (index: number) => {
    setCertificationToDelete(index);
    setShowDeleteDialog(true);
  };

  const confirmDelete = () => {
    if (certificationToDelete !== null) {
      const updatedCertifications = data.map((cert, i) => 
        i === certificationToDelete ? { ...cert, isActive: false } : cert
      );
      onChange(updatedCertifications);
    }
    setShowDeleteDialog(false);
    setCertificationToDelete(null);
  };

  const cancelDelete = () => {
    setShowDeleteDialog(false);
    setCertificationToDelete(null);
  };

  // Filter to show only active certification entries
  const activeCertifications = data.filter(cert => cert.isActive !== false);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Certification(s)</h3>
      </div>

      {activeCertifications.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Award className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 mb-4">No certifications added yet</p>
            <Button onClick={addCertification} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Certification
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {activeCertifications.map((certification, index) => {
            // Find the original index in data array for proper operations
            const originalIndex = data.findIndex(cert => cert.id === certification.id);
            return (
            <Card key={certification.id ?? index}>
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle className="text-base">Certification {index + 1}</CardTitle>
                  <Button
                    onClick={() => handleDeleteClick(originalIndex)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600 hover:text-red-700"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-4">
                {/* Certification */}
                <div className="space-y-2">
                  <Label htmlFor={`certification-${index}`}>
                    Certification <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id={`certification-${index}`}
                    placeholder="Required"
                    value={certification.certification}
                    onChange={(e) =>
                      updateCertification(
                        originalIndex,
                        "certification",
                        e.target.value
                      )
                    }
                    className={
                      errors[`certification-${index}`] ? "border-red-500" : ""
                    }
                  />
                  {errors[`certification-${index}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`certification-${index}`]}
                    </p>
                  )}
                </div>

                {/* Year Completed */}
                <div className="space-y-2">
                  <Label htmlFor={`yearCompleted-${index}`}>
                    Year Completed <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id={`yearCompleted-${index}`}
                    placeholder="Required"
                    value={certification.yearCompleted}
                    onChange={(e) =>
                      updateCertification(
                        originalIndex,
                        "yearCompleted",
                        e.target.value
                      )
                    }
                    className={
                      errors[`yearCompleted-${index}`] ? "border-red-500" : ""
                    }
                  />
                  {errors[`yearCompleted-${index}`] && (
                    <p className="text-sm text-red-500">
                      {errors[`yearCompleted-${index}`]}
                    </p>
                  )}
                </div>
              </div>

              {/* Comments */}
              <div className="space-y-2">
                <Label htmlFor={`comments-${index}`}>Comments</Label>
                <Textarea
                  id={`comments-${index}`}
                  placeholder="Enter comments"
                  value={certification.comments ?? ""}
                  onChange={(e) =>
                    updateCertification(originalIndex, "comments", e.target.value)
                  }
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>
            );
          })}

          {activeCertifications.length > 0 && (
            <div className="text-center pt-4">
              <Button onClick={addCertification} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Another Certification
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Certification</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this certification record? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={cancelDelete}>No</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Yes
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
