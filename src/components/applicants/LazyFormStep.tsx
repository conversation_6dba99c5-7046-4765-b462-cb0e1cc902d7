/**
 * Dynamic Form Step Loader
 * 
 * This component provides intelligent lazy loading for form steps,
 * only loading components when they are actually needed.
 */

import React, { Suspense, lazy, ComponentType, useEffect, useState } from 'react';
import { FormFallback } from '@/components/ui/LazyLoadFallback';

interface LazyFormStepProps<T = Record<string, unknown>> {
  stepId: string;
  activeStep: string;
  importFn: () => Promise<{ default: ComponentType<T> }>;
  componentProps: T;
  preloadNext?: boolean;
  preloadPrevious?: boolean;
}

/**
 * Cache for loaded components to avoid re-importing
 */
const componentCache = new Map<string, ComponentType<Record<string, unknown>>>();

/**
 * Queue for preloading components
 */
const preloadQueue = new Set<string>();

/**
 * Preload a component without rendering it
 */
const preloadComponent = async (stepId: string, importFn: () => Promise<{ default: ComponentType<Record<string, unknown>> }>) => {
  if (componentCache.has(stepId) || preloadQueue.has(stepId)) {
    return; // Already loaded or being loaded
  }
  
  preloadQueue.add(stepId);
  
  try {
    const module = await importFn();
    componentCache.set(stepId, module.default);
    console.log(`[LazyFormStep] Preloaded ${stepId}`);
  } catch (error) {
    console.warn(`[LazyFormStep] Failed to preload ${stepId}:`, error);
  } finally {
    preloadQueue.delete(stepId);
  }
};

/**
 * LazyFormStep component that only loads when active
 */
export function LazyFormStep<T = Record<string, unknown>>({
  stepId,
  activeStep,
  importFn,
  componentProps,
  preloadNext = true,
  preloadPrevious = true,
}: LazyFormStepProps<T>) {
  const [LoadedComponent, setLoadedComponent] = useState<ComponentType<T> | null>(
    () => componentCache.get(stepId) as ComponentType<T> || null
  );
  const [isLoading, setIsLoading] = useState(false);

  // Load component when step becomes active
  useEffect(() => {
    if (activeStep === stepId && !LoadedComponent && !isLoading) {
      setIsLoading(true);
      
      if (componentCache.has(stepId)) {
        // Use cached component
        setLoadedComponent(componentCache.get(stepId) as ComponentType<T>);
        setIsLoading(false);
      } else {
        // Load component
        importFn()
          .then((module) => {
            const component = module.default;
            componentCache.set(stepId, component as ComponentType<Record<string, unknown>>);
            setLoadedComponent(component);
            console.log(`[LazyFormStep] Loaded ${stepId}`);
          })
          .catch((error) => {
            console.error(`[LazyFormStep] Failed to load ${stepId}:`, error);
          })
          .finally(() => {
            setIsLoading(false);
          });
      }
    }
  }, [activeStep, stepId, LoadedComponent, isLoading, importFn]);

  // Preload adjacent steps for better UX
  useEffect(() => {
    // This would need step order information to work properly
    // For now, we'll skip preloading adjacent steps
    // TODO: Implement with step order context
  }, [activeStep, preloadNext, preloadPrevious]);

  // Only render if this is the active step
  if (activeStep !== stepId) {
    return null;
  }

  // Show loading state
  if (isLoading || !LoadedComponent) {
    return <FormFallback />;
  }

  // Render the loaded component
  return (
    <Suspense fallback={<FormFallback />}>
      <LoadedComponent {...componentProps} />
    </Suspense>
  );
}

/**
 * Higher-order component to create lazy form steps
 */
export function createLazyFormStep<T extends Record<string, unknown>>(
  stepId: string,
  importFn: () => Promise<{ default: ComponentType<T> }>
) {
  return function LazyFormStepWrapper(props: T & { activeStep: string }) {
    const { activeStep, ...componentProps } = props;
    
    return (
      <LazyFormStep
        stepId={stepId}
        activeStep={activeStep}
        importFn={importFn}
        componentProps={componentProps as T}
      />
    );
  };
}

/**
 * Form step manager for coordinated lazy loading
 */
interface FormStepManagerProps {
  steps: Array<{
    id: string;
    component: ComponentType<Record<string, unknown>>;
    props: Record<string, unknown>;
  }>;
  activeStepId: string;
  enablePreloading?: boolean;
}

export function FormStepManager({ steps, activeStepId, enablePreloading = true }: FormStepManagerProps) {
  const activeStepIndex = steps.findIndex(step => step.id === activeStepId);

  // Preload adjacent steps
  useEffect(() => {
    if (!enablePreloading) return;

    const preloadSteps: string[] = [];
    
    // Preload next step
    if (activeStepIndex < steps.length - 1) {
      preloadSteps.push(steps[activeStepIndex + 1].id);
    }
    
    // Preload previous step  
    if (activeStepIndex > 0) {
      preloadSteps.push(steps[activeStepIndex - 1].id);
    }

    // Note: This would need import functions to be available
    // For now, this is a placeholder for the concept
    preloadSteps.forEach(stepId => {
      console.log(`[FormStepManager] Would preload ${stepId}`);
    });
  }, [activeStepIndex, steps, enablePreloading]);

  const activeStep = steps.find(step => step.id === activeStepId);
  
  if (!activeStep) {
    return <FormFallback />;
  }

  const Component = activeStep.component;
  
  return (
    <Suspense fallback={<FormFallback />}>
      <Component {...activeStep.props} />
    </Suspense>
  );
}

/**
 * Preload all form components for instant switching
 * Useful when user is likely to navigate through all steps
 */
export async function preloadAllFormSteps(stepImports: Record<string, () => Promise<{ default: ComponentType<Record<string, unknown>> }>>) {
  const preloadPromises = Object.entries(stepImports).map(async ([stepId, importFn]) => {
    try {
      await preloadComponent(stepId, importFn);
    } catch (error) {
      console.warn(`Failed to preload ${stepId}:`, error);
    }
  });

  await Promise.allSettled(preloadPromises);
  console.log('[LazyFormStep] All form steps preloaded');
}

/**
 * Clear component cache (useful for development/testing)
 */
export function clearFormStepCache() {
  componentCache.clear();
  preloadQueue.clear();
  console.log('[LazyFormStep] Cache cleared');
}

/**
 * Get cache statistics
 */
export function getFormStepCacheStats() {
  return {
    cached: Array.from(componentCache.keys()),
    preloading: Array.from(preloadQueue),
    cacheSize: componentCache.size,
  };
} 