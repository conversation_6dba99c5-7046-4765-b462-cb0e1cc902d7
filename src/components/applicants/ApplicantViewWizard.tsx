import React, { useState, useEffect, Suspense, lazy } from "react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import {
  User,
  FileText,
  Briefcase,
  GraduationCap,
  Award,
  Building,
  Clock,
} from "lucide-react";
import { CardFallback } from "@/components/ui/LazyLoadFallback";

import { Applicant } from "@/types";

// Lazy load view components for better performance
const PersonalInfoView = lazy(() => import("./views/PersonalInfoView"));
const ProfessionalInfoView = lazy(() => import("./views/ProfessionalInfoView"));
const DocumentsView = lazy(() => import("./views/DocumentsView"));
const WorkExperienceView = lazy(() => import("./views/WorkExperienceView"));
const EducationDetailsView = lazy(() => import("./views/EducationDetailsView"));
const CertificationsView = lazy(() => import("./views/CertificationsView"));
const EmployerDetailsView = lazy(() => import("./views/EmployerDetailsView"));


interface ApplicantViewWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  applicant: Applicant | null;
}

const viewSections = [
  {
    id: "personal-info",
    label: "Personal Info",
    icon: User,
    component: PersonalInfoView,
  },
  {
    id: "professional-info",
    label: "Professional Info",
    icon: Briefcase,
    component: ProfessionalInfoView,
  },
  {
    id: "documents",
    label: "Documents",
    icon: FileText,
    component: DocumentsView,
  },
  {
    id: "education-details",
    label: "Education Details",
    icon: GraduationCap,
    component: EducationDetailsView,
  },
  {
    id: "certifications",
    label: "Certifications",
    icon: Award,
    component: CertificationsView,
  },
  {
    id: "employer-details",
    label: "Employer Details",
    icon: Building,
    component: EmployerDetailsView,
  },
  {
    id: "work-experience",
    label: "Work Experience",
    icon: Clock,
    component: WorkExperienceView,
  },
];

export default function ApplicantViewWizard({
  open,
  onOpenChange,
  applicant,
}: ApplicantViewWizardProps) {
  const [activeSection, setActiveSection] = useState("personal-info");

  // Reset active section when dialog opens
  useEffect(() => {
    if (open) {
      setActiveSection("personal-info");
    }
  }, [open]);

  const renderActiveSection = () => {
    if (!applicant) return null;

    const activeComponent = viewSections.find(section => section.id === activeSection);
    if (!activeComponent) return null;

    const Component = activeComponent.component;
    return (
      <Suspense fallback={<CardFallback />}>
        <Component applicant={applicant} />
      </Suspense>
    );
  };



  const handleClose = () => {
    onOpenChange(false);
  };

  if (!applicant) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-screen h-screen max-w-none max-h-none p-0 flex flex-col">
        {/* Fixed Header */}
        <DialogHeader className="px-6 py-4 border-b bg-white z-10 flex-shrink-0">
          <DialogTitle>
            View Applicant - {applicant.firstName} {applicant.lastName}
          </DialogTitle>
        </DialogHeader>

        {/* Desktop Layout (md and above) */}
        <div className="hidden md:flex flex-1 min-h-0">
          {/* Left Panel - Navigation */}
          <div className="w-64 border-r bg-gray-50/50">
            <ScrollArea className="h-full">
              <div className="p-4 space-y-2">
                {viewSections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;

                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        "w-full flex items-center gap-3 px-3 py-2.5 text-left rounded-lg transition-colors",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-gray-100 text-gray-700"
                      )}
                    >
                      <Icon className="h-4 w-4 flex-shrink-0" />
                      <span className="flex-1 text-sm font-medium">
                        {section.label}
                      </span>
                    </button>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Right Panel - Content */}
          <div className="flex-1 flex flex-col min-h-0">
            <ScrollArea className="flex-1">
              <div className="p-6">{renderActiveSection()}</div>
            </ScrollArea>

            {/* Footer */}
            <div className="border-t p-4 flex justify-end gap-3 bg-white flex-shrink-0">
              <Button variant="outline" onClick={handleClose}>
                Close
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Layout (below md breakpoint) */}
        <div className="md:hidden flex-1 flex flex-col min-h-0">
          {/* Mobile Navigation */}
          <div className="border-b bg-white flex-shrink-0">
            <ScrollArea className="w-full">
              <div className="flex p-3 gap-2 min-w-max">
                {viewSections.map((section) => {
                  const Icon = section.icon;
                  const isActive = activeSection === section.id;

                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={cn(
                        "flex items-center gap-2 px-3 py-2 rounded-md whitespace-nowrap transition-colors",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                      )}
                    >
                      <Icon className="h-4 w-4" />
                      <span className="text-sm font-medium">{section.label}</span>
                    </button>
                  );
                })}
              </div>
            </ScrollArea>
          </div>

          {/* Mobile Content */}
          <div className="flex-1 overflow-auto">
            <div className="p-4">{renderActiveSection()}</div>
          </div>

          {/* Mobile Footer */}
          <div className="border-t p-4 bg-white flex-shrink-0">
            <Button variant="outline" onClick={handleClose} className="w-full">
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
