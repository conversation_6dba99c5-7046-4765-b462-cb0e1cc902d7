import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { StatusBadge } from './StatusBadge';
import { Mail, Edit, Trash2, Eye, UserCheck } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ApplicantCardData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  status: string;
  applicationDate: string;
  avatar?: string;
  isActive: boolean;
}

export interface ApplicantCardProps {
  applicant: ApplicantCardData;
  onEdit: (applicant: ApplicantCardData) => void;
  onDelete: (applicantId: string) => void;
  onActivate: (applicantId: string) => void;
  onView?: (applicant: ApplicantCardData) => void;
  className?: string;
}

/**
 * Reusable applicant card component for displaying applicant information
 * Provides consistent layout and actions across applicant listings
 */
export const ApplicantCard: React.FC<ApplicantCardProps> = ({
  applicant,
  onEdit,
  onDelete,
  onActivate,
  onView,
  className,
}) => {
  const initials = `${applicant.firstName[0]}${applicant.lastName[0]}`;
  const fullName = `${applicant.firstName} ${applicant.lastName}`;
  const formattedDate = new Date(applicant.applicationDate).toLocaleDateString();

  return (
    <Card className={cn('hover:shadow-md transition-shadow', className)}>
      <CardContent className="p-3">
        <div className="flex items-start space-x-2 lg:space-x-3">
          <Avatar className="h-8 w-8 lg:h-10 lg:w-10 flex-shrink-0">
            <AvatarImage src={applicant.avatar} alt={fullName} />
            <AvatarFallback className="text-xs">{initials}</AvatarFallback>
          </Avatar>

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-sm text-gray-900 truncate leading-tight">
              {fullName}
            </h3>

            <div className="flex items-center text-xs text-gray-500 mb-1 lg:mb-2">
              <Mail className="mr-1 h-3 w-3 flex-shrink-0" />
              <span className="truncate">{applicant.email}</span>
            </div>

            <div className="flex items-center justify-between mb-1 lg:mb-2">
              <StatusBadge status={applicant.status} />
              <span className="text-xs text-gray-400 hidden sm:inline lg:inline">
                {formattedDate}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-1">
              {onView && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onView(applicant)}
                  className="flex-1 h-7 text-xs px-1 min-w-0"
                >
                  <Eye className="mr-1 h-3 w-3 flex-shrink-0" />
                  <span className="truncate">View</span>
                </Button>
              )}
              <Button
                size="sm"
                variant="outline"
                onClick={() => onEdit(applicant)}
                className="flex-1 h-7 text-xs px-1 min-w-0"
              >
                <Edit className="mr-1 h-3 w-3 flex-shrink-0" />
                <span className="truncate">Edit</span>
              </Button>

              {applicant.isActive ? (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 h-7 px-1 min-w-[28px] flex-shrink-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Deactivate Applicant</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to deactivate {fullName}?
                        This will mark the applicant as inactive but preserve their data.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => onDelete(applicant.id)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Deactivate
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              ) : (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-green-600 hover:text-green-700 hover:bg-green-50 h-7 px-1 min-w-[28px] flex-shrink-0"
                    >
                      <UserCheck className="h-3 w-3" />
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Activate Applicant</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to activate {fullName}?
                        This will mark the applicant as active and restore their visibility.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={() => onActivate(applicant.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Activate
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApplicantCard;
