import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { AlertTriangle, CheckCircle, WifiOff, XCircle } from 'lucide-react';

export type OperationType = 'add' | 'edit';
export type DialogType = 'success' | 'failure';
export type ErrorType = 'validation' | 'network' | 'server' | 'unknown';

interface ApplicantOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: DialogType;
  operation: OperationType;
  errorType?: ErrorType;
  errorMessage?: string;
  onRetry?: () => void;
}

const getDialogContent = (
  type: DialogType,
  operation: OperationType,
  errorType?: ErrorType,
  errorMessage?: string
) => {
  if (type === 'success') {
    return {
      icon: <CheckCircle className="h-6 w-6 text-green-600" aria-hidden="true" />,
      title: operation === 'add' ? 'Applicant Added Successfully' : 'Applicant Updated Successfully',
      description: operation === 'add' 
        ? 'The applicant has been successfully added to the system. You can now view their profile in the applicant list.'
        : 'The applicant information has been successfully updated. All changes have been saved.',
      buttonText: 'Continue',
      buttonClass: 'bg-green-600 hover:bg-green-700 focus:ring-green-600',
    };
  }

  // Failure dialogs
  const baseTitle = operation === 'add' ? 'Failed to Add Applicant' : 'Failed to Update Applicant';
  
  switch (errorType) {
    case 'validation':
      return {
        icon: <AlertTriangle className="h-6 w-6 text-amber-600" aria-hidden="true" />,
        title: baseTitle,
        description: errorMessage || 'Please check the form for validation errors and ensure all required fields are completed correctly.',
        buttonText: 'Fix Errors',
        buttonClass: 'bg-amber-600 hover:bg-amber-700 focus:ring-amber-600',
      };
    
    case 'network':
      return {
        icon: <WifiOff className="h-6 w-6 text-red-600" aria-hidden="true" />,
        title: baseTitle,
        description: 'Unable to connect to the server. Please check your internet connection and try again.',
        buttonText: 'Retry',
        buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-600',
      };
    
    case 'server':
      return {
        icon: <XCircle className="h-6 w-6 text-red-600" aria-hidden="true" />,
        title: baseTitle,
        description: errorMessage || 'A server error occurred while processing your request. Please try again later or contact support if the problem persists.',
        buttonText: 'Retry',
        buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-600',
      };
    
    default:
      return {
        icon: <XCircle className="h-6 w-6 text-red-600" aria-hidden="true" />,
        title: baseTitle,
        description: errorMessage || 'An unexpected error occurred. Please try again or contact support if the problem persists.',
        buttonText: 'Close',
        buttonClass: 'bg-red-600 hover:bg-red-700 focus:ring-red-600',
      };
  }
};

export default function ApplicantOperationDialog({
  open,
  onOpenChange,
  type,
  operation,
  errorType,
  errorMessage,
  onRetry,
}: ApplicantOperationDialogProps) {
  const content = getDialogContent(type, operation, errorType, errorMessage);
  
  const handleAction = () => {
    if (type === 'failure' && (errorType === 'network' || errorType === 'server') && onRetry) {
      onRetry();
    } else {
      onOpenChange(false);
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogContent className="sm:max-w-md">
        <AlertDialogHeader>
          <div className="flex items-center gap-3">
            {content.icon}
            <AlertDialogTitle className="text-left">
              {content.title}
            </AlertDialogTitle>
          </div>
          <AlertDialogDescription className="text-left mt-2">
            {content.description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogAction 
            onClick={handleAction}
            className={content.buttonClass}
            aria-label={`${content.buttonText} - ${content.title}`}
          >
            {content.buttonText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
} 