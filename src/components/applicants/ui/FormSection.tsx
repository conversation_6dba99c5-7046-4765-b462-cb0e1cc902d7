import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, X } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FormSectionProps {
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  className?: string;
  headerActions?: React.ReactNode;
}

export interface ArrayFormSectionProps extends FormSectionProps {
  onAdd: () => void;
  addButtonText?: string;
  showAddButton?: boolean;
}

export interface ArrayItemProps {
  title: string;
  index: number;
  onRemove: () => void;
  children: React.ReactNode;
  showRemoveButton?: boolean;
  className?: string;
}

/**
 * Base form section component for consistent section styling
 */
export const FormSection: React.FC<FormSectionProps> = ({
  title,
  icon: Icon,
  children,
  className,
  headerActions,
}) => {
  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          {Icon && <Icon className="h-5 w-5 text-gray-600" />}
          <h3 className="text-lg font-semibold">{title}</h3>
        </div>
        {headerActions}
      </div>
      {children}
    </div>
  );
};

/**
 * Form section with add button for array-based forms
 */
export const ArrayFormSection: React.FC<ArrayFormSectionProps> = ({
  title,
  icon,
  children,
  className,
  onAdd,
  addButtonText = 'Add Item',
  showAddButton = true,
}) => {
  return (
    <FormSection
      title={title}
      icon={icon}
      className={className}
      headerActions={
        showAddButton ? (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onAdd}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {addButtonText}
          </Button>
        ) : undefined
      }
    >
      {children}
    </FormSection>
  );
};

/**
 * Individual item in an array form with remove functionality
 */
export const ArrayItem: React.FC<ArrayItemProps> = ({
  title,
  index,
  onRemove,
  children,
  showRemoveButton = true,
  className,
}) => {
  return (
    <Card className={className}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">
            {title} {index + 1}
          </CardTitle>
          {showRemoveButton && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={onRemove}
              className="text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );
};

export default FormSection;
