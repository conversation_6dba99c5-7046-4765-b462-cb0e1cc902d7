import React from 'react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface StatusBadgeProps {
  status: string;
  className?: string;
}

/**
 * Standardized status badge component for candidates
 * Provides consistent color coding and styling across the application
 */
export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  className,
}) => {
  const getStatusStyles = (status: string): string => {
    const normalizedStatus = status.toLowerCase();
    switch (normalizedStatus) {
      case 'hired':
      case 'onboarding':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'offer':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'interview':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'screening':
        return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      case 'applied':
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
      case 'rejected':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusLabel = (status: string): string => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  return (
    <Badge 
      className={cn(
        getStatusStyles(status),
        'transition-colors duration-200',
        className
      )}
    >
      {getStatusLabel(status)}
    </Badge>
  );
};

export default StatusBadge;
