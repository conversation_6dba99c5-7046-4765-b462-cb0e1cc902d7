import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Eye, MoreHorizontal } from 'lucide-react';
import { CandidateData, getStatusColor } from '@/hooks/useDashboardData';

interface CandidateListProps {
  title: string;
  candidates: CandidateData[];
  showViewAll?: boolean;
  onViewAll?: () => void;
  onViewCandidate?: (candidateId: string) => void;
  emptyMessage?: string;
}

const CandidateList: React.FC<CandidateListProps> = ({
  title,
  candidates,
  showViewAll = true,
  onViewAll,
  onViewCandidate,
  emptyMessage = "No candidates found",
}) => {
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return 'Recent';
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
        {showViewAll && candidates.length > 0 && (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onViewAll}
            className="text-blue-600 hover:text-blue-700"
          >
            View All
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {candidates.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <p>{emptyMessage}</p>
          </div>
        ) : (
          <div className="space-y-4">
            {candidates.map((candidate) => (
              <div
                key={candidate.id}
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={candidate.avatar} alt={candidate.name} />
                    <AvatarFallback className="text-sm">
                      {getInitials(candidate.name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {candidate.name}
                    </p>
                    <p className="text-sm text-gray-500 truncate">
                      {candidate.position}
                    </p>
                    <p className="text-xs text-gray-400">
                      {formatDate(candidate.submittedAt)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant="secondary" 
                    className={getStatusColor(candidate.status)}
                  >
                    {candidate.status}
                  </Badge>
                  <div className="flex items-center space-x-1">
                    {onViewCandidate && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onViewCandidate(candidate.id)}
                        className="h-8 w-8 p-0"
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                    >
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CandidateList;
