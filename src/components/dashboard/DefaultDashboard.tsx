import React from 'react';
import { useNavigate } from 'react-router-dom';
import StatCard from './StatCard';
import ApplicantList from './ApplicantList';
import ProgressCard from './ProgressCard';
import { useGeneralDashboardData } from '@/hooks/useDashboardData';
import { getSecureRandomInt } from '@/lib/utils';
import {
  Users,
  Briefcase,
  Calendar,
  TrendingUp,
} from 'lucide-react';

const DefaultDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { stats, recentCandidates, onboardingCandidates } = useGeneralDashboardData();

  // Convert onboarding candidates to progress items
  const onboardingProgress = onboardingCandidates.map(candidate => ({
    id: candidate.id,
    name: candidate.name,
    position: candidate.position,
    progress: getSecureRandomInt(60, 100), // Mock progress 60-100%
    status: 'onboarding',
    daysRemaining: getSecureRandomInt(1, 8),
  }));

  const handleViewAllCandidates = () => {
    navigate('/candidates');
  };

  const handleViewCandidate = (candidateId: string) => {
    navigate(`/candidates/${candidateId}`);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Overview of your recruitment pipeline</p>
      </div>

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Candidates"
          value={stats.totalCandidates}
          change="+12%"
          changeType="positive"
          icon={Users}
        />
        <StatCard
          title="Active Jobs"
          value={stats.activeJobs}
          change="+4%"
          changeType="positive"
          icon={Briefcase}
        />
        <StatCard
          title="Interviews This Week"
          value={stats.scheduledInterviews}
          change="+8%"
          changeType="positive"
          icon={Calendar}
        />
        <StatCard
          title="Hire Rate"
          value={stats.hireRate}
          change="+2%"
          changeType="positive"
          icon={TrendingUp}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Candidates */}
        <ApplicantList
          title="Recent Applicants"
          candidates={recentCandidates}
          onViewAll={handleViewAllCandidates}
          onViewCandidate={handleViewCandidate}
          emptyMessage="No recent applicants"
        />

        {/* Onboarding Progress */}
        <ProgressCard
          title="Onboarding Progress"
          items={onboardingProgress}
          emptyMessage="No candidates in onboarding"
        />
      </div>
    </div>
  );
};

export default DefaultDashboard;
