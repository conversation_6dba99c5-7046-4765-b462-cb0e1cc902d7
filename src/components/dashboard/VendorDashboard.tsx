import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import StatCard from './StatCard';
import ApplicantList from './ApplicantList';
import { useVendorDashboardData } from '@/hooks/useDashboardData';
import { getSecureRandomInt } from '@/lib/utils';
import {
  Briefcase,
  Users,
  CheckCircle,
  Star,
  Building2,
  Target,
  TrendingUp,
  Award,
} from 'lucide-react';

const VendorDashboard: React.FC = () => {
  const navigate = useNavigate();
  const dashboardData = useVendorDashboardData();

  if (!dashboardData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Dashboard...</h2>
          <p className="text-gray-600">Please wait while we load your dashboard.</p>
        </div>
      </div>
    );
  }

  const { organization, connectedClients, stats, recentSubmissions } = dashboardData;

  const handleViewAllSubmissions = () => {
    navigate('/vendor-submissions');
  };

  const handleViewSubmission = (applicantId: string) => {
    navigate(`/applicants/${applicantId}`);
  };


  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Vendor Dashboard</h1>
        <p className="text-gray-600">Track your submissions and client relationships</p>
      </div>

      {/* Organization Info */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-purple-100 rounded-lg">
              <Building2 className="h-8 w-8 text-purple-600" />
            </div>
            <div>
              <p className="font-semibold text-gray-900">{organization.name}</p>
              <p className="text-sm text-gray-500">Vendor Organization</p>
            </div>
            <div className="ml-auto flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-500">Client Rating</p>
                <div className="flex items-center space-x-1">
                  <Star className="h-4 w-4 text-yellow-400 fill-current" />
                  <span className="font-semibold text-gray-900">{stats.clientRating}</span>
                </div>
              </div>
              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                {stats.connectedClientsCount} Clients
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Assigned Jobs"
          value={stats.assignedJobs}
          change="+3"
          changeType="positive"
          icon={Briefcase}
        />
        <StatCard
          title="Submitted Candidates"
          value={stats.submittedCandidates}
          change="+15%"
          changeType="positive"
          icon={Users}
        />
        <StatCard
          title="Approved Candidates"
          value={stats.approvedCandidates}
          change="+8%"
          changeType="positive"
          icon={CheckCircle}
        />
        <StatCard
          title="Active Submissions"
          value={stats.activeSubmissions}
          change="+2"
          changeType="positive"
          icon={Target}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Submissions */}
        <ApplicantList
          title="Recent Submissions"
          candidates={recentSubmissions}
          onViewAll={handleViewAllSubmissions}
          onViewCandidate={handleViewSubmission}
          emptyMessage="No recent submissions"
        />

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">Performance Metrics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Award className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Approval Rate</p>
                    <p className="text-sm text-gray-500">Last 30 days</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600">
                    {Math.round((stats.approvedCandidates / stats.submittedCandidates) * 100)}%
                  </p>
                  <p className="text-xs text-green-600">+5% from last month</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Response Time</p>
                    <p className="text-sm text-gray-500">Average</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-blue-600">2.3 days</p>
                  <p className="text-xs text-blue-600">-0.5 days improved</p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Star className="h-5 w-5 text-yellow-600" />
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Client Satisfaction</p>
                    <p className="text-sm text-gray-500">Average rating</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-yellow-600">{stats.clientRating}/5.0</p>
                  <p className="text-xs text-yellow-600">+0.2 from last month</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Connected Clients */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold">Connected Clients</CardTitle>
        </CardHeader>
        <CardContent>
          {connectedClients.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No clients connected yet</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {connectedClients.map((client) => (
                <div
                  key={client.id}
                  className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium text-gray-900">{client.name}</h3>
                    <Badge variant="secondary" className="bg-green-100 text-green-800">
                      Active
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">{client.description}</p>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">
                      {getSecureRandomInt(2, 7)} active jobs
                    </span>
                    <span className="text-blue-600 font-medium">
                      {getSecureRandomInt(5, 15)} submissions
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorDashboard;
