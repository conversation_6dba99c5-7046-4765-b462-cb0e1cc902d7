package com.chidhagni.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfigurationSource;

/**
 * Security configuration for the Application Tracking System (ATS).
 * Configures Spring Security for a stateless REST API application.
 * 
 * Features:
 * - Stateless session management (no sessions)
 * - CSRF disabled (appropriate for APIs)
 * - Public access to API endpoints and documentation
 * - HTTP Basic authentication ready for future implementation
 * 
 * <AUTHOR> Development Team
 * @version 2.0.0
 */
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    /**
     * Configures the security filter chain for a stateless REST API application.
     * 
     * Configuration includes:
     * - Stateless session management (no HTTP sessions)
     * - CSRF protection disabled (appropriate for stateless APIs)
     * - CORS configuration for cross-origin requests
     * - Public access to API endpoints and documentation
     * - HTTP Basic authentication (can be extended with JWT later)
     * 
     * @param http HttpSecurity configuration object
     * @param corsConfigurationSource CORS configuration for cross-origin requests
     * @return Configured SecurityFilterChain
     * @throws Exception if configuration fails
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, CorsConfigurationSource corsConfigurationSource) throws Exception {
        http
                // CORS configuration
                .cors(cors -> cors.configurationSource(corsConfigurationSource))
                
                // Disable CSRF for stateless API
                .csrf(csrf -> csrf.disable())
                
//                // Stateless session management
//                .sessionManagement(session -> session
//                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS))
//
                // Authorization rules
                .authorizeHttpRequests(authz -> authz
                        // Public endpoints - API documentation
                        .requestMatchers("/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html").permitAll()
                        .requestMatchers("/ats/v3/api-docs/**", "/ats/swagger-ui/**", "/ats/swagger-ui.html").permitAll()
                        .requestMatchers("/ats/swagger-ui/index.html").permitAll()
                        
                        // Public endpoints - Health checks
                        .requestMatchers("/api/health/**", "/ats/api/health/**").permitAll()
                        .requestMatchers("/actuator/**").permitAll()

                        // Allow list-values endpoints
                        .requestMatchers("/api/v1/list-value/**").permitAll()

                        // Allow list-values endpoints
                        .requestMatchers("/api/v1/applicant/**").permitAll()

                        // Allow list-values endpoints
                        .requestMatchers("/api/v1/files/get-file-content").permitAll()
                        
                        // Public endpoints - API resources (for now, to test functionality)
                        .requestMatchers("/api/v1/resources/**", "/ats/api/v1/resources/**").permitAll()
                        
                        // Public endpoints - Static resources
                        .requestMatchers("/css/**", "/js/**", "/images/**", "/webjars/**").permitAll()
                        .requestMatchers("/favicon.ico", "/error").permitAll()
                        
                        // All other requests require authentication
                        .anyRequest().authenticated()
                )
                
                // HTTP Basic authentication (no form login for API)
                .httpBasic(httpBasic -> httpBasic.realmName("ATS API"))
                
                // Exception handling for API responses
                .exceptionHandling(exceptions -> exceptions
                        .authenticationEntryPoint((request, response, authException) -> {
                            response.setStatus(401);
                            response.setContentType("application/json");
                            response.getWriter().write(
                                "{\"timestamp\":\"" + java.time.LocalDateTime.now() + "\"," +
                                "\"status\":401," +
                                "\"error\":\"Unauthorized\"," +
                                "\"message\":\"Authentication required\"," +
                                "\"path\":\"" + request.getRequestURI() + "\"}"
                            );
                        })
                        .accessDeniedHandler((request, response, accessDeniedException) -> {
                            response.setStatus(403);
                            response.setContentType("application/json");
                            response.getWriter().write(
                                "{\"timestamp\":\"" + java.time.LocalDateTime.now() + "\"," +
                                "\"status\":403," +
                                "\"error\":\"Forbidden\"," +
                                "\"message\":\"Access denied\"," +
                                "\"path\":\"" + request.getRequestURI() + "\"}"
                            );
                        })

                );

        return http.build();
    }

    /**
     * Password encoder bean for encoding user passwords.
     * 
     * @return BCryptPasswordEncoder instance
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
