package com.chidhagni.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.servers.Server;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI/Swagger configuration for the Application Tracking System (ATS).
 * This configuration provides comprehensive API documentation following industry best practices.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@Configuration
public class OpenApiConfig {

    private static final Logger logger = LoggerFactory.getLogger(OpenApiConfig.class);

    /**
     * Configures the OpenAPI specification for the ATS application.
     * 
     * @param contextPath The application context path from configuration
     * @param serverPort The server port from configuration
     * @return Configured OpenAPI instance
     */
    @Bean
    public OpenAPI openAPI(
            @Value("${server.servlet.context-path:/}") String contextPath,
            @Value("${server.port:8080}") String serverPort) {
        logger.info("Configuring OpenAPI with context path: {}", contextPath);

        OpenAPI openAPI = new OpenAPI()
                .info(new Info()
                        .title("Application Tracking System (ATS) API")
                        .description("Comprehensive application tracking system for managing job applications, candidates, and recruitment processes")
                        .version("1.0.0"))
                // Add multiple server configurations for different environments
                .addServersItem(new Server()
                        .url("http://localhost:" + serverPort + contextPath)
                        .description("Local Development Server"))
                .addServersItem(new Server()
                        .url(contextPath)
                        .description("Relative URL"));
                // Note: Security requirements removed to allow unauthenticated access to Swagger UI

        logger.debug("OpenAPI configuration created: {}", openAPI);
        return openAPI;
    }
}
