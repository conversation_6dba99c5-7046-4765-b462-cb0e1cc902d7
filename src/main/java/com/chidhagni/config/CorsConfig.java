package com.chidhagni.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Arrays;
import java.util.List;

/**
 * CORS (Cross-Origin Resource Sharing) configuration for the ATS application.
 * This configuration follows industry best practices for secure cross-origin requests.
 * 
 * Key Features:
 * - Configurable origins, methods, and headers via application properties
 * - Secure header handling with explicit allow/expose lists
 * - Proper credential handling
 * - Comprehensive logging for debugging
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    private static final Logger logger = LoggerFactory.getLogger(CorsConfig.class);

    @Value("${cors.allowed-origins}")
    private String allowedOrigins;

    @Value("${cors.allowed-methods}")
    private String allowedMethods;

    @Value("${cors.allow-credentials}")
    private boolean allowCredentials;

    @Value("${cors.max-age}")
    private long maxAge;

    /**
     * Global CORS configuration for all endpoints.
     * This method configures CORS at the MVC level.
     * 
     * @param registry CorsRegistry to configure CORS mappings
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        List<String> origins = Arrays.asList(allowedOrigins.split(","));
        List<String> methods = Arrays.asList(allowedMethods.split(","));

        logger.info("Configuring CORS with origins: {}", origins);
        logger.info("Configuring CORS with methods: {}", methods);
        logger.info("CORS credentials allowed: {}", allowCredentials);

        registry.addMapping("/**")
                .allowedOrigins(origins.toArray(new String[0]))
                .allowedMethods(methods.toArray(new String[0]))
                .allowedHeaders(
                    "Authorization",
                    "Content-Type",
                    "Accept",
                    "Origin",
                    "Access-Control-Request-Method",
                    "Access-Control-Request-Headers",
                    "X-Requested-With",
                    "X-CSRF-Token",
                    "Cache-Control",
                    "Pragma",
                    "Content-Disposition",
                    "Content-Length"
                )
                .exposedHeaders(
                    "Access-Control-Allow-Origin",
                    "Access-Control-Allow-Credentials",
                    "Authorization",
                    "Content-Disposition",
                    "Content-Length",
                    "X-Total-Count"
                )
                .allowCredentials(allowCredentials)
                .maxAge(maxAge);
    }

    /**
     * CORS configuration source for Spring Security integration.
     * This provides CORS configuration at the security filter level.
     * 
     * @return CorsConfigurationSource configured with application properties
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        
        // Parse and set allowed origins
        List<String> origins = Arrays.asList(allowedOrigins.split(","));
        configuration.setAllowedOrigins(origins);
        
        // Parse and set allowed methods
        List<String> methods = Arrays.asList(allowedMethods.split(","));
        configuration.setAllowedMethods(methods);
        
        // Set allowed headers - comprehensive list for API compatibility
        configuration.setAllowedHeaders(Arrays.asList(
            "Authorization",
            "Content-Type",
            "Accept",
            "Origin",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "X-Requested-With",
            "X-CSRF-Token",
            "Cache-Control",
            "Pragma",
            "If-Modified-Since",
            "If-None-Match"
        ));
        
        // Set exposed headers - headers that client-side code can access
        configuration.setExposedHeaders(Arrays.asList(
            "Access-Control-Allow-Origin",
            "Access-Control-Allow-Credentials",
            "Authorization",
            "Content-Disposition",
            "Content-Length",
            "X-Total-Count",
            "Location",
            "ETag",
            "Last-Modified"
        ));
        
        // Configure credentials and caching
        configuration.setAllowCredentials(allowCredentials);
        configuration.setMaxAge(maxAge);
        
        // Apply configuration to all paths
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        
        logger.debug("CORS configuration source created with origins: {}", origins);
        return source;
    }
}
