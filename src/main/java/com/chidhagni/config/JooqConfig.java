package com.chidhagni.config;

import lombok.extern.slf4j.Slf4j;
import org.jooq.ExecuteContext;
import org.jooq.ExecuteListener;
import org.jooq.conf.Settings;
import org.jooq.impl.DefaultConfiguration;

import org.springframework.boot.autoconfigure.jooq.DefaultConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.time.Instant;

/**
 * jOOQ configuration for query performance monitoring and optimization.
 * 
 * Features:
 * - Query execution time logging
 * - Slow query detection (>1000ms)
 * - Query statistics collection
 * - Performance monitoring integration
 * 
 * <AUTHOR> Development Team
 */
@Configuration
@Slf4j
public class JooqConfig {

    /**
     * Customizes jOOQ configuration with performance monitoring
     */
    @Bean
    public DefaultConfigurationCustomizer jooqConfigurationCustomizer() {
        return (DefaultConfiguration configuration) -> {
            configuration.set(new Settings()
                .withExecuteLogging(true)
                .withQueryTimeout(30) // 30 seconds timeout
                .withFetchWarnings(false)
            );
            
            // Add performance monitoring listener
            configuration.set(new ExecuteListener[] { 
                new QueryPerformanceListener() 
            });
            
            log.info("jOOQ configuration customized with performance monitoring");
        };
    }

    /**
     * Custom execute listener for query performance monitoring
     */
    public static class QueryPerformanceListener implements ExecuteListener {
        
        private static final long SLOW_QUERY_THRESHOLD_MS = 1000L; // 1 second
        private Instant queryStart;

        @Override
        public void executeStart(ExecuteContext ctx) {
            queryStart = Instant.now();
            log.debug("Executing query: {}", ctx.sql());
        }

        @Override
        public void executeEnd(ExecuteContext ctx) {
            if (queryStart != null) {
                Duration executionTime = Duration.between(queryStart, Instant.now());
                long executionMs = executionTime.toMillis();
                
                if (executionMs > SLOW_QUERY_THRESHOLD_MS) {
                    log.warn("SLOW QUERY detected - Execution time: {}ms, SQL: {}", 
                        executionMs, ctx.sql());
                } else {
                    log.debug("Query executed in {}ms", executionMs);
                }
                
                // Log query statistics for monitoring
                if (ctx.result() != null) {
                    log.debug("Query returned {} records in {}ms", 
                        ctx.result().size(), executionMs);
                }
            }
        }

        @Override
        public void exception(ExecuteContext ctx) {
            if (queryStart != null) {
                Duration executionTime = Duration.between(queryStart, Instant.now());
                log.error("Query failed after {}ms - SQL: {}, Error: {}", 
                    executionTime.toMillis(), ctx.sql(), ctx.exception().getMessage());
            }
        }
    }
}
