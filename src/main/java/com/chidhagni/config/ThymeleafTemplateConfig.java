package com.chidhagni.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.thymeleaf.spring6.SpringTemplateEngine;
import org.thymeleaf.spring6.templateresolver.SpringResourceTemplateResolver;
import org.thymeleaf.templatemode.TemplateMode;
import nz.net.ultraq.thymeleaf.layoutdialect.LayoutDialect;

import java.nio.charset.StandardCharsets;

/**
 * Thymeleaf template configuration for ATS application.
 * 
 * This configuration sets up Thymeleaf template engine with custom resolvers
 * and dialects for rendering HTML templates in the ATS application.
 * 
 * <AUTHOR> Development Team
 */
@Configuration
@RequiredArgsConstructor
public class ThymeleafTemplateConfig {

    private final ApplicationContext applicationContext;

    /**
     * Configures the Spring Template Engine with custom template resolvers and dialects.
     * 
     * @return configured SpringTemplateEngine
     */
    @Bean
    public SpringTemplateEngine springTemplateEngine() {
        SpringTemplateEngine templateEngine = new SpringTemplateEngine();
        templateEngine.addTemplateResolver(htmlTemplateResolver());
        templateEngine.addTemplateResolver(emailTemplateResolver());
        
        // Add Layout Dialect for advanced templating features
        templateEngine.addDialect(new LayoutDialect());
        
        return templateEngine;
    }

    /**
     * Template resolver for web pages (HTML templates).
     * 
     * @return configured SpringResourceTemplateResolver for web templates
     */
    @Bean
    public SpringResourceTemplateResolver htmlTemplateResolver() {
        SpringResourceTemplateResolver templateResolver = new SpringResourceTemplateResolver();
        templateResolver.setApplicationContext(applicationContext);
        templateResolver.setPrefix("classpath:/templates/");
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCharacterEncoding(StandardCharsets.UTF_8.name());
        templateResolver.setCacheable(false); // Set to true in production
        templateResolver.setOrder(1);
        return templateResolver;
    }

    /**
     * Template resolver specifically for email templates.
     * 
     * @return configured SpringResourceTemplateResolver for email templates
     */
    @Bean
    public SpringResourceTemplateResolver emailTemplateResolver() {
        SpringResourceTemplateResolver templateResolver = new SpringResourceTemplateResolver();
        templateResolver.setApplicationContext(applicationContext);
        templateResolver.setPrefix("classpath:/templates/email/");
        templateResolver.setSuffix(".html");
        templateResolver.setTemplateMode(TemplateMode.HTML);
        templateResolver.setCharacterEncoding(StandardCharsets.UTF_8.name());
        templateResolver.setCacheable(false); // Set to true in production
        templateResolver.setOrder(2);
        return templateResolver;
    }
}
