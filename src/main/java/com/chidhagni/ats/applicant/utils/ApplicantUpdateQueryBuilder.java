package com.chidhagni.ats.applicant.utils;

import com.chidhagni.ats.db.jooq.tables.pojos.Applicant;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.UpdateSetFirstStep;
import org.jooq.UpdateSetMoreStep;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.function.Function;

import static com.chidhagni.ats.db.jooq.tables.Applicant.APPLICANT;

/**
 * Optimized query builder for applicant updates using fluent API pattern.
 * Eliminates repetitive null checks and provides a clean, maintainable approach
 * for building dynamic update queries with only non-null fields.
 * 
 * Industry Best Practice: Builder Pattern + Fluent API for complex object construction
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class ApplicantUpdateQueryBuilder {

    /**
     * Builds an optimized update query with only non-null fields.
     * Uses fluent API pattern to eliminate repetitive null checks.
     * 
     * @param dslContext JOOQ DSL context
     * @param updatedApplicant Applicant entity with fields to update
     * @return UpdateSetMoreStep ready for WHERE clause and execution
     */
    public UpdateSetMoreStep<?> buildUpdateQuery(DSLContext dslContext, Applicant updatedApplicant) {
        log.debug("Building optimized update query for applicant");

        // Start with mandatory timestamp update
        UpdateSetMoreStep<?> query = dslContext.update(APPLICANT)
                .set(APPLICANT.UPDATED_ON, LocalDateTime.now());

        // TODO: Add UPDATED_BY field when UserPrincipal is available
        // query = query.set(APPLICANT.UPDATED_BY, updatedBy);

        // Apply all field updates using fluent pattern
        query = applyBasicFieldUpdates(query, updatedApplicant);
        query = applyJsonbFieldUpdates(query, updatedApplicant);
        query = applyBusinessFieldUpdates(query, updatedApplicant);

        log.debug("Update query built successfully with non-null fields only");
        return query;
    }

    /**
     * Applies basic field updates (firstName, lastName, email, etc.)
     */
    private UpdateSetMoreStep<?> applyBasicFieldUpdates(UpdateSetMoreStep<?> query, Applicant applicant) {
        query = setIfNotNull(query, APPLICANT.FIRST_NAME, applicant.getFirstName());
        query = setIfNotNull(query, APPLICANT.MIDDLE_NAME, applicant.getMiddleName());
        query = setIfNotNull(query, APPLICANT.LAST_NAME, applicant.getLastName());
        query = setIfNotNull(query, APPLICANT.PREFERRED_NAME, applicant.getPreferredName());
        query = setIfNotNull(query, APPLICANT.EMAIL, applicant.getEmail());
        query = setIfNotNull(query, APPLICANT.DATE_OF_BIRTH, applicant.getDateOfBirth());
        return query;
    }

    /**
     * Applies JSONB field updates (contactInfo, addresses, etc.)
     */
    private UpdateSetMoreStep<?> applyJsonbFieldUpdates(UpdateSetMoreStep<?> query, Applicant applicant) {
        query = setIfNotNull(query, APPLICANT.CONTACT_INFO, applicant.getContactInfo());
        query = setIfNotNull(query, APPLICANT.SOCIAL_PROFILES, applicant.getSocialProfiles());
        query = setIfNotNull(query, APPLICANT.ADDRESSES, applicant.getAddresses());
        query = setIfNotNull(query, APPLICANT.EMPLOYER_DETAILS, applicant.getEmployerDetails());
        query = setIfNotNull(query, APPLICANT.WORK_EXPERIENCE, applicant.getWorkExperience());
        query = setIfNotNull(query, APPLICANT.EDUCATION, applicant.getEducation());
        query = setIfNotNull(query, APPLICANT.CERTIFICATIONS, applicant.getCertifications());
        query = setIfNotNull(query, APPLICANT.ADDITIONAL_INFO, applicant.getAdditionalInfo());
        query = setIfNotNull(query, APPLICANT.DOCUMENTS, applicant.getDocuments());
        return query;
    }

    /**
     * Applies business field updates (status, priority, etc.)
     */
    private UpdateSetMoreStep<?> applyBusinessFieldUpdates(UpdateSetMoreStep<?> query, Applicant applicant) {
        query = setIfNotNull(query, APPLICANT.STATUS, applicant.getStatus());
        query = setIfNotNull(query, APPLICANT.PRIORITY_LEVEL, applicant.getPriorityLevel());
        query = setIfNotNull(query, APPLICANT.ASSIGNED_TO, applicant.getAssignedTo());
        query = setIfNotNull(query, APPLICANT.ADDITIONAL_COMMENTS, applicant.getAdditionalComments());
        query = setIfNotNull(query, APPLICANT.APPLICATION_DATE, applicant.getApplicationDate());
        query = setIfNotNull(query, APPLICANT.IS_ACTIVE, applicant.getIsActive());
        return query;
    }

    /**
     * Helper method to set a field only if the value is not null.
     * This eliminates repetitive null checks and provides a clean API.
     *
     * @param query The current update query
     * @param field The database field to set
     * @param value The value to set (can be null)
     * @param <T> The type of the value
     * @return The updated query for method chaining
     */
    private <T> UpdateSetMoreStep<?> setIfNotNull(UpdateSetMoreStep<?> query, org.jooq.Field<T> field, T value) {
        return value != null ? query.set(field, value) : query;
    }

}
