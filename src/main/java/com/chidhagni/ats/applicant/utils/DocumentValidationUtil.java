package com.chidhagni.ats.applicant.utils;

import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.documentrepo.DocumentRepoRepository;
import lombok.extern.slf4j.Slf4j;

/**
 * Utility class for document validation operations.
 * Centralizes document validation logic to ensure consistency across the application.
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Slf4j
public final class DocumentValidationUtil {

    private DocumentValidationUtil() {
        // Utility class - prevent instantiation
    }

    /**
     * ✅ CENTRALIZED LOGIC: Determines if a document is new (needs file upload).
     * A document is considered new if:
     * 1. It has no ID, OR
     * 2. It has no filePath (needs file upload)
     *
     * ✅ CRITICAL FIX: Removed database lookup to avoid transaction timing issues.
     * In the PUT endpoint, new documents are uploaded within the same transaction,
     * so checking database existence during JSONB building could give inconsistent results.
     *
     * This method ensures consistent logic across ApplicantServiceImpl and DocumentMapper.
     *
     * @param doc Document DTO from frontend
     * @param documentRepoRepository Repository for database lookups (kept for interface compatibility)
     * @return true if document is new, false if existing
     */
    public static boolean isNewDocument(DocumentDTO doc, DocumentRepoRepository documentRepoRepository) {
        // If document has no ID, it's definitely new
        if (doc.getId() == null) {
            log.debug("Document has no ID, treating as new document");
            return true;
        }

        // ✅ CRITICAL FIX: If document has no filePath, it needs file upload (treat as new)
        // This is the most reliable indicator for new documents in the PUT endpoint
        if (doc.getFilePath() == null || doc.getFilePath().trim().isEmpty()) {
            log.debug("Document ID: {} has no filePath, treating as new document for file upload", doc.getId());
            return true;
        }

        // ✅ CRITICAL FIX: Document has both ID and filePath, it's existing
        log.debug("Document ID: {} has filePath: {}, treating as existing document", doc.getId(), doc.getFilePath());
        return false;
    }
}
