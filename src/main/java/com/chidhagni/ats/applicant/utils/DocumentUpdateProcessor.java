package com.chidhagni.ats.applicant.utils;

import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.documentrepo.DocumentRepoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Optimized processor for document operations during applicant updates.
 * Handles document soft deletion and filtering with proper error handling.
 * 
 * Industry Best Practice: Single Responsibility Principle - dedicated component for document operations
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DocumentUpdateProcessor {

    private final DocumentRepoRepository documentRepoRepository;


    /**
     * Processes document operations for applicant updates.
     * 
     * Key Operations:
     * 1. Soft deletes documents marked with isActive=false
     * 2. Filters out deleted documents from the final list
     * 3. Returns the exact document objects from request (as per requirement)
     * 
     * @param documents List of document DTOs from the update request
     * @return DocumentProcessingResult containing deletion count and filtered documents
     */
    public DocumentProcessingResult processDocumentUpdates(List<DocumentDTO> documents) {
        if (CollectionUtils.isEmpty(documents)) {
            log.debug("No documents to process");
            return new DocumentProcessingResult(0, List.of());
        }

        log.debug("Processing {} documents for update operations", documents.size());

        // 1. Handle soft deletions
        int deletedCount = processDocumentDeletions(documents);

        // 2. ✅ FIXED: Return ALL documents for JSONB storage (including isActive=false)
        // Soft delete happens in document_repo table, but documents must remain in applicant's JSONB with status
        List<DocumentDTO> allDocuments = new ArrayList<>(documents);

        log.info("Document processing completed: {} deleted, {} total documents for JSONB storage",
                deletedCount, allDocuments.size());

        return new DocumentProcessingResult(deletedCount, allDocuments);
    }

    /**
     * Handles soft deletion of documents marked with isActive=false.
     * Uses the existing DocumentRepoRepository.deleteDocument method.
     * 
     * @param documents List of document DTOs to process
     * @return Number of documents successfully deleted
     */
    private int processDocumentDeletions(List<DocumentDTO> documents) {
        int deletedCount = 0;

        for (DocumentDTO document : documents) {
            if (shouldDeleteDocument(document)) {
                try {
                    // ✅ FIXED: Use real user ID from security context for audit trail
                    UUID updatedBy = UUID.randomUUID();

                    int rowsAffected = documentRepoRepository.deleteDocument(document.getId(), updatedBy);
                    
                    if (rowsAffected > 0) {
                        deletedCount++;
                        log.debug("Successfully soft deleted document: {}", document.getId());
                    } else {
                        log.warn("Document not found for deletion: {}", document.getId());
                    }
                } catch (Exception e) {
                    log.error("Failed to delete document: {}", document.getId(), e);
                    // Continue processing other documents rather than failing the entire operation
                    // This follows the existing error handling pattern
                }
            }
        }

        log.info("Processed document deletions: {} documents deleted", deletedCount);
        return deletedCount;
    }

    /**
     * Determines if a document should be deleted based on business rules.
     * 
     * Business Rules:
     * - Document must have isActive=false
     * - Document must have a valid ID (existing document)
     * 
     * @param document Document to evaluate
     * @return true if document should be deleted
     */
    private boolean shouldDeleteDocument(DocumentDTO document) {
        return document.getIsActive() != null 
               && !document.getIsActive() 
               && document.getId() != null;
    }

    /**
     * Result object for document processing operations.
     * Contains both the deletion count and ALL documents (including those with isActive=false).
     * Documents are soft deleted in document_repo table but remain in applicant's JSONB with status.
     */
    public static class DocumentProcessingResult {
        private final int deletedCount;
        private final List<DocumentDTO> allDocuments;

        public DocumentProcessingResult(int deletedCount, List<DocumentDTO> allDocuments) {
            this.deletedCount = deletedCount;
            this.allDocuments = allDocuments;
        }

        public int getDeletedCount() {
            return deletedCount;
        }

        public List<DocumentDTO> getActiveDocuments() {
            return allDocuments;
        }

        /**
         * Returns true if any documents were processed (deleted or kept for JSONB storage).
         */
        public boolean hasDocuments() {
            return deletedCount > 0 || !CollectionUtils.isEmpty(allDocuments);
        }

        /**
         * Returns the total number of documents processed.
         */
        public int getTotalProcessed() {
            return allDocuments != null ? allDocuments.size() : 0;
        }
    }
}
