
package com.chidhagni.ats.applicant.utils;

import com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO;
import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.applicant.dto.request.ApplicantUpdateRequestDTO;
import com.chidhagni.ats.applicant.dto.response.*;
import com.chidhagni.ats.db.jooq.tables.pojos.Applicant;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Mapper(componentModel = "spring")
public interface ApplicantMapper {

    @Mapping(target = "id", source = "id")
    @Mapping(target = "orgId", source = "orgId")
    @Mapping(target = "systemCode", source = "systemCode")
    @Mapping(target = "firstName", source = "firstName")
    @Mapping(target = "middleName", source = "middleName")
    @Mapping(target = "lastName", source = "lastName")
    @Mapping(target = "preferredName", source = "preferredName")
    @Mapping(target = "email", source = "email")
    @Mapping(target = "dateOfBirth", source = "dateOfBirth")
    @Mapping(target = "contactInfo", source = "contactInfo")
    @Mapping(target = "socialProfiles", source = "socialProfiles")
    @Mapping(target = "addresses", source = "addresses")
    @Mapping(target = "employerDetails", source = "employerDetails")
    @Mapping(target = "workExperience", source = "workExperience")
    @Mapping(target = "education", source = "education")
    @Mapping(target = "certifications", source = "certifications")
    @Mapping(target = "additionalInfo", source = "additionalInfo")
    @Mapping(target = "documents", source = "documents")
    @Mapping(target = "status", source = "status")
    @Mapping(target = "gender", source = "gender")
    @Mapping(target = "priorityLevel", source = "priorityLevel")
    @Mapping(target = "assignedTo", source = "assignedTo")
    @Mapping(target = "additionalComments", source = "additionalComments")
    @Mapping(target = "applicationDate", source = "applicationDate")
    @Mapping(target = "isActive", source = "isActive")
    @Mapping(target = "createdBy", source = "createdBy")
    @Mapping(target = "updatedBy", source = "updatedBy")
    @Mapping(target = "createdOn", source = "createdOn")
    @Mapping(target = "updatedOn", source = "updatedOn")
    ApplicantResponseDTO applicantToApplicantDetailResponse(Applicant applicant);




    /**
     * Maps ApplicantRequestDTO to Applicant entity with complete system fields
     * This is the consolidated mapping method that includes system code generation
     */
    @Mapping(target = "id", expression = "java(java.util.UUID.randomUUID())")
    @Mapping(target = "systemCode", expression = "java(generateSystemCode())")
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "createdOn", expression = "java(getCurrentTimestamp())")
    @Mapping(target = "updatedOn", expression = "java(getCurrentTimestamp())")
    @Mapping(target = "documents", ignore = true)            // Set in service with file upload metadata
    @Mapping(target = "languages", ignore = true)            // Not in RequestDTO
    @Mapping(target = "gender", expression = "java(extractGenderFromAdditionalInfo(requestDTO.getAdditionalInfo()))")
    @Mapping(target = "applicationDate", expression = "java(getApplicationDate(requestDTO.getApplicationDate()))")
    Applicant toCompleteEntity(ApplicantRequestDTO requestDTO);


    /**
     * Helper methods for MapStruct expressions
     */
    @Named("generateSystemCode")
    default String generateSystemCode() {
        return com.chidhagni.utils.CommonOperations.generateSystemCode(com.chidhagni.utils.SystemCodes.APP);
    }

    @Named("getCurrentTimestamp")
    default LocalDateTime getCurrentTimestamp() {
        return com.chidhagni.utils.DateUtils.currentTimeIST();
    }

    @Named("getApplicationDate")
    default LocalDate getApplicationDate(LocalDate providedDate) {
        return providedDate != null ? providedDate : LocalDate.now();
    }

    @Named("extractGenderFromAdditionalInfo")
    default UUID extractGenderFromAdditionalInfo(AdditionalInfoDTO additionalInfo) {
        return additionalInfo != null ? additionalInfo.getGender() : null;
    }

    /**
     * Maps ApplicantUpdateRequestDTO to Applicant entity for partial updates.
     * Only maps non-null fields to support partial update functionality.
     * This method is optimized to avoid repetitive null checks.
     *
     * @param updateRequestDTO The update request with potentially null fields
     * @return Applicant entity with only non-null fields set
     */
    @Mapping(target = "id", ignore = true)                    // ID is provided separately
    @Mapping(target = "orgId", ignore = true)                 // OrgId should not be updated
    @Mapping(target = "systemCode", ignore = true)            // SystemCode should not be updated
    @Mapping(target = "createdBy", ignore = true)             // CreatedBy should not be updated
    @Mapping(target = "createdOn", ignore = true)             // CreatedOn should not be updated
    @Mapping(target = "updatedBy", ignore = true)             // TODO: Set when UserPrincipal available
    @Mapping(target = "updatedOn", ignore = true)             // Set in repository layer
    @Mapping(target = "languages", ignore = true)             // Not in UpdateRequestDTO
    @Mapping(target = "gender", ignore = true)                // Not in UpdateRequestDTO
    @Mapping(target = "documents", ignore = true)             // Handled separately in service layer
    Applicant toUpdateEntity(ApplicantUpdateRequestDTO updateRequestDTO);

    /**
     * Optimized mapping method that applies only non-null properties from source to target.
     * This method eliminates the need for manual null checks by leveraging MapStruct's
     * NullValuePropertyMappingStrategy.IGNORE feature, similar to the ProjectHouzer implementation.
     * 
     * This approach is more maintainable and performant than the manual query builder approach
     * as it:
     * 1. Automatically handles null value checking
     * 2. Reduces boilerplate code
     * 3. Provides compile-time safety
     * 4. Follows industry best practices for partial updates
     *
     * @param target The existing applicant entity to update (modified in-place)
     * @param source The update request containing potentially null fields
     */
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)                    // ID should not be updated
    @Mapping(target = "orgId", ignore = true)                 // OrgId should not be updated  
    @Mapping(target = "systemCode", ignore = true)            // SystemCode should not be updated
    @Mapping(target = "createdBy", ignore = true)             // CreatedBy should not be updated
    @Mapping(target = "createdOn", ignore = true)             // CreatedOn should not be updated
    @Mapping(target = "updatedBy", ignore = true)             // TODO: Set when UserPrincipal available
    @Mapping(target = "updatedOn", ignore = true)             // Set in repository layer
    @Mapping(target = "languages", ignore = true)             // Not in UpdateRequestDTO
    @Mapping(target = "gender", expression = "java(extractGenderFromAdditionalInfo(source.getAdditionalInfo()))")
    @Mapping(target = "documents", ignore = true)             // Handled separately in service layer
    void mapNotNullPropertiesToApplicant(@MappingTarget Applicant target, ApplicantUpdateRequestDTO source);
}
