
package com.chidhagni.ats.applicant.constants;

/**
 * Constants class for Applicant API metadata including MIME types and other constants.
 *
 * This class follows the convention established in the pure-heart-backend project
 * for defining API-specific constants and MIME types.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ApplicantMetaData {

    /**
     * Private constructor to prevent instantiation of utility class.
     */
    private ApplicantMetaData() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // ========================================
    // MIME TYPE CONSTANTS
    // ========================================

    /**
     * MIME type for GET applicant by ID response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String APPLICANT_GET_BY_ID_RES_V1 =
            "application/vnd-chidhagni-ats.applicant.get.by-id.res-v1+json";

    /**
     * MIME type for activate applicant response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String APPLICANT_ACTIVATE_RES_V1 =
            "application/vnd-chidhagni-ats.applicant.activate.res-v1+json";

    /**
     * MIME type for deactivate applicant response (version 1).
     * Used in controller produces attribute and response headers.
     * Note: This is an extension to the OpenAPI spec.
     */
    public static final String APPLICANT_DEACTIVATE_RES_V1 =
            "application/vnd-chidhagni-ats.applicant.deactivate.res-v1+json";

    /**
     * MIME type for GET ALL applicants request (version 1).
     * Used in controller consumes attribute for request body.
     */
    public static final String APPLICANT_GET_ALL_REQ_V1 =
            "application/vnd-chidhagni-ats.applicant.get.all.req-v1+json";

    /**
     * MIME type for GET ALL applicants response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String APPLICANT_GET_ALL_RES_V1 =
            "application/vnd-chidhagni-ats.applicant.get.all.res-v1+json";

    /**
     * MIME type for create applicant response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String APPLICANT_CREATE_RES_V1 =
            "application/vnd-chidhagni-ats.applicant.create.res-v1+json";

    /**
     * MIME type for update applicant response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String APPLICANT_UPDATE_RES_V1 =
            "application/vnd.ats.applicant.update.response.v1+json";

    // ========================================
    // API OPERATION CONSTANTS
    // ========================================

    /**
     * Operation ID for get applicant by ID endpoint.
     */
    public static final String GET_APPLICANT_BY_ID_OPERATION = "getApplicantById";

    /**
     * Operation ID for activate applicant endpoint.
     */
    public static final String ACTIVATE_APPLICANT_OPERATION = "activateApplicant";

    /**
     * Operation ID for deactivate applicant endpoint.
     */
    public static final String DEACTIVATE_APPLICANT_OPERATION = "deactivateApplicant";

    /**
     * Operation ID for get all applicants endpoint.
     */
    public static final String GET_ALL_APPLICANTS_OPERATION = "getAllApplicants";

    /**
     * Operation ID for update applicant endpoint.
     */
    public static final String UPDATE_APPLICANT_OPERATION = "updateApplicant";

    // ========================================
    // ERROR MESSAGES
    // ========================================

    /**
     * Error message for applicant not found.
     */
    public static final String APPLICANT_NOT_FOUND = "Applicant not found";

    /**
     * Error message template for invalid JSON format.
     */
    public static final String INVALID_JSON_FORMAT = "Invalid JSON format in applicant data: %s";

    /**
     * Error message template for file validation failure.
     */
    public static final String FILE_VALIDATION_FAILED = "File validation failed: %s";

    /**
     * Success message for applicant creation.
     */
    public static final String APPLICANT_CREATED_SUCCESSFULLY = "Applicant created successfully";

    /**
     * Success message for applicant update.
     */
    public static final String APPLICANT_UPDATED_SUCCESSFULLY = "Applicant updated successfully";

    // ========================================
    // STATUS CONSTANTS
    // ========================================

    /**
     * Status constant for active applicants.
     */
    public static final String ACTIVE = "active";

    /**
     * Status constant for inactive applicants.
     */
    public static final String INACTIVE = "inactive";

    // ========================================
    // SUCCESS MESSAGES
    // ========================================

    /**
     * Success message for applicant activation.
     */
    public static final String APPLICANT_ACTIVATED_SUCCESSFULLY = "Applicant activated successfully";

    /**
     * Success message for applicant deactivation.
     */
    public static final String APPLICANT_DEACTIVATED_SUCCESSFULLY = "Applicant deactivated successfully";

    // ========================================
    // LOG MESSAGES
    // ========================================

    /**
     * Log message for successful activation.
     */
    public static final String LOG_ACTIVATED = "activated";

    /**
     * Log message for successful deactivation.
     */
    public static final String LOG_DEACTIVATED = "deactivated";

    /**
     * Log message for activation action.
     */
    public static final String LOG_ACTIVATE = "activate";

    /**
     * Log message for deactivation action.
     */
    public static final String LOG_DEACTIVATE = "deactivate";

    // ========================================
    // TABLE CONSTANTS
    // ========================================

    /**
     * Table name constant for applicant table.
     */
    public static final String TABLE_APPLICANT = "applicant";


}
