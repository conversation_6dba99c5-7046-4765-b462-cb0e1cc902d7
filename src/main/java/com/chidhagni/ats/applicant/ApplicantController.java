package com.chidhagni.ats.applicant;

import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.applicant.dto.request.ApplicantUpdateRequestDTO;
import com.chidhagni.ats.applicant.dto.request.GetAllApplicantsRequestDTO;
import com.chidhagni.ats.applicant.dto.response.CreateApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.ActivateDeactivateResponseDTO;
import com.chidhagni.ats.applicant.dto.response.ApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.GetAllApplicantsResponseDTO;
import com.chidhagni.ats.applicant.dto.response.UpdateApplicantResponseDTO;
import com.chidhagni.ats.applicant.service.ApplicantService;
import com.chidhagni.ats.utils.PiiMaskingUtil;
import com.chidhagni.ats.validation.FileValidationService;
import com.chidhagni.ats.validation.ValidApplicantData;
import com.chidhagni.exception.ValidationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.net.URI;
import java.util.List;
import java.util.UUID;

import static com.chidhagni.ats.applicant.constants.ApplicantMetaData.*;

/**
 * REST Controller for Applicant management operations.
 * This controller implements the endpoints specified in the OpenAPI specification
 * for applicant retrieval, activation, and deactivation operations.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/applicant")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "Applicant", description = "Applicant management operations")
public class ApplicantController {

    private final ApplicantService applicantService;
    private final ObjectMapper objectMapper;
    private final FileValidationService fileValidationService;

    @GetMapping(value = "/{id}", produces = APPLICANT_GET_BY_ID_RES_V1)
    @Operation(
            summary = "Get applicant details",
            description = "Retrieves complete applicant details including all JSONB field data",
            operationId = GET_APPLICANT_BY_ID_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved applicant",
                    content = @Content(
                            mediaType = APPLICANT_GET_BY_ID_RES_V1,
                            schema = @Schema(implementation = ApplicantResponseDTO.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad Request - Invalid UUID format",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Not Found - Applicant not found",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal Server Error",
                    content = @Content(mediaType = "application/problem+json")
            )
    })
    public ResponseEntity<ApplicantResponseDTO> getApplicantById(
            @Parameter(
                    description = "Applicant UUID",
                    required = true,
                    example = "550e8400-e29b-41d4-a716-************"
            )
            @PathVariable("id")
            @NotNull(message = "Applicant ID cannot be null")
            UUID id) {

        log.info("Received request to get applicant details for ID: {}", id);

        ApplicantResponseDTO response = applicantService.getApplicantById(id);

        log.info("Successfully retrieved applicant details for ID: {}", id);

        return ResponseEntity.ok(response);
    }

    @PatchMapping(value = "/{id}/activate", produces = APPLICANT_ACTIVATE_RES_V1)
    @Operation(
            summary = "Activate applicant",
            description = "Activates a previously deactivated applicant by setting isActive to true",
            operationId = ACTIVATE_APPLICANT_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Applicant activated successfully",
                    content = @Content(
                            mediaType = APPLICANT_ACTIVATE_RES_V1,
                            schema = @Schema(implementation = ActivateDeactivateResponseDTO.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad Request - Invalid UUID format",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Not Found - Applicant not found",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal Server Error",
                    content = @Content(mediaType = "application/problem+json")
            )
    })
    public ResponseEntity<ActivateDeactivateResponseDTO> activateApplicant(
            @Parameter(
                    description = "Applicant UUID",
                    required = true,
                    example = "550e8400-e29b-41d4-a716-************"
            )
            @PathVariable("id")
            @NotNull(message = "Applicant ID cannot be null")
            UUID id) {

        log.info("Received request to activate applicant with ID: {}", id);

        ActivateDeactivateResponseDTO response = applicantService.activateApplicant(id);

        log.info("Successfully processed activation request for applicant ID: {}", id);

        return ResponseEntity.ok(response);
    }

    @DeleteMapping(value = "/{id}/deactivate", produces = APPLICANT_DEACTIVATE_RES_V1)
    @Operation(
            summary = "Deactivate applicant",
            description = "Deactivates an active applicant by setting isActive to false. Uses PATCH method for consistency with activation endpoint.",
            operationId = DEACTIVATE_APPLICANT_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Applicant deactivated successfully",
                    content = @Content(
                            mediaType = APPLICANT_DEACTIVATE_RES_V1,
                            schema = @Schema(implementation = ActivateDeactivateResponseDTO.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad Request - Invalid UUID format",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Not Found - Applicant not found",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal Server Error",
                    content = @Content(mediaType = "application/problem+json")
            )
    })
    public ResponseEntity<ActivateDeactivateResponseDTO> deactivateApplicant(
            @Parameter(
                    description = "Applicant UUID",
                    required = true,
                    example = "550e8400-e29b-41d4-a716-************"
            )
            @PathVariable("id")
            @NotNull(message = "Applicant ID cannot be null")
            UUID id) {

        log.info("Received request to deactivate applicant with ID: {}", id);

        ActivateDeactivateResponseDTO response = applicantService.deactivateApplicant(id);

        log.info("Successfully processed deactivation request for applicant ID: {}", id);

        return ResponseEntity.ok(response);
    }

    @PostMapping(
            consumes = APPLICANT_GET_ALL_REQ_V1,
            produces = APPLICANT_GET_ALL_RES_V1
    )
    @Operation(
            summary = "Search applicants",
            description = "Retrieves a paginated list of applicants with filtering and sorting options. " +
                         "If no filters are provided, returns all applicants with default pagination. " +
                         "Uses POST method to support complex search criteria in request body.",
            operationId = GET_ALL_APPLICANTS_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved applicants",
                    content = @Content(
                            mediaType = APPLICANT_GET_ALL_RES_V1,
                            schema = @Schema(implementation = GetAllApplicantsResponseDTO.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Invalid request parameters",
                    content = @Content(schema = @Schema(hidden = true))
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content(schema = @Schema(hidden = true))
            )
    })
    public ResponseEntity<GetAllApplicantsResponseDTO> getAllApplicants(
            @Parameter(
                    description = "Filtering, sorting, and pagination parameters (all optional)",
                    required = false
            )
            @Valid
            @RequestBody(required = false)
            GetAllApplicantsRequestDTO request) {

        // Set default values if request is null
        if (request == null) {
            request = GetAllApplicantsRequestDTO.builder().build();
        }

        log.info("Received request to get all applicants: page={}, pageSize={}, status={}, search={}",
                request.getPage(), request.getPageSize(), request.getStatus(), request.getSearch());

        GetAllApplicantsResponseDTO response = applicantService.getAllApplicants(request);

        log.info("Successfully retrieved {} applicants out of {} total for page {}",
                response.getApplicants().size(), response.getTotalCount(), response.getCurrentPage());

        return ResponseEntity.ok(response);
    }


    @PostMapping(
            value = "/create",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = APPLICANT_CREATE_RES_V1
    )
    @Operation(
            summary = "Create new applicant with optional file uploads",
            description = "Creates a new applicant with all form sections data and optional file uploads in a single multipart request"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "201",
                    description = "Applicant created successfully",
                    content = @Content(
                            mediaType = APPLICANT_CREATE_RES_V1,
                            schema = @Schema(implementation = CreateApplicantResponseDTO.class)
                    )
            ),
            @ApiResponse(responseCode = "400", description = "Bad Request - Invalid input data"),
            @ApiResponse(responseCode = "409", description = "Conflict - Duplicate applicant"),
            @ApiResponse(responseCode = "413", description = "File too large"),
            @ApiResponse(responseCode = "415", description = "Unsupported file type"),
            @ApiResponse(responseCode = "500", description = "Internal Server Error")
    })
    public ResponseEntity<CreateApplicantResponseDTO> createApplicant(
            @Parameter(
                    description = "JSON string containing all applicant data",
                    required = true,
                    schema = @Schema(type = "string", example = "{\"firstName\":\"John\",\"lastName\":\"Doe\",\"email\":\"<EMAIL>\"}")
            )
            @RequestPart(value = "applicantData") 
            @NotNull(message = "Applicant data cannot be null")
            @ValidApplicantData
            String applicantDataJson,

            @Parameter(
                    description = "Optional document files to upload (resumes, certificates, etc.)",
                    required = false
            )
            @RequestPart(value = "files", required = false) List<MultipartFile> files
    ) {
        // Validate uploaded files first
        if (files != null && !files.isEmpty()) {
            fileValidationService.validateFiles(files);
            log.info("Successfully validated {} uploaded files", files.size());
        }

        // Parse JSON string to DTO using shared method
        ApplicantRequestDTO applicantData = parseAndValidateApplicantData(applicantDataJson, ApplicantRequestDTO.class, "Creating");
        log.info("Creating new applicant with email: {}", PiiMaskingUtil.maskEmail(applicantData.getEmail()));

        CreateApplicantResponseDTO response = applicantService.createApplicant(applicantData, files);

        URI location = URI.create("/api/v1/applicant/" + response.getId());

        log.info("Successfully created applicant with ID: {}", response.getId());
        return ResponseEntity.created(location).body(response);
    }

    @PutMapping(
            value = "/{id}",
            consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
            produces = APPLICANT_UPDATE_RES_V1
    )
    @Operation(
            summary = "Update existing applicant with document management",
            description = """
                Updates an existing applicant with comprehensive document management capabilities.

                **Document Operations Supported:**
                - Add new documents with specific subcategories
                - Replace existing documents (soft delete + upload new)
                - Delete documents (soft delete)
                - Partial applicant data updates

                **File-Metadata Mapping:**
                - Uses fileIndex for perfect file-to-metadata alignment
                - Preserves document subcategories per file
                - Maintains transaction consistency

                **Key Features:**
                - Atomic operations with rollback support
                - File cleanup on transaction failures
                - Comprehensive validation and error handling
                - Support for multiple file formats
                """,
            operationId = UPDATE_APPLICANT_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Applicant updated successfully",
                    content = @Content(
                            mediaType = APPLICANT_UPDATE_RES_V1,
                            schema = @Schema(implementation = UpdateApplicantResponseDTO.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "400",
                    description = "Bad Request - Invalid input data",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "404",
                    description = "Not Found - Applicant or document not found",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "409",
                    description = "Conflict - Email already exists for different applicant",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "413",
                    description = "Payload Too Large - File size exceeded",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "415",
                    description = "Unsupported Media Type - Invalid file type",
                    content = @Content(mediaType = "application/problem+json")
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal Server Error",
                    content = @Content(mediaType = "application/problem+json")
            )
    })
    public ResponseEntity<UpdateApplicantResponseDTO> updateApplicant(
            @Parameter(
                    description = "UUID of the applicant to update",
                    required = true,
                    schema = @Schema(type = "string", format = "uuid", example = "550e8400-e29b-41d4-a716-************")
            )
            @PathVariable("id") UUID applicantId,

            @Parameter(
                    description = "JSON string containing the applicant update data. All fields are optional for partial updates.",
                    required = true,
                    schema = @Schema(type = "string", example = """
                        {
                          "firstName": "John",
                          "lastName": "Doe Updated",
                          "email": "<EMAIL>",
                          "documents": [
                            {
                              "title": "Updated Resume",
                              "category": "550e8400-e29b-41d4-a716-************",
                              "subcategory": "650e8400-e29b-41d4-a716-************",
                              "fileIndex": 0,
                              "comments": "Latest version with new experience",
                              "isActive": true
                            },
                            {
                              "id": "old-document-uuid-to-delete",
                              "isActive": false
                            }
                          ]
                        }
                        """)
            )
            @RequestPart(value = "applicantData")
            @NotNull(message = "Applicant data cannot be null")
            String applicantDataJson,

            @Parameter(
                    description = """
                        Optional array of new files to upload.
                        Files are mapped to document metadata using fileIndex:
                        - files[0] maps to document with fileIndex: 0
                        - files[1] maps to document with fileIndex: 1

                        **Supported file types:** PDF, DOC, DOCX, JPG, JPEG, PNG, TXT, CSV
                        **Maximum file size:** 10MB per file
                        **Maximum total files:** 20 files per request
                        """,
                    required = false
            )
            @RequestPart(value = "files", required = false) List<MultipartFile> files
    ) {
        // Validate uploaded files first
        if (files != null && !files.isEmpty()) {
            fileValidationService.validateFiles(files);
            log.info("Successfully validated {} uploaded files for update", files.size());
        }

        // Parse JSON string to DTO using shared method
        ApplicantUpdateRequestDTO applicantUpdateData = parseAndValidateApplicantData(applicantDataJson, ApplicantUpdateRequestDTO.class, "Updating");
        log.info("Updating applicant with ID: {} and email: {}",
                applicantId,
                applicantUpdateData.getEmail() != null ? PiiMaskingUtil.maskEmail(applicantUpdateData.getEmail()) : "unchanged");

        UpdateApplicantResponseDTO response = applicantService.updateApplicant(applicantId, applicantUpdateData, files);

        log.info("Successfully updated applicant with ID: {}, documentsAdded: {}, documentsDeleted: {}",
                response.getId(), response.getDocumentsAdded(), response.getDocumentsDeleted());
        return ResponseEntity.ok(response);
    }

    /**
     * ✅ SHARED METHOD: Parses and validates JSON data to DTO with consistent error handling.
     * Reduces code duplication between POST and PUT endpoints while maintaining clarity.
     * 
     * @param jsonData JSON string to parse
     * @param dtoClass Target DTO class for deserialization
     * @param operation Operation name for logging (e.g., "Creating", "Updating")
     * @param <T> Type of DTO
     * @return Parsed and validated DTO
     * @throws ValidationException if JSON parsing fails
     */
    private <T> T parseAndValidateApplicantData(String jsonData, Class<T> dtoClass, String operation) {
        try {
            T parsedData = objectMapper.readValue(jsonData, dtoClass);
            log.debug("{} applicant with parsed data: {}", operation, dtoClass.getSimpleName());
            return parsedData;
        } catch (JsonProcessingException e) {
            log.error("Failed to parse {} data JSON for {}: {}", dtoClass.getSimpleName(), operation, e.getMessage());
            throw new ValidationException(String.format(INVALID_JSON_FORMAT, e.getMessage()), e);
        }
    }

}