package com.chidhagni.ats.applicant.exception;

import com.chidhagni.exception.EntityNotFoundException;
import org.springframework.http.HttpStatus;

import java.util.Map;
import java.util.UUID;

/**
 * Exception thrown when an applicant is not found.
 * Extends EntityNotFoundException for consistent error handling.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ApplicantNotFoundException extends EntityNotFoundException {

    private static final String ERROR_CODE = "ATS_APPLICANT_NOT_FOUND";
    private static final String DEFAULT_MESSAGE = "Applicant not found";

    /**
     * Creates an ApplicantNotFoundException with default message.
     */
    public ApplicantNotFoundException() {
        super(DEFAULT_MESSAGE, ERROR_CODE, HttpStatus.NOT_FOUND);
    }

    /**
     * Creates an ApplicantNotFoundException with custom message.
     *
     * @param message Custom error message
     */
    public ApplicantNotFoundException(String message) {
        super(message, ERROR_CODE, HttpStatus.NOT_FOUND);
    }


    /**
     * Factory method for creating exception when applicant is not found by ID.
     *
     * @param applicantId The UUID of the applicant that was not found
     * @return ApplicantNotFoundException with appropriate message and metadata
     */
    public static ApplicantNotFoundException forId(UUID applicantId) {
        String message = String.format("Applicant not found with ID: %s", applicantId);
        ApplicantNotFoundException exception = new ApplicantNotFoundException(message);
        exception.addMetadata("applicantId", applicantId.toString());
        exception.addMetadata("operation", "findById");
        return exception;
    }

    /**
     * Factory method for creating exception when applicant is not found by email.
     *
     * @param email The email of the applicant that was not found
     * @return ApplicantNotFoundException with appropriate message and metadata
     */
    public static ApplicantNotFoundException forEmail(String email) {
        String message = String.format("Applicant not found with email: %s", email);
        ApplicantNotFoundException exception = new ApplicantNotFoundException(message);
        exception.addMetadata("email", email);
        exception.addMetadata("operation", "findByEmail");
        return exception;
    }

    /**
     * Factory method for creating exception during update operations.
     *
     * @param applicantId The UUID of the applicant that was not found during update
     * @return ApplicantNotFoundException with appropriate message and metadata
     */
    public static ApplicantNotFoundException forUpdate(UUID applicantId) {
        String message = String.format("Cannot update applicant - not found with ID: %s", applicantId);
        ApplicantNotFoundException exception = new ApplicantNotFoundException(message);
        exception.addMetadata("applicantId", applicantId.toString());
        exception.addMetadata("operation", "update");
        return exception;
    }
}
