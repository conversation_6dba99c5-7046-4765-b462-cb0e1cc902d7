package com.chidhagni.ats.applicant;

import com.chidhagni.ats.applicant.dto.request.ApplicantUpdateRequestDTO;
import com.chidhagni.ats.applicant.dto.request.GetAllApplicantsRequestDTO;
import com.chidhagni.ats.applicant.exception.ApplicantNotFoundException;
import com.chidhagni.ats.applicant.utils.ApplicantMapper;
import com.chidhagni.ats.db.jooq.tables.daos.ApplicantDao;
import com.chidhagni.ats.db.jooq.tables.pojos.Applicant;
import com.chidhagni.ats.utils.PiiMaskingUtil;
import com.chidhagni.exception.DatabaseOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.jooq.SelectJoinStep;
import org.jooq.SortField;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.jooq.impl.DSL.noCondition;

import static com.chidhagni.ats.applicant.constants.ApplicantMetaData.TABLE_APPLICANT;
import static com.chidhagni.ats.db.jooq.Tables.APPLICANT;

/**
 * Repository layer for Applicant database operations using JOOQ.
 * <p>
 * This repository follows the conventions established in the pure-heart-backend project
 * for JOOQ-based database operations with proper error handling and logging.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class ApplicantRepository {

    // ========================================
    // DEPENDENCIES
    // ========================================

    private final DSLContext dslContext;
    private final ApplicantDao applicantDao;
    private final ApplicantMapper applicantMapper;

    // ========================================
    // QUERY METHODS
    // ========================================

    /**
     * Finds an applicant by ID.
     *
     * @param id The UUID of the applicant to find
     * @return Applicant entity or null if not found
     */
    public Applicant findById(UUID id) {
        try {
            log.debug("Finding applicant by ID: {}", id);

            Applicant applicant = dslContext
                    .selectFrom(APPLICANT)
                    .where(APPLICANT.ID.eq(id))
                    .fetchOneInto(Applicant.class);

            if (applicant != null) {
                log.debug("Found applicant with ID: {}", id);
            } else {
                log.debug("No applicant found with ID: {}", id);
            }

            return applicant;
        } catch (Exception ex) {
            log.error("Error finding applicant by ID: {}", id, ex);
            throw new DatabaseOperationException("SELECT", TABLE_APPLICANT, ex);
        }
    }


    // ========================================
    // UPDATE METHODS
    // ========================================

    /**
     * Updates the active status of an applicant.
     * <p>
     * TODO: AUDIT TRACKING - Add 'updatedBy' parameter
     * Method signature should be enhanced to:
     * public int updateStatus(UUID id, boolean isActive, UUID updatedBy)
     * This will enable proper audit tracking in the database.
     *
     * @param id       The UUID of the applicant to update
     * @param isActive The new active status
     */
    public void updateStatus(UUID id, boolean isActive) {
        try {
            log.debug("Updating active status for applicant ID: {} to: {}", id, isActive);

            // TODO: AUDIT TRACKING - Add UPDATED_BY field to the update
            // When updatedBy parameter is added, include:
            // .set(APPLICANT.UPDATED_BY, updatedBy)
            dslContext
                    .update(APPLICANT)
                    .set(APPLICANT.IS_ACTIVE, isActive)
                    .set(APPLICANT.UPDATED_ON, LocalDateTime.now())
                    .where(APPLICANT.ID.eq(id))
                    .execute();

            log.debug("Successfully updated active status for applicant ID: {} to: {}", id, isActive);

        } catch (Exception ex) {
            log.error("Error updating active status for applicant ID: {} to: {}", id, isActive, ex);
            throw new DatabaseOperationException("UPDATE", TABLE_APPLICANT, ex);
        }
    }

    // ========================================
    // QUERY METHODS FOR GET ALL FUNCTIONALITY
    // ========================================

    /**
     * Finds all applicants with filtering, searching, and pagination.
     *
     * @param request The request containing filter criteria and pagination parameters
     * @return List of applicants matching the criteria
     */
    public List<Applicant> findAllWithFilters(GetAllApplicantsRequestDTO request) {
        try {
            log.debug("Finding applicants with filters: page={}, pageSize={}, status={}, search={}",
                    request.getPage(), request.getPageSize(), request.getStatus(), request.getSearch());

            SelectJoinStep<org.jooq.Record> query = dslContext.select().from(APPLICANT);

            // Apply filters
            Condition condition = buildFilterCondition(request);
            SelectConditionStep<org.jooq.Record> conditionQuery = query.where(condition);

            // Apply sorting
            SortField<?> sortField = buildSortField(request.getSortDirection());

            // Apply pagination
            int offset = (request.getPage() - 1) * request.getPageSize();

            List<Applicant> applicants = conditionQuery
                    .orderBy(sortField)
                    .limit(request.getPageSize())
                    .offset(offset)
                    .fetchInto(Applicant.class);

            log.debug("Found {} applicants matching criteria", applicants.size());
            return applicants;

        } catch (Exception ex) {
            log.error("Error finding applicants with filters", ex);
            throw new DatabaseOperationException("SELECT", TABLE_APPLICANT, ex);
        }
    }

    /**
     * Counts total number of applicants matching the filter criteria.
     *
     * @param request The request containing filter criteria
     * @return Total count of applicants matching the criteria
     */
    public int countWithFilters(GetAllApplicantsRequestDTO request) {
        try {
            log.debug("Counting applicants with filters: status={}, search={}",
                    request.getStatus(), request.getSearch());

            Condition condition = buildFilterCondition(request);

            Record1<Integer> result = dslContext
                    .selectCount()
                    .from(APPLICANT)
                    .where(condition)
                    .fetchOne();

            int count = result != null ? result.value1() : 0;
            log.debug("Found {} total applicants matching criteria", count);
            return count;

        } catch (Exception ex) {
            log.error("Error counting applicants with filters", ex);
            throw new DatabaseOperationException("COUNT", TABLE_APPLICANT, ex);
        }
    }

    // ========================================
    // PRIVATE HELPER METHODS
    // ========================================

    /**
     * Builds filter condition based on request parameters.
     *
     * @param request The request containing filter criteria
     * @return JOOQ Condition for filtering
     */
    private Condition buildFilterCondition(GetAllApplicantsRequestDTO request) {
        Condition condition = noCondition();

        // Filter by status if provided
        if (request.getStatus() != null) {
            condition = condition.and(APPLICANT.STATUS.eq(request.getStatus()));
        }

        // Filter by search term if provided
        if (request.getSearch() != null && !request.getSearch().trim().isEmpty()) {
            String searchTerm = "%" + request.getSearch().trim().toLowerCase() + "%";

            // Search across firstName, lastName, middleName, preferredName, and email
            // Using ILIKE for case-insensitive search (PostgreSQL)
            Condition searchCondition = APPLICANT.FIRST_NAME.likeIgnoreCase(searchTerm)
                    .or(APPLICANT.LAST_NAME.likeIgnoreCase(searchTerm))
                    .or(APPLICANT.MIDDLE_NAME.likeIgnoreCase(searchTerm))
                    .or(APPLICANT.PREFERRED_NAME.likeIgnoreCase(searchTerm))
                    .or(APPLICANT.EMAIL.likeIgnoreCase(searchTerm));

            condition = condition.and(searchCondition);
        }

        return condition;
    }

    /**
     * Builds sort field based on sort direction.
     * Default sorting is by createdOn field.
     *
     * @param sortDirection The sort direction from request
     * @return JOOQ SortField for ordering
     */
    private SortField<?> buildSortField(GetAllApplicantsRequestDTO.SortDirection sortDirection) {
        // Default sort by createdOn (most recent first for DESC, oldest first for ASC)
        if (sortDirection == GetAllApplicantsRequestDTO.SortDirection.DESC) {
            return APPLICANT.CREATED_ON.desc();
        } else {
            return APPLICANT.CREATED_ON.asc();
        }
    }

    /**
     * Creates a new applicant record
     */
    public Applicant createApplicant(Applicant applicant) {
        log.debug("Creating applicant with email: {}", applicant.getEmail());

        try {
            applicantDao.insert(applicant);
            log.info("Successfully created applicant with ID: {}", applicant.getId());
            return applicant;
        } catch (Exception e) {
            log.error("Failed to create applicant with email: {}", applicant.getEmail(), e);
            throw new DatabaseOperationException("Failed to create applicant", e);
        }
    }

    /**
     * Checks if an applicant with the given email already exists.
     * Optimized to use EXISTS instead of COUNT for better performance.
     */
    public boolean existsByEmail(String email) {
        log.debug("Checking if applicant exists with email: {}", PiiMaskingUtil.maskEmail(email));

        try {
            Boolean exists = dslContext.fetchExists(
                    dslContext.selectOne()
                            .from(APPLICANT)
                            .where(APPLICANT.EMAIL.equalIgnoreCase(email))
                            .and(APPLICANT.IS_ACTIVE.isTrue())
            );

            boolean result = exists != null && exists;
            log.debug("Applicant exists check for email {}: {}", PiiMaskingUtil.maskEmail(email), result);
            return result;
        } catch (Exception e) {
            log.error("Failed to check applicant existence for email: {}", email, e);
            throw new DatabaseOperationException("Failed to check applicant existence", e);
        }
    }

    /**
     * Checks if an applicant with the given email already exists, excluding a specific applicant ID.
     * Used during updates to check email uniqueness while allowing the current applicant to keep their email.
     */
    public boolean existsByEmailExcludingId(String email, UUID excludeId) {
        log.debug("Checking if applicant exists with email: {} excluding ID: {}",
                 PiiMaskingUtil.maskEmail(email), excludeId);

        try {
            Boolean exists = dslContext.fetchExists(
                    dslContext.selectOne()
                            .from(APPLICANT)
                            .where(APPLICANT.EMAIL.equalIgnoreCase(email))
                            .and(APPLICANT.IS_ACTIVE.isTrue())
                            .and(APPLICANT.ID.ne(excludeId))
            );

            boolean result = exists != null && exists;
            log.debug("Applicant exists check for email {} excluding ID {}: {}",
                     PiiMaskingUtil.maskEmail(email), excludeId, result);
            return result;
        } catch (Exception e) {
            log.error("Failed to check applicant existence for email: {} excluding ID: {}", email, excludeId, e);
            throw new DatabaseOperationException("Failed to check applicant existence", e);
        }
    }

    /**
     * Updates an existing applicant record with optimistic locking.
     * Only updates non-null fields to support partial updates.
     *
     * OPTIMIZED VERSION: Uses MapStruct with NullValuePropertyMappingStrategy.IGNORE
     * to eliminate repetitive null checks, following the ProjectHouzer pattern.
     * This approach is more maintainable and performant than manual query building.
     *
     * Benefits of this approach:
     * 1. Automatic null value handling via MapStruct annotations
     * 2. Compile-time safety and code generation
     * 3. Reduced boilerplate code and maintenance overhead
     * 4. Industry best practice for partial updates
     * 5. Consistent with ProjectHouzer implementation pattern
     *
     * TODO: Add updatedBy parameter once UserPrincipal is available
     * Method signature should be enhanced to:
     * public Applicant updateApplicant(UUID id, ApplicantUpdateRequestDTO updateRequest, UUID updatedBy)
     *
     * @param id The UUID of the applicant to update
     * @param updateRequest The update request DTO with potentially null fields
     * @return Updated applicant record
     * @throws ApplicantNotFoundException if applicant not found
     * @throws DatabaseOperationException if update fails
     */
    public Applicant updateApplicant(UUID id, ApplicantUpdateRequestDTO updateRequest) {
        log.debug("Updating applicant with ID: {} using optimized MapStruct mapping", id);

        try {
            // 1. Fetch existing applicant for optimistic locking and data integrity
            Applicant existingApplicant = findById(id);
            if (existingApplicant == null) {
                log.warn("No applicant found to update with ID: {}", id);
                throw new ApplicantNotFoundException("Applicant not found with ID: " + id);
            }

            // 2. Use MapStruct to apply only non-null properties from request to existing entity
            // This leverages NullValuePropertyMappingStrategy.IGNORE for automatic null handling
            applicantMapper.mapNotNullPropertiesToApplicant(existingApplicant, updateRequest);

            // 3. Handle documents separately (as they are processed differently in service layer)
            // Documents are set in the updateRequest by the service layer after processing files
            if (updateRequest.getDocuments() != null) {
                existingApplicant.setDocuments(updateRequest.getDocuments());
                log.debug("Updated documents field with {} documents", updateRequest.getDocuments().size());
            }

            // 4. Set system fields
            existingApplicant.setUpdatedOn(LocalDateTime.now());
            // TODO: Set updatedBy when UserPrincipal is available
            // existingApplicant.setUpdatedBy(updatedBy);

            // 5. Update using DAO for optimized database operation
            applicantDao.update(existingApplicant);

            log.info("Successfully updated applicant with ID: {} using MapStruct optimization", id);
            return existingApplicant;

        } catch (ApplicantNotFoundException e) {
            throw e; // Re-throw as-is
        } catch (Exception e) {
            log.error("Failed to update applicant with ID: {} using MapStruct approach", id, e);
            throw new DatabaseOperationException("UPDATE", TABLE_APPLICANT, e);
        }
    }
}