package com.chidhagni.ats.applicant.service;

import java.util.UUID;

/**
 * Interface for applicant validation operations.
 * Provides contract for validation logic to improve testability.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ApplicantValidator {

    /**
     * Validates email uniqueness globally.
     *
     * @param email Email to validate
     * @param orgId Organization ID (optional - can be null)
     */
    void validateEmailUniqueness(String email, UUID orgId);

    // validateApplicantRequest method removed - redundant since Bean Validation
    // is handled by @ValidApplicantData and only email uniqueness check is needed
}