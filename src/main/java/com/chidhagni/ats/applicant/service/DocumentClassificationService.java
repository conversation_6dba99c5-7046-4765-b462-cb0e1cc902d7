package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.ats.documentrepo.DocumentRepoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service for efficiently classifying documents as new, existing, or soft-deleted.
 * Optimizes database access by using batch queries instead of individual lookups.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentClassificationService {

    private final DocumentRepoRepository documentRepoRepository;

    /**
     * Document classification types.
     */
    public enum DocumentType {
        NEW,           // Document needs file upload
        EXISTING,      // Document already exists with file
        SOFT_DELETED   // Document marked for deletion
    }

    /**
     * Result of document classification containing type and existing document info.
     */
    public static class DocumentClassification {
        private final DocumentType type;
        private final DocumentRepo existingDocument;

        public DocumentClassification(DocumentType type, DocumentRepo existingDocument) {
            this.type = type;
            this.existingDocument = existingDocument;
        }

        public DocumentType getType() { return type; }
        public DocumentRepo getExistingDocument() { return existingDocument; }
        public boolean isNew() { return type == DocumentType.NEW; }
        public boolean isExisting() { return type == DocumentType.EXISTING; }
        public boolean isSoftDeleted() { return type == DocumentType.SOFT_DELETED; }
    }

    /**
     * Efficiently classifies multiple documents using a single batch query.
     * Replaces individual isNewDocument() calls that caused N+1 query problems.
     * 
     * @param documents List of documents to classify
     * @return Map of document ID to classification result
     */
    public Map<UUID, DocumentClassification> classifyDocuments(List<DocumentDTO> documents) {
        if (CollectionUtils.isEmpty(documents)) {
            log.debug("No documents to classify");
            return Collections.emptyMap();
        }

        log.debug("Classifying {} documents using batch query", documents.size());

        // ✅ OPTIMIZATION: Single batch query instead of N individual queries
        Map<UUID, DocumentRepo> existingDocuments = fetchExistingDocuments(documents);
        
        // Classify each document based on its properties and database existence
        Map<UUID, DocumentClassification> classifications = documents.stream()
                .filter(doc -> doc.getId() != null) // Only classify documents with IDs
                .collect(Collectors.toMap(
                    DocumentDTO::getId,
                    doc -> classifyDocument(doc, existingDocuments.get(doc.getId()))
                ));

        // Handle documents without IDs (always new)
        documents.stream()
                .filter(doc -> doc.getId() == null)
                .forEach(doc -> {
                    // Use a temporary UUID for mapping (these are always new)
                    UUID tempId = UUID.randomUUID();
                    classifications.put(tempId, new DocumentClassification(DocumentType.NEW, null));
                });

        log.debug("Document classification completed: {} total classifications", classifications.size());
        return classifications;
    }

    /**
     * Determines if a document is new (needs file upload).
     * Uses batch classification for efficiency when called multiple times.
     * 
     * @param document Document to check
     * @return true if document is new, false otherwise
     */
    public boolean isNewDocument(DocumentDTO document) {
        // Quick checks for obvious new documents
        if (document.getId() == null) {
            log.debug("Document has no ID, treating as new document");
            return true;
        }

        if (document.getFilePath() == null || document.getFilePath().trim().isEmpty()) {
            log.debug("Document ID: {} has no filePath, treating as new document for file upload", document.getId());
            return true;
        }

        // For single document check, use individual query (acceptable for single calls)
        try {
            DocumentRepo existingDocument = documentRepoRepository.getDocumentByID(document.getId());
            boolean isNew = existingDocument == null;
            log.debug("Document ID: {} - exists in database: {}", document.getId(), !isNew);
            return isNew;
        } catch (Exception e) {
            log.warn("Error checking if document exists for ID: {} - treating as new document: {}",
                    document.getId(), e.getMessage());
            return true;
        }
    }

    /**
     * Fetches existing documents from database using a single batch query.
     * 
     * @param documents List of documents to check
     * @return Map of document ID to existing DocumentRepo
     */
    private Map<UUID, DocumentRepo> fetchExistingDocuments(List<DocumentDTO> documents) {
        // Extract all non-null document IDs
        Set<UUID> documentIds = documents.stream()
                .map(DocumentDTO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (documentIds.isEmpty()) {
            log.debug("No document IDs to fetch from database");
            return Collections.emptyMap();
        }

        try {
            // ✅ SINGLE BATCH QUERY: Replaces N individual queries
            List<DocumentRepo> existingDocs = documentRepoRepository.findByIdIn(documentIds);
            
            Map<UUID, DocumentRepo> result = existingDocs.stream()
                    .collect(Collectors.toMap(DocumentRepo::getId, Function.identity()));
            
            log.debug("Fetched {} existing documents from database using batch query", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("Error fetching existing documents from database", e);
            return Collections.emptyMap();
        }
    }

    /**
     * Classifies a single document based on its properties and database existence.
     * 
     * @param document Document to classify
     * @param existingDocument Existing document from database (may be null)
     * @return Classification result
     */
    private DocumentClassification classifyDocument(DocumentDTO document, DocumentRepo existingDocument) {
        // Check for soft deletion first
        if (Boolean.FALSE.equals(document.getIsActive())) {
            return new DocumentClassification(DocumentType.SOFT_DELETED, existingDocument);
        }

        // Check if document needs file upload (new document)
        if (document.getFilePath() == null || document.getFilePath().trim().isEmpty()) {
            return new DocumentClassification(DocumentType.NEW, existingDocument);
        }

        // Check if document doesn't exist in database (new document)
        if (existingDocument == null) {
            return new DocumentClassification(DocumentType.NEW, null);
        }

        // Document exists and has file path (existing document)
        return new DocumentClassification(DocumentType.EXISTING, existingDocument);
    }

    /**
     * Filters documents by classification type.
     * 
     * @param documents List of documents to filter
     * @param targetType Type to filter for
     * @return List of documents matching the target type
     */
    public List<DocumentDTO> filterDocumentsByType(List<DocumentDTO> documents, DocumentType targetType) {
        Map<UUID, DocumentClassification> classifications = classifyDocuments(documents);
        
        return documents.stream()
                .filter(doc -> {
                    UUID key = doc.getId() != null ? doc.getId() : UUID.randomUUID();
                    DocumentClassification classification = classifications.get(key);
                    return classification != null && classification.getType() == targetType;
                })
                .collect(Collectors.toList());
    }
}
