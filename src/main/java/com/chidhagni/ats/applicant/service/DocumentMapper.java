package com.chidhagni.ats.applicant.service;


import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.ats.documentrepo.DocumentRepoRepository;
import com.chidhagni.ats.documentrepo.dto.response.DocumentUploadResponseDTO;
import com.chidhagni.exception.DocumentProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service responsible for document transformation and mapping logic.
 * Follows Single Responsibility Principle - only handles document mapping operations.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentMapper {

    private final JsonProcessingService jsonProcessingService;
    private final DocumentRepoRepository documentRepoRepository;
    private final DocumentClassificationService documentClassificationService;


    /**
     * ✅ DOCUMENT-FILE MAPPING: Where the magic happens!
     * <p>
     * This method implements the exact file-metadata alignment logic:
     * <p>
     * 1. uploadedFiles contains: [{fileIndex: 0, filePath: "path1"}, {fileIndex: 1, filePath: "path2"}]
     * 2. frontendDocuments contains: [{title: "Resume", fileIndex: 0}, {title: "Cover Letter", fileIndex: 1}]
     * 3. We match them: uploadedFile.fileIndex == frontendDoc.fileIndex
     * <p>
     * RESULT:
     * - File at index 0 gets "Resume" title (because both have fileIndex: 0)
     * - File at index 1 gets "Cover Letter" title (because both have fileIndex: 1)
     * <p>
     * ✅ Perfect alignment: first file → first metadata, second file → second metadata!
     *
     * @param frontendDocuments Document metadata from frontend
     * @param uploadedFiles Uploaded file responses with file paths
     * @return JSON string representing the combined document structure
     */
    public String buildDocumentsJsonb(List<DocumentDTO> frontendDocuments,
                                     List<DocumentUploadResponseDTO> uploadedFiles) {

        log.debug("Building documents JSONB with {} frontend docs and {} uploaded files",
                Optional.ofNullable(frontendDocuments).map(List::size).orElse(0), uploadedFiles.size());

        try {
            // ✅ MAPPING EXECUTION: Each uploadedFile finds its matching frontendDocument
            //
            // For each uploadedFile (with fileIndex: 0, 1, 2...):
            // → buildDocumentObject() calls findFrontendDocumentByIndex()
            // → Finds frontendDocument with same fileIndex
            // → Combines: uploadedFile.filePath + frontendDocument.title/category/subcategory
            List<Map<String, Object>> documentObjects = uploadedFiles.stream()
                    .map(uploadedFile -> buildDocumentObject(uploadedFile, frontendDocuments))
                    .collect(Collectors.toList());

            return jsonProcessingService.convertToJsonString(documentObjects);

        } catch (Exception e) {
            log.error("Failed to build documents JSONB: {}", e.getMessage(), e);
            throw DocumentProcessingException.forMetadataProcessing("Failed to process document metadata", e);
        }
    }


    /**
     * ✅ CORE MAPPING LOGIC: Combines file data + metadata by fileIndex
     * <p>
     * Input: uploadedFile = {fileIndex: 0, filePath: "path1", documentId: "uuid1"}
     * Process: findFrontendDocumentByIndex(frontendDocuments, 0)
     * Finds: frontendDoc = {title: "Resume", fileIndex: 0, subcategory: "resume-uuid"}
     * Output: Combined object with file path + metadata
     * <p>
     * This implements the exact loop logic:
     * filter(d -> d.getFileIndex() == i) // We do: d.getFileIndex() == uploadedFile.getFileIndex()
     *
     * @param uploadedFile File upload response with file path and index
     * @param frontendDocuments Document metadata from frontend
     * @return Map representing the combined document object
     */
    private Map<String, Object> buildDocumentObject(DocumentUploadResponseDTO uploadedFile,
                                                   List<DocumentDTO> frontendDocuments) {
        // ✅ THE CRITICAL MAPPING: Find frontend document with same fileIndex as uploaded file
        Optional<DocumentDTO> frontendDoc = findFrontendDocumentByIndex(frontendDocuments, uploadedFile.getFileIndex());

        // ✅ COMBINE DATA: File info (from upload) + Metadata (from frontend)
        Map<String, Object> documentObj = new HashMap<>();
        documentObj.put("id", uploadedFile.getDocumentId());                    // From file upload
        documentObj.put("title", frontendDoc.map(DocumentDTO::getTitle).orElse(uploadedFile.getOriginalFileName())); // From metadata
        documentObj.put("category", frontendDoc.map(DocumentDTO::getCategory));         // From metadata
        documentObj.put("subcategory", frontendDoc.map(DocumentDTO::getSubcategory)); // From metadata (DIFFERENT per doc!)
        documentObj.put("comments", frontendDoc.map(DocumentDTO::getComments)); // From metadata
        documentObj.put("filePath", uploadedFile.getFilePath());               // ✅ From actual file upload (CRITICAL!)
        documentObj.put("fileIndex", uploadedFile.getFileIndex());             // ✅ The bridge that made mapping possible!
        documentObj.put("isActive", frontendDoc.map(DocumentDTO::getIsActive).orElse(true)); // ✅ Include isActive with default true

        return documentObj;
    }

    /**
     * Builds document object from frontend data only (no uploaded files).
     *
     * @param doc Document DTO from frontend
     * @return Map representing the document object
     */
    private Map<String, Object> buildFrontendOnlyDocumentObject(DocumentDTO doc) {
        Map<String, Object> documentObj = new HashMap<>();
        documentObj.put("id", Optional.ofNullable(doc.getId()).orElse(UUID.randomUUID()));
        documentObj.put("title", Optional.ofNullable(doc.getTitle()).orElse(""));
        documentObj.put("category", Optional.ofNullable(doc.getCategory()));
        documentObj.put("subcategory", Optional.ofNullable(doc.getSubcategory()));
        documentObj.put("comments", Optional.ofNullable(doc.getComments()).orElse(""));

        // ✅ FIXED: Preserve existing filePath for existing documents, null for new documents
        String filePath = getExistingFilePath(doc);
        documentObj.put("filePath", filePath);

        documentObj.put("fileIndex", Optional.ofNullable(doc.getFileIndex()).orElse(0));
        documentObj.put("isActive", Optional.ofNullable(doc.getIsActive()).orElse(true)); // ✅ Include isActive with default true

        return documentObj;
    }

    /**
     * ✅ EXACT UNDERSTANDING IMPLEMENTED!
     * <p>
     * This method implements the exact loop logic:
     * <p>
     * Original code: DocumentDTO doc = applicantData.getDocuments().stream()
     * .filter(d -> d.getFileIndex() == i) // ← THIS IS EXACTLY WHAT WE DO!
     * .findFirst()
     * .orElseThrow();
     * <p>
     * Our code: docs.stream()
     * .filter(doc -> fileIndex.equals(doc.getFileIndex())) // ← SAME LOGIC!
     * .findFirst();
     * <p>
     * MAPPING EXAMPLES:
     * ================
     * <p>
     * Call: findFrontendDocumentByIndex(documents, 0)
     * Finds: {title: "Resume", fileIndex: 0, subcategory: "resume-uuid"}
     * <p>
     * Call: findFrontendDocumentByIndex(documents, 1)
     * Finds: {title: "Cover Letter", fileIndex: 1, subcategory: "cover-letter-uuid"}
     * <p>
     * ✅ RESULT: Perfect file-metadata alignment by index matching!
     *
     * @param frontendDocuments List of document metadata from frontend
     * @param fileIndex File index to match
     * @return Optional containing the matching document, or empty if not found
     */
    private Optional<DocumentDTO> findFrontendDocumentByIndex(List<DocumentDTO> frontendDocuments, Integer fileIndex) {
        return Optional.ofNullable(frontendDocuments)
                .filter(docs -> !docs.isEmpty())
                .filter(docs -> fileIndex != null)
                .flatMap(docs -> docs.stream()
                        .filter(doc -> fileIndex.equals(doc.getFileIndex())) // ✅ EXACT LOGIC: d.getFileIndex() == i
                        .findFirst());
    }

    /**
     * Builds documents JSONB for metadata-only operations (no file uploads).
     * Used for document updates that don't involve new file uploads.
     *
     * @param frontendDocuments Document metadata from frontend
     * @return JSON string representing the document structure
     */
    public String buildDocumentsJsonbFromMetadataOnly(List<DocumentDTO> frontendDocuments) {
        log.debug("Building documents JSONB from metadata only with {} documents",
                Optional.ofNullable(frontendDocuments).map(List::size).orElse(0));

        if (CollectionUtils.isEmpty(frontendDocuments)) {
            return "[]";
        }

        try {
            // ✅ FIXED: Keep ALL documents in JSONB including those with isActive=false (soft delete requirement)
            // Documents are soft deleted in document_repo table but must remain in applicant's JSONB with status
            List<Map<String, Object>> documentObjects = frontendDocuments.stream()
                    .map(this::buildFrontendOnlyDocumentObject)
                    .collect(Collectors.toList());

            return jsonProcessingService.convertToJsonString(documentObjects);

        } catch (Exception e) {
            log.error("Failed to build documents JSONB from metadata: {}", e.getMessage(), e);
            throw DocumentProcessingException.forMetadataProcessing("Failed to process document metadata", e);
        }
    }

    /**
     * ✅ NEW METHOD: Builds documents JSONB for mixed scenarios (existing + new documents).
     * Used in PUT endpoint to handle both existing documents and new file uploads.
     *
     * @param frontendDocuments Document metadata from frontend
     * @param uploadedFiles New file uploads (for new documents only)
     * @return JSON string representing the combined document structure
     */
    public String buildDocumentsJsonbMixed(List<DocumentDTO> frontendDocuments,
                                          List<DocumentUploadResponseDTO> uploadedFiles) {
        log.debug("Building mixed documents JSONB with {} frontend docs and {} uploaded files",
                Optional.ofNullable(frontendDocuments).map(List::size).orElse(0),
                Optional.ofNullable(uploadedFiles).map(List::size).orElse(0));

        if (CollectionUtils.isEmpty(frontendDocuments)) {
            return "[]";
        }

        try {
            List<Map<String, Object>> documentObjects = new ArrayList<>();

            // ✅ DEBUG: Log all uploaded files for debugging
            log.info("🔍 DEBUG: buildDocumentsJsonbMixed - uploadedFiles details:");
            uploadedFiles.forEach(uploaded ->
                log.info("🔍 DEBUG: Uploaded file - documentId={}, fileIndex={}, filePath={}, originalFileName={}",
                        uploaded.getDocumentId(), uploaded.getFileIndex(), uploaded.getFilePath(), uploaded.getOriginalFileName()));

            for (DocumentDTO doc : frontendDocuments) {
                log.info("🔍 DEBUG: Processing document: id={}, title={}, fileIndex={}, filePath={}, isActive={}",
                         doc.getId(), doc.getTitle(), doc.getFileIndex(), doc.getFilePath(), doc.getIsActive());

                boolean isNew = isNewDocument(doc);
                log.info("🔍 DEBUG: Document isNewDocument check result: {} for document with id={}, filePath={}",
                        isNew, doc.getId(), doc.getFilePath());

                if (isNew) {
                    log.info("🔍 DEBUG: Document identified as NEW document - looking for uploaded file with fileIndex: {}", doc.getFileIndex());

                    // New document - find corresponding uploaded file
                    DocumentUploadResponseDTO uploadedFile = findUploadedFileByIndex(uploadedFiles, doc.getFileIndex());
                    if (uploadedFile != null) {
                        log.info("🔍 DEBUG: ✅ FOUND uploaded file for fileIndex {}: documentId={}, filePath={}",
                                 doc.getFileIndex(), uploadedFile.getDocumentId(), uploadedFile.getFilePath());

                        // Build document object with uploaded file data
                        Map<String, Object> documentObj = buildDocumentObject(uploadedFile, List.of(doc));
                        documentObjects.add(documentObj);

                        log.info("🔍 DEBUG: ✅ Created document object with filePath: {}", documentObj.get("filePath"));
                    } else {
                        log.error("🔍 DEBUG: ❌ NO uploaded file found for new document with fileIndex: {} - available uploaded files: {}",
                                doc.getFileIndex(),
                                uploadedFiles.stream().map(f -> "fileIndex:" + f.getFileIndex()).collect(java.util.stream.Collectors.toList()));
                        // Still add the document but with null filePath
                        Map<String, Object> documentObj = buildFrontendOnlyDocumentObject(doc);
                        documentObjects.add(documentObj);
                        log.error("🔍 DEBUG: ❌ Added document with NULL filePath: {}", documentObj.get("filePath"));
                    }
                } else {
                    log.info("🔍 DEBUG: Document identified as EXISTING document - preserving existing filePath: {}", doc.getFilePath());
                    // Existing document - preserve existing filePath
                    Map<String, Object> documentObj = buildFrontendOnlyDocumentObject(doc);
                    documentObjects.add(documentObj);
                    log.info("🔍 DEBUG: Added existing document with filePath: {}", documentObj.get("filePath"));
                }
            }

            String result = jsonProcessingService.convertToJsonString(documentObjects);
            log.info("🔍 DEBUG: Final JSONB result: {}", result);
            return result;

        } catch (Exception e) {
            log.error("Failed to build mixed documents JSONB: {}", e.getMessage(), e);
            throw DocumentProcessingException.forMetadataProcessing("Failed to process mixed document metadata", e);
        }
    }

    /**
     * ✅ OPTIMIZED: Determines if a document is new (needs file upload).
     * Now uses DocumentClassificationService for efficient batch processing.
     *
     * @param doc Document DTO from frontend
     * @return true if document is new, false if existing
     */
    private boolean isNewDocument(DocumentDTO doc) {
        // ✅ PERFORMANCE FIX: Use optimized classification service
        return documentClassificationService.isNewDocument(doc);
    }

    /**
     * ✅ HELPER METHOD: Finds uploaded file by fileIndex.
     *
     * @param uploadedFiles List of uploaded files
     * @param fileIndex File index to search for
     * @return Matching uploaded file or null if not found
     */
    private DocumentUploadResponseDTO findUploadedFileByIndex(List<DocumentUploadResponseDTO> uploadedFiles, Integer fileIndex) {
        if (CollectionUtils.isEmpty(uploadedFiles) || fileIndex == null) {
            return null;
        }

        return uploadedFiles.stream()
                .filter(file -> fileIndex.equals(file.getFileIndex()))
                .findFirst()
                .orElse(null);
    }

    /**
     * ✅ FIXED: Gets existing filePath for documents, preserving filePath for soft-deleted documents.
     * Priority: 1) Use filePath from frontend DTO (preserves existing path for soft deletes)
     *          2) Query database only if DTO has no filePath
     *          3) Return null for truly new documents
     *
     * @param doc Document DTO from frontend
     * @return Existing filePath from DTO or database, or null for new documents
     */
    private String getExistingFilePath(DocumentDTO doc) {
        // If document has no ID, it's a new document
        if (doc.getId() == null) {
            log.debug("Document has no ID, treating as new document");
            return null;
        }

        // ✅ FIX: First check if the DTO already contains a filePath (preserves soft delete filePath)
        if (doc.getFilePath() != null && !doc.getFilePath().trim().isEmpty()) {
            log.debug("Using existing filePath from DTO for document ID: {} - filePath: {}",
                     doc.getId(), doc.getFilePath());
            return doc.getFilePath();
        }

        // Only query database if DTO doesn't have filePath
        try {
            // Try to fetch existing document from database
            DocumentRepo existingDocument = documentRepoRepository.getDocumentByID(doc.getId());

            if (existingDocument != null && existingDocument.getPath() != null) {
                log.debug("Found existing document with filePath from database: {} for ID: {}",
                         existingDocument.getPath(), doc.getId());
                return existingDocument.getPath();
            } else {
                log.debug("Document not found in database or has null path, treating as new document: {}", doc.getId());
                return null;
            }

        } catch (Exception e) {
            log.warn("Error retrieving existing document path for ID: {} - treating as new document: {}",
                    doc.getId(), e.getMessage());
            return null;
        }
    }
}
