package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.applicant.ApplicantRepository;
import com.chidhagni.ats.utils.LoggingUtil;
import com.chidhagni.exception.DuplicateApplicantException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * Simplified applicant validator using standard Bean Validation.
 * Replaces the over-engineered validation strategy pattern with simple, effective validation.
 * Uses industry-standard JSR-303 Bean Validation for basic field validation.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SimplifiedApplicantValidator implements ApplicantValidator {

    private final ApplicantRepository applicantRepository;

    /**
     * Validates email uniqueness globally (organization ID is now optional).
     *
     * @param email Email to validate
     * @param orgId Organization ID (optional - can be null)
     * @throws DuplicateApplicantException if email already exists
     */
    public void validateEmailUniqueness(String email, UUID orgId) {
        LoggingUtil.logValidation("email uniqueness", "started");

        if (applicantRepository.existsByEmail(email)) {
            LoggingUtil.logDuplicateEntity("applicant", email);
            throw DuplicateApplicantException.forEmail(email);
        }

        LoggingUtil.logValidation("email uniqueness", "passed");
    }

}
