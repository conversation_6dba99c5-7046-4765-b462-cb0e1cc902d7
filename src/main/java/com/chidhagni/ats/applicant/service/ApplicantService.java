package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.applicant.dto.request.ApplicantUpdateRequestDTO;
import com.chidhagni.ats.applicant.dto.request.GetAllApplicantsRequestDTO;
import com.chidhagni.ats.applicant.dto.response.ActivateDeactivateResponseDTO;
import com.chidhagni.ats.applicant.dto.response.ApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.CreateApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.GetAllApplicantsResponseDTO;
import com.chidhagni.ats.applicant.dto.response.UpdateApplicantResponseDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for Applicant management operations.
 * Provides contract for applicant CRUD operations and business logic.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ApplicantService {

    /**
     * Retrieves complete applicant details by ID.
     *
     * @param applicantId The UUID of the applicant to retrieve
     * @return ApplicantResponseDTO containing complete applicant information
     */
    ApplicantResponseDTO getApplicantById(UUID applicantId);

    /**
     * Activates an applicant by setting isActive to true.
     *
     * @param applicantId The UUID of the applicant to activate
     * @return ActivateDeactivateResponseDTO indicating the result
     */
    ActivateDeactivateResponseDTO activateApplicant(UUID applicantId);

    /**
     * Deactivates an applicant by setting isActive to false.
     *
     * @param applicantId The UUID of the applicant to deactivate
     * @return ActivateDeactivateResponseDTO indicating the result
     */
    ActivateDeactivateResponseDTO deactivateApplicant(UUID applicantId);

    /**
     * Retrieves all applicants with filtering, searching, and pagination.
     *
     * @param request The request containing filter criteria and pagination parameters
     * @return GetAllApplicantsResponseDTO containing paginated applicant list and metadata
     */
    GetAllApplicantsResponseDTO getAllApplicants(GetAllApplicantsRequestDTO request);

    /**
     * Creates an applicant with optional file uploads.
     *
     * @param requestDTO Applicant data to create
     * @param files Optional files to upload
     * @return CreateApplicantResponseDTO containing creation result
     */
    CreateApplicantResponseDTO createApplicant(ApplicantRequestDTO requestDTO, List<MultipartFile> files);

    /**
     * Updates an existing applicant with optional file uploads and document management.
     * Supports partial updates, document additions, and soft deletions.
     *
     * @param applicantId The UUID of the applicant to update
     * @param requestDTO Applicant update data (all fields optional for partial updates)
     * @param files Optional files to upload for new documents
     * @return UpdateApplicantResponseDTO containing update result and document operation counts
     */
    UpdateApplicantResponseDTO updateApplicant(UUID applicantId, ApplicantUpdateRequestDTO requestDTO, List<MultipartFile> files);
}
