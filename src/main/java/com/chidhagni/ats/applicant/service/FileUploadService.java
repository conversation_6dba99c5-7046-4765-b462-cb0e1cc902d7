package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.documentrepo.DocumentRepoService;
import com.chidhagni.ats.documentrepo.dto.request.DocumentRepoDTO;
import com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO;
import com.chidhagni.ats.documentrepo.dto.response.DocumentUploadResponseDTO;
import com.chidhagni.exception.ValidationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service responsible for handling file upload operations.
 * Follows Single Responsibility Principle - only handles file upload logic.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileUploadService {

    private final DocumentRepoService documentRepoService;


    /**
     * Handles file uploads and returns complete metadata.
     * Industry Best Practice: Return all necessary data to avoid additional queries.
     *
     * ✅ FIXED: Now creates individual DocumentRepoDTO for each file with correct category/subcategory
     *
     * @param files List of files to upload
     * @param requestDTO Applicant request data for building document metadata
     * @return List of uploaded document responses with metadata
     * @throws ValidationException if file upload fails
     */
    public List<DocumentUploadResponseDTO> uploadFilesWithMetadata(List<MultipartFile> files,
                                                                  ApplicantRequestDTO requestDTO) {
        log.info("Processing {} file uploads with metadata", files.size());

        try {
            // ✅ FIXED: Upload files with individual metadata for each file
            List<DocumentUploadResponseDTO> uploadedDocuments =
                documentRepoService.uploadMultipleFilesWithIndividualMetadata(files, requestDTO);

            log.info("Successfully uploaded {} files with complete metadata", uploadedDocuments.size());
            return uploadedDocuments;

        } catch (Exception e) {
            log.error("Failed to upload files with metadata: {}", e.getMessage(), e);
            throw new ValidationException("File upload failed: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes uploaded files for compensation in case of transaction rollback.
     * Used to cleanup files when database operations fail after successful uploads.
     *
     * @param uploadedDocuments List of uploaded documents to delete
     */
    public void deleteFiles(List<DocumentUploadResponseDTO> uploadedDocuments) {
        if (uploadedDocuments == null || uploadedDocuments.isEmpty()) {
            log.debug("No files to delete");
            return;
        }

        log.info("Deleting {} uploaded files for compensation", uploadedDocuments.size());

        for (DocumentUploadResponseDTO document : uploadedDocuments) {
            try {
                if (document.getDocumentId() != null) {
                    documentRepoService.deleteDocument(document.getDocumentId());
                    log.debug("Successfully deleted file with document ID: {}", document.getDocumentId());
                }
            } catch (Exception e) {
                log.error("Failed to delete file with document ID: {} - {}", 
                         document.getDocumentId(), e.getMessage(), e);
                // Continue with other deletions
            }
        }

        log.info("Completed file deletion compensation for {} files", uploadedDocuments.size());
    }

    /**
     * Builds DocumentRepoDTO for file upload service.
     * Encapsulates the logic for creating document repository metadata.
     *
     * @param requestDTO Applicant request data
     * @return DocumentRepoDTO for file upload
     */
    private DocumentRepoDTO buildDocumentRepoDTO(ApplicantRequestDTO requestDTO) {
        // Build sender details from request
        ParticipantDetailsDTO senderDetails = ParticipantDetailsDTO.builder()
                .individualName(requestDTO.getFirstName() + " " + requestDTO.getLastName())
                .email(requestDTO.getEmail())
                .build();

        // Build recipient details (system/organization)
        ParticipantDetailsDTO recipientDetails = ParticipantDetailsDTO.builder()
                .organisationId(requestDTO.getOrgId())
                .organisationName("")
                .build();

        return DocumentRepoDTO.builder()
                .category(requestDTO.getDocuments().getFirst().getCategory())
                .senderDetails(senderDetails)
                .recipientDetails(List.of(recipientDetails))
                .remarks(requestDTO.getDocuments().getFirst().getComments())
                .fileDate(LocalDateTime.now())
                .createdOn(LocalDateTime.now())
                .updatedOn(LocalDateTime.now())
                .build();
    }
}
