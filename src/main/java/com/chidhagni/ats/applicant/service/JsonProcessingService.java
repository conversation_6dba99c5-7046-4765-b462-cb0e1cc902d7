package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.utils.JsonProcessingUtil;
import com.chidhagni.exception.DocumentProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service responsible for JSON processing operations.
 * Extracted from DocumentMapper to follow Single Responsibility Principle.
 * Provides centralized, reusable JSON processing functionality.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class JsonProcessingService {

    private final JsonProcessingUtil jsonProcessingUtil;

    /**
     * Converts a list of objects to JSON string with proper error handling.
     * 
     * @param objectList List of objects to convert
     * @return JSON string representation
     * @throws DocumentProcessingException if JSON conversion fails
     */
    public String convertToJsonString(List<Map<String, Object>> objectList) {
        try {
            log.debug("Converting {} objects to JSON string", objectList.size());
            String jsonResult = jsonProcessingUtil.mapListToJsonString(objectList);
            log.debug("Successfully converted objects to JSON string with {} characters", jsonResult.length());
            return jsonResult;
            
        } catch (Exception e) {
            log.error("Failed to convert objects to JSON: {}", e.getMessage(), e);
            throw DocumentProcessingException.forJsonProcessing("convert objects to JSON", e);
        }
    }
}
