package com.chidhagni.ats.applicant.service;

import com.chidhagni.ats.applicant.ApplicantRepository;
import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.applicant.dto.request.ApplicantUpdateRequestDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.applicant.dto.request.GetAllApplicantsRequestDTO;
import com.chidhagni.ats.applicant.dto.response.ActivateDeactivateResponseDTO;
import com.chidhagni.ats.applicant.dto.response.ApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.CreateApplicantResponseDTO;
import com.chidhagni.ats.applicant.dto.response.GetAllApplicantsResponseDTO;
import com.chidhagni.ats.applicant.dto.response.UpdateApplicantResponseDTO;
import com.chidhagni.ats.applicant.utils.ApplicantMapper;
import com.chidhagni.ats.applicant.utils.DocumentUpdateProcessor;
import com.chidhagni.ats.db.jooq.tables.pojos.Applicant;
import com.chidhagni.ats.documentrepo.DocumentRepoRepository;
import com.chidhagni.ats.documentrepo.dto.response.DocumentUploadResponseDTO;
import com.chidhagni.ats.logging.SecureLogger;
import com.chidhagni.ats.utils.JsonProcessingUtil;
import com.chidhagni.ats.utils.LoggingUtil;
import com.chidhagni.exception.EntityNotFoundException;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.chidhagni.ats.applicant.constants.ApplicantMetaData.*;

/**
 * Implementation of ApplicantService interface.
 * Provides business logic for applicant management operations.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ApplicantServiceImpl implements ApplicantService {

    private final ApplicantRepository applicantRepository;
    private final ApplicantMapper applicantMapper;
    private final JsonProcessingUtil jsonProcessingUtil;
    
    // Focused services following Single Responsibility Principle
    private final FileUploadService fileUploadService;
    private final DocumentMapper documentMapper;
    private final SimplifiedApplicantValidator applicantValidator;
    private final DocumentRepoRepository documentRepoRepository;
    private final DocumentUpdateProcessor documentUpdateProcessor;
    private final DocumentClassificationService documentClassificationService;
    private final SecureLogger secureLogger;

    @Override
    public ApplicantResponseDTO getApplicantById(UUID applicantId) {
        LoggingUtil.logOperationStart("retrieve applicant details", applicantId);

        // Validate applicant exists and retrieve
        Applicant applicant = getApplicantOrThrow(applicantId);

        // Map to response DTO
        ApplicantResponseDTO response = applicantMapper.applicantToApplicantDetailResponse(applicant);

        LoggingUtil.logOperationSuccess("retrieve applicant details", applicantId);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivateDeactivateResponseDTO activateApplicant(UUID applicantId) {
        return changeApplicantStatus(applicantId, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ActivateDeactivateResponseDTO deactivateApplicant(UUID applicantId) {
        return changeApplicantStatus(applicantId, false);
    }

    @Override
    @Transactional(readOnly = true)
    public GetAllApplicantsResponseDTO getAllApplicants(GetAllApplicantsRequestDTO request) {
        LoggingUtil.logOperationWithContext("Retrieve all applicants", 
                String.format("page=%d, pageSize=%d, status=%s, search=%s",
                        request.getPage(), request.getPageSize(), request.getStatus(), request.getSearch()));

        // Retrieve applicants with filters and pagination
        List<Applicant> applicants = applicantRepository.findAllWithFilters(request);

        // Get total count for pagination metadata
        int totalCount = applicantRepository.countWithFilters(request);

        // Map to response DTOs
        List<ApplicantResponseDTO> applicantDTOs = applicants.stream()
                .map(applicantMapper::applicantToApplicantDetailResponse)
                .toList();

        // Calculate pagination metadata
        int totalPages = (int) Math.ceil((double) totalCount / request.getPageSize());
        boolean hasNextPage = request.getPage() < totalPages;
        boolean hasPreviousPage = request.getPage() > 1;

        // Build response
        GetAllApplicantsResponseDTO response = GetAllApplicantsResponseDTO.builder()
                .applicants(applicantDTOs)
                .totalCount(totalCount)
                .currentPage(request.getPage())
                .pageSize(request.getPageSize())
                .totalPages(totalPages)
                .hasNextPage(hasNextPage)
                .hasPreviousPage(hasPreviousPage)
                .build();

        LoggingUtil.logOperationWithContext("Retrieved applicants", 
                String.format("%d out of %d total", applicantDTOs.size(), totalCount));

        return response;
    }

    @Override
    public CreateApplicantResponseDTO createApplicant(ApplicantRequestDTO requestDTO, List<MultipartFile> files) {
        LoggingUtil.logOperationStart("applicant creation", null);
        
        // 1. Validate applicant data using dedicated validator (OUTSIDE transaction)
        applicantValidator.validateEmailUniqueness(requestDTO.getEmail(), requestDTO.getOrgId());

        // 2. Handle file uploads OUTSIDE transaction (avoid holding DB connections during I/O)
        List<DocumentUploadResponseDTO> uploadedDocuments = List.of();
        String documentsJsonb = "[]";
        
        if (!CollectionUtils.isEmpty(files)) {
            LoggingUtil.logFileOperation("Processing file uploads with metadata", files.size());
            uploadedDocuments = fileUploadService.uploadFilesWithMetadata(files, requestDTO);
            documentsJsonb = documentMapper.buildDocumentsJsonb(requestDTO.getDocuments(), uploadedDocuments);
        }

        try {
            // 3. Database operation in focused transaction
            return createApplicantRecord(requestDTO, documentsJsonb, uploadedDocuments);
            
        } catch (Exception e) {
            // 4. Compensate - cleanup uploaded files if database operation fails
            if (!uploadedDocuments.isEmpty()) {
                LoggingUtil.logOperationWithContext("Compensating file uploads", 
                        "Database operation failed, cleaning up " + uploadedDocuments.size() + " uploaded files");
                try {
                    fileUploadService.deleteFiles(uploadedDocuments);
                    LoggingUtil.logOperationSuccess("file cleanup compensation", null);
                } catch (Exception cleanupException) {
                    log.error("Failed to cleanup uploaded files after database failure", cleanupException);
                    // Continue with original exception
                }
            }
            throw e;
        }
    }

    /**
     * Creates applicant record in database within a focused transaction.
     * This method only handles database operations to keep transaction scope minimal.
     * 
     * @param requestDTO Applicant request data
     * @param documentsJsonb Pre-built documents JSONB string
     * @param uploadedDocuments Uploaded document information for logging
     * @return CreateApplicantResponseDTO
     */
    @Transactional(rollbackFor = Exception.class)
    private CreateApplicantResponseDTO createApplicantRecord(ApplicantRequestDTO requestDTO, 
                                                           String documentsJsonb,
                                                           List<DocumentUploadResponseDTO> uploadedDocuments) {
        LoggingUtil.logOperationStart("database transaction", null);
        
        // Build applicant entity with complete data
        Applicant applicant = buildCompleteApplicant(requestDTO, documentsJsonb);

        log.debug("Built complete applicant entity for persistence");
        
        // Save applicant with all data in focused transaction
        Applicant savedApplicant = applicantRepository.createApplicant(applicant);
        log.info("Applicant created with ID: {}, system code: {}, documents: {}",
                savedApplicant.getId(), savedApplicant.getSystemCode(), uploadedDocuments.size());

        // Create response
        CreateApplicantResponseDTO response = CreateApplicantResponseDTO.builder()
                .id(savedApplicant.getId())
                .message(APPLICANT_CREATED_SUCCESSFULLY)
                .success(true)
                .build();

        LoggingUtil.logOperationSuccess("applicant creation", savedApplicant.getId());
        return response;
    }

    /**
     * Helper method to change the applicant's active status.
     * Centralizes activation/deactivation logic to avoid repetition.
     *
     * @param applicantId  The UUID of the applicant
     * @param targetStatus true for activation, false for deactivation
     * @return Response object corresponding to the operation
     */
    private ActivateDeactivateResponseDTO changeApplicantStatus(UUID applicantId, boolean targetStatus) {
        // Validate applicant exists and retrieve
        Applicant applicant = getApplicantOrThrow(applicantId);

        // Idempotent check using Optional for null safety
        boolean currentStatus = Optional.ofNullable(applicant.getIsActive()).orElse(false);
        if (currentStatus == targetStatus) {
            log.debug("Applicant {} is already {}", applicantId, targetStatus ? ACTIVE : INACTIVE);
            return buildResponse(targetStatus, true);
        }

        // Update status
        applicantRepository.updateStatus(applicantId, targetStatus);
        log.info("Successfully {} applicant with ID: {}", targetStatus ? LOG_ACTIVATE : LOG_DEACTIVATE, applicantId);

        return buildResponse(targetStatus, true);
    }

    /**
     * Builds response for activate/deactivate operations.
     *
     * @param isActive true if activation, false if deactivation
     * @param success  Operation success status
     * @return Response DTO
     */
    private ActivateDeactivateResponseDTO buildResponse(boolean isActive, boolean success) {
        String message = isActive ? APPLICANT_ACTIVATED_SUCCESSFULLY : APPLICANT_DEACTIVATED_SUCCESSFULLY;

        return ActivateDeactivateResponseDTO.builder()
                .success(success)
                .message(message)
                .build();
    }

    /**
     * Helper method to retrieve an applicant by ID or throw EntityNotFoundException.
     *
     * @param applicantId The UUID of the applicant to retrieve
     * @return Applicant entity
     * @throws EntityNotFoundException if applicant is not found
     */
    private Applicant getApplicantOrThrow(UUID applicantId) {
        return Optional.ofNullable(applicantRepository.findById(applicantId))
                .orElseThrow(() -> {
                    log.warn("Applicant not found for ID: {}", applicantId);
                    return new EntityNotFoundException(APPLICANT_NOT_FOUND + ": " + applicantId);
                });
    }

    /**
     * Consolidated mapping: All field mapping in one place using ApplicantMapper
     * Industry Best Practice: Single Responsibility - Mapper handles all field mapping
     * Database-First Approach: All data prepared before database operation
     */
    private Applicant buildCompleteApplicant(ApplicantRequestDTO requestDTO, String documentsJsonb) {
        // Use consolidated mapper method that handles ALL field mapping including system code generation
        Applicant applicant = applicantMapper.toCompleteEntity(requestDTO);

        // Set documents JSONB field with complete file metadata
        if (documentsJsonb != null && !documentsJsonb.isEmpty()) {
            try {
                // Use JsonProcessingUtil for consistent JSON handling
                List<DocumentDTO> dto = jsonProcessingUtil.fromJsonString(documentsJsonb, new TypeReference<List<DocumentDTO>>() {});

                // Set the deserialized DTO list to the applicant entity
                applicant.setDocuments(dto);

                log.debug("Successfully set documents JSONB with {} documents", dto.size());

            } catch (Exception e) {
                log.error("Failed to deserialize documents JSONB: {}", e.getMessage(), e);
                log.debug("Problematic JSONB content: {}", documentsJsonb);
                // Set empty list as fallback to prevent null pointer issues
                applicant.setDocuments(new ArrayList<>());
            }
        } else {
            // Set empty list if no documents provided
            applicant.setDocuments(new ArrayList<>());
        }

        log.debug("Built complete applicant with {} characters of document metadata",
                documentsJsonb != null ? documentsJsonb.length() : 0);

        return applicant;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UpdateApplicantResponseDTO updateApplicant(UUID applicantId, ApplicantUpdateRequestDTO requestDTO, List<MultipartFile> files) {
        LoggingUtil.logOperationStart("applicant update", applicantId);

        // 1. Validate applicant exists
        Applicant existingApplicant = getApplicantOrThrow(applicantId);

        // 3. Handle document operations using optimized processor
        DocumentUpdateProcessor.DocumentProcessingResult documentResult =
            documentUpdateProcessor.processDocumentUpdates(requestDTO.getDocuments());
        int documentsDeleted = documentResult.getDeletedCount();

        // 4. ✅ ENHANCED: Handle mixed scenario - existing documents + new file uploads
        List<DocumentUploadResponseDTO> uploadedDocuments = List.of();
        String documentsJsonb = null;

        if (documentResult.hasDocuments()) {
            if (!CollectionUtils.isEmpty(files)) {
                LoggingUtil.logFileOperation("Processing mixed document updates with file uploads", files.size());

                // ✅ NEW APPROACH: Only upload files for new documents
                List<DocumentDTO> newDocuments = filterNewDocuments(documentResult.getActiveDocuments());

                // ✅ DEBUG: Log filtering results
                log.info("🔍 DEBUG: Document filtering results:");
                log.info("🔍 DEBUG: Total active documents: {}", documentResult.getActiveDocuments().size());
                log.info("🔍 DEBUG: Filtered new documents: {}", newDocuments.size());
                documentResult.getActiveDocuments().forEach(doc ->
                    log.info("🔍 DEBUG: Active document - id={}, title={}, fileIndex={}, filePath={}, isActive={}, isNew={}",
                            doc.getId(), doc.getTitle(), doc.getFileIndex(), doc.getFilePath(), doc.getIsActive(), isNewDocument(doc)));
                newDocuments.forEach(doc ->
                    log.info("🔍 DEBUG: NEW document identified - id={}, title={}, fileIndex={}, filePath={}",
                            doc.getId(), doc.getTitle(), doc.getFileIndex(), doc.getFilePath()));

                if (!newDocuments.isEmpty()) {
                    // Create request DTO with only new documents for file upload
                    ApplicantRequestDTO baseRequest = convertUpdateToCreateRequest(requestDTO, existingApplicant);
                    ApplicantRequestDTO fileUploadRequest = ApplicantRequestDTO.builder()
                            .orgId(baseRequest.getOrgId())
                            .firstName(baseRequest.getFirstName())
                            .lastName(baseRequest.getLastName())
                            .email(baseRequest.getEmail())
                            .documents(newDocuments)
                            .build();

                    uploadedDocuments = fileUploadService.uploadFilesWithMetadata(files, fileUploadRequest);
                    log.info("Uploaded {} files for new documents", uploadedDocuments.size());

                    // ✅ SECURE LOGGING: Log uploaded documents with PII masking
                    uploadedDocuments.forEach(uploaded ->
                        secureLogger.logFileUpload(uploaded.getDocumentId(), uploaded.getOriginalFileName(),
                                uploaded.getFileSize(), secureLogger.maskFilePath(uploaded.getFilePath())));
                }

                // ✅ SECURE LOGGING: Log document processing with PII masking
                log.info("Building JSONB with {} frontend documents and {} uploaded files",
                        documentResult.getActiveDocuments().size(), uploadedDocuments.size());
                documentResult.getActiveDocuments().forEach(doc ->
                    log.debug("Processing document: id={}, fileIndex={}, hasFilePath={}, isActive={}",
                            doc.getId(), doc.getFileIndex(), doc.getFilePath() != null, doc.getIsActive()));

                // ✅ FIXED: Use mixed approach to handle both existing and new documents
                documentsJsonb = documentMapper.buildDocumentsJsonbMixed(documentResult.getActiveDocuments(), uploadedDocuments);

            } else {
                // Handle document metadata updates without file uploads (existing approach)
                documentsJsonb = documentMapper.buildDocumentsJsonbFromMetadataOnly(documentResult.getActiveDocuments());
            }
        }

        try {
            // 5. Update applicant record
            Applicant updatedApplicant = updateApplicantRecord(applicantId, requestDTO, documentsJsonb, existingApplicant);

            // 6. Build response
            UpdateApplicantResponseDTO response = UpdateApplicantResponseDTO.builder()
                    .id(updatedApplicant.getId())
                    .message(APPLICANT_UPDATED_SUCCESSFULLY)
                    .success(true)
                    .documentsAdded(uploadedDocuments.size())
                    .documentsDeleted(documentsDeleted)
                    .updatedAt(updatedApplicant.getUpdatedOn())
                    .build();

            // ✅ SECURE LOGGING: Log successful update with secure information
            secureLogger.logApplicantUpdate(applicantId, uploadedDocuments.size() + documentsDeleted);
            LoggingUtil.logOperationSuccess("applicant update", applicantId);
            return response;

        } catch (Exception e) {
            // Cleanup uploaded files on failure
            if (!uploadedDocuments.isEmpty()) {
                LoggingUtil.logFileOperation("Cleaning up uploaded files due to update failure", uploadedDocuments.size());
                uploadedDocuments.forEach(doc -> {
                    try {
                        documentRepoRepository.deleteDocument(doc.getDocumentId());
                    } catch (Exception cleanupEx) {
                        log.warn("Failed to cleanup uploaded document: {}", doc.getDocumentId(), cleanupEx);
                    }
                });
            }
            throw e;
        }
    }

    // NOTE: handleDocumentDeletions method removed - now using optimized DocumentUpdateProcessor

    /**
     * Converts ApplicantUpdateRequestDTO to ApplicantRequestDTO for file upload service compatibility.
     * Uses existing applicant data as fallback for required fields.
     */
    private ApplicantRequestDTO convertUpdateToCreateRequest(ApplicantUpdateRequestDTO updateRequest, Applicant existingApplicant) {
        return ApplicantRequestDTO.builder()
                .firstName(updateRequest.getFirstName() != null ? updateRequest.getFirstName() : existingApplicant.getFirstName())
                .lastName(updateRequest.getLastName() != null ? updateRequest.getLastName() : existingApplicant.getLastName())
                .email(updateRequest.getEmail() != null ? updateRequest.getEmail() : existingApplicant.getEmail())
                .orgId(existingApplicant.getOrgId()) // Use existing org ID
                .documents(updateRequest.getDocuments())
                .build();
    }

    /**
     * Updates the applicant record with the provided data.
     * OPTIMIZED VERSION: Uses MapStruct with NullValuePropertyMappingStrategy.IGNORE
     * following the ProjectHouzer pattern for automatic null handling.
     * This eliminates manual null checks and provides better maintainability.
     */
    private Applicant updateApplicantRecord(UUID applicantId, ApplicantUpdateRequestDTO requestDTO,
                                          String documentsJsonb, Applicant existingApplicant) {

        // Handle documents update in the DTO before passing to repository
        if (documentsJsonb != null) {
            try {
                List<DocumentDTO> updatedDocuments = jsonProcessingUtil.parseJsonToList(
                    documentsJsonb, new TypeReference<List<DocumentDTO>>() {});
                requestDTO.setDocuments(updatedDocuments);
                log.debug("Successfully parsed and set {} updated documents in request DTO", updatedDocuments.size());
            } catch (Exception e) {
                log.error("Failed to parse updated documents JSONB: {}", e.getMessage(), e);
                // Keep existing documents on parse failure for data integrity
                requestDTO.setDocuments(existingApplicant.getDocuments());
            }
        }

        // TODO: Set updatedBy in requestDTO once UserPrincipal is available
        // requestDTO.setUpdatedBy(userPrincipal.getUserId());

        log.debug("Calling repository update with optimized MapStruct approach");
        return applicantRepository.updateApplicant(applicantId, requestDTO);
    }

    /**
     * ✅ HELPER METHOD: Filters documents to identify new ones that need file upload.
     * A document is considered new if it has no ID or if it's not found in the database.
     *
     * @param documents List of all documents from the request
     * @return List of new documents that need file upload
     */
    private List<DocumentDTO> filterNewDocuments(List<DocumentDTO> documents) {
        if (CollectionUtils.isEmpty(documents)) {
            return List.of();
        }

        return documents.stream()
                .filter(this::isNewDocument)
                .collect(Collectors.toList());
    }

    /**
     * ✅ OPTIMIZED: Determines if a document is new (needs file upload).
     * Now uses DocumentClassificationService for efficient batch processing.
     *
     * @param doc Document DTO from frontend
     * @return true if document is new, false if existing
     */
    private boolean isNewDocument(DocumentDTO doc) {
        // ✅ PERFORMANCE FIX: Use optimized classification service
        return documentClassificationService.isNewDocument(doc);
    }
}
