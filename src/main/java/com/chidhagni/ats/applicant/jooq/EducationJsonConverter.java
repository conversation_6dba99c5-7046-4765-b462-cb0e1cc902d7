package com.chidhagni.ats.applicant.jooq;

import com.chidhagni.ats.applicant.dto.request.EducationDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class EducationJsonConverter extends BaseJsonBJooqConverter<List<EducationDTO>> {
    @Override
    @SuppressWarnings("unchecked")
    public @NotNull Class<List<EducationDTO>> toType() {
        // This won't be used due to TypeReference override, but kept for interface compliance
        return (Class<List<EducationDTO>>) (Class<?>) List.class;
    }
    
    @Override
    protected TypeReference<List<EducationDTO>> getTypeReference() {
        return new TypeReference<List<EducationDTO>>() {};
    }
}