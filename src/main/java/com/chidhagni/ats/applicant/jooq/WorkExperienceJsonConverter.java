package com.chidhagni.ats.applicant.jooq;

import com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class WorkExperienceJsonConverter extends BaseJsonBJooqConverter<List<WorkExperienceDTO>> {
    @Override
    @SuppressWarnings("unchecked")
    public @NotNull Class<List<WorkExperienceDTO>> toType() {
        // This won't be used due to TypeReference override, but kept for interface compliance
        return (Class<List<WorkExperienceDTO>>) (Class<?>) List.class;
    }
    
    @Override
    protected TypeReference<List<WorkExperienceDTO>> getTypeReference() {
        return new TypeReference<List<WorkExperienceDTO>>() {};
    }
}