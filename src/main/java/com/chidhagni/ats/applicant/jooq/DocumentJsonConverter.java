package com.chidhagni.ats.applicant.jooq;

import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class DocumentJsonConverter extends BaseJsonBJooqConverter<List<DocumentDTO>> {
    @Override
    @SuppressWarnings("unchecked")
    public @NotNull Class<List<DocumentDTO>> toType() {
        // This won't be used due to TypeReference override, but kept for interface compliance
        return (Class<List<DocumentDTO>>) (Class<?>) List.class;
    }
    
    @Override
    protected TypeReference<List<DocumentDTO>> getTypeReference() {
        return new TypeReference<List<DocumentDTO>>() {};
    }
}