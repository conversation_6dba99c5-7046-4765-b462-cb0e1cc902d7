package com.chidhagni.ats.applicant.jooq;

import com.chidhagni.ats.applicant.dto.request.CertificationsDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class CertificationsJsonConverter extends BaseJsonBJooqConverter<List<CertificationsDTO>> {
    @Override
    @SuppressWarnings("unchecked")
    public @NotNull Class<List<CertificationsDTO>> toType() {
        // This won't be used due to TypeReference override, but kept for interface compliance
        return (Class<List<CertificationsDTO>>) (Class<?>) List.class;
    }
    
    @Override
    protected TypeReference<List<CertificationsDTO>> getTypeReference() {
        return new TypeReference<List<CertificationsDTO>>() {};
    }
}