package com.chidhagni.ats.applicant.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Request DTO for updating existing applicants.
 * All fields are optional to support partial updates.
 * This follows the OpenAPI specification for ApplicantUpdateRequest.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicantUpdateRequestDTO {
    
    // Basic Fields - All optional for partial updates
    @Size(max = 100)
    private String firstName;

    @Size(max = 100)
    private String middleName;

    @Size(max = 100)
    private String lastName;

    @Size(max = 100)
    private String preferredName;

    @Email
    @Size(max = 100)
    private String email;

    private LocalDate dateOfBirth;

    // JSONB Fields - All optional for partial updates
    @Valid
    private ContactInfoDTO contactInfo;

    @Valid
    private SocialProfilesDTO socialProfiles;

    @Valid
    private AddressesDTO addresses;

    @Valid
    private EmployerDetailsDTO employerDetails;

    @Valid
    private List<WorkExperienceDTO> workExperience;

    @Valid
    private List<EducationDTO> education;

    @Valid
    private List<CertificationsDTO> certifications;

    @Valid
    private AdditionalInfoDTO additionalInfo;

    /**
     * Document operations for the update:
     * - New documents: include fileIndex for file mapping, no id
     * - Documents to delete: include id and set isActive=false
     * - Existing documents to keep: not included in this list
     */
    @Valid
    private List<DocumentDTO> documents;

    // Non-JSONB Fields - All optional for partial updates
    private UUID status;

    private UUID priorityLevel;

    private UUID assignedTo;

    @Size(max = 1000)
    private String additionalComments;

    private LocalDate applicationDate;

    private Boolean isActive;

    /**
     * TODO: Add updatedBy field once UserPrincipal is available
     * This field will be populated from the authenticated user context
     * when authentication is implemented.
     */
    // private UUID updatedBy;
}
