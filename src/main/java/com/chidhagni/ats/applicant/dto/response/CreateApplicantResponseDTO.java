package com.chidhagni.ats.applicant.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Response DTO for applicant creation operation.
 * 
 * Simple response containing only essential information after successful applicant creation.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateApplicantResponseDTO {

    /**
     * The ID of the created applicant
     */
    private UUID id;

    /**
     * Success message
     */
    private String message;

    /**
     * Indicates whether the creation operation was successful
     */
    private Boolean success;
}