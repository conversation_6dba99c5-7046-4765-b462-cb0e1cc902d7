package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkExperienceDTO {
    private UUID id;

    @NotNull
    @Size(max = 200)
    private String companyName;

    @NotNull
    @Size(max = 100)
    private String jobTitle;

    @NotNull
    private String startDate;

    private String endDate;

    @Size(max = 200)
    private String location;

    private UUID employeeType; // UUID reference

    @Size(max = 1000)
    private String responsibilities;

    private Boolean isActive;
}
