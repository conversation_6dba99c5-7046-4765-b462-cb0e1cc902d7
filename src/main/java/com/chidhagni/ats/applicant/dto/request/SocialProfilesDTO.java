package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SocialProfilesDTO {
    private UUID id;

    @Size(max = 500)
    private String linkedin;

    @Size(max = 500)
    private String facebook;

    @Size(max = 500)
    private String twitter;

    @Size(max = 100)
    private String skypeId;

    @Size(max = 500)
    private String videoReference;
}
