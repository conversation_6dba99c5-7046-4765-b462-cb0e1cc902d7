package com.chidhagni.ats.applicant.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Request DTO for retrieving all applicants with filtering, sorting, and pagination.
 * This DTO matches the OpenAPI specification for the GET ALL (POST) endpoint.
 * 
 * All fields are optional to support flexible filtering and default pagination.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request parameters for retrieving all applicants with filtering and pagination")
public class GetAllApplicantsRequestDTO {

    /**
     * Page number for pagination (1-based).
     * Default: 1
     */
    @Schema(
        description = "Page number (1-based)",
        example = "1",
        minimum = "1",
        defaultValue = "1"
    )
    @Min(value = 1, message = "Page number must be at least 1")
    @Builder.Default
    private Integer page = 1;

    /**
     * Number of items per page.
     * Default: 20, Maximum: 100
     */
    @Schema(
        description = "Number of items per page",
        example = "20",
        minimum = "1",
        maximum = "100",
        defaultValue = "20"
    )
    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 100, message = "Page size cannot exceed 100")
    @Builder.Default
    private Integer pageSize = 20;

    /**
     * Sort direction for results.
     * Default: ASC
     */
    @Schema(
        description = "Sort direction",
        example = "ASC",
        allowableValues = {"ASC", "DESC"},
        defaultValue = "ASC"
    )
    @Builder.Default
    private SortDirection sortDirection = SortDirection.ASC;

    /**
     * Filter by applicant status UUID.
     * Optional filter to retrieve applicants with specific status.
     */
    @Schema(
        description = "Applicant status UUID for filtering",
        example = "f72d35d6-6b36-4a4d-9dbf-24c4e91b2f3d",
        format = "uuid"
    )
    private UUID status;

    /**
     * Search term for text-based filtering.
     * Applied to firstName, lastName, middleName, preferredName, and email fields.
     */
    @Schema(
        description = "Search term applied to firstName, lastName, middleName, preferredName, or email",
        example = "<EMAIL>"
    )
    private String search;

    /**
     * Enumeration for sort direction options.
     */
    public enum SortDirection {
        ASC, DESC
    }
}
