package com.chidhagni.ats.applicant.dto.response;

import com.chidhagni.ats.applicant.dto.request.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Response DTO for applicant data
 * Contains complete applicant information including all JSONB fields
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicantResponseDTO {
    
    // Basic Fields
    private UUID id;
    private UUID orgId;
    private String systemCode;
    private String firstName;
    private String middleName;
    private String lastName;
    private String preferredName;
    private String email;
    private LocalDate dateOfBirth;
    
    // JSONB Fields
    private ContactInfoDTO contactInfo;
    private SocialProfilesDTO socialProfiles;
    private AddressesDTO addresses;
    private EmployerDetailsDTO employerDetails;
    private List<WorkExperienceDTO> workExperience;
    private List<EducationDTO> education;
    private List<CertificationsDTO> certifications;
    private AdditionalInfoDTO additionalInfo;
    private List<DocumentDTO> documents;
    
    // Non-JSONB Fields
    private UUID status;
    private UUID priorityLevel;
    private UUID assignedTo;
    private String additionalComments;
    private UUID gender;
    private LocalDate applicationDate;
    private Boolean isActive;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
}
