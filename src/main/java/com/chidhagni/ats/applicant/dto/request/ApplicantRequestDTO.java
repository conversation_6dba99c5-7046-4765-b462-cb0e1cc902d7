package com.chidhagni.ats.applicant.dto.request;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Request DTO for creating new applicants
 * This matches the JSONB payload structure exactly as defined in PAYLOAD_JSONB_STRUCTURE.md
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicantRequestDTO {
    private UUID id;

    private UUID orgId;

    @NotNull
    @Size(max = 100)
    private String firstName;

    @Size(max = 100)
    private String middleName;

    @NotNull
    @Size(max = 100)
    private String lastName;

    @Size(max = 100)
    private String preferredName;

    @NotNull
    @Email
    @Size(max = 100)
    private String email;

    private LocalDate dateOfBirth;


    // JSONB Fields
    @Valid
    private ContactInfoDTO contactInfo;

    @Valid
    private SocialProfilesDTO socialProfiles;

    @Valid
    private AddressesDTO addresses;

    @Valid
    private EmployerDetailsDTO employerDetails;

    @Valid
    private List<WorkExperienceDTO> workExperience;

    @Valid
    private List<EducationDTO> education;

    @Valid
    private List<CertificationsDTO> certifications;

    @Valid
    private AdditionalInfoDTO additionalInfo;


    @Valid
    private List<DocumentDTO> documents;

    // Non-JSONB Fields
    private UUID status;

    private UUID priorityLevel;

    private UUID assignedTo;

    @Size(max = 1000)
    private String additionalComments;

    private LocalDate applicationDate;

    private Boolean isActive;

    private UUID createdBy;

    private UUID updatedBy;
}
