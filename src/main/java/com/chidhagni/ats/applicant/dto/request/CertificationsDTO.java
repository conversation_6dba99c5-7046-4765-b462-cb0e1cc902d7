package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CertificationsDTO {
    private UUID id;

    @NotNull
    @Size(max = 200)
    private String certification;

    @Size(max = 4)
    private String yearCompleted;

    @Size(max = 500)
    private String comments;

    private Boolean isActive;
}
