package com.chidhagni.ats.applicant.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Response DTO for applicant deactivation operation.
 *
 * This DTO represents the response for the PATCH & DELETE endpoints.
 * Note: This endpoint is an extension to the OpenAPI specification as requested by the user.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivateDeactivateResponseDTO {

    /**
     * Indicates whether the activation/deactivation operation was successful.
     */
    private Boolean success;

    /**
     * Human-readable message describing the result of the activation/deactivation operation.
     */
    private String message;
}
