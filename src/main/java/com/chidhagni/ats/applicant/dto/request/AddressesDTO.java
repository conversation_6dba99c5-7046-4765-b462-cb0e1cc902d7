package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddressesDTO {
    private UUID id;

    @NotNull
    private UUID country; // UUID reference

    @NotNull
    private UUID state; // UUID reference

    @NotNull
    @Size(max = 100)
    private String city;

    @Size(max = 20)
    private String zipcode;

    private Boolean isPrimary;
}
