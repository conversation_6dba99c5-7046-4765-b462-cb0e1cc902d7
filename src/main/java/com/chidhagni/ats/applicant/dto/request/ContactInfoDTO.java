package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ContactInfoDTO {
    private UUID id;
    private String homePhone;
    private String mobilePhone;
    private String workPhone;
    private String otherPhone;

    @Email
    private String alternateEmail;
}
