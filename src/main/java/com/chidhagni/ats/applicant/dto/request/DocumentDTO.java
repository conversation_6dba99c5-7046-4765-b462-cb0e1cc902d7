package com.chidhagni.ats.applicant.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDTO {
    private UUID id;

    @NotNull
    @Size(max = 200)
    private String title;

    private UUID category; // UUID reference

    private UUID subcategory; // UUID reference

    @Size(max = 500)
    private String comments;

    @Size(max = 500)
    private String filePath;

    private Integer fileIndex;

    private Boolean isActive;
}
