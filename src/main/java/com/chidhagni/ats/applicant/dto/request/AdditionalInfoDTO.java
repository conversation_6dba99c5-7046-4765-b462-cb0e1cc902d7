package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdditionalInfoDTO {
    private UUID id;

    private ExperienceDTO experience;

    @Size(max = 100)
    private String jobTitle;

    private SalaryDTO expectedPay;

    private SalaryDTO currentCTC;

    private List<UUID> skills; // List of UUID references

    private List<UUID> primarySkills; // List of UUID references

    private UUID technology; // UUID reference

    private List<UUID> industry; // UUID reference

    private List<UUID> function; // UUID reference

    private UUID taxTerms; // UUID reference

    private UUID noticePeriod; // UUID reference

    @Size(max = 200)
    private String currentCompany;

    private UUID applicantStatus; // UUID reference

    private UUID ownership; // UUID reference

    private Boolean relocation;

    @Size(max = 1000)
    private String additionalComments;

    @Size(max = 20)
    private String panCardNumber;

    @Size(max = 20)
    private String aadhaarNumber;

    @Size(max = 10)
    private String gpa;

    private UUID gender; // UUID reference

    private UUID raceEthnicity; // UUID reference

    private UUID veteranStatus; // UUID reference

    private UUID veteranType; // UUID reference

    private UUID disability; // UUID reference

    private UUID workAuthorization; // UUID reference

    private Boolean clearance;

    private UUID source; // UUID reference

    @Size(max = 100)
    private String referredBy;

    @Size(max = 20)
    private String ssn;
}
