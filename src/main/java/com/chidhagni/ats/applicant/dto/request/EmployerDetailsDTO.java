package com.chidhagni.ats.applicant.dto.request;


import com.chidhagni.ats.applicant.constants.SelectionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployerDetailsDTO {
    private UUID id;

    private SelectionTypeEnum selectionType;

    private UUID vendorContact; // UUID reference

    @Size(max = 100)
    private String firstName;

    @Size(max = 100)
    private String lastName;

    @Size(max = 200)
    private String employerName;

    @Size(max = 20)
    private String officeNumber;

    @Email
    @Size(max = 100)
    private String emailId;

    @Size(max = 20)
    private String mobileNumber;

    private Boolean status;
    private Boolean isActive;
}
