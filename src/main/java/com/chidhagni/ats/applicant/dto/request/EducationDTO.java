package com.chidhagni.ats.applicant.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EducationDTO {
    private UUID id;

    @NotNull
    @Size(max = 200)
    private String schoolName;

    @NotNull
    private UUID degree; // UUID reference

    @Size(max = 4)
    private String yearCompleted;

    @Size(max = 100)
    private String minorStudy;

    @Size(max = 100)
    private String majorStudy;

    @Size(max = 10)
    private String gpa;

    private UUID country; // UUID reference

    private UUID state; // UUID reference

    @Size(max = 100)
    private String city;

    private Boolean isHigherEducation;

    private Boolean isActive;
}
