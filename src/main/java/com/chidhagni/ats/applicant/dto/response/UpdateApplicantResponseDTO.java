package com.chidhagni.ats.applicant.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO for applicant update operations.
 * Follows the OpenAPI specification for UpdateApplicantResponse.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateApplicantResponseDTO {

    /**
     * UUID of the updated applicant
     */
    private UUID id;

    /**
     * Success message describing the update operation
     */
    private String message;

    /**
     * Indicates if the update operation was successful
     */
    private boolean success;

    /**
     * Number of new documents successfully added
     */
    private int documentsAdded;

    /**
     * Number of documents successfully soft deleted
     */
    private int documentsDeleted;

    /**
     * Timestamp when the applicant was last updated
     */
    private LocalDateTime updatedAt;
}
