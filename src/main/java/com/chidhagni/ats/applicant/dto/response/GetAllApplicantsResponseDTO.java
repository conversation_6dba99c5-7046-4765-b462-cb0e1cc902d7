package com.chidhagni.ats.applicant.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Response DTO for GET ALL applicants endpoint.
 * Contains paginated list of applicants with metadata about the result set.
 * 
 * This follows the established pattern from the pure-heart-backend project
 * for paginated responses with row count information.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Response containing paginated list of applicants")
public class GetAllApplicantsResponseDTO {

    /**
     * List of applicant response DTOs for the current page.
     * Contains complete applicant information including JSONB fields.
     */
    @Schema(
        description = "List of applicants for the current page",
        implementation = ApplicantResponseDTO.class
    )
    private List<ApplicantResponseDTO> applicants;

    /**
     * Total number of applicants matching the filter criteria.
     * Used for pagination calculations and UI display.
     */
    @Schema(
        description = "Total number of applicants matching the filter criteria",
        example = "150"
    )
    private Integer totalCount;

    /**
     * Current page number (1-based).
     * Echoed back from the request for client convenience.
     */
    @Schema(
        description = "Current page number",
        example = "1"
    )
    private Integer currentPage;

    /**
     * Number of items per page.
     * Echoed back from the request for client convenience.
     */
    @Schema(
        description = "Number of items per page",
        example = "20"
    )
    private Integer pageSize;

    /**
     * Total number of pages available.
     * Calculated based on totalCount and pageSize.
     */
    @Schema(
        description = "Total number of pages available",
        example = "8"
    )
    private Integer totalPages;

    /**
     * Indicates if there are more pages available.
     * Convenience field for pagination controls.
     */
    @Schema(
        description = "Indicates if there are more pages available",
        example = "true"
    )
    private Boolean hasNextPage;

    /**
     * Indicates if there are previous pages available.
     * Convenience field for pagination controls.
     */
    @Schema(
        description = "Indicates if there are previous pages available",
        example = "false"
    )
    private Boolean hasPreviousPage;
}
