package com.chidhagni.ats.resource.mapper;

import com.chidhagni.ats.resource.dto.request.ResourceDTO;
import com.chidhagni.ats.resource.dto.response.ResourceResponseDTO;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * Mapper utility for converting Resource DTOs to Response DTOs.
 * Focuses on hierarchical tree structure mapping with proper null handling.
 */
@Component
public class ResourceMapper {
    
    /**
     * Convert ResourceDTO to ResourceResponseDTO for tree structure
     * 
     * This method follows mapping best practices:
     * - Null safety checks
     * - Recursive mapping for hierarchical structures
     * - Proper child count calculation
     * - Immutable builder pattern
     * - Stream API for functional programming
     */
    public ResourceResponseDTO toResponseDTO(ResourceDTO resourceDTO) {
        if (resourceDTO == null) {
            return null;
        }
        
        return ResourceResponseDTO.builder()
                .id(resourceDTO.getId())
                .name(resourceDTO.getName())
                .description(resourceDTO.getDescription())
                .type(resourceDTO.getType())
                .parentResourceId(resourceDTO.getParentResourceId())
                .validations(resourceDTO.getValidations())
                .isActive(resourceDTO.getIsActive())
                .createdOn(resourceDTO.getCreatedOn())
                .updatedOn(resourceDTO.getUpdatedOn())
                .createdBy(resourceDTO.getCreatedBy())
                .updatedBy(resourceDTO.getUpdatedBy())
                // Recursive mapping for hierarchical children
                .children(resourceDTO.getChildDTO() != null ? 
                    resourceDTO.getChildDTO().stream()
                        .map(this::toResponseDTO)
                        .collect(Collectors.toList()) : null)
                // Calculate child count for metadata
                .childCount(resourceDTO.getChildDTO() != null ? 
                    resourceDTO.getChildDTO().size() : 0)
                .build();
    }
}
