package com.chidhagni.ats.resource.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jooq.JSONB;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Response DTO for Resource API endpoints.
 * Provides a clean separation between internal data model and API responses.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResourceResponseDTO {
    
    private UUID id;
    private String name;
    private String description;
    private String type;
    private UUID parentResourceId;
    private JSONB validations;
    private Boolean isActive;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private UUID createdBy;
    private UUID updatedBy;
    
    // For tree responses
    private List<ResourceResponseDTO> children;
    
    // For metadata
    private Integer childCount;
    private Integer depth;
}

