package com.chidhagni.ats.resource;

import com.chidhagni.ats.resource.dto.request.ResourceDTO;
import com.chidhagni.ats.resource.dto.response.ResourceResponseDTO;
import com.chidhagni.exception.ResourceNotFoundException;
import com.chidhagni.ats.resource.mapper.ResourceMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
 * Service layer for Resource tree operations.
 * Provides business logic for retrieving hierarchical resource structures.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceService {

    private final ResourceRepository resourceRepository;
    private final ResourceMapper resourceMapper;

    /**
     * Get complete resource tree for a parent ID
     * 
     * This method implements several best practices:
     * - Read-only transaction for optimal performance
     * - Proper exception handling with meaningful error messages
     * - Comprehensive logging for monitoring and debugging
     * - Null checking and validation
     * - Clean separation of concerns with mapper pattern
     */
    @Transactional(readOnly = true)
    public ResourceResponseDTO getResourceTree(UUID parentId) throws JsonProcessingException {
        if (parentId == null) {
            log.warn("Attempt to fetch resource tree with null parent ID");
            throw new IllegalArgumentException("Parent ID cannot be null");
        }
        
        log.info("Fetching resource tree for parent ID: {}", parentId);
        
        try {
            ResourceDTO resourceDTO = resourceRepository.getCompleteTree(parentId);
            
            if (resourceDTO == null) {
                log.warn("Resource not found for parent ID: {}", parentId);
                throw new ResourceNotFoundException(parentId);
            }
            
            ResourceResponseDTO responseDTO = resourceMapper.toResponseDTO(resourceDTO);
            
            log.info("Successfully retrieved resource tree for parent ID: {} with {} direct children", 
                    parentId, 
                    responseDTO.getChildren() != null ? responseDTO.getChildren().size() : 0);
            
            return responseDTO;
            
        } catch (JsonProcessingException e) {
            log.error("JSON processing error while building resource tree for parent ID: {}", parentId, e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error while fetching resource tree for parent ID: {}", parentId, e);
            throw new RuntimeException("Failed to retrieve resource tree", e);
        }
    }
}
