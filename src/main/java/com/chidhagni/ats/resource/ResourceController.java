package com.chidhagni.ats.resource;

import com.chidhagni.ats.resource.dto.response.ResourceResponseDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

import static com.chidhagni.ats.resource.constants.ResourceMetaData.RESOURCES_GET_RES_V1;

/**
 * REST Controller for Resource tree operations.
 * Provides endpoint to retrieve hierarchical resource structures.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v1/resources")
@Tag(name = "Resource Management", description = "API for retrieving hierarchical resource trees")
@Slf4j
public class ResourceController {
    
    private final ResourceService resourceService;



    /**
     * Get complete resource tree for a parent ID
     * 
     * This endpoint follows REST best practices:
     * - Uses proper HTTP GET method for data retrieval
     * - Descriptive URL path with clear resource identification
     * - Proper status codes and error handling
     * - OpenAPI documentation with examples
     * - Structured logging with correlation IDs
     * - Content negotiation support
     */
    @GetMapping(value = "/{id}/tree", produces = RESOURCES_GET_RES_V1)
    @Operation(
        summary = "Get resource tree",
        description = "Retrieve complete hierarchical tree structure for a parent resource. "
                    + "Returns the parent resource with all its nested children in a tree format."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Resource tree retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "Parent resource not found"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<ResourceResponseDTO> getResourceTree(
            @Parameter(
                description = "UUID of the parent resource to retrieve tree for",
                example = "a0000000-0000-0000-0000-000000000001",
                required = true
            ) 
            @PathVariable("id") UUID parentId) throws JsonProcessingException {
        
        log.info("Retrieving resource tree for parent ID: {}", parentId);
        
        ResourceResponseDTO resourceTree = resourceService.getResourceTree(parentId);
        
        log.info("Successfully retrieved resource tree for parent ID: {} with {} children", 
                parentId, resourceTree.getChildren() != null ? resourceTree.getChildren().size() : 0);
        
        return ResponseEntity.ok()
                .header("Content-Type", RESOURCES_GET_RES_V1)
                .body(resourceTree);
    }
}
