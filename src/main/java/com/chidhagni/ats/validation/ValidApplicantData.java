package com.chidhagni.ats.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * Custom validation annotation for applicant data JSON strings.
 * Validates that the JSON string can be properly parsed into ApplicantRequestDTO.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Documented
@Constraint(validatedBy = ApplicantDataValidator.class)
@Target({ElementType.PARAMETER, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidApplicantData {
    
    String message() default "Invalid applicant data format";
    
    Class<?>[] groups() default {};
    
    Class<? extends Payload>[] payload() default {};
}
