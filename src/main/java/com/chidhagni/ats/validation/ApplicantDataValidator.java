package com.chidhagni.ats.validation;

import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Validator implementation for @ValidApplicantData annotation.
 * Validates that JSON strings can be properly parsed into ApplicantRequestDTO.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ApplicantDataValidator implements ConstraintValidator<ValidApplicantData, String> {

    private final ObjectMapper objectMapper;

    @Override
    public void initialize(ValidApplicantData constraintAnnotation) {
        // No initialization needed
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null || value.trim().isEmpty()) {
            addCustomMessage(context, "Applicant data cannot be null or empty");
            return false;
        }

        try {
            ApplicantRequestDTO applicantData = objectMapper.readValue(value, ApplicantRequestDTO.class);
            
            // Basic validation of required fields
            if (applicantData.getFirstName() == null || applicantData.getFirstName().trim().isEmpty()) {
                addCustomMessage(context, "First name is required in applicant data");
                return false;
            }
            
            if (applicantData.getLastName() == null || applicantData.getLastName().trim().isEmpty()) {
                addCustomMessage(context, "Last name is required in applicant data");
                return false;
            }
            
            if (applicantData.getEmail() == null || applicantData.getEmail().trim().isEmpty()) {
                addCustomMessage(context, "Email is required in applicant data");
                return false;
            }

            return true;
            
        } catch (Exception e) {
            log.debug("Failed to parse applicant data JSON: {}", e.getMessage());
            addCustomMessage(context, "Invalid JSON format: " + e.getMessage());
            return false;
        }
    }

    private void addCustomMessage(ConstraintValidatorContext context, String message) {
        context.disableDefaultConstraintViolation();
        context.buildConstraintViolationWithTemplate(message).addConstraintViolation();
    }
}
