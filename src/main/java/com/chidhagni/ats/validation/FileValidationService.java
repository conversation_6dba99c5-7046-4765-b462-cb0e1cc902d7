package com.chidhagni.ats.validation;

import com.chidhagni.exception.FileSizeExceededException;
import com.chidhagni.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * Service for comprehensive file validation following Spring Boot best practices.
 * Implements file size, content type, and security validations for uploaded files.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
public class FileValidationService {

     private final DataSize maxFileSize = DataSize.ofBytes(15 * 1024 * 1024);

    @Value("${ats.file-upload.allowed-types}")
    private String allowedContentTypes;

    private static final Set<String> DANGEROUS_EXTENSIONS = Set.of(
            "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "sh"
    );


    /**
     * Validates a list of uploaded files for size, content type, and security.
     *
     * @param files List of files to validate
     * @throws ValidationException if any validation fails
     */
    public void validateFiles(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            return; // No files to validate
        }

        log.debug("Validating {} uploaded files", files.size());

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);
            validateSingleFile(file, i);
        }

        log.debug("All {} files passed validation", files.size());
    }

    /**
     * Validates a single file for all criteria.
     *
     * @param file File to validate
     * @param index File index for error reporting
     * @throws ValidationException if validation fails
     */
    private void validateSingleFile(MultipartFile file, int index) {
        if (file == null || file.isEmpty()) {
            throw new ValidationException(String.format("File at index %d is empty or null", index));
        }

        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new ValidationException(String.format("File at index %d has no filename", index));
        }

        // Validate file size
        validateFileSize(file, index);

        // Validate content type
        validateContentType(file, index);

        // Validate file extension for security
        validateFileExtension(originalFilename, index);

        log.debug("File '{}' at index {} passed all validations", originalFilename, index);
    }

    /**
     * Validates file size against configured maximum.
     */
    private void validateFileSize(MultipartFile file, int index) {
        if (file.getSize() > maxFileSize.toBytes()) {
            throw new FileSizeExceededException(
                    file.getOriginalFilename(),          // fileName
                    file.getSize(),                      // actual size in bytes
                    maxFileSize.toBytes()                         // limit in bytes
            );

        }
    }

    /**
     * Validates file content type against allowed types.
     */
    private void validateContentType(MultipartFile file, int index) {
        String contentType = file.getContentType();
        if (contentType == null) {
            throw new ValidationException(String.format(
                    "File '%s' at index %d has no content type",
                    file.getOriginalFilename(), index
            ));
        }

        List<String> allowedTypes = Arrays.asList(allowedContentTypes.split(","));
        if (!allowedTypes.contains(contentType)) {
            throw new ValidationException(String.format(
                    "File '%s' at index %d has unsupported content type '%s'. Allowed types: %s",
                    file.getOriginalFilename(), index, contentType, allowedContentTypes
            ));
        }
    }

    /**
     * Validates file extension for security (prevents dangerous file types).
     */
    private void validateFileExtension(String filename, int index) {
        String extension = getFileExtension(filename).toLowerCase();
        
        if (DANGEROUS_EXTENSIONS.contains(extension)) {
            throw new ValidationException(String.format(
                    "File '%s' at index %d has dangerous extension '%s' and is not allowed",
                    filename, index, extension
            ));
        }
    }

    /**
     * Extracts file extension from filename.
     */
    private String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1) : "";
    }

    /**
     * Gets the configured maximum file size.
     */
    public long getMaxFileSize() {
        return maxFileSize.toBytes();
    }

    /**
     * Gets the configured allowed content types.
     */
    public String getAllowedContentTypes() {
        return allowedContentTypes;
    }
}
