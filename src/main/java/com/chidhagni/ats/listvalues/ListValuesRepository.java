package com.chidhagni.ats.listvalues;

import com.chidhagni.ats.listvalues.dto.response.GetAllListValuesDTO;
import com.chidhagni.ats.listvalues.dto.response.ListValueResponseDTO;
import com.chidhagni.exception.DatabaseOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.ats.db.jooq.tables.ListNames.LIST_NAMES;
import static com.chidhagni.ats.db.jooq.tables.ListValues.LIST_VALUES;


/**
 * Repository class for ListValues entity operations.
 * Handles database interactions using JOOQ DSL with proper error handling and logging.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class ListValuesRepository {

    private final DSLContext dslContext;
    private static final String LIST_VALUE_ALIAS = "listValue";

    /**
     * Retrieves all list values by ListName ID with filtering and ordering.
     *
     * @param listNameId The UUID of the ListName to filter by
     * @return List of ListValuesDTO containing filtered and ordered results
     */
    public List<GetAllListValuesDTO.ListValuesDTO> getAllListValuesById(UUID listNameId) {
        try {
            log.debug("Retrieving all list values for listNameId: {}", listNameId);

            List<GetAllListValuesDTO.ListValuesDTO> result = dslContext.select(
                            LIST_VALUES.ID,
                            LIST_VALUES.IS_ACTIVE,
                            LIST_VALUES.NAME.as(LIST_VALUE_ALIAS))
                    .from(LIST_VALUES)
                    .join(LIST_NAMES).on(LIST_NAMES.ID.eq(LIST_VALUES.LIST_NAMES_ID))
                    .where(LIST_NAMES.ID.eq(listNameId))
                    .orderBy(LIST_VALUES.NAME.asc())
                    .fetchInto(GetAllListValuesDTO.ListValuesDTO.class);

            log.debug("Found {} list values for listNameId: {}", result.size(), listNameId);
            return result;

        } catch (Exception e) {
            log.error("Error retrieving list values for listNameId: {}", listNameId, e);
            throw new DatabaseOperationException("Failed to retrieve list values by listNameId: " + listNameId, e);
        }
    }

    /**
     * Retrieves all list values without any filtering.
     * Returns only id and name fields as specified in the OpenAPI specification.
     *
     * @return List of ListValues entities ordered by name
     */
    public List<ListValueResponseDTO> getAllListValues() {
        try {
            // Database operation
            log.debug("Retrieving all list values");

            List<ListValueResponseDTO> result = dslContext.select(
                            LIST_VALUES.ID,
                            LIST_VALUES.NAME)
                    .from(LIST_VALUES)
                    .orderBy(LIST_VALUES.NAME.asc())
                    .fetchInto(ListValueResponseDTO.class);

            log.debug("Found {} total list values", result.size());
            return result;
        } catch (DataAccessException e) {
            log.error("Database error retrieving list values", e);
            throw new DatabaseOperationException("Failed to retrieve all list values", e);
        } catch (Exception e) {
            log.error("Unexpected error retrieving list values", e);
            throw new DatabaseOperationException("Unexpected error occurred", e);
        }
    }
}


