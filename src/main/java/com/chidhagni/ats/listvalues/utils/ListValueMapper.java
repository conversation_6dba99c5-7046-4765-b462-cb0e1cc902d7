package com.chidhagni.ats.listvalues.utils;

import com.chidhagni.ats.listvalues.dto.response.ListValueResponseDTO;
import com.chidhagni.ats.db.jooq.tables.pojos.ListValues;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * MapStruct mapper for converting between ListValues entity and DTOs.
 * 
 * This mapper follows the established patterns in the codebase for
 * entity-to-DTO conversions using MapStruct annotations.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper(componentModel = "spring")
public interface ListValueMapper {

    /**
     * MapStruct can handle identical field names automatically
     * @param listValues The ListValues entity to map
     * @return ListValueResponseDTO containing id and name
     */
    ListValueResponseDTO listValuesToListValueResponse(ListValues listValues);
}
