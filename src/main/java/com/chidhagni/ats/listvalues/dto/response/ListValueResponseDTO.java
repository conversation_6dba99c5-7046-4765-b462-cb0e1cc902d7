package com.chidhagni.ats.listvalues.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Response DTO for ListValue entity.
 * Contains only id and name fields as specified in the OpenAPI specification.
 * 
 * This DTO is used for the GET ALL list values endpoint that returns
 * a simplified view of list values without additional metadata.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "Response DTO for list value containing id and name")
public class ListValueResponseDTO {

    /**
     * Unique identifier for the list value.
     */
    @Schema(
        description = "List value ID",
        example = "2e9c51f4-c2f5-4745-9a29-8f3e4287152c",
        format = "uuid"
    )
    private UUID id;

    /**
     * Name of the list value.
     */
    @Schema(
        description = "List value name",
        example = "Placed"
    )
    private String name;
}
