package com.chidhagni.ats.listvalues.constants;

/**
 * Constants class for ListValues API metadata including MIME types and other constants.
 *
 * This class follows the convention established in the pure-heart-backend project
 * for defining API-specific constants and MIME types.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ListValuesMetaData {

    /**
     * Private constructor to prevent instantiation of utility class.
     */
    private ListValuesMetaData() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }

    // ========================================
    // MIME TYPE CONSTANTS
    // ========================================

    /**
     * MIME type for GET all list values by ListName ID response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String LIST_VALUES_GET_ALL_BY_LISTNAMEID_RES_V1 =
            "application/vnd-chidhagni-ats.listvalues.get.all.by-listnameid.res-v1+json";

    /**
     * MIME type for GET all list values response (version 1).
     * Used in controller produces attribute and response headers.
     */
    public static final String LIST_VALUES_GET_ALL_RES_V1 =
            "application/vnd-chidhagni-ats.list-values.get.all.res-v1+json";

    // ========================================
    // API OPERATION CONSTANTS
    // ========================================

    /**
     * Operation ID for get all list values endpoint.
     */
    public static final String GET_ALL_LIST_VALUES_OPERATION = "getAllListValues";

}
