package com.chidhagni.ats.listvalues;

import com.chidhagni.ats.listvalues.dto.response.GetAllListValuesDTO;
import com.chidhagni.ats.listvalues.dto.response.ListValueResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

import static com.chidhagni.ats.listvalues.constants.ListValuesMetaData.*;

/**
 * REST Controller for ListValues management operations.
 * This controller implements the endpoints specified in the OpenAPI specification
 * for list value retrieval operations.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/v1/list-value")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "ListValues", description = "List values management operations")
public class ListValuesController {

    private final ListValuesService listValuesService;

    @GetMapping( produces = LIST_VALUES_GET_ALL_RES_V1)
    @Operation(
            summary = "Get all list values",
            description = "Retrieves all list values without any filters or pagination. Returns only `id` and `name`.",
            operationId = GET_ALL_LIST_VALUES_OPERATION
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Successfully retrieved list values",
                    content = @Content(
                            mediaType = LIST_VALUES_GET_ALL_RES_V1,
                            array = @ArraySchema(schema = @Schema(implementation = ListValueResponseDTO.class)),
                            examples = @ExampleObject(
                                    name = "List Values Response",
                                    value = """
                                            [
                                              {
                                                "id": "2e9c51f4-c2f5-4745-9a29-8f3e4287152c",
                                                "name": "Placed"
                                              },
                                              {
                                                "id": "3f8d62e5-d3g6-5856-0b3a-9g4f5398263d",
                                                "name": "Active"
                                              }
                                            ]
                                            """
                            )
                    )
            ),
            @ApiResponse(responseCode = "401", description = "Unauthorized"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "500", description = "Internal Server Error")
    })
    public ResponseEntity<List<ListValueResponseDTO>> getAllListValues() {

        log.info("Received request to get all list values");

        List<ListValueResponseDTO> response = listValuesService.getAllListValues();

        log.info("Successfully retrieved {} list values", response.size());

        return ResponseEntity.ok(response);
    }

    @Operation(
            summary = "Get all list values by ListName ID",
            description = "Fetches all list values associated with the provided ListName ID."
    )
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully retrieved list values"),
            @ApiResponse(responseCode = "400", description = "Invalid ListName ID supplied"),
            @ApiResponse(responseCode = "404", description = "List values not found for the given ID")
    })
    @GetMapping(
            value = "/list-name",
            produces = LIST_VALUES_GET_ALL_BY_LISTNAMEID_RES_V1
    )
    public ResponseEntity<GetAllListValuesDTO> getAllListValuesById(
            @Parameter(description = "UUID of the ListName for which values are to be fetched", required = true)
            @RequestParam UUID id) {

        return ResponseEntity.ok(listValuesService.getAllListValuesById(id));
    }
}
