package com.chidhagni.ats.listvalues;

import com.chidhagni.ats.db.jooq.tables.pojos.ListValues;
import com.chidhagni.ats.listnames.ListNamesRepository;
import com.chidhagni.ats.listvalues.dto.response.GetAllListValuesDTO;
import com.chidhagni.ats.listvalues.dto.response.ListValueResponseDTO;
import com.chidhagni.ats.listvalues.utils.ListValueMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;

/**
 * Service class for ListValues business logic operations.
 * Handles business rules, validation, and transaction management.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ListValuesService {

    private final ListValuesRepository listValuesRepository;
    private final ListNamesRepository listNamesRepository;
    private final ListValueMapper listValueMapper;

    /**
     * Retrieves all list values by ListName ID with validation.
     *
     * @param listNameId The UUID of the ListName to filter by
     * @return GetAllListValuesDTO containing filtered list values
     */
    @Transactional(readOnly = true)
    public GetAllListValuesDTO getAllListValuesById(UUID listNameId) {
        log.debug("Retrieving all list values for listNameId: {}", listNameId);

        validateListNameId(listNameId);

        List<GetAllListValuesDTO.ListValuesDTO> fetchedListValues =
                listValuesRepository.getAllListValuesById(listNameId);

        log.debug("Successfully retrieved {} list values for listNameId: {}",
                fetchedListValues.size(), listNameId);

        return GetAllListValuesDTO.builder()
                .listValues(fetchedListValues)
                .build();
    }

    /**
     * Retrieves all list values without any filtering.
     * Returns only id and name fields as specified in the OpenAPI specification.
     *
     * @return List of ListValueResponseDTO containing all list values
     */
    @Transactional(readOnly = true)
    public List<ListValueResponseDTO> getAllListValues() {
        log.debug("Retrieving all list values");

        long startTime = System.currentTimeMillis();
        List<ListValueResponseDTO> response = listValuesRepository.getAllListValues();
        long duration = System.currentTimeMillis() - startTime;
        log.debug("Retrieved {} list values in {}ms", response.size(), duration);

        log.debug("Successfully retrieved {} total list values", response.size());
        return response;
    }

    /**
     * Validates that the provided ListName ID exists in the database.
     *
     * @param listNameId The UUID to validate
     * @throws IllegalArgumentException if the ListName ID does not exist
     */
    private void validateListNameId(UUID listNameId) {
        if (!listNamesRepository.existsById(listNameId)) {
            log.error("Invalid listNameId: {}", listNameId);
            throw new IllegalArgumentException("Invalid listNameId: " + listNameId);
        }
    }
}
