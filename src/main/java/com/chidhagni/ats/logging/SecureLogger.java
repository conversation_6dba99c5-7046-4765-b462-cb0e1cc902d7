package com.chidhagni.ats.logging;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * Secure logging component that masks PII and provides structured logging.
 * Ensures compliance with GDPR/CCPA by hashing sensitive information.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class SecureLogger {

    private static final String SALT = "ats-secure-logging-salt-2024";
    private static final String MASKED_PLACEHOLDER = "***MASKED***";

    /**
     * Logs applicant creation with PII-safe information.
     * 
     * @param email Email address (will be hashed)
     * @param applicantId Applicant UUID
     */
    public void logApplicantCreation(String email, UUID applicantId) {
        log.info("applicant_created - correlation_id={}, applicant_id={}, email_hash={}", 
                getCorrelationId(), applicantId, hashPii(email));
    }

    /**
     * Logs applicant update with PII-safe information.
     * 
     * @param applicantId Applicant UUID
     * @param documentCount Number of documents processed
     */
    public void logApplicantUpdate(UUID applicantId, int documentCount) {
        log.info("applicant_updated - correlation_id={}, applicant_id={}, document_count={}", 
                getCorrelationId(), applicantId, documentCount);
    }

    /**
     * Logs file upload completion with secure information.
     * 
     * @param documentId Document UUID
     * @param fileName Original file name (will be masked)
     * @param fileSize File size in bytes
     * @param fileExtension File extension
     */
    public void logFileUpload(UUID documentId, String fileName, long fileSize, String fileExtension) {
        log.info("file_uploaded - correlation_id={}, document_id={}, file_extension={}, file_size={}, original_name_hash={}", 
                getCorrelationId(), documentId, fileExtension, fileSize, hashPii(fileName));
    }

    /**
     * Logs file upload failure with secure information.
     * 
     * @param fileName Original file name (will be masked)
     * @param fileSize File size in bytes
     * @param errorMessage Error message
     */
    public void logFileUploadFailure(String fileName, long fileSize, String errorMessage) {
        log.error("file_upload_failed - correlation_id={}, file_extension={}, file_size={}, error={}, original_name_hash={}", 
                getCorrelationId(), getFileExtension(fileName), fileSize, errorMessage, hashPii(fileName));
    }

    /**
     * Logs document processing with secure information.
     * 
     * @param documentId Document UUID
     * @param operation Operation performed (create, update, delete)
     * @param success Whether operation was successful
     */
    public void logDocumentProcessing(UUID documentId, String operation, boolean success) {
        log.info("document_processed - correlation_id={}, document_id={}, operation={}, success={}", 
                getCorrelationId(), documentId, operation, success);
    }

    /**
     * Logs batch document processing results.
     * 
     * @param totalDocuments Total documents processed
     * @param successCount Number of successful operations
     * @param failureCount Number of failed operations
     * @param durationMs Processing duration in milliseconds
     */
    public void logBatchDocumentProcessing(int totalDocuments, int successCount, int failureCount, long durationMs) {
        log.info("batch_document_processing_completed - correlation_id={}, total={}, success={}, failures={}, duration_ms={}", 
                getCorrelationId(), totalDocuments, successCount, failureCount, durationMs);
    }

    /**
     * Logs database operation performance.
     * 
     * @param operation Database operation name
     * @param recordCount Number of records affected
     * @param durationMs Operation duration in milliseconds
     */
    public void logDatabaseOperation(String operation, int recordCount, long durationMs) {
        log.info("database_operation - correlation_id={}, operation={}, record_count={}, duration_ms={}", 
                getCorrelationId(), operation, recordCount, durationMs);
    }

    /**
     * Logs security events with appropriate level.
     * 
     * @param event Security event description
     * @param userId User ID involved (if available)
     * @param severity Severity level (INFO, WARN, ERROR)
     */
    public void logSecurityEvent(String event, UUID userId, String severity) {
        String logMessage = "security_event - correlation_id={}, event={}, user_id={}, severity={}";
        
        switch (severity.toUpperCase()) {
            case "ERROR":
                log.error(logMessage, getCorrelationId(), event, userId, severity);
                break;
            case "WARN":
                log.warn(logMessage, getCorrelationId(), event, userId, severity);
                break;
            default:
                log.info(logMessage, getCorrelationId(), event, userId, severity);
        }
    }

    /**
     * Masks sensitive file path information while preserving useful details.
     * 
     * @param filePath Full file path
     * @return Masked file path with only extension and directory structure
     */
    public String maskFilePath(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }

        try {
            // Extract file extension and directory depth
            String extension = getFileExtension(filePath);
            int directoryDepth = filePath.split("/").length - 1;
            
            return String.format("***MASKED_PATH***/depth_%d/%s", directoryDepth, extension);
        } catch (Exception e) {
            return MASKED_PLACEHOLDER;
        }
    }

    /**
     * Masks email address while preserving domain for debugging.
     * 
     * @param email Email address to mask
     * @return Masked email (e.g., "***@domain.com")
     */
    public String maskEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return null;
        }

        try {
            int atIndex = email.indexOf('@');
            if (atIndex > 0) {
                return "***" + email.substring(atIndex);
            }
            return MASKED_PLACEHOLDER;
        } catch (Exception e) {
            return MASKED_PLACEHOLDER;
        }
    }

    /**
     * Hashes PII data for secure logging while maintaining uniqueness.
     * 
     * @param piiData Sensitive data to hash
     * @return SHA-256 hash of the data with salt
     */
    private String hashPii(String piiData) {
        if (piiData == null || piiData.trim().isEmpty()) {
            return "null";
        }

        try {
            String saltedData = piiData + SALT;
            return DigestUtils.md5DigestAsHex(saltedData.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.warn("Failed to hash PII data", e);
            return "hash_error";
        }
    }

    /**
     * Extracts file extension from file name or path.
     * 
     * @param fileName File name or path
     * @return File extension or "unknown"
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return "unknown";
        }

        try {
            int lastDotIndex = fileName.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
                return fileName.substring(lastDotIndex + 1).toLowerCase();
            }
            return "no_extension";
        } catch (Exception e) {
            return "unknown";
        }
    }

    /**
     * Gets correlation ID from MDC or generates a default.
     * 
     * @return Correlation ID for request tracing
     */
    private String getCorrelationId() {
        String correlationId = MDC.get("correlationId");
        return correlationId != null ? correlationId : "no_correlation_id";
    }
}
