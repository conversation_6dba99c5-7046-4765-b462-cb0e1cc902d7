package com.chidhagni.ats.documentrepo.jooq;

import com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO;
import com.chidhagni.utils.BaseJsonBJooqConverter;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.validation.constraints.NotNull;

import java.util.List;

public class DocumentRepoRecipientJsonConverter extends BaseJsonBJooqConverter<List<ParticipantDetailsDTO>> {
    @Override
    @SuppressWarnings("unchecked")
    public @NotNull Class<List<ParticipantDetailsDTO>> toType() {
        // This won't be used due to TypeReference override, but kept for interface compliance
        return (Class<List<ParticipantDetailsDTO>>) (Class<?>) List.class;
    }
    
    @Override
    protected TypeReference<List<ParticipantDetailsDTO>> getTypeReference() {
        return new TypeReference<List<ParticipantDetailsDTO>>() {};
    }
}