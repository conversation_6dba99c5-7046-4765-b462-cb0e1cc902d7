package com.chidhagni.ats.documentrepo.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

/**
 * Response DTO for document upload operations
 * Contains all necessary information to update applicant document references
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentUploadResponseDTO {

    /**
     * Document ID from document_repo table
     */
    private UUID documentId;

    /**
     * Original filename from upload
     */
    private String originalFileName;

    /**
     * File path where the document is stored
     */
    private String filePath;

    /**
     * File size in bytes
     */
    private Long fileSize;

    /**
     * File type/extension
     */
    private String fileType;

    /**
     * Upload timestamp
     */
    private String uploadedAt;

    /**
     * File index from frontend (for mapping to document metadata)
     */
    private Integer fileIndex;
}