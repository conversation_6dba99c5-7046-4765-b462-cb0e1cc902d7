package com.chidhagni.ats.documentrepo.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParticipantDetailsDTO {
    private UUID organisationId;
    private String organisationName;
    private UUID individualId;
    private String individualName;
    private String email;
    private String phoneNumber;
}