package com.chidhagni.ats.documentrepo.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentRepoDTO {
    @NotNull
    private UUID category;
    private UUID subCategory;
    private ParticipantDetailsDTO senderDetails;
    private List<ParticipantDetailsDTO> recipientDetails;
    private String path;
    private String remarks;
    private LocalDateTime fileDate;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private TagsDTO tags;
}