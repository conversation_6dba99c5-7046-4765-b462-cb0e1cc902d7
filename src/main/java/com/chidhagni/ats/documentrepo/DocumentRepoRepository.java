package com.chidhagni.ats.documentrepo;

import com.chidhagni.ats.db.jooq.tables.daos.DocumentRepoDao;
import com.chidhagni.ats.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.exception.DatabaseOperationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.exception.DataAccessException;
import org.slf4j.MDC;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;
import java.util.UUID;

import static com.chidhagni.ats.db.jooq.tables.DocumentRepo.DOCUMENT_REPO;

@Repository
@RequiredArgsConstructor
@Slf4j
public class DocumentRepoRepository {

    private final DSLContext dslContext;
    private final DocumentRepoDao documentRepoDao;

    /**
     * Inserts a new document record with enhanced exception handling
     */
    public void insertDocument(DocumentRepo documentRepo) {
        String correlationId = MDC.get("correlationId");

        if (documentRepo == null) {
            throw new IllegalArgumentException("DocumentRepo cannot be null");
        }

        log.info("Inserting document - correlation_id={}, documentId={}, path={}",
                correlationId, documentRepo.getId(), documentRepo.getPath());

        try {
            documentRepoDao.insert(documentRepo);
            log.info("Document inserted successfully - correlation_id={}, documentId={}, path={}",
                    correlationId, documentRepo.getId(), documentRepo.getPath());

        } catch (DataIntegrityViolationException e) {
            log.error("Data integrity violation inserting document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentRepo.getId(), e.getMessage(), e);
            throw new DatabaseOperationException("Data integrity violation while inserting document", e);

        } catch (DataAccessException e) {
            log.error("Database access error inserting document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentRepo.getId(), e.getMessage(), e);
            throw new DatabaseOperationException("Database access error while inserting document", e);

        } catch (Exception e) {
            log.error("Unexpected error inserting document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentRepo.getId(), e.getMessage(), e);
            throw new DatabaseOperationException("Unexpected error while inserting document", e);
        }
    }

    /**
     * Gets document by ID with enhanced exception handling
     */
    public DocumentRepo getDocumentByID(UUID documentId) {
        String correlationId = MDC.get("correlationId");

        if (documentId == null) {
            throw new IllegalArgumentException("Document ID cannot be null");
        }

        log.debug("Retrieving document by ID - correlation_id={}, documentId={}",
                 correlationId, documentId);

        try {
            DocumentRepo document = dslContext.selectFrom(DOCUMENT_REPO)
                    .where(DOCUMENT_REPO.ID.eq(documentId))
                    .and(DOCUMENT_REPO.IS_ACTIVE.eq(true))
                    .fetchOneInto(DocumentRepo.class);

            if (document != null) {
                log.debug("Document retrieved successfully - correlation_id={}, documentId={}, path={}",
                         correlationId, documentId, document.getPath());
            } else {
                log.warn("Document not found - correlation_id={}, documentId={}",
                        correlationId, documentId);
            }

            return document;

        } catch (DataAccessException e) {
            log.error("Database access error retrieving document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentId, e.getMessage(), e);
            throw new DatabaseOperationException("Database access error while retrieving document", e);

        } catch (Exception e) {
            log.error("Unexpected error retrieving document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentId, e.getMessage(), e);
            throw new DatabaseOperationException("Unexpected error while retrieving document", e);
        }
    }

    /**
     * Checks if document exists by ID
     */
    public boolean isDocumentExistById(UUID documentId) {
        try {
            Integer count = dslContext.selectCount()
                    .from(DOCUMENT_REPO)
                    .where(DOCUMENT_REPO.ID.eq(documentId))
                    .and(DOCUMENT_REPO.IS_ACTIVE.eq(true))
                    .fetchOne(0, Integer.class);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("Error checking document existence: {}", documentId, e);
            throw new DatabaseOperationException("Failed to check document existence", e);
        }
    }

    /**
     * Soft deletes a document by setting is_active to false with enhanced exception handling
     */
    public int deleteDocument(UUID documentId, UUID updatedBy) {
        String correlationId = MDC.get("correlationId");

        if (documentId == null) {
            throw new IllegalArgumentException("Document ID cannot be null");
        }
        if (updatedBy == null) {
            throw new IllegalArgumentException("Updated by user ID cannot be null");
        }

        log.info("Soft deleting document - correlation_id={}, documentId={}, updatedBy={}",
                correlationId, documentId, updatedBy);

        try {
            int rowsAffected = dslContext.update(DOCUMENT_REPO)
                    .set(DOCUMENT_REPO.IS_ACTIVE, false)
                    .set(DOCUMENT_REPO.UPDATED_BY, updatedBy)
                    .set(DOCUMENT_REPO.UPDATED_ON, java.time.LocalDateTime.now())
                    .where(DOCUMENT_REPO.ID.eq(documentId))
                    .and(DOCUMENT_REPO.IS_ACTIVE.eq(true)) // Only delete active documents
                    .execute();

            if (rowsAffected > 0) {
                log.info("Document soft deleted successfully - correlation_id={}, documentId={}, rowsAffected={}",
                        correlationId, documentId, rowsAffected);
            } else {
                log.warn("No document found to delete - correlation_id={}, documentId={}",
                        correlationId, documentId);
            }

            return rowsAffected;

        } catch (DataAccessException e) {
            log.error("Database access error deleting document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentId, e.getMessage(), e);
            throw new DatabaseOperationException("Database access error while deleting document", e);

        } catch (Exception e) {
            log.error("Unexpected error deleting document - correlation_id={}, documentId={}, error={}",
                     correlationId, documentId, e.getMessage(), e);
            throw new DatabaseOperationException("Unexpected error while deleting document", e);
        }
    }

    /**
     * Deletes a document for compensation scenarios where updatedBy is not available.
     * Uses system UUID for compensation scenarios.
     *
     * @param documentId Document ID to delete
     * @return Number of rows affected
     */
    public int deleteDocument(UUID documentId) {
        // Use a system UUID for compensation scenarios
        UUID systemUserId = UUID.randomUUID();
        return deleteDocument(documentId, systemUserId);
    }

    /**
     * ✅ PERFORMANCE OPTIMIZATION: Batch query to fetch multiple documents by IDs.
     * Replaces N individual queries with a single batch query to fix N+1 performance issues.
     *
     * @param documentIds Set of document IDs to fetch
     * @return List of existing documents
     */
    public List<DocumentRepo> findByIdIn(Set<UUID> documentIds) {
        String correlationId = MDC.get("correlationId");

        if (documentIds == null || documentIds.isEmpty()) {
            log.debug("No document IDs provided for batch fetch - correlation_id={}", correlationId);
            return List.of();
        }

        try {
            log.debug("Fetching {} documents using batch query - correlation_id={}", documentIds.size(), correlationId);

            List<DocumentRepo> documents = dslContext
                    .selectFrom(DOCUMENT_REPO)
                    .where(DOCUMENT_REPO.ID.in(documentIds))
                    .and(DOCUMENT_REPO.IS_ACTIVE.eq(true))
                    .fetchInto(DocumentRepo.class);

            log.debug("Batch query returned {} documents - correlation_id={}", documents.size(), correlationId);
            return documents;

        } catch (DataAccessException e) {
            log.error("Database error during batch document fetch - correlation_id={}, documentIds={}",
                     correlationId, documentIds, e);
            throw new DatabaseOperationException("Failed to fetch documents by IDs", e);
        } catch (Exception e) {
            log.error("Unexpected error during batch document fetch - correlation_id={}, documentIds={}",
                     correlationId, documentIds, e);
            throw new DatabaseOperationException("Unexpected error during document fetch", e);
        }
    }
}