package com.chidhagni.ats.documentrepo;

import com.chidhagni.ats.applicant.dto.request.ApplicantRequestDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.ats.documentrepo.dto.request.DocumentRepoDTO;
import com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO;
import com.chidhagni.ats.documentrepo.dto.response.DocumentUploadResponseDTO;
import com.chidhagni.ats.documentrepo.utils.DocumentRepoMapper;
import com.chidhagni.ats.logging.SecureLogger;
import com.chidhagni.exception.DocumentNotFoundException;
import com.chidhagni.exception.FileUploadException;
import com.chidhagni.exception.StorageException;
import com.chidhagni.exception.ValidationException;
import com.chidhagni.filestore.service.FileStoreService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentRepoService {

    private final DocumentRepoRepository documentRepoRepository;
    private final FileStoreService fileStoreService;
    private final SecureLogger secureLogger;

    /**
     * Uploads a single file and stores document metadata
     * Following ProjectHouzer pattern with enhanced exception handling
     */
    public UUID upload(MultipartFile file, DocumentRepoDTO documentRepoDTO) {
        String correlationId = MDC.get("correlationId");
        log.info("Starting document upload - correlation_id={}, fileName={}",
                correlationId, file != null ? file.getOriginalFilename() : "null");

        try {
            // Validate inputs
            validateRecipientAndSenderDetails(documentRepoDTO);
            validateFileType(file);

            UUID documentRepoId = UUID.randomUUID();
            UUID result = processFiles(file, documentRepoId, documentRepoDTO);

            log.info("Document upload completed successfully - correlation_id={}, documentId={}, fileName={}",
                    correlationId, result, Objects.requireNonNull(file).getOriginalFilename());
            return result;

        } catch (ValidationException | FileUploadException | StorageException e) {
            log.error("Document upload failed - correlation_id={}, fileName={}, error={}",
                    correlationId, file != null ? file.getOriginalFilename() : "null", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during document upload - correlation_id={}, fileName={}",
                    correlationId, file != null ? file.getOriginalFilename() : "null", e);
            throw new FileUploadException("Unexpected error during document upload", e);
        }
    }

    /**
     * ✅ FILE-METADATA MAPPING: Core Implementation
     *
     * This method implements the EXACT strategy you described:
     *
     * 1. Frontend appends files in order:
     *    formData.append("files", resumeFile);      // index 0
     *    formData.append("files", coverLetterFile); // index 1
     *
     * 2. Spring Boot preserves order:
     *    List<MultipartFile> files → files.get(0) = resumeFile, files.get(1) = coverLetterFile
     *
     * 3. We track index during upload:
     *    files.get(0) gets fileIndex: 0 → maps to document with fileIndex: 0 ("Resume")
     *    files.get(1) gets fileIndex: 1 → maps to document with fileIndex: 1 ("Cover Letter")
     *
     * ✅ RESULT: Perfect file-metadata alignment!
     */
    public List<DocumentUploadResponseDTO> uploadMultipleFilesWithMetadata(List<MultipartFile> files, DocumentRepoDTO documentRepoDTO) {
        String correlationId = MDC.get("correlationId");
        log.info("Starting multiple file upload with metadata - correlation_id={}, fileCount={}",
                correlationId, files != null ? files.size() : 0);

        if (files == null || CollectionUtils.isEmpty(files)) {
            throw ValidationException.forNullOrEmpty("files");
        }

        // ✅ CRITICAL MAPPING LOGIC: IntStream preserves file order and assigns correct indices
        //
        // IntStream.range(0, files.size()) creates: [0, 1, 2, ...]
        // files.get(0) → gets index 0 → will map to document with fileIndex: 0
        // files.get(1) → gets index 1 → will map to document with fileIndex: 1
        //
        // This is EXACTLY your understanding: first file = index 0, second file = index 1!
        List<DocumentUploadResponseDTO> successfulUploads = IntStream.range(0, files.size())
                .mapToObj(index -> uploadFileWithIndex(files.get(index), index, documentRepoDTO, correlationId))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        // Log failed uploads (those that returned empty Optional)
        List<String> failedFiles = IntStream.range(0, files.size())
                .filter(index -> files.get(index) == null ||
                        !isFileUploadSuccessful(files.get(index), index, successfulUploads))
                .mapToObj(index -> files.get(index) != null ? files.get(index).getOriginalFilename() : "null-file-" + index)
                .collect(Collectors.toList());

        log.info("Multiple file upload with metadata completed - correlation_id={}, successful={}, failed={}, failedFiles={}",
                correlationId, successfulUploads.size(), failedFiles.size(), failedFiles);

        return successfulUploads;
    }

    /**
     * ✅ FIXED: Uploads multiple files with individual metadata for each file.
     * Each file gets its own DocumentRepoDTO with correct category/subcategory from corresponding DocumentDTO.
     *
     * ✅ CRITICAL FIX: Now preserves original fileIndex from document metadata instead of using array position.
     * This fixes the PUT endpoint issue where new documents have non-sequential fileIndex values.
     *
     * @param files List of files to upload
     * @param requestDTO Applicant request data containing document metadata
     * @return List of successful upload responses
     * @throws ValidationException if input validation fails
     */
    public List<DocumentUploadResponseDTO> uploadMultipleFilesWithIndividualMetadata(List<MultipartFile> files,
                                                                                    ApplicantRequestDTO requestDTO) {
        String correlationId = MDC.get("correlationId");
        log.info("Starting batch file upload with individual metadata - correlation_id={}, fileCount={}", correlationId, files.size());

        if (CollectionUtils.isEmpty(files)) {
            throw ValidationException.forNullOrEmpty("files");
        }

        // ✅ CRITICAL FIX: Map files to documents by fileIndex, not by array position
        // This fixes the PUT endpoint issue where documents have non-sequential fileIndex values
        log.info("🔍 DEBUG: Starting file upload mapping - correlation_id={}, fileCount={}, documentCount={}",
                correlationId, files.size(), requestDTO.getDocuments().size());

        // Log all documents for debugging
        requestDTO.getDocuments().forEach(doc ->
            log.info("🔍 DEBUG: Document in upload request - title={}, fileIndex={}, filePath={}, id={}",
                    doc.getTitle(), doc.getFileIndex(), doc.getFilePath(), doc.getId()));

        // ✅ CRITICAL FIX: Map files to documents using fileIndex directly, not array position
        List<DocumentUploadResponseDTO> successfulUploads = new ArrayList<>();
        
        // Get only new documents (those that need file upload)
        List<DocumentDTO> newDocuments = requestDTO.getDocuments().stream()
                .filter(doc -> doc.getFileIndex() != null && (doc.getFilePath() == null || doc.getFilePath().trim().isEmpty()))
                .sorted((d1, d2) -> Integer.compare(d1.getFileIndex(), d2.getFileIndex())) // Sort by fileIndex to match file order
                .collect(Collectors.toList());
        
        log.info("🔍 DEBUG: Found {} new documents that need file upload", newDocuments.size());
        newDocuments.forEach(doc ->
            log.info("🔍 DEBUG: New document - title={}, fileIndex={}", doc.getTitle(), doc.getFileIndex()));
        
        // Map files to new documents in order
        for (int fileIndex = 0; fileIndex < files.size() && fileIndex < newDocuments.size(); fileIndex++) {
            MultipartFile file = files.get(fileIndex);
            DocumentDTO document = newDocuments.get(fileIndex);
            
            log.info("🔍 DEBUG: Mapping file[{}]='{}' to document with fileIndex={}, title='{}'",
                    fileIndex, file.getOriginalFilename(), document.getFileIndex(), document.getTitle());
            
            // Upload file with the document's actual fileIndex (critical for later mapping)
            Optional<DocumentUploadResponseDTO> uploadResult = uploadFileWithIndividualMetadata(
                    file, document.getFileIndex(), requestDTO, correlationId);
            
            if (uploadResult.isPresent()) {
                successfulUploads.add(uploadResult.get());
                log.info("🔍 DEBUG: ✅ Successfully uploaded file with fileIndex={}", document.getFileIndex());
            } else {
                log.error("🔍 DEBUG: ❌ Failed to upload file for document with fileIndex={}", document.getFileIndex());
            }
        }

        // Log upload results for debugging
        if (successfulUploads.size() < files.size()) {
            log.warn("Some files failed to upload - correlation_id={}, totalFiles={}, successfulUploads={}",
                    correlationId, files.size(), successfulUploads.size());
        }

        log.info("Batch file upload completed - correlation_id={}, successful={}, failed={}",
                correlationId, successfulUploads.size(), files.size() - successfulUploads.size());

        return successfulUploads;
    }

    /**
     * ✅ INDEX ASSIGNMENT: This is where each file gets its mapping index!
     *
     * CRITICAL UNDERSTANDING:
     * - file parameter: The actual file from files.get(index)
     * - index parameter: The position in the files array (0, 1, 2...)
     *
     * MAPPING LOGIC:
     * - files.get(0) → index=0 → fileIndex: 0 in response → maps to document with fileIndex: 0
     * - files.get(1) → index=1 → fileIndex: 1 in response → maps to document with fileIndex: 1
     *
     * This ensures: "first file is for fileIndex = 0, second file is for fileIndex = 1"
     */
    private Optional<DocumentUploadResponseDTO> uploadFileWithIndex(MultipartFile file, int index,
                                                                    DocumentRepoDTO documentRepoDTO,
                                                                    String correlationId) {

        try {
            UUID documentId = upload(file, documentRepoDTO);

            // Get the stored document details from repository
            DocumentRepo documentRepo = documentRepoRepository.getDocumentByID(documentId);

            // Build response with complete metadata
            DocumentUploadResponseDTO response = DocumentUploadResponseDTO.builder()
                    .documentId(documentId)
                    .originalFileName(file.getOriginalFilename())
                    .filePath(documentRepo.getPath())  // ✅ Return complete path with document ID and filename
                    .fileSize(file.getSize())
                    .fileType(getFileExtension(Objects.requireNonNull(file.getOriginalFilename())))
                    .fileIndex(index) // ✅ CRITICAL INDEX MAPPING: This creates the bridge!
                    // index=0 → fileIndex: 0 → finds document with fileIndex: 0 ("Resume")
                    // index=1 → fileIndex: 1 → finds document with fileIndex: 1 ("Cover Letter")
                    .uploadedAt(documentRepo.getCreatedOn().toString())
                    .build();

            log.debug("File uploaded successfully with metadata - correlation_id={}, fileName={}, documentId={}, fileIndex={}, filePath={}",
                    correlationId, file.getOriginalFilename(), documentId, index, documentRepo.getPath());

            return Optional.of(response);

        } catch (Exception e) {
            String fileName = file.getOriginalFilename();
            log.error("Failed to upload file in batch - correlation_id={}, fileName={}, index={}, error={}",
                    correlationId, fileName, index, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * ✅ NEW METHOD: Uploads a single file with individual metadata based on fileIndex.
     * Creates DocumentRepoDTO with correct category/subcategory from corresponding DocumentDTO.
     *
     * @param file The file to upload
     * @param index The file index (0, 1, 2...)
     * @param requestDTO Applicant request data containing document metadata
     * @param correlationId Correlation ID for logging
     * @return Optional containing upload response if successful
     */
    private Optional<DocumentUploadResponseDTO> uploadFileWithIndividualMetadata(MultipartFile file, int index,
                                                                                ApplicantRequestDTO requestDTO,
                                                                                String correlationId) {
        try {
            // ✅ CRITICAL FIX: Find the DocumentDTO that matches this file's index
            DocumentDTO documentMetadata = findDocumentByFileIndex(requestDTO.getDocuments(), index);

            if (documentMetadata == null) {
                log.warn("No document metadata found for fileIndex: {} - correlation_id={}", index, correlationId);
                return Optional.empty();
            }

            // ✅ FIXED: Create individual DocumentRepoDTO with correct category/subcategory
            DocumentRepoDTO documentRepoDTO = buildDocumentRepoDTO(requestDTO, documentMetadata);

            UUID documentId = upload(file, documentRepoDTO);

            // Get the stored document details from repository
            DocumentRepo documentRepo = documentRepoRepository.getDocumentByID(documentId);

            // Build response with complete metadata
            DocumentUploadResponseDTO response = DocumentUploadResponseDTO.builder()
                    .documentId(documentId)
                    .originalFileName(file.getOriginalFilename())
                    .filePath(documentRepo.getPath())
                    .fileSize(file.getSize())
                    .fileType(getFileExtension(Objects.requireNonNull(file.getOriginalFilename())))
                    .fileIndex(index)
                    .uploadedAt(documentRepo.getCreatedOn().toString())
                    .build();

            // ✅ SECURE LOGGING: Use SecureLogger for PII-safe logging
            secureLogger.logFileUpload(documentId, file.getOriginalFilename(), file.getSize(),
                    getFileExtension(Objects.requireNonNull(file.getOriginalFilename())));

            log.info("File uploaded successfully - correlation_id={}, documentId={}, fileIndex={}, category={}, subcategory={}",
                    correlationId, documentId, index, documentMetadata.getCategory(), documentMetadata.getSubcategory());

            return Optional.of(response);

        } catch (Exception e) {
            String fileName = file.getOriginalFilename();
            log.error("Failed to upload file with individual metadata - correlation_id={}, fileName={}, index={}, error={}",
                    correlationId, fileName, index, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * ✅ HELPER METHOD: Finds DocumentDTO by fileIndex from the documents list.
     *
     * @param documents List of document metadata
     * @param fileIndex The file index to search for
     * @return DocumentDTO matching the fileIndex, or null if not found
     */
    private DocumentDTO findDocumentByFileIndex(List<DocumentDTO> documents, int fileIndex) {
        if (CollectionUtils.isEmpty(documents)) {
            return null;
        }

        return documents.stream()
                .filter(doc -> doc.getFileIndex() != null && doc.getFileIndex().equals(fileIndex))
                .findFirst()
                .orElse(null);
    }


    /**
     * ✅ HELPER METHOD: Builds DocumentRepoDTO with individual document metadata.
     *
     * @param requestDTO Applicant request data
     * @param documentMetadata Individual document metadata
     * @return DocumentRepoDTO with correct category/subcategory
     */
    private DocumentRepoDTO buildDocumentRepoDTO(ApplicantRequestDTO requestDTO, DocumentDTO documentMetadata) {
        // Build sender details from request
        ParticipantDetailsDTO senderDetails = ParticipantDetailsDTO.builder()
                .individualName(requestDTO.getFirstName() + " " + requestDTO.getLastName())
                .email(requestDTO.getEmail())
                .build();

        // Build recipient details (system/organization)
        ParticipantDetailsDTO recipientDetails = ParticipantDetailsDTO.builder()
                .organisationId(requestDTO.getOrgId())
                .organisationName("")
                .build();

        return DocumentRepoDTO.builder()
                .category(documentMetadata.getCategory())        // ✅ FIXED: Individual category
                .subCategory(documentMetadata.getSubcategory())  // ✅ FIXED: Individual subcategory
                .senderDetails(senderDetails)
                .recipientDetails(List.of(recipientDetails))
                .remarks(documentMetadata.getComments())
                .fileDate(LocalDateTime.now())
                .createdOn(LocalDateTime.now())
                .updatedOn(LocalDateTime.now())
                .build();
    }

    /**
     * ✅ HELPER METHOD: Checks if file upload was successful
     */
    private boolean isFileUploadSuccessful(MultipartFile file, int index, List<DocumentUploadResponseDTO> successfulUploads) {
        return successfulUploads.stream()
                .anyMatch(upload -> upload.getFileIndex().equals(index) &&
                        Objects.equals(upload.getOriginalFileName(), file.getOriginalFilename()));
    }

    /**
     * Legacy method for backward compatibility
     * @deprecated Use uploadMultipleFilesWithMetadata instead
     */
    @Deprecated
    public List<UUID> uploadMultipleFiles(List<MultipartFile> files, DocumentRepoDTO documentRepoDTO) {
        return uploadMultipleFilesWithMetadata(files, documentRepoDTO)
                .stream()
                .map(DocumentUploadResponseDTO::getDocumentId)
                .collect(Collectors.toList());
    }

    /**
     * Gets document paths by document IDs with enhanced exception handling
     */
    public List<String> getByDocumentRepoIdList(List<UUID> documentsIds) {
        String correlationId = MDC.get("correlationId");
        log.info("Retrieving document paths - correlation_id={}, documentCount={}",
                correlationId, documentsIds != null ? documentsIds.size() : 0);

        if (documentsIds == null || documentsIds.isEmpty()) {
            throw ValidationException.forNullOrEmpty("documentsIds");
        }

        try {
            List<String> documentPaths = new ArrayList<>();
            List<UUID> notFoundIds = new ArrayList<>();

            for (UUID documentId : documentsIds) {
                if (documentId == null) {
                    log.warn("Skipping null document ID - correlation_id={}", correlationId);
                    continue;
                }

                try {
                    DocumentRepo document = documentRepoRepository.getDocumentByID(documentId);
                    if (document != null && document.getPath() != null) {
                        documentPaths.add(document.getPath());
                        log.debug("Document path retrieved - correlation_id={}, documentId={}, path={}",
                                correlationId, documentId, document.getPath());
                    } else {
                        notFoundIds.add(documentId);
                        log.warn("Document not found or has null path - correlation_id={}, documentId={}",
                                correlationId, documentId);
                    }
                } catch (Exception e) {
                    log.error("Error retrieving document - correlation_id={}, documentId={}, error={}",
                            correlationId, documentId, e.getMessage(), e);
                    notFoundIds.add(documentId);
                }
            }

            if (!notFoundIds.isEmpty()) {
                log.warn("Some documents were not found - correlation_id={}, notFoundCount={}, notFoundIds={}",
                        correlationId, notFoundIds.size(), notFoundIds);
            }

            log.info("Document paths retrieval completed - correlation_id={}, foundCount={}, notFoundCount={}",
                    correlationId, documentPaths.size(), notFoundIds.size());

            return documentPaths;

        } catch (Exception e) {
            log.error("Unexpected error retrieving document paths - correlation_id={}", correlationId, e);
            throw new DocumentNotFoundException("Failed to retrieve document paths", e);
        }
    }

    /**
     * Processes the uploaded document file with enhanced exception handling
     */
    public UUID processFiles(MultipartFile file, UUID documentID, DocumentRepoDTO documentRepoDTO) {
        String correlationId = MDC.get("correlationId");
        log.debug("Processing document file - correlation_id={}, documentId={}, fileName={}",
                correlationId, documentID, file != null ? file.getOriginalFilename() : "null");

        try {
            return uploadSingle(file, documentID, documentRepoDTO);
        } catch (Exception e) {
            log.error("Unexpected error processing document file - correlation_id={}, documentId={}, fileName={}",
                    correlationId, documentID, file != null ? file.getOriginalFilename() : "null", e);
            throw new FileUploadException("Unexpected error processing document file", e);
        }
    }

    /**
     * Handles the actual file upload and storage process with enhanced exception handling
     */
    public UUID uploadSingle(MultipartFile file, UUID documentID, DocumentRepoDTO documentRepoDTO) {
        String correlationId = MDC.get("correlationId");
        String fileName = file != null ? file.getOriginalFilename() : "unknown";
        log.info("Starting single file upload - correlation_id={}, documentId={}, fileName={}",
                correlationId, documentID, fileName);

        Path tempPath = null;
        try {
            // Validate inputs
            if (file == null || file.isEmpty()) {
                throw ValidationException.forNullOrEmpty("file");
            }
            if (documentID == null) {
                throw ValidationException.forNullOrEmpty("documentID");
            }
            if (documentRepoDTO == null) {
                throw ValidationException.forNullOrEmpty("documentRepoDTO");
            }

            String folders = buildFolderPath(documentRepoDTO);
            log.debug("Built folder path - correlation_id={}, documentId={}, folderPath={}",
                    correlationId, documentID, folders);

            // Create temporary file with proper cleanup
            tempPath = createTempFile(file, fileName);
            log.debug("Created temporary file - correlation_id={}, documentId={}, tempPath={}",
                    correlationId, documentID, tempPath);

            // Build file path for storage
            String filePath = folders + "/" + documentID;
            Path absolutePath = tempPath.toAbsolutePath();

            // Store file using FileStoreService
            String fileLocation = storeFileWithRetry(filePath, absolutePath, fileName);
            log.debug("File stored successfully - correlation_id={}, documentId={}, fileLocation={}",
                    correlationId, documentID, fileLocation);

            // Map to entity and save
            DocumentRepo documentRepo = DocumentRepoMapper.mapToEntity(documentID, documentRepoDTO, fileLocation);
            documentRepoRepository.insertDocument(documentRepo);

            log.info("Single file upload completed successfully - correlation_id={}, documentId={}, fileName={}",
                    correlationId, documentID, fileName);
            return documentRepo.getId();

        } catch (ValidationException | FileUploadException | StorageException e) {
            log.error("Document upload failed - correlation_id={}, documentId={}, fileName={}, error={}",
                    correlationId, documentID, fileName, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during file upload - correlation_id={}, documentId={}, fileName={}",
                    correlationId, documentID, fileName, e);
            throw new FileUploadException("Unexpected error during file upload", e);
        } finally {
            // Clean up temporary file
            cleanupTempFile(tempPath, correlationId, documentID);
        }
    }

    /**
     * Validates file type with enhanced exception handling
     */
    private void validateFileType(MultipartFile file) {
        String correlationId = MDC.get("correlationId");

        if (file == null) {
            throw ValidationException.forNullOrEmpty("file");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.trim().isEmpty()) {
            throw ValidationException.forNullOrEmpty("fileName");
        }

        // Check file size
        long fileSize = file.getSize();
        long maxSize = 10 * 1024 * 1024; // 10MB - should be configurable
        if (fileSize > maxSize) {
            log.warn("File size validation failed - correlation_id={}, fileName={}, fileSize={}, maxSize={}",
                    correlationId, fileName, fileSize, maxSize);
            throw ValidationException.forInvalidFileSize(fileName, fileSize, maxSize);
        }

        String fileExtension = getFileExtension(fileName).toLowerCase();
        List<String> allowedExtensions = List.of("jpg", "jpeg", "png", "gif", "pdf", "docx", "xlsx", "txt", "csv");

        if (!allowedExtensions.contains(fileExtension)) {
            log.warn("File type validation failed - correlation_id={}, fileName={}, fileExtension={}, allowedExtensions={}",
                    correlationId, fileName, fileExtension, allowedExtensions);
            throw ValidationException.forInvalidFileType(fileName, fileExtension, allowedExtensions);
        }

        log.debug("File type validation passed - correlation_id={}, fileName={}, fileExtension={}, fileSize={}",
                correlationId, fileName, fileExtension, fileSize);
    }

    /**
     * Validates recipient and sender details with enhanced exception handling
     */
    private void validateRecipientAndSenderDetails(DocumentRepoDTO documentRepoDTO) {
        String correlationId = MDC.get("correlationId");

        if (documentRepoDTO == null) {
            throw ValidationException.forNullOrEmpty("documentRepoDTO");
        }

        if (documentRepoDTO.getSenderDetails() == null) {
            log.warn("Sender details validation failed - correlation_id={}", correlationId);
            throw new ValidationException("senderDetails", null, "Sender details are required");
        }

        if (documentRepoDTO.getRecipientDetails() == null || documentRepoDTO.getRecipientDetails().isEmpty()) {
            log.warn("Recipient details validation failed - correlation_id={}", correlationId);
            throw new ValidationException("recipientDetails", null, "Recipient details are required");
        }

        // Validate category
        if (documentRepoDTO.getCategory() == null) {
            log.warn("Category validation failed - correlation_id={}", correlationId);
            throw new ValidationException("category", null, "Document category is required");
        }

        log.debug("Participant details validation passed - correlation_id={}, recipientCount={}, hasSender={}, category={}",
                correlationId, documentRepoDTO.getRecipientDetails().size(),
                documentRepoDTO.getSenderDetails() != null, documentRepoDTO.getCategory());
    }

    /**
     * Builds folder path for file storage
     */
    private String buildFolderPath(DocumentRepoDTO documentRepoDTO) {
        // Build path based on category and subcategory
        StringBuilder pathBuilder = new StringBuilder("documents");

        if (documentRepoDTO.getCategory() != null) {
            pathBuilder.append("/").append(documentRepoDTO.getCategory());
        }

        if (documentRepoDTO.getSubCategory() != null) {
            pathBuilder.append("/").append(documentRepoDTO.getSubCategory());
        }

        return pathBuilder.toString();
    }

    /**
     * Extracts file extension from filename
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            return "";
        }
        return fileName.substring(lastDotIndex + 1);
    }

    /**
     * Creates a temporary file for upload processing with proper error handling
     */
    private Path createTempFile(MultipartFile file, String fileName) {
        String correlationId = MDC.get("correlationId");

        try {
            // Create temp file with unique name to avoid conflicts
            String tempFileName = UUID.randomUUID() + "_" + fileName;
            Path tempPath = Path.of(System.getProperty("java.io.tmpdir"), tempFileName);

            log.debug("Creating temporary file - correlation_id={}, tempPath={}, originalFileName={}",
                    correlationId, tempPath, fileName);

            file.transferTo(tempPath);

            log.debug("Temporary file created successfully - correlation_id={}, tempPath={}, size={}",
                    correlationId, tempPath, Files.size(tempPath));

            return tempPath;

        } catch (IOException e) {
            log.error("Failed to create temporary file - correlation_id={}, fileName={}, error={}",
                    correlationId, fileName, e.getMessage(), e);
            throw FileUploadException.forTempFileFailure(fileName,
                    System.getProperty("java.io.tmpdir"), e);
        }
    }

    /**
     * Stores file with retry logic and proper error handling
     */
    private String storeFileWithRetry(String filePath, Path absolutePath, String fileName) {
        String correlationId = MDC.get("correlationId");

        try {
            log.debug("Storing file - correlation_id={}, filePath={}, absolutePath={}",
                    correlationId, filePath, absolutePath);

            String fileLocation = fileStoreService.storeFile(filePath, absolutePath);

            if (fileLocation == null || fileLocation.isEmpty()) {
                log.error("File storage returned null or empty location - correlation_id={}, filePath={}",
                        correlationId, filePath);
                throw StorageException.forFileSystemFailure(filePath, "store",
                        new IllegalStateException("File storage service returned null or empty location"));
            }

            log.debug("File stored successfully - correlation_id={}, fileLocation={}",
                    correlationId, fileLocation);

            return fileLocation;

        } catch (Exception e) {
            log.error("File storage failed - correlation_id={}, filePath={}, error={}",
                    correlationId, filePath, e.getMessage(), e);
            throw StorageException.forFileSystemFailure(filePath, "store", e);
        }
    }

    /**
     * Cleans up temporary file with proper error handling
     */
    private void cleanupTempFile(Path tempPath, String correlationId, UUID documentId) {
        if (tempPath != null && Files.exists(tempPath)) {
            try {
                Files.delete(tempPath);
                log.debug("Temporary file cleaned up - correlation_id={}, documentId={}, tempPath={}",
                        correlationId, documentId, tempPath);
            } catch (IOException e) {
                log.warn("Failed to cleanup temporary file - correlation_id={}, documentId={}, tempPath={}, error={}",
                        correlationId, documentId, tempPath, e.getMessage());
                // Don't throw exception for cleanup failures
            }
        }
    }

    /**
     * Deletes a document by ID with proper cleanup of both database record and file storage.
     * Used for compensation when database operations fail after successful file uploads.
     *
     * @param documentId UUID of the document to delete
     * @throws DocumentNotFoundException if document is not found
     */
    public void deleteDocument(UUID documentId) {
        String correlationId = MDC.get("correlationId");
        log.info("Starting document deletion - correlation_id={}, documentId={}", correlationId, documentId);

        if (documentId == null) {
            throw ValidationException.forNullOrEmpty("documentId");
        }

        try {
            // 1. Retrieve document details before deletion
            DocumentRepo document = documentRepoRepository.getDocumentByID(documentId);
            if (document == null) {
                log.warn("Document not found for deletion - correlation_id={}, documentId={}", correlationId, documentId);
                throw new DocumentNotFoundException("Document not found: " + documentId);
            }

            String filePath = document.getPath();
            log.debug("Document found for deletion - correlation_id={}, documentId={}, filePath={}", 
                     correlationId, documentId, filePath);

            // 2. Delete file from storage first (if path exists)
            if (filePath != null && !filePath.trim().isEmpty()) {
                try {
                    fileStoreService.deleteFile(filePath);
                    log.debug("File deleted from storage - correlation_id={}, documentId={}, filePath={}", 
                             correlationId, documentId, filePath);
                } catch (Exception e) {
                    log.warn("Failed to delete file from storage (continuing with database deletion) - correlation_id={}, documentId={}, filePath={}, error={}", 
                            correlationId, documentId, filePath, e.getMessage());
                    // Continue with database deletion even if file deletion fails
                }
            } else {
                log.debug("No file path to delete - correlation_id={}, documentId={}", correlationId, documentId);
            }

            // 3. Delete database record
            documentRepoRepository.deleteDocument(documentId);
            log.info("Document deleted successfully - correlation_id={}, documentId={}, filePath={}", 
                    correlationId, documentId, filePath);

        } catch (DocumentNotFoundException e) {
            log.warn("Document deletion failed - document not found - correlation_id={}, documentId={}", 
                    correlationId, documentId);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during document deletion - correlation_id={}, documentId={}, error={}", 
                     correlationId, documentId, e.getMessage(), e);
            throw new StorageException("Failed to delete document: " + documentId, e);
        }
    }
}