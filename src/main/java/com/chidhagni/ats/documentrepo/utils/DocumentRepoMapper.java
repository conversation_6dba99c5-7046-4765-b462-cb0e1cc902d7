package com.chidhagni.ats.documentrepo.utils;

import com.chidhagni.ats.documentrepo.dto.request.DocumentRepoDTO;
import com.chidhagni.ats.db.jooq.tables.pojos.DocumentRepo;
import com.chidhagni.exception.ValidationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.jooq.JSONB;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Mapper utility for DocumentRepo entity and DTOs with enhanced exception handling
 */
@Slf4j
public class DocumentRepoMapper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Maps DocumentRepoDTO to DocumentRepo entity with enhanced exception handling
     */
    public static DocumentRepo mapToEntity(UUID documentId, DocumentRepoDTO documentRepoDTO, String fileLocation) {
        String correlationId = MDC.get("correlationId");

        log.debug("Mapping DTO to entity - correlation_id={}, documentId={}, fileLocation={}",
                 correlationId, documentId, fileLocation);

        // Validate inputs
        if (documentId == null) {
            throw ValidationException.forNullOrEmpty("documentId");
        }
        if (documentRepoDTO == null) {
            throw ValidationException.forNullOrEmpty("documentRepoDTO");
        }
        if (fileLocation == null || fileLocation.trim().isEmpty()) {
            throw ValidationException.forNullOrEmpty("fileLocation");
        }

        try {
            DocumentRepo documentRepo = new DocumentRepo();

            documentRepo.setId(documentId);
            documentRepo.setCategory(documentRepoDTO.getCategory());
            documentRepo.setSubCategory(documentRepoDTO.getSubCategory());
            documentRepo.setPath(fileLocation);
            documentRepo.setRemarks(documentRepoDTO.getRemarks());
            documentRepo.setFileDate(documentRepoDTO.getFileDate() != null ?
                                    documentRepoDTO.getFileDate() : LocalDateTime.now());

            // Set audit fields with random UUIDs (replacing userPrincipal)
            UUID systemUserId = UUID.randomUUID();
            documentRepo.setCreatedBy(systemUserId);
            documentRepo.setCreatedOn(LocalDateTime.now());
            documentRepo.setUpdatedBy(systemUserId);
            documentRepo.setUpdatedOn(LocalDateTime.now());
            documentRepo.setIsActive(true);

            // Set sender details as JSONB with proper error handling
            if (documentRepoDTO.getSenderDetails() != null) {
                try {
                    documentRepo.setSender(documentRepoDTO.getSenderDetails());
                    log.debug("Sender details mapped successfully - correlation_id={}, documentId={}",
                             correlationId, documentId);
                } catch (Exception e) {
                    log.error("Failed to serialize sender details - correlation_id={}, documentId={}, error={}",
                             correlationId, documentId, e.getMessage(), e);
                    throw ValidationException.forJsonProcessing("senderDetails", e);
                }
            }

            // Set recipient details as JSONB with proper error handling
            if (documentRepoDTO.getRecipientDetails() != null) {
                try {
                    documentRepo.setRecipients(documentRepoDTO.getRecipientDetails());
                    log.debug("Recipient details mapped successfully - correlation_id={}, documentId={}, recipientCount={}",
                             correlationId, documentId, documentRepoDTO.getRecipientDetails().size());
                } catch (Exception e) {
                    log.error("Failed to serialize recipient details - correlation_id={}, documentId={}, error={}",
                             correlationId, documentId, e.getMessage(), e);
                    throw ValidationException.forJsonProcessing("recipientDetails", e);
                }
            }

            // Set tags as JSONB with proper error handling
            if (documentRepoDTO.getTags() != null) {
                try {
                    String tagsJson = objectMapper.writeValueAsString(documentRepoDTO.getTags());
                    documentRepo.setTags(JSONB.valueOf(tagsJson));
                    log.debug("Tags mapped successfully - correlation_id={}, documentId={}",
                             correlationId, documentId);
                } catch (JsonProcessingException e) {
                    log.error("Failed to serialize tags - correlation_id={}, documentId={}, error={}",
                             correlationId, documentId, e.getMessage(), e);
                    throw ValidationException.forJsonProcessing("tags", e);
                }
            }

            log.debug("Entity mapping completed successfully - correlation_id={}, documentId={}",
                     correlationId, documentId);
            return documentRepo;

        } catch (ValidationException e) {
            // Re-throw validation exceptions as-is
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during entity mapping - correlation_id={}, documentId={}, error={}",
                     correlationId, documentId, e.getMessage(), e);
            throw ValidationException.forJsonProcessing("entityMapping", e);
        }
    }

    /**
     * Maps DocumentRepo entity to DocumentRepoDTO with enhanced exception handling
     */
    public static DocumentRepoDTO mapToDTO(DocumentRepo documentRepo) {
        String correlationId = MDC.get("correlationId");

        if (documentRepo == null) {
            throw ValidationException.forNullOrEmpty("documentRepo");
        }

        log.debug("Mapping entity to DTO - correlation_id={}, documentId={}",
                 correlationId, documentRepo.getId());

        try {
            DocumentRepoDTO.DocumentRepoDTOBuilder builder = DocumentRepoDTO.builder()
                    .category(documentRepo.getCategory())
                    .subCategory(documentRepo.getSubCategory())
                    .path(documentRepo.getPath())
                    .remarks(documentRepo.getRemarks())
                    .fileDate(documentRepo.getFileDate())
                    .createdBy(documentRepo.getCreatedBy())
                    .updatedBy(documentRepo.getUpdatedBy())
                    .createdOn(documentRepo.getCreatedOn())
                    .updatedOn(documentRepo.getUpdatedOn());

            // Handle JSONB fields with proper error handling
            try {
                if (documentRepo.getSender() != null) {
                    // Note: JSONB deserialization would be implemented here if needed
                    // For now, we'll leave it as null as per original comment
                    log.debug("Sender details present but not deserialized - correlation_id={}, documentId={}",
                             correlationId, documentRepo.getId());
                }

                if (documentRepo.getRecipients() != null) {
                    // Note: JSONB deserialization would be implemented here if needed
                    // For now, we'll leave it as null as per original comment
                    log.debug("Recipient details present but not deserialized - correlation_id={}, documentId={}",
                             correlationId, documentRepo.getId());
                }

                if (documentRepo.getTags() != null) {
                    // Note: JSONB deserialization would be implemented here if needed
                    // For now, we'll leave it as null as per original comment
                    log.debug("Tags present but not deserialized - correlation_id={}, documentId={}",
                             correlationId, documentRepo.getId());
                }
            } catch (Exception e) {
                log.warn("Error processing JSONB fields during DTO mapping - correlation_id={}, documentId={}, error={}",
                        correlationId, documentRepo.getId(), e.getMessage());
                // Continue with mapping, leaving JSONB fields as null
            }

            DocumentRepoDTO result = builder.build();
            log.debug("DTO mapping completed successfully - correlation_id={}, documentId={}",
                     correlationId, documentRepo.getId());

            return result;

        } catch (Exception e) {
            log.error("Unexpected error during DTO mapping - correlation_id={}, documentId={}, error={}",
                     correlationId, documentRepo.getId(), e.getMessage(), e);
            throw ValidationException.forJsonProcessing("dtoMapping", e);
        }
    }
}