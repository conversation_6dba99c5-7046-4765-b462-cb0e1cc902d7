package com.chidhagni.ats.listnames;

import com.chidhagni.ats.db.jooq.tables.daos.ListNamesDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
@RequiredArgsConstructor
public class ListNamesRepository {

    private final ListNamesDao listNamesDao;

    public boolean existsById(UUID listNameId) {
        // fetchOneById returns the entity or null, so check for null
        return listNamesDao.fetchOneById(listNameId) != null;
    }
}
