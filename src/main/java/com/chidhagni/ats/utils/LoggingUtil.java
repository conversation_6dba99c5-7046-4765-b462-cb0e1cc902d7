package com.chidhagni.ats.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.UUID;

/**
 * Centralized logging utility to eliminate duplicate logging patterns.
 * Provides consistent logging methods across the application.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@Slf4j
public class LoggingUtil {

    /**
     * Logs the start of an operation.
     *
     * @param operation Operation name
     * @param entityId Entity ID (optional)
     */
    public static void logOperationStart(String operation, UUID entityId) {
        if (entityId != null) {
            log.info("Starting {} operation for ID: {}", operation, entityId);
        } else {
            log.info("Starting {} operation", operation);
        }
    }

    /**
     * Logs the successful completion of an operation.
     *
     * @param operation Operation name
     * @param entityId Entity ID (optional)
     */
    public static void logOperationSuccess(String operation, UUID entityId) {
        if (entityId != null) {
            log.info("Successfully completed {} operation for ID: {}", operation, entityId);
        } else {
            log.info("Successfully completed {} operation", operation);
        }
    }

    /**
     * Logs an operation with additional context information.
     *
     * @param operation Operation name
     * @param context Additional context
     */
    public static void logOperationWithContext(String operation, String context) {
        log.info("{} - {}", operation, context);
    }

    /**
     * Logs validation operations.
     *
     * @param validationType Type of validation
     * @param status Status (started/completed/failed)
     */
    public static void logValidation(String validationType, String status) {
        log.debug("{} validation {}", validationType, status);
    }

    /**
     * Logs repository operations.
     *
     * @param operation Repository operation
     * @param entityType Entity type
     * @param entityId Entity ID (optional)
     */
    public static void logRepositoryOperation(String operation, String entityType, UUID entityId) {
        if (entityId != null) {
            log.debug("Repository {}: {} with ID: {}", operation, entityType, entityId);
        } else {
            log.debug("Repository {}: {}", operation, entityType);
        }
    }

    /**
     * Logs file processing operations.
     *
     * @param operation File operation
     * @param fileCount Number of files
     */
    public static void logFileOperation(String operation, int fileCount) {
        log.info("{} {} file(s)", operation, fileCount);
    }

    /**
     * Logs entity not found scenarios.
     *
     * @param entityType Entity type
     * @param entityId Entity ID
     */
    public static void logEntityNotFound(String entityType, UUID entityId) {
        log.warn("{} not found for ID: {}", entityType, entityId);
    }

    /**
     * Logs duplicate entity attempts.
     *
     * @param entityType Entity type
     * @param identifier Identifier (email, etc.)
     */
    public static void logDuplicateEntity(String entityType, String identifier) {
        log.warn("Attempt to create duplicate {} with identifier: {}", entityType, 
                PiiMaskingUtil.maskEmail(identifier));
    }
}
