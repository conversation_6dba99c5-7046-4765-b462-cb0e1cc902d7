package com.chidhagni.ats.utils;

import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.exception.ValidationException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Utility class for JSON processing operations.
 * Centralizes JSON serialization/deserialization logic to follow DRY principle.
 * Provides consistent error handling and logging for JSON operations.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class JsonProcessingUtil {

    private final ObjectMapper objectMapper;

    /**
     * Safely converts an object to JSON string with error handling.
     *
     * @param object Object to serialize
     * @return JSON string representation
     * @throws ValidationException if serialization fails
     */
    public String toJsonString(Object object) {
        try {
            String json = objectMapper.writeValueAsString(object);
            log.debug("Successfully serialized object of type {} to JSON", object.getClass().getSimpleName());
            return json;
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize object to JSON: {}", e.getMessage(), e);
            throw new ValidationException("Failed to serialize object to JSON: " + e.getMessage(), e);
        }
    }

    /**
     * Safely converts JSON string to specified type with error handling.
     *
     * @param json JSON string to deserialize
     * @param targetType Target class type
     * @param <T> Type parameter
     * @return Deserialized object
     * @throws ValidationException if deserialization fails
     */
    public <T> T fromJsonString(String json, Class<T> targetType) {
        try {
            T result = objectMapper.readValue(json, targetType);
            log.debug("Successfully deserialized JSON to type {}", targetType.getSimpleName());
            return result;
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize JSON to type {}: {}", targetType.getSimpleName(), e.getMessage(), e);
            throw new ValidationException(
                String.format("Failed to deserialize JSON to %s: %s", targetType.getSimpleName(), e.getMessage()), e);
        }
    }

    /**
     * Safely converts JSON string to specified type using TypeReference.
     *
     * @param json JSON string to deserialize
     * @param typeReference TypeReference for complex types (e.g., List<SomeClass>)
     * @param <T> Type parameter
     * @return Deserialized object
     * @throws ValidationException if deserialization fails
     */
    public <T> T fromJsonString(String json, TypeReference<T> typeReference) {
        try {
            T result = objectMapper.readValue(json, typeReference);
            log.debug("Successfully deserialized JSON using TypeReference");
            return result;
        } catch (JsonProcessingException e) {
            log.error("Failed to deserialize JSON using TypeReference: {}", e.getMessage(), e);
            throw new ValidationException("Failed to deserialize JSON: " + e.getMessage(), e);
        }
    }

    /**
     * Safely converts a list of objects to JSON array string.
     *
     * @param list List of objects to serialize
     * @return JSON array string
     */
    public String listToJsonString(List<?> list) {
        if (list == null || list.isEmpty()) {
            return "[]";
        }
        return toJsonString(list);
    }

    /**
     * Safely converts JSON array string to list of specified type.
     *
     * @param json JSON array string
     * @param elementType Type of list elements
     * @param <T> Element type parameter
     * @return List of deserialized objects
     */
    public <T> List<T> jsonStringToList(String json, Class<T> elementType) {
        if (json == null || json.trim().isEmpty() || "[]".equals(json.trim())) {
            return List.of();
        }

        TypeReference<List<T>> typeReference = new TypeReference<List<T>>() {};
        return fromJsonString(json, typeReference);
    }

    /**
     * Safely converts a list of maps to JSON string (common for document structures).
     *
     * @param mapList List of maps to serialize
     * @return JSON string
     */
    public String mapListToJsonString(List<Map<String, Object>> mapList) {
        return listToJsonString(mapList);
    }

    /**
     * Safely converts JSON string to list of maps (common for document structures).
     *
     * @param json JSON string
     * @return List of maps
     */
    public List<Map<String, Object>> jsonStringToMapList(String json) {
        if (json == null || json.trim().isEmpty() || "[]".equals(json.trim())) {
            return List.of();
        }

        TypeReference<List<Map<String, Object>>> typeReference = new TypeReference<List<Map<String, Object>>>() {};
        return fromJsonString(json, typeReference);
    }

    /**
     * Validates if a string is valid JSON.
     *
     * @param json String to validate
     * @return true if valid JSON, false otherwise
     */
    public boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }

        try {
            objectMapper.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            log.debug("Invalid JSON format: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Safely gets a JSON string with fallback to empty array.
     *
     * @param object Object to serialize
     * @return JSON string or "[]" if null/empty
     */
    public String toJsonStringWithFallback(Object object) {
        if (object == null) {
            return "[]";
        }

        if (object instanceof List<?> list && list.isEmpty()) {
            return "[]";
        }

        return toJsonString(object);
    }

    /**
     * Pretty prints JSON for debugging purposes.
     *
     * @param object Object to pretty print
     * @return Pretty formatted JSON string
     */
    public String toPrettyJsonString(Object object) {
        try {
            String json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(object);
            log.debug("Successfully created pretty JSON for object of type {}", object.getClass().getSimpleName());
            return json;
        } catch (JsonProcessingException e) {
            log.error("Failed to create pretty JSON: {}", e.getMessage(), e);
            return toJsonString(object); // Fallback to regular JSON
        }
    }

    /**
     * Safely parses JSON string to a List of specified type using TypeReference.
     * This method provides a generic way to parse JSON arrays into strongly-typed lists.
     *
     * @param json JSON string to parse
     * @param typeReference TypeReference for the target list type (e.g., new TypeReference<List<DocumentDTO>>() {})
     * @param <T> The type of objects in the list
     * @return List of deserialized objects
     * @throws ValidationException if parsing fails
     */
    public <T> List<T> parseJsonToList(String json, TypeReference<List<T>> typeReference) {
        if (json == null || json.trim().isEmpty()) {
            log.debug("Empty or null JSON provided, returning empty list");
            return List.of();
        }

        if ("[]".equals(json.trim())) {
            log.debug("Empty JSON array provided, returning empty list");
            return List.of();
        }

        try {
            List<T> result = objectMapper.readValue(json, typeReference);
            log.debug("Successfully parsed JSON to list with {} elements", result != null ? result.size() : 0);
            return result != null ? result : List.of();
        } catch (JsonProcessingException e) {
            log.error("Failed to parse JSON to list using TypeReference: {}", e.getMessage(), e);
            log.debug("Problematic JSON content: {}", json);
            throw new ValidationException("Failed to parse JSON to list: " + e.getMessage(), e);
        }
    }
}
