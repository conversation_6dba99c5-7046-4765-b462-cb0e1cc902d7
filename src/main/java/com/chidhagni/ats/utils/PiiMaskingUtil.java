package com.chidhagni.ats.utils;

import org.springframework.util.StringUtils;

/**
 * Utility class for masking PII (Personally Identifiable Information) data in logs.
 * Implements GDPR compliance by ensuring sensitive data is not exposed in application logs.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class PiiMaskingUtil {

    private PiiMaskingUtil() {
        // Utility class - prevent instantiation
    }

    /**
     * Masks email addresses for secure logging.
     * 
     * Examples:
     * - <EMAIL> -> j****@e******.com
     * - <EMAIL> -> a****@b****.co
     * - null/empty -> returns as-is
     * 
     * @param email The email to mask
     * @return Masked email safe for logging
     */
    public static String maskEmail(String email) {
        if (!StringUtils.hasText(email) || !email.contains("@")) {
            return email;
        }

        String[] parts = email.split("@");
        if (parts.length != 2) {
            return email;
        }

        String localPart = parts[0];
        String domainPart = parts[1];

        // Mask local part (keep first character, mask rest)
        String maskedLocal = localPart.length() > 1 
            ? localPart.charAt(0) + "****" 
            : localPart;

        // Mask domain part (keep first character and extension, mask middle)
        String maskedDomain;
        if (domainPart.contains(".")) {
            int lastDotIndex = domainPart.lastIndexOf(".");
            String domainName = domainPart.substring(0, lastDotIndex);
            String extension = domainPart.substring(lastDotIndex);
            
            maskedDomain = domainName.length() > 1
                ? domainName.charAt(0) + "****" + extension
                : domainName + extension;
        } else {
            maskedDomain = domainPart.length() > 1
                ? domainPart.charAt(0) + "****"
                : domainPart;
        }

        return maskedLocal + "@" + maskedDomain;
    }

    /**
     * Generic method to mask any PII string by showing only first and last characters.
     * 
     * @param piiData The PII data to mask
     * @return Masked string safe for logging
     */
    public static String maskPiiData(String piiData) {
        if (!StringUtils.hasText(piiData)) {
            return piiData;
        }

        if (piiData.length() <= 2) {
            return "****";
        }

        return piiData.charAt(0) + "****" + piiData.charAt(piiData.length() - 1);
    }
}
