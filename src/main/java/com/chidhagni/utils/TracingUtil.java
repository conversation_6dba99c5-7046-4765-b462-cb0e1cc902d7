package com.chidhagni.utils;

import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.function.Supplier;

/**
 * Utility class for tracing operations in the ATS application.
 * This class provides both logging and actual OpenTelemetry span creation.
 * The Java Agent handles automatic instrumentation, while this class creates
 * custom business-specific spans for enhanced observability.
 * 
 * <AUTHOR> Development Team
 */
@Component
@Slf4j
public class TracingUtil {

    // Constants for tracing operations
    private static final String TRACER_NAME = "ats-application";
    private static final String OPERATION_TYPE_KEY = "operation.type";
    private static final String ATTRIBUTE_LOG_MESSAGE_TEMPLATE = "Setting tracing attribute: {}={}";
    private static final String EVENT_SEPARATOR = ": ";
    private static final String ATTRIBUTE_SEPARATOR = "=";
    private static final String UNKNOWN_ID_VALUE = "unknown";

    private final Tracer tracer;

    public TracingUtil() {
        this.tracer = GlobalOpenTelemetry.getTracer(TRACER_NAME);
    }

    /**
     * Creates a custom span for business operations with automatic lifecycle management.
     * 
     * @param spanName The name of the span
     * @param operation The operation to execute within the span
     * @param <T> The return type of the operation
     * @return The result of the operation
     */
    public <T> T withSpan(String spanName, Supplier<T> operation) {
        return withSpan(spanName, null, operation);
    }

    /**
     * Creates a custom span for business operations with operation type classification.
     * 
     * @param spanName The name of the span
     * @param operationType The type of operation (e.g., "database", "service_call", "business_logic")
     * @param operation The operation to execute within the span
     * @param <T> The return type of the operation
     * @return The result of the operation
     */
    public <T> T withSpan(String spanName, String operationType, Supplier<T> operation) {
        log.debug("Creating custom span: {}", spanName);
        
        Span span = tracer.spanBuilder(spanName)
                .startSpan();
        
        try (Scope scope = span.makeCurrent()) {
            // Add operation type if provided
            if (operationType != null) {
                span.setAttribute(OPERATION_TYPE_KEY, operationType);
                log.debug(ATTRIBUTE_LOG_MESSAGE_TEMPLATE, OPERATION_TYPE_KEY, operationType);
            }
            
            // Execute the operation
            T result = operation.get();
            
            // Mark span as successful
            span.setStatus(StatusCode.OK);
            log.debug("Custom span completed successfully: {}", spanName);
            
            return result;
            
        } catch (Exception e) {
            // Record the exception in the span
            span.recordException(e);
            span.setStatus(StatusCode.ERROR, e.getMessage());
            log.error("Custom span failed with exception: {} - {}", spanName, e.getMessage());
            
            // Re-throw the exception
            throw e;
            
        } finally {
            span.end();
        }
    }

    /**
     * Executes an operation within a custom span (void operations).
     * 
     * @param spanName The name of the span
     * @param operation The operation to execute within the span
     */
    public void withSpan(String spanName, Runnable operation) {
        withSpan(spanName, null, operation);
    }

    /**
     * Executes an operation within a custom span with operation type (void operations).
     * 
     * @param spanName The name of the span
     * @param operationType The type of operation
     * @param operation The operation to execute within the span
     */
    public void withSpan(String spanName, String operationType, Runnable operation) {
        withSpan(spanName, operationType, () -> {
            operation.run();
            return null;
        });
    }

    /**
     * Adds an event to the current span with a simple message.
     * 
     * @param eventName The name of the event
     * @param message The event message
     */
    public void addEvent(String eventName, String message) {
        log.debug("Adding span event: {} - {}", eventName, message);
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.addEvent(eventName + EVENT_SEPARATOR + message);
        } else {
            log.debug("No active span found for event: {}", eventName);
        }
    }

    /**
     * Adds an event to the current span with key-value attributes.
     * 
     * @param eventName The name of the event
     * @param attributeKey The attribute key
     * @param attributeValue The attribute value
     */
    public void addEvent(String eventName, String attributeKey, String attributeValue) {
        log.debug("Adding span event with attribute: {} - {}={}", eventName, attributeKey, attributeValue);
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.addEvent(eventName + EVENT_SEPARATOR + attributeKey + ATTRIBUTE_SEPARATOR + attributeValue);
        } else {
            log.debug("No active span found for event: {}", eventName);
        }
    }

    /**
     * Sets an attribute on the current span.
     * 
     * @param key The attribute key
     * @param value The attribute value
     */
    public void setSpanAttribute(String key, String value) {
        log.debug(ATTRIBUTE_LOG_MESSAGE_TEMPLATE, key, value);
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.setAttribute(key, value);
        } else {
            log.debug("No active span found for attribute: {}", key);
        }
    }

    /**
     * Sets multiple attributes on the current span.
     * 
     * @param attributes Map of key-value attributes
     */
    public void setSpanAttributes(java.util.Map<String, String> attributes) {
        if (attributes == null || attributes.isEmpty()) {
            return;
        }
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            attributes.forEach((key, value) -> {
                currentSpan.setAttribute(key, value);
                log.debug(ATTRIBUTE_LOG_MESSAGE_TEMPLATE, key, value);
            });
        } else {
            log.debug("No active span found for attributes");
        }
    }

    /**
     * Records an exception in the current span.
     * 
     * @param exception The exception to record
     */
    public void recordException(Throwable exception) {
        log.debug("Recording exception in span: {}", exception.getMessage());
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.recordException(exception);
            currentSpan.setStatus(StatusCode.ERROR, exception.getMessage());
        } else {
            log.debug("No active span found for exception recording");
        }
    }

    /**
     * Sets the current span status to error with a description.
     * 
     * @param description Error description
     */
    public void setSpanError(String description) {
        log.debug("Setting span error status: {}", description);
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.setStatus(StatusCode.ERROR, description);
        } else {
            log.debug("No active span found for error status");
        }
    }

    /**
     * Sets the current span status to OK.
     */
    public void setSpanOk() {
        log.debug("Setting span status to OK");
        
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            currentSpan.setStatus(StatusCode.OK);
        } else {
            log.debug("No active span found for OK status");
        }
    }

    /**
     * Gets the current trace ID for logging and correlation.
     * 
     * @return The current trace ID or "unknown" if no active span
     */
    public String getCurrentTraceId() {
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            return currentSpan.getSpanContext().getTraceId();
        }
        return UNKNOWN_ID_VALUE;
    }

    /**
     * Gets the current span ID for logging and correlation.
     * 
     * @return The current span ID or "unknown" if no active span
     */
    public String getCurrentSpanId() {
        Span currentSpan = Span.current();
        if (currentSpan != null && !currentSpan.equals(Span.getInvalid())) {
            return currentSpan.getSpanContext().getSpanId();
        }
        return UNKNOWN_ID_VALUE;
    }
}
