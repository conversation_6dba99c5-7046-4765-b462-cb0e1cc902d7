package com.chidhagni.utils;

import org.apache.commons.lang3.RandomStringUtils;

import java.security.SecureRandom;

/**
 * Utility class for cryptographically secure random number generation.
 * Replaces insecure java.util.Random usage with SecureRandom for security-sensitive operations.
 * This class contains only static methods and should not be instantiated.
 */
public class SecureRandomUtils {

    // Constants for secure random generation
    private static final int OTP_RANGE = 900000;
    private static final int OTP_MINIMUM_VALUE = 100000;
    private static final int RESET_CODE_LENGTH = 64;
    private static final String UTILITY_CLASS_EXCEPTION_MESSAGE = "This is a utility class and cannot be instantiated";

    private static final SecureRandom secureRandom = new SecureRandom();

    // Character sets for different types of random strings
    private static final String ALPHANUMERIC_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    /**
     * Private constructor to prevent instantiation of this utility class.
     * This class contains only static methods and should not be instantiated.
     */
    private SecureRandomUtils() {
        throw new UnsupportedOperationException(UTILITY_CLASS_EXCEPTION_MESSAGE);
    }


    /**
     * Generates a cryptographically secure random string using alphanumeric characters.
     * 
     * @param length the length of the random string to generate
     * @return a secure random alphanumeric string
     */
    public static String generateSecureAlphanumericString(int length) {
        return RandomStringUtils.random(length, 0, ALPHANUMERIC_CHARS.length(), 
                                      false, false, ALPHANUMERIC_CHARS.toCharArray(), secureRandom);
    }



    /**
     * Generates a cryptographically secure random integer within the specified range.
     * 
     * @param bound the upper bound (exclusive) for the random number
     * @return a secure random integer between 0 (inclusive) and bound (exclusive)
     */
    public static int generateSecureInt(int bound) {
        return secureRandom.nextInt(bound);
    }

    /**
     * Generates a cryptographically secure 6-digit OTP.
     * 
     * @return a secure 6-digit OTP as a string
     */
    public static String generateSecureOTP() {
        // Generate a number between 100000 and 999999 (6 digits)
        int otp = secureRandom.nextInt(OTP_RANGE) + OTP_MINIMUM_VALUE;
        return String.valueOf(otp);
    }

    /**
     * Generates a cryptographically secure random password reset code.
     * 
     * @return a secure 64-character alphanumeric reset code
     */
    public static String generateSecureResetCode() {
        return generateSecureAlphanumericString(RESET_CODE_LENGTH);
    }
}