package com.chidhagni.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

import static com.chidhagni.utils.SecureRandomUtils.generateSecureInt;

@Component
@Slf4j
@RequiredArgsConstructor
public class CommonOperations {
    
    // Constants for system code generation
    private static final String TIMESTAMP_FORMAT_PATTERN = "yyMMddHHmmss";
    private static final int RANDOM_NUMBER_BOUND = 100_000;
    private static final int COUNTER_MODULO = 1_000;
    private static final String FIVE_DIGIT_FORMAT = "%05d";
    private static final String THREE_DIGIT_FORMAT = "%03d";
    
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(TIMESTAMP_FORMAT_PATTERN);
    private static final AtomicInteger counter = new AtomicInteger(0);

    public static String generateSystemCode(SystemCodes categoryCode) {
        String timestamp = DateUtils.currentTimeIST().format(formatter);
        int randomNum = generateSecureInt(RANDOM_NUMBER_BOUND); // Five-digit secure random number
        int count = counter.getAndIncrement() % COUNTER_MODULO; // Three-digit counter

        return categoryCode + timestamp + String.format(FIVE_DIGIT_FORMAT, randomNum) + String.format(THREE_DIGIT_FORMAT, count);
    }
}