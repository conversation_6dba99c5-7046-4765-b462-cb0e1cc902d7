package com.chidhagni.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class DateUtils {

    // Constants for timezone and formatting
    private static final String ASIA_KOLKATA_TIMEZONE = "Asia/Kolkata";
    private static final String UTC_TIMEZONE = "UTC";
    private static final String DATE_FORMAT_PATTERN = "dd/MM/yyyy";
    private static final String UTILITY_CLASS_EXCEPTION_MESSAGE = "Utility class - cannot be instantiated";

    /**
     * Private constructor to prevent instantiation of this utility class.
     * This class contains only static utility methods and should not be instantiated.
     */
    private DateUtils() {
        throw new UnsupportedOperationException(UTILITY_CLASS_EXCEPTION_MESSAGE);
    }
    public static LocalDateTime currentTimeIST() {
        return LocalDateTime.now(ZoneId.of(ASIA_KOLKATA_TIMEZONE));
    }

    public static String currentDateInIST() {
        // Current date
        // Get the current date and time in UTC
        ZonedDateTime nowInUTC = ZonedDateTime.now(ZoneId.of(UTC_TIMEZONE));

        // Convert to Asia/Kolkata timezone
        ZonedDateTime nowInKolkata = nowInUTC.withZoneSameInstant(ZoneId.of(ASIA_KOLKATA_TIMEZONE));

        // Extract the date part in Asia/Kolkata timezone
        LocalDate todayInKolkata = nowInKolkata.toLocalDate();

        // Format it to "dd/MM/yyyy"
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_FORMAT_PATTERN);
        return todayInKolkata.format(formatter);
    }

    public static LocalDate currentDate() {
        return LocalDate.now();
    }

    public static LocalDateTime currentDatetime() {
        return LocalDateTime.now();
    }
}