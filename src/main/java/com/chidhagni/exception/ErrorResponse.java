package com.chidhagni.exception;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Standardized error response structure for all API endpoints in ATS.
 * Provides consistent error information including correlation IDs for distributed tracing.
 * 
 * <AUTHOR> Development Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {
    
    /**
     * Timestamp when the error occurred
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS")
    private LocalDateTime timestamp;
    
    /**
     * HTTP status code
     */
    private int status;
    
    /**
     * Error category/type
     */
    private String error;
    
    /**
     * Human-readable error message
     */
    private String message;
    
    /**
     * Request path where the error occurred
     */
    private String path;
    
    /**
     * Unique correlation ID for request tracing
     */
    private String correlationId;
    
    /**
     * Application-specific error code
     */
    private String errorCode;
    
    /**
     * Validation errors (for field-level validation failures)
     */
    private Map<String, String> validationErrors;
    
    /**
     * Additional metadata for debugging
     */
    private Map<String, Object> metadata;
    
    /**
     * Indicates if the operation can be retried
     */
    private Boolean retryable;
    
    /**
     * Suggested retry delay in milliseconds (if retryable)
     */
    private Long retryAfterMs;
}
