package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when document processing operations fail.
 * Provides specific context for document-related errors instead of generic RuntimeException.
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DocumentProcessingException extends BaseApplicationException {

    private static final String ERROR_CODE = "DOCUMENT_PROCESSING_ERROR";
    private static final HttpStatus HTTP_STATUS = HttpStatus.INTERNAL_SERVER_ERROR;
    private static final LogLevel LOG_LEVEL = LogLevel.ERROR;

    public DocumentProcessingException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, true);
    }

    public DocumentProcessingException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, true);
    }

    /**
     * Creates exception for document transformation failures
     */
    public static DocumentProcessingException forTransformation(String operation, Throwable cause) {
        return new DocumentProcessingException(
            String.format("Failed to %s document: %s", operation, cause.getMessage()), 
            cause
        );
    }

    /**
     * Creates exception for JSON processing failures in document operations
     */
    public static DocumentProcessingException forJsonProcessing(String operation, Throwable cause) {
        return new DocumentProcessingException(
            String.format("Failed to %s document JSON: %s", operation, cause.getMessage()), 
            cause
        );
    }

    /**
     * Creates exception for document metadata processing failures
     */
    public static DocumentProcessingException forMetadataProcessing(String details, Throwable cause) {
        return new DocumentProcessingException(
            String.format("Failed to process document metadata: %s", details), 
            cause
        );
    }

    @Override
    public String getCategory() {
        return "DOCUMENT_PROCESSING";
    }
}
