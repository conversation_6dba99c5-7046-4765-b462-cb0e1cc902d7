package com.chidhagni.exception;

/**
 * Exception thrown when a job application is not found in the ATS system.
 * Extends EntityNotFoundException for consistent behavior.
 * 
 * <AUTHOR> Development Team
 */
public class ApplicationNotFoundException extends EntityNotFoundException {

    public ApplicationNotFoundException(String message) {
        super(message);
    }

    public ApplicationNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Convenience constructor for application not found by ID
     */
    public ApplicationNotFoundException(Long applicationId) {
        super("Application", applicationId);
        addMetadata("applicationId", applicationId.toString());
    }

    /**
     * Convenience constructor for application not found by candidate and job
     */
    public ApplicationNotFoundException(Long candidateId, Long jobId) {
        super(String.format("Application for candidate %d and job %d not found", candidateId, jobId));
        addMetadata("candidateId", candidateId.toString());
        addMetadata("jobId", jobId.toString());
    }

    @Override
    public String getCategory() {
        return "APPLICATION_NOT_FOUND";
    }
}
