package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when business validation fails in ATS.
 * Used for domain-specific validation that goes beyond basic input validation.
 * 
 * <AUTHOR> Development Team
 */
public class ValidationException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_VALIDATION_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.BAD_REQUEST;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public ValidationException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public ValidationException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Constructor for field-specific validation errors
     *
     * @param fieldName the field that failed validation
     * @param fieldValue the invalid value
     * @param reason the reason for validation failure
     */
    public ValidationException(String fieldName, Object fieldValue, String reason) {
        super(String.format("Validation failed for field '%s' with value '%s': %s",
              fieldName, fieldValue, reason),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("fieldName", fieldName);
        addMetadata("fieldValue", fieldValue != null ? fieldValue.toString() : "null");
        addMetadata("reason", reason);
    }

    /**
     * Factory method for file type validation failures
     *
     * @param fileName the name of the file
     * @param fileExtension the file extension that failed validation
     * @param allowedExtensions the list of allowed extensions
     */
    public static ValidationException forInvalidFileType(String fileName, String fileExtension,
                                                        java.util.List<String> allowedExtensions) {
        ValidationException exception = new ValidationException(
            String.format("File type '%s' is not supported for file '%s'. Allowed types: %s",
                         fileExtension, fileName, allowedExtensions));
        exception.addMetadata("fileName", fileName);
        exception.addMetadata("fileExtension", fileExtension);
        exception.addMetadata("allowedExtensions", allowedExtensions.toString());
        exception.addMetadata("validationType", "FILE_TYPE");
        return exception;
    }

    /**
     * Factory method for file size validation failures
     *
     * @param fileName the name of the file
     * @param fileSize the actual file size
     * @param maxSize the maximum allowed size
     */
    public static ValidationException forInvalidFileSize(String fileName, long fileSize, long maxSize) {
        ValidationException exception = new ValidationException(
            String.format("File '%s' size %d bytes exceeds maximum allowed size of %d bytes",
                         fileName, fileSize, maxSize));
        exception.addMetadata("fileName", fileName);
        exception.addMetadata("fileSize", fileSize);
        exception.addMetadata("maxSize", maxSize);
        exception.addMetadata("validationType", "FILE_SIZE");
        return exception;
    }

    /**
     * Factory method for null or empty field validation failures
     *
     * @param fieldName the name of the field that is null or empty
     */
    public static ValidationException forNullOrEmpty(String fieldName) {
        ValidationException exception = new ValidationException(
            String.format("%s cannot be null or empty", fieldName));
        exception.addMetadata("fieldName", fieldName);
        exception.addMetadata("validationType", "NULL_OR_EMPTY");
        return exception;
    }

    /**
     * Factory method for JSON processing validation failures
     *
     * @param fieldName the field that failed JSON processing
     * @param cause the underlying JSON processing exception
     */
    public static ValidationException forJsonProcessing(String fieldName, Throwable cause) {
        ValidationException exception = new ValidationException(
            String.format("Failed to process JSON for field '%s'", fieldName), cause);
        exception.addMetadata("fieldName", fieldName);
        exception.addMetadata("validationType", "JSON_PROCESSING");
        return exception;
    }

    @Override
    public boolean isRetryable() {
        // Validation failures are generally not retryable without fixing the input
        return false;
    }

    @Override
    public String getCategory() {
        return "VALIDATION_ERROR";
    }
}
