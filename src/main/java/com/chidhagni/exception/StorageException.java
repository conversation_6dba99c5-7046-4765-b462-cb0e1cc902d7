package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when storage operations fail.
 * This includes file system operations, cloud storage operations, and storage service failures.
 * Extends BaseApplicationException for consistent error handling and observability.
 * 
 * <AUTHOR> Development Team
 */
public class StorageException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_STORAGE_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.INTERNAL_SERVER_ERROR;
    private static final LogLevel LOG_LEVEL = LogLevel.ERROR;
    private static final boolean SHOULD_LOG = true;

    public StorageException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public StorageException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Factory method for file system storage failures
     * 
     * @param filePath the file path where storage failed
     * @param operation the storage operation that failed (e.g., "write", "read", "delete")
     * @param cause the underlying cause
     */
    public static StorageException forFileSystemFailure(String filePath, String operation, Throwable cause) {
        StorageException exception = new StorageException(
            String.format("File system %s operation failed for path '%s'", operation, filePath), cause);
        exception.addMetadata("filePath", filePath);
        exception.addMetadata("operation", operation);
        exception.addMetadata("storageType", "FILE_SYSTEM");
        return exception;
    }

    /**
     * Factory method for cloud storage service failures
     * 
     * @param bucketName the storage bucket name
     * @param objectKey the object key/path in storage
     * @param operation the storage operation that failed
     * @param cause the underlying cause
     */
    public static StorageException forCloudStorageFailure(String bucketName, String objectKey, 
                                                         String operation, Throwable cause) {
        StorageException exception = new StorageException(
            String.format("Cloud storage %s operation failed for object '%s' in bucket '%s'", 
                         operation, objectKey, bucketName), cause);
        exception.addMetadata("bucketName", bucketName);
        exception.addMetadata("objectKey", objectKey);
        exception.addMetadata("operation", operation);
        exception.addMetadata("storageType", "CLOUD_STORAGE");
        return exception;
    }

    /**
     * Factory method for storage service unavailability
     * 
     * @param serviceName the name of the storage service
     * @param cause the underlying cause
     */
    public static StorageException forServiceUnavailable(String serviceName, Throwable cause) {
        StorageException exception = new StorageException(
            String.format("Storage service '%s' is currently unavailable", serviceName), cause);
        exception.addMetadata("serviceName", serviceName);
        exception.addMetadata("storageType", "SERVICE_UNAVAILABLE");
        return exception;
    }

    /**
     * Factory method for storage quota exceeded failures
     * 
     * @param currentUsage the current storage usage
     * @param quotaLimit the storage quota limit
     * @param fileSize the size of the file being stored
     */
    public static StorageException forQuotaExceeded(long currentUsage, long quotaLimit, long fileSize) {
        StorageException exception = new StorageException(
            String.format("Storage quota exceeded. Current usage: %d bytes, Quota: %d bytes, File size: %d bytes", 
                         currentUsage, quotaLimit, fileSize));
        exception.addMetadata("currentUsage", currentUsage);
        exception.addMetadata("quotaLimit", quotaLimit);
        exception.addMetadata("fileSize", fileSize);
        exception.addMetadata("storageType", "QUOTA_EXCEEDED");
        return exception;
    }

    /**
     * Factory method for storage path creation failures
     * 
     * @param directoryPath the directory path that failed to be created
     * @param cause the underlying cause
     */
    public static StorageException forDirectoryCreationFailure(String directoryPath, Throwable cause) {
        StorageException exception = new StorageException(
            String.format("Failed to create storage directory '%s'", directoryPath), cause);
        exception.addMetadata("directoryPath", directoryPath);
        exception.addMetadata("storageType", "DIRECTORY_CREATION");
        return exception;
    }

    /**
     * Factory method for storage configuration failures
     * 
     * @param configurationKey the configuration key that failed
     * @param cause the underlying cause
     */
    public static StorageException forConfigurationFailure(String configurationKey, Throwable cause) {
        StorageException exception = new StorageException(
            String.format("Storage configuration failed for key '%s'", configurationKey), cause);
        exception.addMetadata("configurationKey", configurationKey);
        exception.addMetadata("storageType", "CONFIGURATION");
        return exception;
    }

    @Override
    public boolean isRetryable() {
        // Storage operations can often be retried, except for quota exceeded scenarios
        String storageType = (String) getMetadata().get("storageType");
        return !"QUOTA_EXCEEDED".equals(storageType) && !"CONFIGURATION".equals(storageType);
    }

    @Override
    public String getCategory() {
        return "STORAGE_ERROR";
    }
}
