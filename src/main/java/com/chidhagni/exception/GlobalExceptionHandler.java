package com.chidhagni.exception;

import com.chidhagni.utils.TracingUtil;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import org.springframework.web.servlet.NoHandlerFoundException;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Global exception handler for centralized error handling and observability in ATS.
 * Provides consistent JSON error responses for API endpoints.
 * Integrates with metrics, tracing, and structured logging for enhanced observability.
 */
@RestControllerAdvice
@Slf4j
@RequiredArgsConstructor
public class GlobalExceptionHandler {

    @Autowired(required = false)
    private final MeterRegistry meterRegistry;

    @Autowired(required = false)
    private final TracingUtil tracingUtil;

    // Constants
    private static final String CORRELATION_ID_KEY = "correlationId";
    private static final String ENDPOINT_TAG = "endpoint";
    private static final String EXCEPTION_COUNT_METRIC = "exception.count";
    private static final String EXCEPTION_APPLICATION_ERROR_EVENT = "exception.application_error";
    private static final String EXCEPTION_UNHANDLED_EVENT = "exception.unhandled";
    private static final String ERROR_CODE_TAG = "error_code";
    private static final String EXCEPTION_TYPE_TAG = "exception_type";

    /** BaseApplicationException handling */
    @ExceptionHandler(BaseApplicationException.class)
    public ResponseEntity<ErrorResponse> handleBaseApplicationException(
            BaseApplicationException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        recordExceptionMetrics(ex.getErrorCode(), ex.getClass().getSimpleName(), request.getRequestURI());
        recordTracingEvent(EXCEPTION_APPLICATION_ERROR_EVENT, ex, request);
        logException(ex, correlationId, request);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(ex.getTimestamp())
                .status(ex.getHttpStatus().value())
                .error(ex.getHttpStatus().getReasonPhrase())
                .message(ex.getMessage())
                .path(request.getRequestURI())
                .correlationId(correlationId)
                .errorCode(ex.getErrorCode())
                .metadata(ex.getMetadata().isEmpty() ? null : ex.getMetadata())
                .retryable(ex.isRetryable())
                .build();

        return ResponseEntity.status(ex.getHttpStatus()).body(errorResponse);
    }

    // Delegates for subtypes of BaseApplicationException
    @ExceptionHandler({
            EntityNotFoundException.class,
            DocumentNotFoundException.class,
            ValidationException.class,
            AuthorizationException.class,
            BusinessLogicException.class,
            DatabaseOperationException.class,
            ExternalServiceException.class,
            FileUploadException.class,
            StorageException.class
    })
    public ResponseEntity<ErrorResponse> handleAppExceptions(
            BaseApplicationException ex, HttpServletRequest request) {
        return handleBaseApplicationException(ex, request);
    }

    /** Spring Security authentication failures */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ErrorResponse> handleBadCredentials(
            BadCredentialsException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Authentication failed - correlation_id={}, endpoint={}, message={}",
                correlationId, request.getRequestURI(), ex.getMessage());

        return buildError(HttpStatus.UNAUTHORIZED, "Authentication Failed",
                "Invalid credentials provided", "ATS_AUTHENTICATION_FAILED",
                request.getRequestURI(), correlationId);
    }

    /** Spring Security authorization failures */
    @ExceptionHandler({AccessDeniedException.class, InsufficientAuthenticationException.class})
    public ResponseEntity<ErrorResponse> handleAccessDenied(
            Exception ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Access denied - correlation_id={}, endpoint={}, type={}",
                correlationId, request.getRequestURI(), ex.getClass().getSimpleName());

        return buildError(HttpStatus.FORBIDDEN, "Access Denied",
                "Insufficient permissions to access this resource", "ATS_ACCESS_DENIED",
                request.getRequestURI(), correlationId);
    }

    /** Validation errors */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ErrorResponse> handleValidationErrors(
            MethodArgumentNotValidException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        Map<String, String> validationErrors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            validationErrors.put(fieldName, error.getDefaultMessage());
        });

        log.warn("Validation failed - correlation_id={}, endpoint={}, errors={}",
                correlationId, request.getRequestURI(), validationErrors);

        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(HttpStatus.BAD_REQUEST.value())
                .error("Validation Failed")
                .message("Request validation failed")
                .path(request.getRequestURI())
                .correlationId(correlationId)
                .errorCode("ATS_VALIDATION_FAILED")
                .validationErrors(validationErrors)
                .build();

        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
    }

    /** Illegal arguments */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ErrorResponse> handleIllegalArgument(
            IllegalArgumentException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Invalid argument - correlation_id={}, endpoint={}, message={}",
                correlationId, request.getRequestURI(), ex.getMessage());

        return buildError(HttpStatus.BAD_REQUEST, "Invalid Request", ex.getMessage(),
                "ATS_INVALID_ARGUMENT", request.getRequestURI(), correlationId);
    }

    /** Method not supported */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Method not supported - correlation_id={}, endpoint={}, method={}",
                correlationId, request.getRequestURI(), request.getMethod());

        return buildError(HttpStatus.METHOD_NOT_ALLOWED, "Method Not Allowed",
                String.format("HTTP method '%s' is not supported for this endpoint", request.getMethod()),
                "ATS_METHOD_NOT_ALLOWED", request.getRequestURI(), correlationId);
    }

    /** Unsupported media type */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ResponseEntity<ErrorResponse> handleUnsupportedMediaType(
            HttpMediaTypeNotSupportedException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Unsupported media type - correlation_id={}, endpoint={}, contentType={}",
                correlationId, request.getRequestURI(), request.getContentType());

        return buildError(HttpStatus.UNSUPPORTED_MEDIA_TYPE, "Unsupported Media Type",
                "Content type not supported", "ATS_UNSUPPORTED_MEDIA_TYPE",
                request.getRequestURI(), correlationId);
    }

    /** Missing request parameter */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ErrorResponse> handleMissingParameter(
            MissingServletRequestParameterException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Missing parameter - correlation_id={}, endpoint={}, parameter={}",
                correlationId, request.getRequestURI(), ex.getParameterName());

        return buildError(HttpStatus.BAD_REQUEST, "Missing Parameter",
                String.format("Required parameter '%s' is missing", ex.getParameterName()),
                "ATS_MISSING_PARAMETER", request.getRequestURI(), correlationId);
    }

    /** Type mismatch */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ErrorResponse> handleTypeMismatch(
            MethodArgumentTypeMismatchException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Type mismatch - correlation_id={}, endpoint={}, parameter={}, value={}",
                correlationId, request.getRequestURI(), ex.getName(), ex.getValue());

        return buildError(HttpStatus.BAD_REQUEST, "Type Mismatch",
                String.format("Parameter '%s' should be of type %s",
                        ex.getName(), ex.getRequiredType().getSimpleName()),
                "ATS_TYPE_MISMATCH", request.getRequestURI(), correlationId);
    }

    /** File upload size exceeded */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<ErrorResponse> handleMaxUploadSizeExceeded(
            MaxUploadSizeExceededException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Upload size exceeded - correlation_id={}, endpoint={}, maxSize={}",
                correlationId, request.getRequestURI(), ex.getMaxUploadSize());

        return buildError(HttpStatus.PAYLOAD_TOO_LARGE, "File Too Large",
                "Uploaded file exceeds maximum allowed size",
                "ATS_FILE_SIZE_EXCEEDED", request.getRequestURI(), correlationId);
    }

    /** File size exceeded by controller/service validation */
    @ExceptionHandler(FileSizeExceededException.class)
    public ResponseEntity<ErrorResponse> handleFileSizeExceeded(
            FileSizeExceededException ex, HttpServletRequest request) {


        String correlationId = getOrCreateCorrelationId();
        log.warn("File size exceeded - correlation_id={}, endpoint={}, message={}",
                correlationId, request.getRequestURI(), ex.getMessage());

        return buildError(HttpStatus.PAYLOAD_TOO_LARGE, "Payload Too Large",
                ex.getMessage(), "ATS_FILE_SIZE_EXCEEDED",
                request.getRequestURI(), correlationId);
    }




    /** JSON processing errors */
    @ExceptionHandler(JsonProcessingException.class)
    public ResponseEntity<ErrorResponse> handleJsonProcessingException(
            JsonProcessingException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("JSON processing failed - correlation_id={}, endpoint={}, message={}",
                correlationId, request.getRequestURI(), ex.getMessage());

        return buildError(HttpStatus.BAD_REQUEST, "Invalid JSON Format",
                "The provided JSON data is malformed or invalid: " + ex.getOriginalMessage(),
                "ATS_JSON_PROCESSING_ERROR", request.getRequestURI(), correlationId);
    }


    /** 404 not found */
    @ExceptionHandler(NoHandlerFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFound(
            NoHandlerFoundException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Resource not found - correlation_id={}, endpoint={}",
                correlationId, request.getRequestURI());

        return buildError(HttpStatus.NOT_FOUND, "Not Found",
                "The requested resource was not found",
                "ATS_RESOURCE_NOT_FOUND", request.getRequestURI(), correlationId);

    }

    /** Catch-all for unhandled exceptions */
    @ExceptionHandler(Exception.class)

    public ResponseEntity<ErrorResponse> handleGenericException(
            Exception ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        recordExceptionMetrics("ATS_INTERNAL_ERROR", ex.getClass().getSimpleName(), request.getRequestURI());
        recordTracingEvent(EXCEPTION_UNHANDLED_EVENT, ex, request);

        log.error("Unhandled exception - correlation_id={}, endpoint={}, type={}, message={}",
                correlationId, request.getRequestURI(), ex.getClass().getSimpleName(), ex.getMessage(), ex);


        return buildError(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error",
                "An unexpected error occurred",
                "ATS_INTERNAL_ERROR", request.getRequestURI(), correlationId);
    }

    // ===== Utility methods =====

    private String getOrCreateCorrelationId() {
        String correlationId = MDC.get(CORRELATION_ID_KEY);
        if (correlationId == null) {
            correlationId = UUID.randomUUID().toString();
            MDC.put(CORRELATION_ID_KEY, correlationId);
        }
        return correlationId;
    }

    private ResponseEntity<ErrorResponse> buildError(HttpStatus status, String error,
                                                     String message, String errorCode,
                                                     String path, String correlationId) {
        ErrorResponse errorResponse = ErrorResponse.builder()
                .timestamp(LocalDateTime.now())
                .status(status.value())
                .error(error)
                .message(message)
                .path(path)
                .correlationId(correlationId)
                .errorCode(errorCode)
                .build();

        return ResponseEntity.status(status).body(errorResponse);
    }

    private void logException(BaseApplicationException ex, String correlationId, HttpServletRequest request) {
        String logMessage = "Application exception - correlation_id={}, endpoint={}, error_code={}, message={}, metadata={}";
        Object[] logArgs = { correlationId, request.getRequestURI(), ex.getErrorCode(), ex.getMessage(), ex.getMetadata() };

        switch (ex.getLogLevel()) {
            case ERROR -> log.error(logMessage, logArgs, ex);
            case WARN -> log.warn(logMessage, logArgs);
            case INFO -> log.info(logMessage, logArgs);
            case DEBUG -> log.debug(logMessage, logArgs);
        }
    }


    private void recordExceptionMetrics(String errorCode, String exceptionType, String endpoint) {
        if (meterRegistry != null) {
            try {
                Counter.builder(EXCEPTION_COUNT_METRIC)
                        .description("Count of exceptions by type and endpoint")
                        .tag(ERROR_CODE_TAG, errorCode != null ? errorCode : "unknown")
                        .tag(EXCEPTION_TYPE_TAG, exceptionType != null ? exceptionType : "unknown")
                        .tag(ENDPOINT_TAG, endpoint != null ? endpoint : "unknown")
                        .register(meterRegistry)
                        .increment();

                log.debug("Recorded exception metric - error_code={}, exception_type={}, endpoint={}",
                        errorCode, exceptionType, endpoint);
            } catch (Exception e) {
                log.debug("Failed to record exception metric: {}", e.getMessage());
            }
        }
    }

    private void recordTracingEvent(String eventName, Throwable exception, HttpServletRequest request) {
        if (tracingUtil != null) {
            try {
                tracingUtil.addEvent(eventName, "exception_type", exception.getClass().getSimpleName());
                tracingUtil.addEvent(eventName, "endpoint", request.getRequestURI());
                tracingUtil.addEvent(eventName, "message", exception.getMessage());
                tracingUtil.recordException(exception);
                tracingUtil.setSpanAttribute("exception.type", exception.getClass().getSimpleName());
                tracingUtil.setSpanAttribute("exception.message", exception.getMessage());
                tracingUtil.setSpanAttribute("request.uri", request.getRequestURI());
                tracingUtil.setSpanAttribute("request.method", request.getMethod());

                log.debug("Recorded tracing event - event={}, exception_type={}, endpoint={}",
                        eventName, exception.getClass().getSimpleName(), request.getRequestURI());
            } catch (Exception e) {
                log.debug("Failed to record tracing event: {}", e.getMessage());
            }
        }
    }
}
