package com.chidhagni.exception;

import com.chidhagni.exception.BaseApplicationException;
import org.springframework.http.HttpStatus;

/**
 * Exception thrown when attempting to create a duplicate applicant.
 * Extends BaseApplicationException for consistent error handling and observability.
 * 
 * <AUTHOR> Development Team
 */
public class DuplicateApplicantException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_DUPLICATE_APPLICANT";
    private static final HttpStatus HTTP_STATUS = HttpStatus.CONFLICT;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public DuplicateApplicantException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public DuplicateApplicantException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Convenience constructor for duplicate applicant with email
     */
    public static DuplicateApplicantException forEmail(String email) {
        DuplicateApplicantException exception = new DuplicateApplicantException(
            String.format("Applicant with email '%s' already exists", email));
        exception.addMetadata("email", email);
        exception.addMetadata("duplicateField", "email");
        return exception;
    }

    /**
     * Convenience constructor for duplicate applicant with organization and email
     */
    public DuplicateApplicantException(String orgId, String email) {
        super(String.format("Applicant with email '%s' already exists in organization '%s'", email, orgId), 
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("orgId", orgId);
        addMetadata("email", email);
        addMetadata("duplicateField", "email");
    }

    @Override
    public String getCategory() {
        return "BUSINESS_LOGIC";
    }

    @Override
    public boolean isRetryable() {
        // Duplicate applicant errors are not retryable without changing the data
        return false;
    }
}