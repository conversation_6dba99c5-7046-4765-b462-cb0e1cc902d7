package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when file upload operations fail.
 * This includes file transfer, storage, and metadata persistence failures.
 * Extends BaseApplicationException for consistent error handling and observability.
 * 
 * <AUTHOR> Development Team
 */
public class FileUploadException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_FILE_UPLOAD_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.INTERNAL_SERVER_ERROR;
    private static final LogLevel LOG_LEVEL = LogLevel.ERROR;
    private static final boolean SHOULD_LOG = true;

    public FileUploadException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }


    /**
     * Constructor for file transfer failures
     * 
     * @param fileName the name of the file that failed to upload
     * @param cause the underlying cause of the failure
     */
    public FileUploadException(String fileName, Throwable cause) {
        super(String.format("Failed to upload file '%s'", fileName), cause,
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("fileName", fileName);
        addMetadata("failureType", "FILE_TRANSFER");
    }

    /**
     * Constructor for storage service failures
     * 
     * @param fileName the name of the file
     * @param storagePath the intended storage path
     * @param cause the underlying cause
     */
    public FileUploadException(String fileName, String storagePath, Throwable cause) {
        super(String.format("Failed to store file '%s' at path '%s'", fileName, storagePath), cause,
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("fileName", fileName);
        addMetadata("storagePath", storagePath);
        addMetadata("failureType", "STORAGE_SERVICE");
    }

    /**
     * Constructor for metadata persistence failures
     * 
     * @param fileName the name of the file
     * @param entityId the entity ID associated with the file
     * @param cause the underlying cause
     */
    public FileUploadException(String fileName, java.util.UUID entityId, Throwable cause) {
        super(String.format("Failed to persist metadata for file '%s' with entity ID '%s'", fileName, entityId), cause,
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("fileName", fileName);
        addMetadata("entityId", entityId.toString());
        addMetadata("failureType", "METADATA_PERSISTENCE");
    }

    /**
     * Factory method for temporary file creation failures
     * 
     * @param fileName the name of the file
     * @param tempPath the temporary file path
     * @param cause the underlying cause
     */
    public static FileUploadException forTempFileFailure(String fileName, String tempPath, Throwable cause) {
        FileUploadException exception = new FileUploadException(
            String.format("Failed to create temporary file for '%s' at path '%s'", fileName, tempPath), cause);
        exception.addMetadata("fileName", fileName);
        exception.addMetadata("tempPath", tempPath);
        exception.addMetadata("failureType", "TEMP_FILE_CREATION");
        return exception;
    }

    /**
     * Factory method for file processing failures
     * 
     * @param fileName the name of the file
     * @param operation the operation that failed
     * @param cause the underlying cause
     */
    public static FileUploadException forProcessingFailure(String fileName, String operation, Throwable cause) {
        FileUploadException exception = new FileUploadException(
            String.format("Failed to process file '%s' during operation '%s'", fileName, operation), cause);
        exception.addMetadata("fileName", fileName);
        exception.addMetadata("operation", operation);
        exception.addMetadata("failureType", "FILE_PROCESSING");
        return exception;
    }

    @Override
    public boolean isRetryable() {
        // Upload operations can be retried in most cases except for validation failures
        return !getMetadata().containsValue("VALIDATION");
    }

    @Override
    public String getCategory() {
        return "FILE_UPLOAD_ERROR";
    }
}
