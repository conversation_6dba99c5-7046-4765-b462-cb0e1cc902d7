package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

import java.util.UUID;

/**
 * Exception thrown when a requested document is not found in the document repository.
 * Extends BaseApplicationException for consistent error handling and observability.
 * 
 * <AUTHOR> Development Team
 */
public class DocumentNotFoundException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_DOCUMENT_NOT_FOUND";
    private static final HttpStatus HTTP_STATUS = HttpStatus.NOT_FOUND;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;


    public DocumentNotFoundException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Convenience constructor for document not found by ID
     * 
     * @param documentId the ID of the document
     */
    public DocumentNotFoundException(UUID documentId) {
        super(String.format("Document with ID '%s' not found", documentId),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("documentId", documentId.toString());
    }

    /**
     * Convenience constructor for document not found by path
     * 
     * @param documentPath the path of the document
     */
    public DocumentNotFoundException(String documentPath) {
        super(String.format("Document at path '%s' not found", documentPath),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("documentPath", documentPath);
    }

    /**
     * Convenience constructor for multiple documents not found
     * 
     * @param documentIds the list of document IDs that were not found
     */
    public DocumentNotFoundException(java.util.List<UUID> documentIds) {
        super(String.format("Documents with IDs %s not found", documentIds),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("documentIds", documentIds.toString());
        addMetadata("documentCount", documentIds.size());
    }

    /**
     * Factory method for document not found by type and ID
     * 
     * @param documentType the type of document
     * @param documentId the ID of the document
     */
    public static DocumentNotFoundException forDocumentType(String documentType, UUID documentId) {
        DocumentNotFoundException exception = new DocumentNotFoundException(
            String.format("%s document with ID '%s' not found", documentType, documentId));
        exception.addMetadata("documentType", documentType);
        exception.addMetadata("documentId", documentId.toString());
        return exception;
    }

    /**
     * Factory method for document not found by criteria
     * 
     * @param criteria the search criteria used
     * @param criteriaValue the value of the criteria
     */
    public static DocumentNotFoundException forCriteria(String criteria, Object criteriaValue) {
        DocumentNotFoundException exception = new DocumentNotFoundException(
            String.format("Document not found with %s '%s'", criteria, criteriaValue));
        exception.addMetadata("searchCriteria", criteria);
        exception.addMetadata("criteriaValue", criteriaValue.toString());
        return exception;
    }

    @Override
    public String getCategory() {
        return "DOCUMENT_NOT_FOUND";
    }
}
