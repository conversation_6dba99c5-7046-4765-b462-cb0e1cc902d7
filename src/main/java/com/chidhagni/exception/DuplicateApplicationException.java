package com.chidhagni.exception;

/**
 * Exception thrown when attempting to create a duplicate job application.
 * Extends BusinessLogicException for consistent behavior.
 * 
 * <AUTHOR> Development Team
 */
public class DuplicateApplicationException extends BusinessLogicException {

    private static final String ERROR_CODE = "ATS_DUPLICATE_APPLICATION";

    public DuplicateApplicationException(String message) {
        super(message);
    }

    public DuplicateApplicationException(String message, Throwable cause) {
        super(message, cause);
    }

    /**
     * Convenience constructor for duplicate application
     */
    public DuplicateApplicationException(Long candidateId, Long jobId) {
        super(String.format("Candidate %d has already applied for job %d", candidateId, jobId));
        addMetadata("candidateId", candidateId.toString());
        addMetadata("jobId", jobId.toString());
    }

    @Override
    public String getErrorCode() {
        return ERROR_CODE;
    }

    @Override
    public String getCategory() {
        return "DUPLICATE_APPLICATION";
    }
}
