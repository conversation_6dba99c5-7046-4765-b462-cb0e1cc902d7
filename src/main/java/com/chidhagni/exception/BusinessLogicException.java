package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when business logic constraints are violated in ATS.
 * Used for domain-specific business rule violations.
 * 
 * <AUTHOR> Development Team
 */
public class BusinessLogicException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_BUSINESS_LOGIC_ERROR";
    private static final HttpStatus HTTP_STATUS = HttpStatus.CONFLICT;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public BusinessLogicException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public BusinessLogicException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Constructor for business rule violations with context
     * 
     * @param businessRule the business rule that was violated
     * @param context additional context about the violation
     */
    public BusinessLogicException(String businessRule, String context) {
        super(String.format("Business rule violation: %s. Context: %s", businessRule, context),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("businessRule", businessRule);
        addMetadata("context", context);
    }

    @Override
    public String getCategory() {
        return "BUSINESS_LOGIC_ERROR";
    }
}
