package com.chidhagni.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
public class FileSizeExceededException extends RuntimeException {

    public FileSizeExceededException(String fileName, long fileSizeBytes, long limitBytes) {
//        super(buildMessage(fileName, fileSizeBytes, limitBytes));
        super(String.format("File %s exceeds allowed size: %d bytes (limit %d bytes)",
                fileName,fileSizeBytes,limitBytes));
    }

    private static String buildMessage(String fileName, long fileSizeBytes, long limitBytes) {
        String safeName = (fileName == null || fileName.isBlank()) ? "unnamed" : fileName;
        double mb = 1024.0 * 1024.0;
        return String.format(
                "File '%s' (%.2f MB) exceeds the maximum allowed size of %.2f MB for endpoint '%s'",
                safeName, fileSizeBytes / mb, limitBytes / mb
        );
    }
}