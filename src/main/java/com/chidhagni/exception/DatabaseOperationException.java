package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when database operations fail in ATS.
 * Used for database constraint violations, connection issues, and data integrity problems.
 * 
 * <AUTHOR> Development Team
 */
public class DatabaseOperationException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_DATABASE_OPERATION_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.INTERNAL_SERVER_ERROR;
    private static final LogLevel LOG_LEVEL = LogLevel.ERROR;
    private static final boolean SHOULD_LOG = true;

    public DatabaseOperationException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public DatabaseOperationException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Constructor for specific database operation failures
     * 
     * @param operation the database operation that failed
     * @param entityType the type of entity involved
     * @param cause the underlying cause
     */
    public DatabaseOperationException(String operation, String entityType, Throwable cause) {
        super(String.format("Database operation '%s' failed for entity type '%s'", 
              operation, entityType), cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("operation", operation);
        addMetadata("entityType", entityType);
    }

    @Override
    public boolean isRetryable() {
        // Some database operations can be retried (e.g., deadlocks, timeouts)
        return getCause() != null && 
               (getCause().getMessage().contains("deadlock") || 
                getCause().getMessage().contains("timeout"));
    }

    @Override
    public String getCategory() {
        return "DATABASE_ERROR";
    }
}
