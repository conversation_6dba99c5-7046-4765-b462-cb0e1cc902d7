package com.chidhagni.exception;

import java.util.UUID;

/**
 * Exception thrown when a requested resource is not found.
 */
public class ResourceNotFoundException extends EntityNotFoundException {
    
    public ResourceNotFoundException(UUID resourceId) {
        super("Resource not found with ID: " + resourceId, "RESOURCE_NOT_FOUND");
    }
    
    public ResourceNotFoundException(String resourceName) {
        super("Resource not found with name: " + resourceName, "RESOURCE_NOT_FOUND");
    }
}

