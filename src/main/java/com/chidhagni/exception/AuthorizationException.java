package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when a user lacks sufficient permissions for an operation in ATS.
 * Used for authorization failures beyond basic authentication.
 * 
 * <AUTHOR> Development Team
 */
public class AuthorizationException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_AUTHORIZATION_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.FORBIDDEN;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public AuthorizationException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public AuthorizationException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Constructor for permission-specific authorization errors
     * 
     * @param userId the user attempting the operation
     * @param operation the operation being attempted
     * @param resource the resource being accessed
     */
    public AuthorizationException(String userId, String operation, String resource) {
        super(String.format("User '%s' is not authorized to '%s' on resource '%s'", 
              userId, operation, resource),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("userId", userId);
        addMetadata("operation", operation);
        addMetadata("resource", resource);
    }

    @Override
    public String getCategory() {
        return "AUTHORIZATION_ERROR";
    }
}
