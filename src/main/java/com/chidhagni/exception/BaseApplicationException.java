package com.chidhagni.exception;

import lombok.Getter;
import org.springframework.http.HttpStatus;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Base exception class for all application-specific exceptions in ATS.
 * Provides common functionality for error handling, logging, and observability.
 * 
 * <AUTHOR> Development Team
 */
@Getter
public abstract class BaseApplicationException extends RuntimeException {
    
    private final String errorCode;
    private final HttpStatus httpStatus;
    private final LocalDateTime timestamp;
    private final transient Map<String, Object> metadata;
    private final boolean shouldLog;
    private final LogLevel logLevel;

    /**
     * Logging levels for different exception scenarios
     */
    public enum LogLevel {
        ERROR, WARN, INFO, DEBUG
    }

    protected BaseApplicationException(
            String message, 
            String errorCode, 
            HttpStatus httpStatus, 
            LogLevel logLevel,
            boolean shouldLog) {
        super(message);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.logLevel = logLevel;
        this.shouldLog = shouldLog;
        this.timestamp = LocalDateTime.now();
        this.metadata = new java.util.HashMap<>();
    }

    protected BaseApplicationException(
            String message, 
            Throwable cause, 
            String errorCode, 
            HttpStatus httpStatus, 
            LogLevel logLevel,
            boolean shouldLog) {
        super(message, cause);
        this.errorCode = errorCode;
        this.httpStatus = httpStatus;
        this.logLevel = logLevel;
        this.shouldLog = shouldLog;
        this.timestamp = LocalDateTime.now();
        this.metadata = new java.util.HashMap<>();
    }

    /**
     * Add metadata for enhanced logging and debugging
     * 
     * @param key metadata key
     * @param value metadata value
     * @return this exception instance for method chaining
     */
    public BaseApplicationException addMetadata(String key, Object value) {
        this.metadata.put(key, value);
        return this;
    }

    /**
     * Get formatted error message for logging
     * 
     * @return formatted error message
     */
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("Error Code: ").append(errorCode)
          .append(", Message: ").append(getMessage())
          .append(", HTTP Status: ").append(httpStatus.value());
        
        if (!metadata.isEmpty()) {
            sb.append(", Metadata: ").append(metadata);
        }
        
        return sb.toString();
    }

    /**
     * Check if this exception should be retried
     * Override in specific exceptions if retryable
     * 
     * @return true if the operation can be retried
     */
    public boolean isRetryable() {
        return false;
    }

    /**
     * Get the exception category for classification
     * 
     * @return exception category
     */
    public abstract String getCategory();
}
