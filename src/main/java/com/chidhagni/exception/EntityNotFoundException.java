package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when a requested entity is not found in ATS.
 * Extends BaseApplicationException for consistent error handling and observability.
 * 
 * <AUTHOR> Development Team
 */
public class EntityNotFoundException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_ENTITY_NOT_FOUND";
    private static final HttpStatus HTTP_STATUS = HttpStatus.NOT_FOUND;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public EntityNotFoundException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public EntityNotFoundException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Convenience constructor for entity not found by ID
     * 
     * @param entityType the type of entity (e.g., "Job Application", "Candidate")
     * @param entityId the ID of the entity
     */
    public EntityNotFoundException(String entityType, Object entityId) {
        super(String.format("%s with ID '%s' not found", entityType, entityId),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("entityType", entityType);
        addMetadata("entityId", entityId.toString());
    }

    /**
     * Convenience constructor for entity not found by field
     * 
     * @param entityType the type of entity
     * @param fieldName the field name used for lookup
     * @param fieldValue the field value used for lookup
     */
    public EntityNotFoundException(String entityType, String fieldName, Object fieldValue) {
        super(String.format("%s with %s '%s' not found", entityType, fieldName, fieldValue),
              ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("entityType", entityType);
        addMetadata("fieldName", fieldName);
        addMetadata("fieldValue", fieldValue.toString());
    }

    @Override
    public String getCategory() {
        return "RESOURCE_NOT_FOUND";
    }
}
