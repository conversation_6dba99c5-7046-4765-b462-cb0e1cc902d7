package com.chidhagni.exception;

import org.springframework.http.HttpStatus;

/**
 * Exception thrown when external service calls fail in ATS.
 * Used for API calls, file storage services, email services, and other external integrations.
 * 
 * <AUTHOR> Development Team
 */
public class ExternalServiceException extends BaseApplicationException {

    private static final String ERROR_CODE = "ATS_EXTERNAL_SERVICE_FAILED";
    private static final HttpStatus HTTP_STATUS = HttpStatus.BAD_GATEWAY;
    private static final LogLevel LOG_LEVEL = LogLevel.WARN;
    private static final boolean SHOULD_LOG = true;

    public ExternalServiceException(String message) {
        super(message, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    public ExternalServiceException(String message, Throwable cause) {
        super(message, cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
    }

    /**
     * Constructor for external service failures with service details
     * 
     * @param serviceName the name of the external service
     * @param operation the operation that failed
     * @param cause the underlying cause
     */
    public ExternalServiceException(String serviceName, String operation, Throwable cause) {
        super(String.format("External service '%s' failed during operation '%s'", 
              serviceName, operation), cause, ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("serviceName", serviceName);
        addMetadata("operation", operation);
    }

    /**
     * Constructor for HTTP-related external service failures
     * 
     * @param serviceName the name of the external service
     * @param operation the operation that failed
     * @param httpStatus the HTTP status code returned
     * @param responseBody the response body (if available)
     */
    public ExternalServiceException(String serviceName, String operation, int httpStatus, String responseBody) {
        super(String.format("External service '%s' failed during operation '%s' with HTTP status %d", 
              serviceName, operation, httpStatus), ERROR_CODE, HTTP_STATUS, LOG_LEVEL, SHOULD_LOG);
        addMetadata("serviceName", serviceName);
        addMetadata("operation", operation);
        addMetadata("httpStatus", httpStatus);
        addMetadata("responseBody", responseBody);
    }

    @Override
    public boolean isRetryable() {
        // External service failures are often retryable
        return true;
    }

    @Override
    public String getCategory() {
        return "EXTERNAL_SERVICE_ERROR";
    }
}
