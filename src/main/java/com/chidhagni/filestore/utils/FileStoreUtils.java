package com.chidhagni.filestore.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Utility class for file storage operations.
 * Provides common file handling utilities for the file storage system.
 */
@Slf4j
public class FileStoreUtils {

    // Common file extensions and MIME types
    public static final List<String> ALLOWED_IMAGE_TYPES = Arrays.asList(
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
    );

    public static final List<String> ALLOWED_DOCUMENT_TYPES = Arrays.asList(
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "text/plain",
            "text/csv"
    );

    public static final List<String> ALLOWED_ARCHIVE_TYPES = Arrays.asList(
            "application/zip",
            "application/x-rar-compressed",
            "application/x-7z-compressed"
    );

    /**
     * Validates if the file type is allowed
     * @param contentType the MIME type of the file
     * @param allowedTypes list of allowed MIME types
     * @return true if file type is allowed, false otherwise
     */
    public static boolean isFileTypeAllowed(String contentType, List<String> allowedTypes) {
        if (contentType == null || allowedTypes == null) {
            return false;
        }
        return allowedTypes.contains(contentType.toLowerCase());
    }

    /**
     * Validates if the file is an allowed image type
     * @param contentType the MIME type of the file
     * @return true if file is an allowed image type, false otherwise
     */
    public static boolean isImageFile(String contentType) {
        return isFileTypeAllowed(contentType, ALLOWED_IMAGE_TYPES);
    }

    /**
     * Validates if the file is an allowed document type
     * @param contentType the MIME type of the file
     * @return true if file is an allowed document type, false otherwise
     */
    public static boolean isDocumentFile(String contentType) {
        return isFileTypeAllowed(contentType, ALLOWED_DOCUMENT_TYPES);
    }

    /**
     * Validates if the file is an allowed archive type
     * @param contentType the MIME type of the file
     * @return true if file is an allowed archive type, false otherwise
     */
    public static boolean isArchiveFile(String contentType) {
        return isFileTypeAllowed(contentType, ALLOWED_ARCHIVE_TYPES);
    }

    /**
     * Generates a unique filename with the original extension
     * @param originalFilename the original filename
     * @return unique filename with UUID prefix
     */
    public static String generateUniqueFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return UUID.randomUUID().toString();
        }

        String extension = getFileExtension(originalFilename);
        String uuid = UUID.randomUUID().toString();
        
        return extension.isEmpty() ? uuid : uuid + "." + extension;
    }

    /**
     * Extracts file extension from filename
     * @param filename the filename
     * @return file extension without the dot, or empty string if no extension
     */
    public static String getFileExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }

        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * Creates a temporary file from MultipartFile
     * @param multipartFile the multipart file to convert
     * @param prefix prefix for the temporary file
     * @return Path to the created temporary file
     * @throws IOException if file creation fails
     */
    public static Path createTempFileFromMultipart(MultipartFile multipartFile, String prefix) throws IOException {
        if (multipartFile == null || multipartFile.isEmpty()) {
            throw new IllegalArgumentException("MultipartFile cannot be null or empty");
        }

        String originalFilename = multipartFile.getOriginalFilename();
        String suffix = originalFilename != null ? "_" + originalFilename : "";
        
        Path tempFile = Files.createTempFile(prefix, suffix);
        Files.copy(multipartFile.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);
        
        log.debug("Created temporary file: {} for original file: {}", tempFile, originalFilename);
        return tempFile;
    }

    /**
     * Safely deletes a file, logging any errors
     * @param filePath the path to the file to delete
     * @return true if file was deleted or didn't exist, false if deletion failed
     */
    public static boolean safeDeleteFile(Path filePath) {
        if (filePath == null) {
            return true;
        }

        try {
            boolean deleted = Files.deleteIfExists(filePath);
            if (deleted) {
                log.debug("Successfully deleted temporary file: {}", filePath);
            }
            return true;
        } catch (IOException e) {
            log.warn("Failed to delete temporary file: {}", filePath, e);
            return false;
        }
    }

    /**
     * Validates file size against maximum allowed size
     * @param fileSize the size of the file in bytes
     * @param maxSizeInBytes maximum allowed size in bytes
     * @return true if file size is within limits, false otherwise
     */
    public static boolean isFileSizeValid(long fileSize, long maxSizeInBytes) {
        return fileSize > 0 && fileSize <= maxSizeInBytes;
    }

    /**
     * Converts bytes to human-readable format
     * @param bytes the number of bytes
     * @return human-readable string representation
     */
    public static String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * Sanitizes filename by removing or replacing invalid characters
     * @param filename the original filename
     * @return sanitized filename safe for storage
     */
    public static String sanitizeFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "unnamed_file";
        }

        // Remove or replace invalid characters for most file systems
        String sanitized = filename.replaceAll("[\\\\/:*?\"<>|]", "_");

        // Remove leading/trailing dots and spaces
        sanitized = sanitized.replaceAll("^[.\\s]+|[.\\s]+$", "");

        // Ensure filename is not empty after sanitization
        if (sanitized.trim().isEmpty()) {
            return "unnamed_file";
        }

        return sanitized;
    }

    // ===== FILE PATH UTILITIES FOR STORAGE CLIENTS =====

    /**
     * Extracts filename from storage location path for temporary file creation.
     * This method handles the common pattern used by both MinIO and DigitalOcean Spaces clients.
     *
     * @param location the storage location path (e.g., "documents/subfolder/filename.pdf")
     * @return extracted filename for temporary file creation
     * @throws IllegalArgumentException if location is invalid or cannot be parsed
     */
    public static String extractFileNameFromStorageLocation(String location) {
        if (location == null || location.trim().isEmpty()) {
            throw new IllegalArgumentException("Storage location cannot be null or empty");
        }

        try {
            List<String> pathParts = Pattern.compile("/").splitAsStream(location.trim())
                    .filter(part -> !part.isEmpty())
                    .toList();

            if (pathParts.size() < 2) {
                throw new IllegalArgumentException("Storage location must contain at least folder and filename: " + location);
            }

            // Combine last two parts (folder + filename) for unique temporary file naming
            String folderName = pathParts.get(pathParts.size() - 2);
            String fileName = pathParts.get(pathParts.size() - 1);

            return folderName + fileName;
        } catch (Exception e) {
            log.warn("Failed to extract filename from storage location: {}", location, e);
            throw new IllegalArgumentException("Invalid storage location format: " + location, e);
        }
    }

    /**
     * Creates a temporary file path for downloaded files from storage.
     * Ensures unique naming and proper system temp directory usage.
     *
     * @param location the storage location path
     * @return full path to temporary file
     */
    public static String createTempFilePath(String location) {
        String fileName = extractFileNameFromStorageLocation(location);
        return System.getProperty("java.io.tmpdir") + File.separator + fileName;
    }

    /**
     * Creates a temporary file path with additional uniqueness suffix.
     * Useful when multiple operations might create temp files simultaneously.
     *
     * @param location the storage location path
     * @param suffix additional suffix for uniqueness (e.g., timestamp, UUID)
     * @return full path to temporary file with suffix
     */
    public static String createUniqueTempFilePath(String location, String suffix) {
        String fileName = extractFileNameFromStorageLocation(location);
        String baseName = getFileNameWithoutExtension(fileName);
        String extension = getFileExtension(fileName);

        String uniqueFileName = extension.isEmpty()
            ? baseName + "_" + suffix
            : baseName + "_" + suffix + "." + extension;

        return System.getProperty("java.io.tmpdir") + File.separator + uniqueFileName;
    }

    /**
     * Extracts filename without extension
     * @param filename the filename
     * @return filename without extension
     */
    private static String getFileNameWithoutExtension(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return "";
        }

        int lastDotIndex = filename.lastIndexOf('.');
        return lastDotIndex == -1 ? filename : filename.substring(0, lastDotIndex);
    }

    /**
     * Creates a TempFileResource for a storage location with automatic cleanup.
     * This is the recommended approach for handling temporary files in storage operations.
     *
     * Example usage:
     * <pre>
     * try (TempFileResource tempResource = FileStoreUtils.createTempFileResource(location)) {
     *     File tempFile = tempResource.getFile();
     *     // Download file to tempFile
     *     // Use tempFile
     *     return tempFile; // or process it
     * } // Automatic cleanup happens here
     * </pre>
     *
     * @param location the storage location path
     * @return TempFileResource for automatic cleanup
     * @throws IOException if temporary file creation fails
     */
    public static TempFileResource createTempFileResource(String location) throws IOException {
        String fileName = extractFileNameFromStorageLocation(location);
        String tempFilePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
        return TempFileResource.of(tempFilePath, location);
    }

    /**
     * Creates a unique TempFileResource with timestamp suffix for concurrent operations.
     *
     * @param location the storage location path
     * @return TempFileResource with unique naming
     * @throws IOException if temporary file creation fails
     */
    public static TempFileResource createUniqueTempFileResource(String location) throws IOException {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uniqueTempFilePath = createUniqueTempFilePath(location, timestamp);
        return TempFileResource.of(uniqueTempFilePath, location);
    }
}
