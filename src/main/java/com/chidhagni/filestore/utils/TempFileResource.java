package com.chidhagni.filestore.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * AutoCloseable wrapper for temporary files to ensure proper cleanup.
 * Provides automatic resource management for temporary files created during file storage operations.
 * 
 * Usage example:
 * <pre>
 * try (TempFileResource tempResource = TempFileResource.create(tempFilePath)) {
 *     File tempFile = tempResource.getFile();
 *     // Use the temporary file
 *     return tempFile;
 * } // Automatic cleanup happens here
 * </pre>
 * 
 * <AUTHOR> Development Team
 */
@Slf4j
public class TempFileResource implements AutoCloseable {
    
    private final File tempFile;
    private final String originalLocation;
    private boolean closed = false;
    
    private TempFileResource(File tempFile, String originalLocation) {
        this.tempFile = tempFile;
        this.originalLocation = originalLocation;
    }
    
    /**
     * Creates a TempFileResource for an existing temporary file
     * 
     * @param tempFile the temporary file to manage
     * @param originalLocation the original storage location (for logging context)
     * @return TempFileResource instance
     */
    public static TempFileResource of(File tempFile, String originalLocation) {
        if (tempFile == null) {
            throw new IllegalArgumentException("Temporary file cannot be null");
        }
        return new TempFileResource(tempFile, originalLocation);
    }
    
    /**
     * Creates a TempFileResource for a temporary file path
     * 
     * @param tempFilePath the path to the temporary file
     * @param originalLocation the original storage location (for logging context)
     * @return TempFileResource instance
     */
    public static TempFileResource of(String tempFilePath, String originalLocation) {
        if (tempFilePath == null || tempFilePath.trim().isEmpty()) {
            throw new IllegalArgumentException("Temporary file path cannot be null or empty");
        }
        return new TempFileResource(new File(tempFilePath), originalLocation);
    }
    
    /**
     * Creates a new temporary file and wraps it in TempFileResource
     * 
     * @param prefix the prefix for the temporary file name
     * @param suffix the suffix for the temporary file name
     * @param originalLocation the original storage location (for logging context)
     * @return TempFileResource instance
     * @throws IOException if temporary file creation fails
     */
    public static TempFileResource create(String prefix, String suffix, String originalLocation) throws IOException {
        try {
            Path tempPath = Files.createTempFile(prefix, suffix);
            File tempFile = tempPath.toFile();
            log.debug("Created temporary file: {} for original location: {}", tempFile.getAbsolutePath(), originalLocation);
            return new TempFileResource(tempFile, originalLocation);
        } catch (IOException e) {
            log.error("Failed to create temporary file with prefix: {}, suffix: {}, for location: {}", 
                     prefix, suffix, originalLocation, e);
            throw e;
        }
    }
    
    /**
     * Gets the temporary file
     * 
     * @return the temporary file
     * @throws IllegalStateException if the resource has been closed
     */
    public File getFile() {
        if (closed) {
            throw new IllegalStateException("TempFileResource has been closed");
        }
        return tempFile;
    }
    
    /**
     * Gets the path of the temporary file
     * 
     * @return the temporary file path
     * @throws IllegalStateException if the resource has been closed
     */
    public Path getPath() {
        return getFile().toPath();
    }
    
    /**
     * Gets the absolute path of the temporary file
     * 
     * @return the absolute path
     * @throws IllegalStateException if the resource has been closed
     */
    public String getAbsolutePath() {
        return getFile().getAbsolutePath();
    }
    
    /**
     * Checks if the temporary file exists
     * 
     * @return true if the file exists, false otherwise
     */
    public boolean exists() {
        return !closed && tempFile != null && tempFile.exists();
    }
    
    /**
     * Gets the size of the temporary file
     * 
     * @return file size in bytes, or -1 if file doesn't exist or resource is closed
     */
    public long size() {
        if (closed || tempFile == null || !tempFile.exists()) {
            return -1;
        }
        return tempFile.length();
    }
    
    /**
     * Manually deletes the temporary file.
     * This is automatically called by close(), but can be called explicitly if needed.
     * 
     * @return true if deletion was successful or file didn't exist, false if deletion failed
     */
    public boolean delete() {
        if (tempFile == null) {
            return true;
        }
        
        try {
            boolean deleted = Files.deleteIfExists(tempFile.toPath());
            if (deleted) {
                log.debug("Successfully deleted temporary file: {} (original location: {})", 
                         tempFile.getAbsolutePath(), originalLocation);
            } else {
                log.debug("Temporary file did not exist for deletion: {} (original location: {})", 
                         tempFile.getAbsolutePath(), originalLocation);
            }
            return true;
        } catch (IOException e) {
            log.warn("Failed to delete temporary file: {} (original location: {})", 
                    tempFile.getAbsolutePath(), originalLocation, e);
            return false;
        }
    }
    
    @Override
    public void close() {
        if (!closed) {
            delete();
            closed = true;
        }
    }
    
    /**
     * Gets the original storage location for logging/debugging purposes
     * 
     * @return the original storage location
     */
    public String getOriginalLocation() {
        return originalLocation;
    }
    
    /**
     * Checks if this resource has been closed
     * 
     * @return true if closed, false otherwise
     */
    public boolean isClosed() {
        return closed;
    }
    
    @Override
    public String toString() {
        return String.format("TempFileResource{tempFile=%s, originalLocation='%s', closed=%s}", 
                           tempFile != null ? tempFile.getAbsolutePath() : "null", 
                           originalLocation, 
                           closed);
    }
}
