package com.chidhagni.filestore.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URI;

/**
 * Configuration class for DigitalOcean Spaces integration.
 * This configuration is only active when the property 'filestore.provider' is set to 'digitalocean'.
 * 
 * DigitalOcean Spaces is S3-compatible, so we use the AWS SDK for Java.
 */
@Configuration
@ConditionalOnProperty(name = "filestore.provider", havingValue = "digitalocean")
@ConfigurationProperties(prefix = "filestore.digitalocean")
public class DOSpacesConfig {

    @Value("${filestore.digitalocean.access-key}")
    private String accessKey;

    @Value("${filestore.digitalocean.secret-key}")
    private String secretKey;

    @Value("${filestore.digitalocean.endpoint}")
    private String endpoint;

    @Value("${filestore.digitalocean.region}")
    private String region;

    /**
     * Creates and configures an S3Client for DigitalOcean Spaces.
     * 
     * @return Configured S3Client instance
     */
    @Bean
    public S3Client s3Client() {
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);
        
        return S3Client.builder()
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .endpointOverride(URI.create(endpoint))
                .region(Region.of(region))
                .forcePathStyle(true)  // Required for DigitalOcean Spaces
                .build();
    }
}
