package com.chidhagni.filestore.config;

import io.minio.MinioClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MinIO configuration class.
 * Creates MinIO client bean when filestore.provider is set to "minio"
 */
@Configuration
@ConditionalOnProperty(name = "filestore.provider", havingValue = "minio", matchIfMissing = true)
public class MinioConfig {

    @Value("${filestore.minio.access.name}")
    private String accessKey;

    @Value("${filestore.minio.access.secret}")
    private String accessSecret;

    @Value("${filestore.minio.url}")
    private String fileStoreUrl;

    /**
     * Creates and configures the MinIO client
     * @return configured MinioClient instance
     */
    @Bean
    public MinioClient generateMinioClient() {
        return MinioClient.builder()
                .endpoint(fileStoreUrl)
                .credentials(accessKey, accessSecret)
                .build();
    }
}

