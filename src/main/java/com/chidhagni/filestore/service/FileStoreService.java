package com.chidhagni.filestore.service;

import com.chidhagni.filestore.repository.IFileStoreClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;

/**
 * Service implementation for file storage operations.
 * Delegates to the configured file store client and provides additional functionality.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileStoreService implements IFileStoreService {

    private final IFileStoreClient fileStoreClient;

    @Override
    public String storeFile(String location, Path path) {
        log.debug("Storing file at location: {} from path: {}", location, path);
        return fileStoreClient.storeFile(location, path);
    }

    @Override
    public File getFile(String location) {
        log.debug("Retrieving file from location: {}", location);
        return fileStoreClient.getFile(location);
    }

    @Override
    public String getFileContent(String location) {
        try {
            File file = fileStoreClient.getFile(location);
            byte[] fileContent = Files.readAllBytes(file.toPath());
            String base64Content = Base64.getEncoder().encodeToString(fileContent);
            
            log.debug("Successfully retrieved and encoded file content for location: {}", location);
            return base64Content;
        } catch (IOException e) {
            log.error("Failed to read file content from location: {}", location, e);
            return null;
        } catch (Exception e) {
            log.error("Error retrieving file content from location: {}", location, e);
            return null;
        }
    }

    @Override
    public void deleteFile(String location) {
        log.debug("Deleting file at location: {}", location);
        fileStoreClient.deleteFile(location);
    }

    @Override
    public boolean checkIfFileExists(String location) {
        boolean exists = fileStoreClient.checkIfFileExists(location);
        log.debug("File exists check for location {}: {}", location, exists);
        return exists;
    }
}

