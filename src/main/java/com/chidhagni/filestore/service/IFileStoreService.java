package com.chidhagni.filestore.service;

import java.io.File;
import java.nio.file.Path;

/**
 * Service interface for file storage operations.
 * Provides high-level file storage operations with additional functionality
 * like base64 encoding for file content retrieval.
 */
public interface IFileStoreService {

    /**
     * Stores a file in the specified location
     * @param location the target location/path in the storage
     * @param path the path to the local file to be stored
     * @return the object name/key of the stored file
     */
    String storeFile(String location, Path path);

    /**
     * Retrieves a file from the storage
     * @param location the file location/path in the storage
     * @return File object
     */
    File getFile(String location);

    /**
     * Retrieves file content as base64 encoded string
     * @param location the file location/path in the storage
     * @return base64 encoded file content, or null if file not found
     */
    String getFileContent(String location);

    /**
     * Deletes a file from the storage
     * @param location the file location/path to delete
     */
    void deleteFile(String location);

    /**
     * Checks if a file exists in the storage
     * @param location the file location/path to check
     * @return true if file exists, false otherwise
     */
    boolean checkIfFileExists(String location);
}

