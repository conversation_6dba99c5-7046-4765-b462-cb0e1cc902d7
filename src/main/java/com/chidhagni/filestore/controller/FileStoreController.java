package com.chidhagni.filestore.controller;

import com.chidhagni.filestore.service.IFileStoreService;
import com.chidhagni.exception.StorageException;
import com.chidhagni.exception.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.Base64;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * REST Controller for file storage operations.
 * Provides endpoints for file upload, download, deletion, and existence checking.
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/files")
@RequiredArgsConstructor
public class FileStoreController {

    private final IFileStoreService fileStoreService;

    @Value("${spring.servlet.multipart.max-file-size:50MB}")
    private String maxFileSize;  // e.g. "10MB"

    @Value("${spring.servlet.multipart.file-size-threshold:8KB}")
    private String bufferSize;   // e.g. "2KB"


    // Security pattern to prevent path traversal attacks
    private static final Pattern INVALID_PATH_PATTERN = Pattern.compile(".*[/\\\\]\\.\\.([/\\\\].*)?");

    /**
     * Uploads a file to the storage
     * @param file the multipart file to upload
     * @param location the target location in storage
     * @return response with the stored file key
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<Map<String, String>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("location") String location) {
        
        try {
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "File is empty"));
            }

            // Create temporary file
            Path tempFile = Files.createTempFile("upload_", "_" + file.getOriginalFilename());
            Files.copy(file.getInputStream(), tempFile, StandardCopyOption.REPLACE_EXISTING);

            // Store the file
            String storedKey = fileStoreService.storeFile(location, tempFile);

            // Clean up temporary file
            Files.deleteIfExists(tempFile);

            log.info("Successfully uploaded file: {} to location: {}", file.getOriginalFilename(), location);
            return ResponseEntity.ok(Map.of(
                    "message", "File uploaded successfully",
                    "fileKey", storedKey,
                    "location", location
            ));

        } catch (IOException e) {
            log.error("Error uploading file: {}", file.getOriginalFilename(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to upload file: " + e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error uploading file: {}", file.getOriginalFilename(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Unexpected error occurred"));
        }
    }

    /**
     * Downloads a file from storage
     * @param location the file location in storage
     * @return file content as byte array
     */
    @GetMapping("/download")
    public ResponseEntity<byte[]> downloadFile(@RequestParam("location") String location) {
        try {
            if (!fileStoreService.checkIfFileExists(location)) {
                return ResponseEntity.notFound().build();
            }

            File file = fileStoreService.getFile(location);
            byte[] fileContent = loadFile(file);

            return ResponseEntity.ok()
                    .contentType(MediaType.APPLICATION_OCTET_STREAM)
                    .header("Content-Disposition", "attachment; filename=\"" + file.getName() + "\"")
                    .body(fileContent);

        } catch (Exception e) {
            log.error("Error downloading file from location: {}", location, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Gets file content as base64 encoded string
     * @param location the file location in storage
     * @return base64 encoded file content
     */
    @GetMapping("/content")
    public ResponseEntity<Map<String, String>> getFileContent(@RequestParam("location") String location) {
        try {
            if (!fileStoreService.checkIfFileExists(location)) {
                return ResponseEntity.notFound().build();
            }

            String base64Content = fileStoreService.getFileContent(location);
            if (base64Content == null) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                        .body(Map.of("error", "Failed to read file content"));
            }

            return ResponseEntity.ok(Map.of(
                    "location", location,
                    "content", base64Content
            ));

        } catch (Exception e) {
            log.error("Error getting file content from location: {}", location, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to retrieve file content"));
        }
    }

    /**
     * Deletes a file from storage
     * @param location the file location to delete
     * @return response indicating success or failure
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, String>> deleteFile(@RequestParam("location") String location) {
        try {
            if (!fileStoreService.checkIfFileExists(location)) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(Map.of("error", "File not found"));
            }

            fileStoreService.deleteFile(location);
            
            log.info("Successfully deleted file at location: {}", location);
            return ResponseEntity.ok(Map.of(
                    "message", "File deleted successfully",
                    "location", location
            ));

        } catch (Exception e) {
            log.error("Error deleting file at location: {}", location, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to delete file"));
        }
    }

    /**
     * Checks if a file exists in storage
     * @param location the file location to check
     * @return response indicating whether file exists
     */
    @GetMapping("/exists")
    public ResponseEntity<Map<String, Object>> checkFileExists(@RequestParam("location") String location) {
        try {
            boolean exists = fileStoreService.checkIfFileExists(location);
            
            return ResponseEntity.ok(Map.of(
                    "location", location,
                    "exists", exists
            ));

        } catch (Exception e) {
            log.error("Error checking file existence at location: {}", location, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(Map.of("error", "Failed to check file existence"));
        }
    }

    /**
     * Utility method to load file content as byte array
     * @param file the file to load
     * @return file content as byte array
     * @throws IOException if file cannot be read
     */
    public static byte[] loadFile(File file) throws IOException {
        return Files.readAllBytes(file.toPath());
    }
    
    /**
     * Validates the location parameter for security vulnerabilities
     * @param location the file location to validate
     * @throws IllegalArgumentException if location is invalid
     */
    private void validateLocation(String location) {
        if (!StringUtils.hasText(location)) {
            throw new IllegalArgumentException("Location parameter cannot be null or empty");
        }
        
        if (INVALID_PATH_PATTERN.matcher(location).matches()) {
            throw new IllegalArgumentException("Invalid location: path traversal detected");
        }
        
        if (location.length() > 255) {
            throw new IllegalArgumentException("Location path too long (max 255 characters)");
        }
    }
    
    /**
     * Validates file size against configured limits
     * @param file the file to validate
     * @throws IllegalArgumentException if file is too large
     */
    private void validateFileSize(File file) {
        long maxBytes = DataSize.parse(maxFileSize).toBytes();
        if (file.length() > maxBytes) {
            throw new IllegalArgumentException(
                String.format("File size %d bytes exceeds maximum allowed size %d bytes", 
                    file.length(), maxFileSize));
        }
    }
    
    /**
     * Streams file content efficiently for large files
     * @param file the file to stream
     * @return byte array of file content
     * @throws IOException if file cannot be read
     */
    private byte[] streamFileContent(File file) throws IOException {
        validateFileSize(file);

        int bufSize = (int) DataSize.parse(bufferSize).toBytes();
        
        try (FileInputStream fis = new FileInputStream(file);
             BufferedInputStream bis = new BufferedInputStream(fis, bufSize);
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            
            byte[] buffer = new byte[bufSize];
            int bytesRead;
            
            while ((bytesRead = bis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            return baos.toByteArray();
        }
    }


    /**
     * Gets file content as base64 encoded string with optimized performance and security
     * @param location the file location in storage
     * @return base64 encoded file content
     * @throws EntityNotFoundException if file is not found
     * @throws StorageException if file cannot be read
     * @throws IllegalArgumentException if location is invalid
     */
    @GetMapping("/get-file-content")
    public ResponseEntity<Map<String, String>> getFile(@RequestParam("location") String location) {
        // Validate input parameters for security
        validateLocation(location);
        
        log.debug("Retrieving file content for location: {}", location);
        
        // Check if file exists using service layer
        if (!fileStoreService.checkIfFileExists(location)) {
            throw new EntityNotFoundException(
                String.format("File not found at location: %s", location))
                .addMetadata("location", location)
                .addMetadata("operation", "getFile");
        }
        
        try {
            // Get file reference from service
            File file = fileStoreService.getFile(location);
            
            if (file == null || !file.exists()) {
                throw new EntityNotFoundException(
                    String.format("File reference is null or file does not exist at location: %s", location))
                    .addMetadata("location", location)
                    .addMetadata("fileExists", file != null && file.exists());
            }
            
            // Stream file content efficiently for better memory management
            byte[] fileContent = streamFileContent(file);
            String encodedString = Base64.getEncoder().encodeToString(fileContent);
            
            log.info("Successfully retrieved file content for location: {} (size: {} bytes)", 
                location, fileContent.length);
            
            return ResponseEntity.ok(Map.of(
                "data", encodedString,
                "location", location,
                "size", String.valueOf(fileContent.length)
            ));
            
        } catch (SecurityException e) {
            log.warn("Security violation while accessing file at location: {}", location, e);
            throw StorageException.forFileSystemFailure(location, "read", e)
                .addMetadata("securityViolation", true);
                
        } catch (IOException e) {
            log.error("I/O error reading file at location: {}", location, e);
            throw StorageException.forFileSystemFailure(location, "read", e);
            
        } catch (OutOfMemoryError e) {
            log.error("Out of memory error reading large file at location: {}", location, e);
            throw StorageException.forFileSystemFailure(location, "read", 
                new IOException("File too large to process", e))
                .addMetadata("memoryError", true);
        }
    }
}
