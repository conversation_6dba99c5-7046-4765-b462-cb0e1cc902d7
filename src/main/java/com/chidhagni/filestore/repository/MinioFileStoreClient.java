package com.chidhagni.filestore.repository;

import com.chidhagni.filestore.utils.FileStoreUtils;
import io.minio.*;
import io.minio.errors.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;

import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * MinIO implementation of the file store client.
 * Handles all MinIO-specific file operations including bucket management.
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "filestore.provider", havingValue = "minio", matchIfMissing = true)
public class MinioFileStoreClient implements IFileStoreClient {

    private final MinioClient minioClient;

    @Value("${filestore.minio.bucket.name}")
    private String bucketName;

    /**
     * Checks for bucket availability and creates it if it doesn't exist
     *
     */
    private void checkForBucketAvailability() {
        try {
            boolean found = minioClient.bucketExists(
                    BucketExistsArgs.builder()
                            .bucket(bucketName)
                            .build()
            );
            
            if (!found) {
                minioClient.makeBucket(
                        MakeBucketArgs.builder()
                                .bucket(bucketName)
                                .build()
                );
                log.info("Created new MinIO bucket: {}", bucketName);
            } else {
                log.debug("MinIO bucket {} already exists", bucketName);
            }
        } catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | 
                 InternalException | InvalidResponseException | NoSuchAlgorithmException | 
                 ServerException | XmlParserException | IllegalArgumentException | IOException e) {
            log.error("Failed to verify or create MinIO bucket: {}", bucketName, e);
            throw new RuntimeException("Something went wrong with bucket verification", e);
        }
    }

    @Override
    public String storeFile(String location, Path filePath) {
        checkForBucketAvailability();
        
        try {
            String objectName = location + "/" + filePath.getFileName().toString();
            ObjectWriteResponse objectWriteResponse = minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .filename(filePath.toString())
                            .build()
            );
            
            log.debug("Successfully uploaded file to MinIO: {}", objectName);
            return objectWriteResponse.object();
        } catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | 
                 InternalException | InvalidResponseException | NoSuchAlgorithmException | 
                 ServerException | XmlParserException | IllegalArgumentException | IOException e) {
            log.error("Failed to upload file to MinIO: {}", filePath, e);
            throw new RuntimeException("Something went wrong with file upload", e);
        }
    }

    @Override
    public File getFile(String location) {
        try {
            // Use utility method for consistent file path handling
            String localFilePath = FileStoreUtils.createTempFilePath(location);

            DownloadObjectArgs args = DownloadObjectArgs.builder()
                    .bucket(bucketName)
                    .object(location)
                    .filename(localFilePath)
                    .build();

            minioClient.downloadObject(args);

            File downloadedFile = new File(localFilePath);
            log.debug("Successfully downloaded file from MinIO: {} to temp file: {}",
                     location, downloadedFile.getAbsolutePath());

            // Note: Caller is responsible for cleaning up the temporary file
            // Consider using TempFileResource for automatic cleanup in future versions
            return downloadedFile;
        } catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | 
                 InternalException | InvalidResponseException | NoSuchAlgorithmException | 
                 ServerException | XmlParserException | IllegalArgumentException | IOException e) {
            log.error("Failed to download file from MinIO: {}", location, e);
            throw new RuntimeException("Something went wrong with file download", e);
        }
    }

    @Override
    public void deleteFile(String location) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(location)
                            .build()
            );
            log.info("Successfully deleted file from MinIO: {}", location);
        } catch (InvalidKeyException | ErrorResponseException | InsufficientDataException | 
                 InternalException | InvalidResponseException | NoSuchAlgorithmException | 
                 ServerException | XmlParserException | IllegalArgumentException | IOException e) {
            log.error("Failed to delete file from MinIO: {}", location, e);
            throw new RuntimeException("Something went wrong with file deletion", e);
        }
    }

    @Override
    public boolean checkIfFileExists(String objectName) {
        try {
            return minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            ) != null;
        } catch (MinioException e) {
            // Handle MinIO exceptions (file not found is normal)
            log.debug("MinIO file not found: {}", objectName);
            return false;
        } catch (Exception e) {
            log.warn("Error checking if file exists in MinIO: {}", objectName, e);
            return false;
        }
    }
}
