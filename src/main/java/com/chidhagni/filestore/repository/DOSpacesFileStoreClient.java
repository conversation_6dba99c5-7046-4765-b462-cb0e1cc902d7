package com.chidhagni.filestore.repository;

import com.chidhagni.exception.StorageException;
import com.chidhagni.filestore.utils.FileStoreUtils;
import com.chidhagni.filestore.utils.TempFileResource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.File;
import java.nio.file.Path;

/**
 * DigitalOcean Spaces implementation of IFileStoreClient.
 * Uses AWS S3 SDK for Java since DigitalOcean Spaces is S3-compatible.
 * This implementation is only active when 'filestore.provider' is set to 'digitalocean'.
 */
@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "filestore.provider", havingValue = "digitalocean")
public class DOSpacesFileStoreClient implements IFileStoreClient {

    private final S3Client s3Client;

    @Value("${filestore.digitalocean.bucket.name}")
    private String bucketName;

    /**
     * Checks if the bucket exists and creates it if it doesn't.
     * Note: DigitalOcean Spaces buckets must be created manually through their console
     * or API, but we can check for existence.
     */
    private void checkForBucketAvailability() {
        try {
            HeadBucketRequest headBucketRequest = HeadBucketRequest.builder()
                    .bucket(bucketName)
                    .build();

            s3Client.headBucket(headBucketRequest);
            log.debug("DigitalOcean Spaces bucket {} exists and is accessible", bucketName);

        } catch (NoSuchBucketException e) {
            log.error("DigitalOcean Spaces bucket {} does not exist. Please create it in DigitalOcean Spaces console.", bucketName);
            throw StorageException.forCloudStorageFailure(bucketName, null, "bucket_availability_check", e);
        } catch (Exception e) {
            log.error("Failed to check bucket availability for bucket: {}", bucketName, e);
            throw StorageException.forCloudStorageFailure(bucketName, null, "bucket_availability_check", e);
        }
    }

    @Override
    public String storeFile(String location, Path filePath) {
        checkForBucketAvailability();

        String fileName = filePath.getFileName().toString();
        String objectKey = location + "/" + fileName;

        try {
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();

            PutObjectResponse response = s3Client.putObject(putObjectRequest,
                    RequestBody.fromFile(filePath));

            log.debug("File uploaded successfully to DigitalOcean Spaces. ETag: {}, ObjectKey: {}",
                     response.eTag(), objectKey);
            return objectKey;

        } catch (Exception e) {
            log.error("Failed to upload file to DigitalOcean Spaces: {}", fileName, e);
            throw StorageException.forCloudStorageFailure(bucketName, objectKey, "upload", e);
        }
    }

    @Override
    public File getFile(String location) {
        try {
            // Use utility method for consistent file path handling
            String tempFilePath = FileStoreUtils.createTempFilePath(location);
            File tempFile = new File(tempFilePath);

            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(location)
                    .build();

            s3Client.getObject(getObjectRequest, tempFile.toPath());

            log.debug("File downloaded successfully from DigitalOcean Spaces: {} to temp file: {}",
                     location, tempFile.getAbsolutePath());

            // Note: Caller is responsible for cleaning up the temporary file
            // Consider using TempFileResource for automatic cleanup in future versions
            return tempFile;

        } catch (NoSuchKeyException e) {
            log.error("File not found in DigitalOcean Spaces: {}", location);
            throw StorageException.forCloudStorageFailure(bucketName, location, "download", e);
        } catch (IllegalArgumentException e) {
            log.error("Invalid storage location format: {}", location, e);
            throw StorageException.forCloudStorageFailure(bucketName, location, "download", e);
        } catch (Exception e) {
            log.error("Failed to download file from DigitalOcean Spaces: {}", location, e);
            throw StorageException.forCloudStorageFailure(bucketName, location, "download", e);
        }
    }

    @Override
    public void deleteFile(String location) {
        try {
            DeleteObjectRequest deleteObjectRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(location)
                    .build();

            s3Client.deleteObject(deleteObjectRequest);
            log.info("File {} deleted successfully from DigitalOcean Spaces.", location);

        } catch (Exception e) {
            log.error("Failed to delete file from DigitalOcean Spaces: {}", location, e);
            throw StorageException.forCloudStorageFailure(bucketName, location, "delete", e);
        }
    }

    @Override
    public boolean checkIfFileExists(String objectName) {
        try {
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectName)
                    .build();

            s3Client.headObject(headObjectRequest);
            return true;

        } catch (NoSuchKeyException e) {
            log.debug("File not found in DigitalOcean Spaces: {}", objectName);
            return false;
        } catch (Exception e) {
            log.warn("Error checking file existence in DigitalOcean Spaces: {}", e.getMessage());
            return false;
        }
    }
}
