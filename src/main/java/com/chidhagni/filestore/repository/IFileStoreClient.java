package com.chidhagni.filestore.repository;

import java.io.File;
import java.nio.file.Path;

/**
 * Interface for file storage operations.
 * Provides abstraction for different storage providers (MinIO, DigitalOcean Spaces, etc.)
 */
public interface IFileStoreClient {

    /**
     * Retrieves a file from the storage and returns it as a File object
     * @param location the file location/path in the storage
     * @return File object
     */
    File getFile(String location);

    /**
     * Stores a file in the specified location
     * @param location the target location/path in the storage
     * @param filePath the path to the local file to be stored
     * @return the object name/key of the stored file
     */
    String storeFile(String location, Path filePath);

    /**
     * Deletes a file from the storage
     * @param location the file location/path to delete
     */
    void deleteFile(String location);

    /**
     * Checks if a file exists in the storage
     * @param objectName the file object name/path to check
     * @return true if file exists, false otherwise
     */
    boolean checkIfFileExists(String objectName);
}

