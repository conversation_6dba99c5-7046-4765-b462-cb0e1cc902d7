package com.chidhagni.filter;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.UUID;

/**
 * Servlet filter that automatically generates and propagates correlation IDs
 * for distributed tracing across the entire request lifecycle in ATS.
 * 
 * This filter:
 * - Extracts correlation ID from incoming request headers
 * - Generates a new correlation ID if none exists
 * - Adds correlation ID to MDC for logging
 * - Adds correlation ID to response headers
 * - Ensures MDC cleanup after request completion
 * 
 * <AUTHOR> Development Team
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
@Slf4j
public class CorrelationIdFilter implements Filter {
    
    private static final String CORRELATION_ID_HEADER = "X-Correlation-ID";
    private static final String CORRELATION_ID_MDC_KEY = "correlationId";
    private static final String REQUEST_ID_MDC_KEY = "requestId";
    private static final String USER_ID_MDC_KEY = "userId";
    private static final String REQUEST_URI_MDC_KEY = "requestUri";
    private static final String REQUEST_METHOD_MDC_KEY = "requestMethod";
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("Initializing ATS CorrelationIdFilter");
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        try {
            // Extract or generate correlation ID
            String correlationId = extractOrGenerateCorrelationId(httpRequest);
            
            // Set up MDC context
            setupMDCContext(httpRequest, correlationId);
            
            // Add correlation ID to response headers
            httpResponse.setHeader(CORRELATION_ID_HEADER, correlationId);
            
            // Log request start
            logRequestStart(httpRequest);
            
            // Continue with the filter chain
            chain.doFilter(request, response);
            
            // Log request completion
            logRequestCompletion(httpRequest, httpResponse, correlationId);
            
        } finally {
            // Always clean up MDC to prevent memory leaks
            clearMDCContext();
        }
    }
    
    @Override
    public void destroy() {
        log.info("Destroying ATS CorrelationIdFilter");
    }
    
    /**
     * Extracts correlation ID from request header or generates a new one
     */
    private String extractOrGenerateCorrelationId(HttpServletRequest request) {
        String correlationId = request.getHeader(CORRELATION_ID_HEADER);
        
        if (correlationId == null || correlationId.trim().isEmpty()) {
            correlationId = generateCorrelationId();
            log.debug("Generated new correlation ID: {}", correlationId);
        } else {
            log.debug("Using existing correlation ID: {}", correlationId);
        }
        
        return correlationId;
    }
    
    /**
     * Generates a new correlation ID
     */
    private String generateCorrelationId() {
        return "ats-" + UUID.randomUUID().toString();
    }
    
    /**
     * Sets up MDC context with correlation ID and other request metadata
     */
    private void setupMDCContext(HttpServletRequest request, String correlationId) {
        MDC.put(CORRELATION_ID_MDC_KEY, correlationId);
        MDC.put(REQUEST_ID_MDC_KEY, generateCorrelationId()); // Unique per request
        MDC.put(REQUEST_URI_MDC_KEY, request.getRequestURI());
        MDC.put(REQUEST_METHOD_MDC_KEY, request.getMethod());
        
        // Extract user ID from request if available (from session or security context)
        String userId = extractUserId(request);
        if (userId != null) {
            MDC.put(USER_ID_MDC_KEY, userId);
        }
    }
    
    /**
     * Extracts user ID from request context (session, security context, etc.)
     */
    private String extractUserId(HttpServletRequest request) {
        try {
            // Try to get user from Spring Security context
            // This will be implemented when authentication is added
            
            // For now, try to get from session
            String userId = (String) request.getSession().getAttribute("userId");
            if (userId != null && !userId.trim().isEmpty()) {
                log.debug("Extracted user ID from session: {}", userId);
                return userId;
            }
            
            // If no user ID found, return null
            log.debug("No user ID found in request");
            return null;
            
        } catch (Exception e) {
            // Log the exception but don't fail the request
            log.debug("Failed to extract user ID: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * Clears all MDC context
     */
    private void clearMDCContext() {
        MDC.remove(CORRELATION_ID_MDC_KEY);
        MDC.remove(REQUEST_ID_MDC_KEY);
        MDC.remove(USER_ID_MDC_KEY);
        MDC.remove(REQUEST_URI_MDC_KEY);
        MDC.remove(REQUEST_METHOD_MDC_KEY);
        // Clear all MDC to be safe
        MDC.clear();
    }
    
    /**
     * Logs request start with structured information
     */
    private void logRequestStart(HttpServletRequest request) {
        log.info("🚀 ATS Request started - method={}, uri={}, remoteAddr={}, userAgent={}",
                request.getMethod(),
                request.getRequestURI(),
                request.getRemoteAddr(),
                request.getHeader("User-Agent"));
    }
    
    /**
     * Logs request completion with structured information
     */
    private void logRequestCompletion(HttpServletRequest request, HttpServletResponse response, String correlationId) {
        log.info("✅ ATS Request completed - correlationId={}, method={}, uri={}, status={}",
                correlationId,
                request.getMethod(),
                request.getRequestURI(),
                response.getStatus());
    }
}
