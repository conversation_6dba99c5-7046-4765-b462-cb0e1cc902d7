package com.chidhagni.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Health check controller for the ATS application.
 * Provides endpoints to check the health and status of the application.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/health")
@Tag(name = "Health Check", description = "Health check and system status endpoints")
public class HealthController {

    /**
     * Basic health check endpoint.
     * 
     * @return ResponseEntity containing health status information
     */
    @GetMapping
    @Operation(
        summary = "Check application health",
        description = "Returns the current health status of the ATS application"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Application is healthy"),
        @ApiResponse(responseCode = "503", description = "Application is unhealthy")
    })
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> healthStatus = new HashMap<>();
        healthStatus.put("status", "UP");
        healthStatus.put("timestamp", LocalDateTime.now());
        healthStatus.put("service", "Application Tracking System");
        healthStatus.put("version", "1.0.0");
        
        return ResponseEntity.ok(healthStatus);
    }

    /**
     * Detailed health check endpoint with system information.
     * 
     * @return ResponseEntity containing detailed health information
     */
    @GetMapping("/detailed")
    @Operation(
        summary = "Get detailed health information",
        description = "Returns detailed health status including system information"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Detailed health information retrieved successfully")
    })
    public ResponseEntity<Map<String, Object>> detailedHealth() {
        Map<String, Object> detailedStatus = new HashMap<>();
        
        // Basic status
        detailedStatus.put("status", "UP");
        detailedStatus.put("timestamp", LocalDateTime.now());
        detailedStatus.put("service", "Application Tracking System");
        detailedStatus.put("version", "1.0.0");
        
        // System information
        Map<String, Object> systemInfo = new HashMap<>();
        systemInfo.put("javaVersion", System.getProperty("java.version"));
        systemInfo.put("osName", System.getProperty("os.name"));
        systemInfo.put("osVersion", System.getProperty("os.version"));
        systemInfo.put("availableProcessors", Runtime.getRuntime().availableProcessors());
        systemInfo.put("maxMemory", Runtime.getRuntime().maxMemory());
        systemInfo.put("totalMemory", Runtime.getRuntime().totalMemory());
        systemInfo.put("freeMemory", Runtime.getRuntime().freeMemory());
        
        detailedStatus.put("system", systemInfo);
        
        return ResponseEntity.ok(detailedStatus);
    }
}
