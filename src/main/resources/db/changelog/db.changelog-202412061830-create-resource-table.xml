<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="************-create-resource-table" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE resource (
                    id UUID,
                    "name" VARCHAR(100) NOT NULL,
                    description VARCHAR(255) NULL,
                    "type" VARCHAR(50) NOT NULL,
                    parent_resource_id UUID NULL,
                    validations JSONB,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_on TIMESTAMP NOT NULL,
                    updated_on TIMESTAMP NOT NULL,
                    created_by UUID NULL,
                    updated_by UUID NULL,
                    CONSTRAINT resource_id_pk PRIMARY KEY (id),
                    CONSTRAINT resource_parent_resource_fk FOREIGN KEY (parent_resource_id) REFERENCES resource(id)
                );
                

                CREATE INDEX idx_resource_type ON resource("type");
                CREATE INDEX idx_resource_parent_id ON resource(parent_resource_id);
                CREATE INDEX idx_resource_is_active ON resource(is_active);
                CREATE INDEX idx_resource_name ON resource("name");

            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
