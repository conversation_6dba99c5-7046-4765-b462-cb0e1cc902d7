<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="************-alter-gender-column-to-uuid" author="Ashraf">
        <comment>Alter the gender column in applicant table from VARCHAR to UUID data type</comment>
        
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                -- Clear existing data in gender column before type conversion
                UPDATE applicant SET gender = NULL;
                
                -- Alter the gender column data type from VARCHAR to UUID
                ALTER TABLE applicant ALTER COLUMN gender TYPE UUID USING (gender::UUID);
            ]]>
        </sql>

    </changeSet>

</databaseChangeLog>
