<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="202412061800-add-documents-jsonb-field" author="system">
        <comment>Add documents JSONB field to applicant table for storing document metadata</comment>
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                ALTER TABLE applicant ADD COLUMN documents JSONB;
              ]]>
        </sql>
    </changeSet>

    <changeSet id="202412061801-rename-employment-info-to-employer-details" author="system">
        <comment>Rename employment_info column to employer_details for consistency with payload structure</comment>
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                ALTER TABLE applicant RENAME COLUMN employment_info TO employer_details;
                ]]>
        </sql>

    </changeSet>

</databaseChangeLog>
