<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-create-document-repo-table.xml" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            CREATE TABLE document_repo (
                id uuid NOT NULL,
                category uuid NOT NULL,
                sub_category uuid,
                sender jsonb,
                recipients jsonb,
                -- "path" is quoted because PATH is a reserved keyword / built-in type in PostgreSQL
                "path" varchar(255),
                file_date timestamp,
                created_by uuid,
                updated_by uuid,
                created_on timestamp,
                updated_on timestamp,
                remarks varchar(255),
                tags jsonb,
                is_active bool DEFAULT true,
                CONSTRAINT document_repo_id_pk PRIMARY KEY (id),
                CONSTRAINT documents_repo_category_fk FOREIGN KEY (category) REFERENCES public.resource(id),
                CONSTRAINT documents_repo_sub_category_fk FOREIGN KEY (sub_category) REFERENCES public.resource(id)
            );

 ]]>
        </sql>
    </changeSet>
</databaseChangeLog>