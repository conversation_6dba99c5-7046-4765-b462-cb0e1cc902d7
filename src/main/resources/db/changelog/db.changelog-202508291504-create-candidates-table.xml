<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="************-create-candidates-table-v4" author="srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                CREATE TABLE candidates (
                    id UUID PRIMARY KEY,
                    org_id UUID ,

                    system_code VARCHAR(50) UNIQUE NOT NULL,

                    first_name VARCHAR(100) NOT NULL,
                    middle_name <PERSON><PERSON><PERSON><PERSON>(100),
                    last_name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
                    preferred_name VARCHAR(100),

                    email VARCHAR(255) NOT NULL,


                    date_of_birth DATE,

                    contact_info JSONB,         -- phone numbers, addresses, etc.
                    social_profiles JSONB,      -- LinkedIn, GitHub, etc.
                    addresses JSONB,            -- structured addresses
                    employment_info JSONB,      -- current/past employment details
                    work_experience JSONB,      -- employment history array
                    education JSONB,            -- academic qualifications
                    certifications JSONB,       -- professional certifications
                    additional_info JSONB,      -- EEO, IDs, security clearance
                    languages JSONB,            -- structured language details




                    status UUID NULL,
                    priority_level UUID,
                    assigned_to UUID ,

                    additional_comments TEXT,
                    gender VARCHAR,

                    application_date DATE,
                    is_active BOOLEAN NOT NULL DEFAULT true,

                    created_by UUID  NULL,
                    updated_by UUID,

                    created_on TIMESTAMP,
                    updated_on TIMESTAMP
                );
            ]]>
        </sql>
    </changeSet>

</databaseChangeLog>
