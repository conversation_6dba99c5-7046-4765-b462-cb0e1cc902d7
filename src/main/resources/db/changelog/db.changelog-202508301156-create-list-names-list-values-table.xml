<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet id="db.changelog-************-create-list-names" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
            CREATE TABLE list_names (
                                       id UUID NOT NULL,
                                       name VARCHAR(100) UNIQUE,
                                       is_active BOOLEAN DEFAULT true,
                                       created_by UUID NULL,
                                       updated_by UUID NULL,
                                       created_on TIMESTAMP NOT NULL,
                                       updated_on TIMESTAMP NOT NULL,
                                       CONSTRAINT list_names_id_pk PRIMARY KEY (id)
            );
            ]]>
        </sql>
    </changeSet>
    <changeSet id="db.changelog-************-create-list-values-table.xml" author="Srivani">
        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
           CREATE TABLE list_values (
                                          id UUID NOT NULL,
                                          name VARCHAR(100),
                                          list_names_id UUID NOT NULL,
                                          is_active BOOLEAN DEFAULT true,
                                          created_by UUID NULL,
                                          updated_by UUID NULL,
                                          created_on TIMESTAMP NOT NULL,
                                          updated_on TIMESTAMP NOT NULL,
                                          CONSTRAINT list_values_id_pk PRIMARY KEY (id),
                                          CONSTRAINT fk_list_names FOREIGN KEY (list_names_id) REFERENCES list_names (id)
            );
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
