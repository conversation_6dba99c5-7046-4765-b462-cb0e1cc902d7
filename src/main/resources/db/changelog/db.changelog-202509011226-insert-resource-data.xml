<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <changeSet id="202509011226-insert-resource-hierarchy-data" author="srivani">

        <sql dbms="postgresql" splitStatements="true" stripComments="true">
            <![CDATA[
                -- Insert parent resource: Document (top-level category, no type specified)
                INSERT INTO resource (
                    id, 
                    "name", 
                    description, 
                    "type", 
                    parent_resource_id, 
                    validations, 
                    is_active, 
                    created_on, 
                    updated_on, 
                    created_by, 
                    updated_by
                ) VALUES (
                    '7d3f9b3b-2b57-44db-94f0-f4a2c1d7f89d'::UUID,
                    'Document',
                    'Top-level document category for all document types',
                    '',
                    NULL,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );
                
                -- Insert child category: Applicant_Profile (category under Document)
                INSERT INTO resource (
                    id, 
                    "name", 
                    description, 
                    "type", 
                    parent_resource_id, 
                    validations, 
                    is_active, 
                    created_on, 
                    updated_on, 
                    created_by, 
                    updated_by
                ) VALUES (
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    'Applicant_Profile',
                    'Category for applicant profile related documents',
                    'CATEGORY',
                    '7d3f9b3b-2b57-44db-94f0-f4a2c1d7f89d'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );
                
                -- Insert subcategories under Applicant_Profile (validations kept NULL)

                -- Driving License
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    '5f6b14f9-8a6c-4641-94f2-47bb8d2a3f65'::UUID,
                    'Driving License',
                    'Applicant driving license document',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );

                -- EML File
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    '0c78ed84-24f9-4969-a428-621d9f7e13bb'::UUID,
                    'EML File',
                    'Email file document for applicant communication',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );

                -- Passport
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    'f44c5f38-46f1-4f12-b37b-d5ab19e7fcd6'::UUID,
                    'Passport',
                    'Applicant passport identification document',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );

                -- Resume
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    '9e0a33b2-d2b8-4eb3-b95d-0fd511e6e275'::UUID,
                    'Resume',
                    'Applicant resume/CV document',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );

                -- SSN
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    '86cf7c25-ccf4-46a0-9a7b-52c00a1b6d8f'::UUID,
                    'SSN',
                    'Social Security Number document',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );

                -- Transcripts
                INSERT INTO resource (
                    id,
                    "name",
                    description,
                    "type",
                    parent_resource_id,
                    validations,
                    is_active,
                    created_on,
                    updated_on,
                    created_by,
                    updated_by
                ) VALUES (
                    '1c19ed29-9d0c-4c48-9e84-4a1879bdbb7a'::UUID,
                    'Transcripts',
                    'Academic transcripts and educational documents',
                    'SUB_CATEGORY',
                    'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e'::UUID,
                    NULL,
                    TRUE,
                    NOW(),
                    NOW(),
                    NULL,
                    NULL
                );
            ]]>
        </sql>
    </changeSet>
</databaseChangeLog>
