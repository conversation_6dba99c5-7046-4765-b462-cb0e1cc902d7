# ========================================
# APPLICATION TRACKING SYSTEM (ATS) - CONFIGURATION
# ========================================

# Application Information
spring.application.name=ats-application
spring.application.description=Application Tracking System for managing job applications and candidates

# ========================================
# SERVER CONFIGURATION
# ========================================
server.port=8080
server.servlet.context-path=/ats
server.servlet.session.timeout=30m
server.compression.enabled=true
#Sets the maximum size of the HTTP request header (all headers combined).
server.max-http-header-size=64KB
#Sets the maximum size of the HTTP POST body that Tomcat will accept.
server.max-http-post-size=20MB

# ========================================
# DATABASE CONFIGURATION
# ========================================
spring.datasource.url=***************************************
spring.datasource.username=ats_user
spring.datasource.password=ats_password
spring.datasource.driver-class-name=org.postgresql.Driver

# HikariCP Connection Pool Configuration
spring.datasource.hikari.pool-name=ATS-HikariCP
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.leak-detection-threshold=60000
# Use prepared statement caching (reduces parsing overhead)
#Enables caching of prepared statements on the PostgreSQL JDBC driver side.
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
#Defines how many prepared statements can be cached per connection.
spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
#Sets the maximum size of a prepared statement that can be cached.
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
# Optimize batching
#Tells the PostgreSQL JDBC driver to rewrite batched inserts into a single multi-row insert
spring.datasource.hikari.data-source-properties.rewriteBatchedInserts=true


# ========================================
# JPA/HIBERNATE CONFIGURATION
# ========================================
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.use_sql_comments=true
spring.jpa.properties.hibernate.jdbc.batch_size=20
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true




# ========================================
# SESSION CONFIGURATION
# ========================================
spring.session.store-type=jdbc
spring.session.jdbc.initialize-schema=always
spring.session.jdbc.table-name=SPRING_SESSION



# ========================================
# ACTUATOR CONFIGURATION (Health Checks & Monitoring)
# ========================================
management.endpoint.health.show-details=always
management.endpoint.shutdown.enabled=true

# ========================================
# LOGGING CONFIGURATION
# ========================================
logging.level.com.chidhagni.ats=DEBUG
logging.level.org.springframework.security=DEBUG
logging.level.org.jooq=DEBUG
logging.level.org.jooq.tools.LoggerListener=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] %logger{36} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{correlationId:-}] [%X{requestId:-}] %logger{36} - %msg%n

# ========================================
# QUERY PERFORMANCE MONITORING
# ========================================
# jOOQ Performance Settings
#Ensures jOOQ generates SQL optimized for PostgreSQL.
jooq.sql.dialect=POSTGRES
#Enables SQL execution logging at the framework level.
jooq.execute-logging=true
#Sets a 30-second timeout for all SQL queries.
jooq.query-timeout=30000
#Sets the threshold for slow queries to 1 second.
jooq.slow-query-threshold=1000

# ========================================
# CORS CONFIGURATION
# ========================================
# CORS configuration for cross-origin requests
cors.allowed-origins=https://dev.ats.hrhighwaves.com,https://www.dev.ats.hrhighwaves.com
cors.allowed-methods=GET,POST,PUT,DELETE,PATCH,OPTIONS
cors.allow-credentials=true
cors.max-age=3600

# ========================================
# SWAGGER/OPENAPI CONFIGURATION
# ========================================
# SpringDoc OpenAPI Configuration
springdoc.api-docs.enabled=true
springdoc.api-docs.path=/v3/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.packages-to-scan=com.chidhagni
springdoc.swagger-ui.config-url=/ats/v3/api-docs/swagger-config
springdoc.swagger-ui.url=/ats/v3/api-docs

# ========================================
# APPLICATION SPECIFIC CONFIGURATION
# ========================================
# File upload configuration
ats.file-upload.max-file-size=20MB
ats.file-upload.max-request-size=20MB
ats.file-upload.upload-dir=./uploads
ats.file-upload.allowed-types=application/pdf,image/jpeg,image/png,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document

# ========================================
# FILE STORAGE CONFIGURATION (MinIO & DigitalOcean Spaces)
# ========================================
# File storage provider: 'minio' or 'digitalocean' (can be extended for AWS S3, etc.)
filestore.provider=digitalocean

# MinIO Configuration (default for local development)
filestore.minio.bucket.name=ats-bucket
filestore.minio.access.name=minio
filestore.minio.access.secret=minio123
filestore.minio.url=http://127.0.0.1:9000

# Spring Servlet Multipart Configuration (for file uploads)
#Enables Spring Boot?s built-in multipart support (for handling file uploads).
spring.servlet.multipart.enabled=true
#Sets the maximum file size that can be uploaded.
spring.servlet.multipart.max-file-size=20MB
#Sets the maximum size of the entire multipart request.
spring.servlet.multipart.max-request-size=20MB
#Enables lazy initialization of multipart requests.
spring.servlet.multipart.resolve-lazily=true
#Sets the threshold after which files will be written to disk.
spring.servlet.multipart.file-size-threshold=2KB

# Tomcat settings to align with ingress limit
server.tomcat.max-swallow-size=20MB
server.tomcat.max-http-post-size=20MB

# Optional: reasonable connection timeout
server.connection-timeout=300000  # 5 minutes

# ========================================
# THYMELEAF CONFIGURATION
# ========================================
# Thymeleaf template configuration
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false
spring.thymeleaf.servlet.content-type=text/html
spring.thymeleaf.check-template-location=true
# DigitalOcean Spaces Configuration
filestore.digitalocean.bucket.name=chidhagni-do-object-storage

# ========================================
# OBSERVABILITY CONFIGURATION
# ========================================
# Metrics
management.endpoint.metrics.enabled=true
management.endpoint.prometheus.enabled=true
# Tracing
management.tracing.enabled=true
management.tracing.sampling.probability=1.0

# Resource attributes for service identifications
deployment.environment=dev