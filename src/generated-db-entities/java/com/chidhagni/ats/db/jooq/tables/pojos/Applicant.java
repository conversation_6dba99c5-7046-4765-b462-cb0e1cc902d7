/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.pojos;


import com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO;
import com.chidhagni.ats.applicant.dto.request.AddressesDTO;
import com.chidhagni.ats.applicant.dto.request.CertificationsDTO;
import com.chidhagni.ats.applicant.dto.request.ContactInfoDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.applicant.dto.request.EducationDTO;
import com.chidhagni.ats.applicant.dto.request.EmployerDetailsDTO;
import com.chidhagni.ats.applicant.dto.request.SocialProfilesDTO;
import com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.JSONB;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Applicant implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private UUID orgId;
    private String systemCode;
    private String firstName;
    private String middleName;
    private String lastName;
    private String preferredName;
    private String email;
    private LocalDate dateOfBirth;
    private ContactInfoDTO contactInfo;
    private SocialProfilesDTO socialProfiles;
    private AddressesDTO addresses;
    private EmployerDetailsDTO employerDetails;
    private List<WorkExperienceDTO> workExperience;
    private List<EducationDTO> education;
    private List<CertificationsDTO> certifications;
    private AdditionalInfoDTO additionalInfo;
    private JSONB languages;
    private UUID status;
    private UUID priorityLevel;
    private UUID assignedTo;
    private String additionalComments;
    private UUID gender;
    private LocalDate applicationDate;
    private Boolean isActive;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private List<DocumentDTO> documents;

    public Applicant() {}

    public Applicant(Applicant value) {
        this.id = value.id;
        this.orgId = value.orgId;
        this.systemCode = value.systemCode;
        this.firstName = value.firstName;
        this.middleName = value.middleName;
        this.lastName = value.lastName;
        this.preferredName = value.preferredName;
        this.email = value.email;
        this.dateOfBirth = value.dateOfBirth;
        this.contactInfo = value.contactInfo;
        this.socialProfiles = value.socialProfiles;
        this.addresses = value.addresses;
        this.employerDetails = value.employerDetails;
        this.workExperience = value.workExperience;
        this.education = value.education;
        this.certifications = value.certifications;
        this.additionalInfo = value.additionalInfo;
        this.languages = value.languages;
        this.status = value.status;
        this.priorityLevel = value.priorityLevel;
        this.assignedTo = value.assignedTo;
        this.additionalComments = value.additionalComments;
        this.gender = value.gender;
        this.applicationDate = value.applicationDate;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
        this.documents = value.documents;
    }

    public Applicant(
        UUID id,
        UUID orgId,
        String systemCode,
        String firstName,
        String middleName,
        String lastName,
        String preferredName,
        String email,
        LocalDate dateOfBirth,
        ContactInfoDTO contactInfo,
        SocialProfilesDTO socialProfiles,
        AddressesDTO addresses,
        EmployerDetailsDTO employerDetails,
        List<WorkExperienceDTO> workExperience,
        List<EducationDTO> education,
        List<CertificationsDTO> certifications,
        AdditionalInfoDTO additionalInfo,
        JSONB languages,
        UUID status,
        UUID priorityLevel,
        UUID assignedTo,
        String additionalComments,
        UUID gender,
        LocalDate applicationDate,
        Boolean isActive,
        UUID createdBy,
        UUID updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn,
        List<DocumentDTO> documents
    ) {
        this.id = id;
        this.orgId = orgId;
        this.systemCode = systemCode;
        this.firstName = firstName;
        this.middleName = middleName;
        this.lastName = lastName;
        this.preferredName = preferredName;
        this.email = email;
        this.dateOfBirth = dateOfBirth;
        this.contactInfo = contactInfo;
        this.socialProfiles = socialProfiles;
        this.addresses = addresses;
        this.employerDetails = employerDetails;
        this.workExperience = workExperience;
        this.education = education;
        this.certifications = certifications;
        this.additionalInfo = additionalInfo;
        this.languages = languages;
        this.status = status;
        this.priorityLevel = priorityLevel;
        this.assignedTo = assignedTo;
        this.additionalComments = additionalComments;
        this.gender = gender;
        this.applicationDate = applicationDate;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
        this.documents = documents;
    }

    /**
     * Getter for <code>applicant.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>applicant.id</code>.
     */
    public Applicant setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>applicant.org_id</code>.
     */
    public UUID getOrgId() {
        return this.orgId;
    }

    /**
     * Setter for <code>applicant.org_id</code>.
     */
    public Applicant setOrgId(UUID orgId) {
        this.orgId = orgId;
        return this;
    }

    /**
     * Getter for <code>applicant.system_code</code>.
     */
    public String getSystemCode() {
        return this.systemCode;
    }

    /**
     * Setter for <code>applicant.system_code</code>.
     */
    public Applicant setSystemCode(String systemCode) {
        this.systemCode = systemCode;
        return this;
    }

    /**
     * Getter for <code>applicant.first_name</code>.
     */
    public String getFirstName() {
        return this.firstName;
    }

    /**
     * Setter for <code>applicant.first_name</code>.
     */
    public Applicant setFirstName(String firstName) {
        this.firstName = firstName;
        return this;
    }

    /**
     * Getter for <code>applicant.middle_name</code>.
     */
    public String getMiddleName() {
        return this.middleName;
    }

    /**
     * Setter for <code>applicant.middle_name</code>.
     */
    public Applicant setMiddleName(String middleName) {
        this.middleName = middleName;
        return this;
    }

    /**
     * Getter for <code>applicant.last_name</code>.
     */
    public String getLastName() {
        return this.lastName;
    }

    /**
     * Setter for <code>applicant.last_name</code>.
     */
    public Applicant setLastName(String lastName) {
        this.lastName = lastName;
        return this;
    }

    /**
     * Getter for <code>applicant.preferred_name</code>.
     */
    public String getPreferredName() {
        return this.preferredName;
    }

    /**
     * Setter for <code>applicant.preferred_name</code>.
     */
    public Applicant setPreferredName(String preferredName) {
        this.preferredName = preferredName;
        return this;
    }

    /**
     * Getter for <code>applicant.email</code>.
     */
    public String getEmail() {
        return this.email;
    }

    /**
     * Setter for <code>applicant.email</code>.
     */
    public Applicant setEmail(String email) {
        this.email = email;
        return this;
    }

    /**
     * Getter for <code>applicant.date_of_birth</code>.
     */
    public LocalDate getDateOfBirth() {
        return this.dateOfBirth;
    }

    /**
     * Setter for <code>applicant.date_of_birth</code>.
     */
    public Applicant setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
        return this;
    }

    /**
     * Getter for <code>applicant.contact_info</code>.
     */
    public ContactInfoDTO getContactInfo() {
        return this.contactInfo;
    }

    /**
     * Setter for <code>applicant.contact_info</code>.
     */
    public Applicant setContactInfo(ContactInfoDTO contactInfo) {
        this.contactInfo = contactInfo;
        return this;
    }

    /**
     * Getter for <code>applicant.social_profiles</code>.
     */
    public SocialProfilesDTO getSocialProfiles() {
        return this.socialProfiles;
    }

    /**
     * Setter for <code>applicant.social_profiles</code>.
     */
    public Applicant setSocialProfiles(SocialProfilesDTO socialProfiles) {
        this.socialProfiles = socialProfiles;
        return this;
    }

    /**
     * Getter for <code>applicant.addresses</code>.
     */
    public AddressesDTO getAddresses() {
        return this.addresses;
    }

    /**
     * Setter for <code>applicant.addresses</code>.
     */
    public Applicant setAddresses(AddressesDTO addresses) {
        this.addresses = addresses;
        return this;
    }

    /**
     * Getter for <code>applicant.employer_details</code>.
     */
    public EmployerDetailsDTO getEmployerDetails() {
        return this.employerDetails;
    }

    /**
     * Setter for <code>applicant.employer_details</code>.
     */
    public Applicant setEmployerDetails(EmployerDetailsDTO employerDetails) {
        this.employerDetails = employerDetails;
        return this;
    }

    /**
     * Getter for <code>applicant.work_experience</code>.
     */
    public List<WorkExperienceDTO> getWorkExperience() {
        return this.workExperience;
    }

    /**
     * Setter for <code>applicant.work_experience</code>.
     */
    public Applicant setWorkExperience(List<WorkExperienceDTO> workExperience) {
        this.workExperience = workExperience;
        return this;
    }

    /**
     * Getter for <code>applicant.education</code>.
     */
    public List<EducationDTO> getEducation() {
        return this.education;
    }

    /**
     * Setter for <code>applicant.education</code>.
     */
    public Applicant setEducation(List<EducationDTO> education) {
        this.education = education;
        return this;
    }

    /**
     * Getter for <code>applicant.certifications</code>.
     */
    public List<CertificationsDTO> getCertifications() {
        return this.certifications;
    }

    /**
     * Setter for <code>applicant.certifications</code>.
     */
    public Applicant setCertifications(List<CertificationsDTO> certifications) {
        this.certifications = certifications;
        return this;
    }

    /**
     * Getter for <code>applicant.additional_info</code>.
     */
    public AdditionalInfoDTO getAdditionalInfo() {
        return this.additionalInfo;
    }

    /**
     * Setter for <code>applicant.additional_info</code>.
     */
    public Applicant setAdditionalInfo(AdditionalInfoDTO additionalInfo) {
        this.additionalInfo = additionalInfo;
        return this;
    }

    /**
     * Getter for <code>applicant.languages</code>.
     */
    public JSONB getLanguages() {
        return this.languages;
    }

    /**
     * Setter for <code>applicant.languages</code>.
     */
    public Applicant setLanguages(JSONB languages) {
        this.languages = languages;
        return this;
    }

    /**
     * Getter for <code>applicant.status</code>.
     */
    public UUID getStatus() {
        return this.status;
    }

    /**
     * Setter for <code>applicant.status</code>.
     */
    public Applicant setStatus(UUID status) {
        this.status = status;
        return this;
    }

    /**
     * Getter for <code>applicant.priority_level</code>.
     */
    public UUID getPriorityLevel() {
        return this.priorityLevel;
    }

    /**
     * Setter for <code>applicant.priority_level</code>.
     */
    public Applicant setPriorityLevel(UUID priorityLevel) {
        this.priorityLevel = priorityLevel;
        return this;
    }

    /**
     * Getter for <code>applicant.assigned_to</code>.
     */
    public UUID getAssignedTo() {
        return this.assignedTo;
    }

    /**
     * Setter for <code>applicant.assigned_to</code>.
     */
    public Applicant setAssignedTo(UUID assignedTo) {
        this.assignedTo = assignedTo;
        return this;
    }

    /**
     * Getter for <code>applicant.additional_comments</code>.
     */
    public String getAdditionalComments() {
        return this.additionalComments;
    }

    /**
     * Setter for <code>applicant.additional_comments</code>.
     */
    public Applicant setAdditionalComments(String additionalComments) {
        this.additionalComments = additionalComments;
        return this;
    }

    /**
     * Getter for <code>applicant.gender</code>.
     */
    public UUID getGender() {
        return this.gender;
    }

    /**
     * Setter for <code>applicant.gender</code>.
     */
    public Applicant setGender(UUID gender) {
        this.gender = gender;
        return this;
    }

    /**
     * Getter for <code>applicant.application_date</code>.
     */
    public LocalDate getApplicationDate() {
        return this.applicationDate;
    }

    /**
     * Setter for <code>applicant.application_date</code>.
     */
    public Applicant setApplicationDate(LocalDate applicationDate) {
        this.applicationDate = applicationDate;
        return this;
    }

    /**
     * Getter for <code>applicant.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>applicant.is_active</code>.
     */
    public Applicant setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>applicant.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>applicant.created_by</code>.
     */
    public Applicant setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>applicant.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>applicant.updated_by</code>.
     */
    public Applicant setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>applicant.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>applicant.created_on</code>.
     */
    public Applicant setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>applicant.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>applicant.updated_on</code>.
     */
    public Applicant setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    /**
     * Getter for <code>applicant.documents</code>.
     */
    public List<DocumentDTO> getDocuments() {
        return this.documents;
    }

    /**
     * Setter for <code>applicant.documents</code>.
     */
    public Applicant setDocuments(List<DocumentDTO> documents) {
        this.documents = documents;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final Applicant other = (Applicant) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.orgId == null) {
            if (other.orgId != null)
                return false;
        }
        else if (!this.orgId.equals(other.orgId))
            return false;
        if (this.systemCode == null) {
            if (other.systemCode != null)
                return false;
        }
        else if (!this.systemCode.equals(other.systemCode))
            return false;
        if (this.firstName == null) {
            if (other.firstName != null)
                return false;
        }
        else if (!this.firstName.equals(other.firstName))
            return false;
        if (this.middleName == null) {
            if (other.middleName != null)
                return false;
        }
        else if (!this.middleName.equals(other.middleName))
            return false;
        if (this.lastName == null) {
            if (other.lastName != null)
                return false;
        }
        else if (!this.lastName.equals(other.lastName))
            return false;
        if (this.preferredName == null) {
            if (other.preferredName != null)
                return false;
        }
        else if (!this.preferredName.equals(other.preferredName))
            return false;
        if (this.email == null) {
            if (other.email != null)
                return false;
        }
        else if (!this.email.equals(other.email))
            return false;
        if (this.dateOfBirth == null) {
            if (other.dateOfBirth != null)
                return false;
        }
        else if (!this.dateOfBirth.equals(other.dateOfBirth))
            return false;
        if (this.contactInfo == null) {
            if (other.contactInfo != null)
                return false;
        }
        else if (!this.contactInfo.equals(other.contactInfo))
            return false;
        if (this.socialProfiles == null) {
            if (other.socialProfiles != null)
                return false;
        }
        else if (!this.socialProfiles.equals(other.socialProfiles))
            return false;
        if (this.addresses == null) {
            if (other.addresses != null)
                return false;
        }
        else if (!this.addresses.equals(other.addresses))
            return false;
        if (this.employerDetails == null) {
            if (other.employerDetails != null)
                return false;
        }
        else if (!this.employerDetails.equals(other.employerDetails))
            return false;
        if (this.workExperience == null) {
            if (other.workExperience != null)
                return false;
        }
        else if (!this.workExperience.equals(other.workExperience))
            return false;
        if (this.education == null) {
            if (other.education != null)
                return false;
        }
        else if (!this.education.equals(other.education))
            return false;
        if (this.certifications == null) {
            if (other.certifications != null)
                return false;
        }
        else if (!this.certifications.equals(other.certifications))
            return false;
        if (this.additionalInfo == null) {
            if (other.additionalInfo != null)
                return false;
        }
        else if (!this.additionalInfo.equals(other.additionalInfo))
            return false;
        if (this.languages == null) {
            if (other.languages != null)
                return false;
        }
        else if (!this.languages.equals(other.languages))
            return false;
        if (this.status == null) {
            if (other.status != null)
                return false;
        }
        else if (!this.status.equals(other.status))
            return false;
        if (this.priorityLevel == null) {
            if (other.priorityLevel != null)
                return false;
        }
        else if (!this.priorityLevel.equals(other.priorityLevel))
            return false;
        if (this.assignedTo == null) {
            if (other.assignedTo != null)
                return false;
        }
        else if (!this.assignedTo.equals(other.assignedTo))
            return false;
        if (this.additionalComments == null) {
            if (other.additionalComments != null)
                return false;
        }
        else if (!this.additionalComments.equals(other.additionalComments))
            return false;
        if (this.gender == null) {
            if (other.gender != null)
                return false;
        }
        else if (!this.gender.equals(other.gender))
            return false;
        if (this.applicationDate == null) {
            if (other.applicationDate != null)
                return false;
        }
        else if (!this.applicationDate.equals(other.applicationDate))
            return false;
        if (this.isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!this.isActive.equals(other.isActive))
            return false;
        if (this.createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!this.createdBy.equals(other.createdBy))
            return false;
        if (this.updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!this.updatedBy.equals(other.updatedBy))
            return false;
        if (this.createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!this.createdOn.equals(other.createdOn))
            return false;
        if (this.updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!this.updatedOn.equals(other.updatedOn))
            return false;
        if (this.documents == null) {
            if (other.documents != null)
                return false;
        }
        else if (!this.documents.equals(other.documents))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.orgId == null) ? 0 : this.orgId.hashCode());
        result = prime * result + ((this.systemCode == null) ? 0 : this.systemCode.hashCode());
        result = prime * result + ((this.firstName == null) ? 0 : this.firstName.hashCode());
        result = prime * result + ((this.middleName == null) ? 0 : this.middleName.hashCode());
        result = prime * result + ((this.lastName == null) ? 0 : this.lastName.hashCode());
        result = prime * result + ((this.preferredName == null) ? 0 : this.preferredName.hashCode());
        result = prime * result + ((this.email == null) ? 0 : this.email.hashCode());
        result = prime * result + ((this.dateOfBirth == null) ? 0 : this.dateOfBirth.hashCode());
        result = prime * result + ((this.contactInfo == null) ? 0 : this.contactInfo.hashCode());
        result = prime * result + ((this.socialProfiles == null) ? 0 : this.socialProfiles.hashCode());
        result = prime * result + ((this.addresses == null) ? 0 : this.addresses.hashCode());
        result = prime * result + ((this.employerDetails == null) ? 0 : this.employerDetails.hashCode());
        result = prime * result + ((this.workExperience == null) ? 0 : this.workExperience.hashCode());
        result = prime * result + ((this.education == null) ? 0 : this.education.hashCode());
        result = prime * result + ((this.certifications == null) ? 0 : this.certifications.hashCode());
        result = prime * result + ((this.additionalInfo == null) ? 0 : this.additionalInfo.hashCode());
        result = prime * result + ((this.languages == null) ? 0 : this.languages.hashCode());
        result = prime * result + ((this.status == null) ? 0 : this.status.hashCode());
        result = prime * result + ((this.priorityLevel == null) ? 0 : this.priorityLevel.hashCode());
        result = prime * result + ((this.assignedTo == null) ? 0 : this.assignedTo.hashCode());
        result = prime * result + ((this.additionalComments == null) ? 0 : this.additionalComments.hashCode());
        result = prime * result + ((this.gender == null) ? 0 : this.gender.hashCode());
        result = prime * result + ((this.applicationDate == null) ? 0 : this.applicationDate.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        result = prime * result + ((this.documents == null) ? 0 : this.documents.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("Applicant (");

        sb.append(id);
        sb.append(", ").append(orgId);
        sb.append(", ").append(systemCode);
        sb.append(", ").append(firstName);
        sb.append(", ").append(middleName);
        sb.append(", ").append(lastName);
        sb.append(", ").append(preferredName);
        sb.append(", ").append(email);
        sb.append(", ").append(dateOfBirth);
        sb.append(", ").append(contactInfo);
        sb.append(", ").append(socialProfiles);
        sb.append(", ").append(addresses);
        sb.append(", ").append(employerDetails);
        sb.append(", ").append(workExperience);
        sb.append(", ").append(education);
        sb.append(", ").append(certifications);
        sb.append(", ").append(additionalInfo);
        sb.append(", ").append(languages);
        sb.append(", ").append(status);
        sb.append(", ").append(priorityLevel);
        sb.append(", ").append(assignedTo);
        sb.append(", ").append(additionalComments);
        sb.append(", ").append(gender);
        sb.append(", ").append(applicationDate);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);
        sb.append(", ").append(documents);

        sb.append(")");
        return sb.toString();
    }
}
