/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.records;


import com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO;
import com.chidhagni.ats.applicant.dto.request.AddressesDTO;
import com.chidhagni.ats.applicant.dto.request.CertificationsDTO;
import com.chidhagni.ats.applicant.dto.request.ContactInfoDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.applicant.dto.request.EducationDTO;
import com.chidhagni.ats.applicant.dto.request.EmployerDetailsDTO;
import com.chidhagni.ats.applicant.dto.request.SocialProfilesDTO;
import com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO;
import com.chidhagni.ats.db.jooq.tables.Applicant;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ApplicantRecord extends UpdatableRecordImpl<ApplicantRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>applicant.id</code>.
     */
    public ApplicantRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>applicant.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>applicant.org_id</code>.
     */
    public ApplicantRecord setOrgId(UUID value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>applicant.org_id</code>.
     */
    public UUID getOrgId() {
        return (UUID) get(1);
    }

    /**
     * Setter for <code>applicant.system_code</code>.
     */
    public ApplicantRecord setSystemCode(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>applicant.system_code</code>.
     */
    public String getSystemCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>applicant.first_name</code>.
     */
    public ApplicantRecord setFirstName(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>applicant.first_name</code>.
     */
    public String getFirstName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>applicant.middle_name</code>.
     */
    public ApplicantRecord setMiddleName(String value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>applicant.middle_name</code>.
     */
    public String getMiddleName() {
        return (String) get(4);
    }

    /**
     * Setter for <code>applicant.last_name</code>.
     */
    public ApplicantRecord setLastName(String value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>applicant.last_name</code>.
     */
    public String getLastName() {
        return (String) get(5);
    }

    /**
     * Setter for <code>applicant.preferred_name</code>.
     */
    public ApplicantRecord setPreferredName(String value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>applicant.preferred_name</code>.
     */
    public String getPreferredName() {
        return (String) get(6);
    }

    /**
     * Setter for <code>applicant.email</code>.
     */
    public ApplicantRecord setEmail(String value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>applicant.email</code>.
     */
    public String getEmail() {
        return (String) get(7);
    }

    /**
     * Setter for <code>applicant.date_of_birth</code>.
     */
    public ApplicantRecord setDateOfBirth(LocalDate value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>applicant.date_of_birth</code>.
     */
    public LocalDate getDateOfBirth() {
        return (LocalDate) get(8);
    }

    /**
     * Setter for <code>applicant.contact_info</code>.
     */
    public ApplicantRecord setContactInfo(ContactInfoDTO value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>applicant.contact_info</code>.
     */
    public ContactInfoDTO getContactInfo() {
        return (ContactInfoDTO) get(9);
    }

    /**
     * Setter for <code>applicant.social_profiles</code>.
     */
    public ApplicantRecord setSocialProfiles(SocialProfilesDTO value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>applicant.social_profiles</code>.
     */
    public SocialProfilesDTO getSocialProfiles() {
        return (SocialProfilesDTO) get(10);
    }

    /**
     * Setter for <code>applicant.addresses</code>.
     */
    public ApplicantRecord setAddresses(AddressesDTO value) {
        set(11, value);
        return this;
    }

    /**
     * Getter for <code>applicant.addresses</code>.
     */
    public AddressesDTO getAddresses() {
        return (AddressesDTO) get(11);
    }

    /**
     * Setter for <code>applicant.employer_details</code>.
     */
    public ApplicantRecord setEmployerDetails(EmployerDetailsDTO value) {
        set(12, value);
        return this;
    }

    /**
     * Getter for <code>applicant.employer_details</code>.
     */
    public EmployerDetailsDTO getEmployerDetails() {
        return (EmployerDetailsDTO) get(12);
    }

    /**
     * Setter for <code>applicant.work_experience</code>.
     */
    public ApplicantRecord setWorkExperience(List<WorkExperienceDTO> value) {
        set(13, value);
        return this;
    }

    /**
     * Getter for <code>applicant.work_experience</code>.
     */
    public List<WorkExperienceDTO> getWorkExperience() {
        return (List<WorkExperienceDTO>) get(13);
    }

    /**
     * Setter for <code>applicant.education</code>.
     */
    public ApplicantRecord setEducation(List<EducationDTO> value) {
        set(14, value);
        return this;
    }

    /**
     * Getter for <code>applicant.education</code>.
     */
    public List<EducationDTO> getEducation() {
        return (List<EducationDTO>) get(14);
    }

    /**
     * Setter for <code>applicant.certifications</code>.
     */
    public ApplicantRecord setCertifications(List<CertificationsDTO> value) {
        set(15, value);
        return this;
    }

    /**
     * Getter for <code>applicant.certifications</code>.
     */
    public List<CertificationsDTO> getCertifications() {
        return (List<CertificationsDTO>) get(15);
    }

    /**
     * Setter for <code>applicant.additional_info</code>.
     */
    public ApplicantRecord setAdditionalInfo(AdditionalInfoDTO value) {
        set(16, value);
        return this;
    }

    /**
     * Getter for <code>applicant.additional_info</code>.
     */
    public AdditionalInfoDTO getAdditionalInfo() {
        return (AdditionalInfoDTO) get(16);
    }

    /**
     * Setter for <code>applicant.languages</code>.
     */
    public ApplicantRecord setLanguages(JSONB value) {
        set(17, value);
        return this;
    }

    /**
     * Getter for <code>applicant.languages</code>.
     */
    public JSONB getLanguages() {
        return (JSONB) get(17);
    }

    /**
     * Setter for <code>applicant.status</code>.
     */
    public ApplicantRecord setStatus(UUID value) {
        set(18, value);
        return this;
    }

    /**
     * Getter for <code>applicant.status</code>.
     */
    public UUID getStatus() {
        return (UUID) get(18);
    }

    /**
     * Setter for <code>applicant.priority_level</code>.
     */
    public ApplicantRecord setPriorityLevel(UUID value) {
        set(19, value);
        return this;
    }

    /**
     * Getter for <code>applicant.priority_level</code>.
     */
    public UUID getPriorityLevel() {
        return (UUID) get(19);
    }

    /**
     * Setter for <code>applicant.assigned_to</code>.
     */
    public ApplicantRecord setAssignedTo(UUID value) {
        set(20, value);
        return this;
    }

    /**
     * Getter for <code>applicant.assigned_to</code>.
     */
    public UUID getAssignedTo() {
        return (UUID) get(20);
    }

    /**
     * Setter for <code>applicant.additional_comments</code>.
     */
    public ApplicantRecord setAdditionalComments(String value) {
        set(21, value);
        return this;
    }

    /**
     * Getter for <code>applicant.additional_comments</code>.
     */
    public String getAdditionalComments() {
        return (String) get(21);
    }

    /**
     * Setter for <code>applicant.gender</code>.
     */
    public ApplicantRecord setGender(UUID value) {
        set(22, value);
        return this;
    }

    /**
     * Getter for <code>applicant.gender</code>.
     */
    public UUID getGender() {
        return (UUID) get(22);
    }

    /**
     * Setter for <code>applicant.application_date</code>.
     */
    public ApplicantRecord setApplicationDate(LocalDate value) {
        set(23, value);
        return this;
    }

    /**
     * Getter for <code>applicant.application_date</code>.
     */
    public LocalDate getApplicationDate() {
        return (LocalDate) get(23);
    }

    /**
     * Setter for <code>applicant.is_active</code>.
     */
    public ApplicantRecord setIsActive(Boolean value) {
        set(24, value);
        return this;
    }

    /**
     * Getter for <code>applicant.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(24);
    }

    /**
     * Setter for <code>applicant.created_by</code>.
     */
    public ApplicantRecord setCreatedBy(UUID value) {
        set(25, value);
        return this;
    }

    /**
     * Getter for <code>applicant.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(25);
    }

    /**
     * Setter for <code>applicant.updated_by</code>.
     */
    public ApplicantRecord setUpdatedBy(UUID value) {
        set(26, value);
        return this;
    }

    /**
     * Getter for <code>applicant.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(26);
    }

    /**
     * Setter for <code>applicant.created_on</code>.
     */
    public ApplicantRecord setCreatedOn(LocalDateTime value) {
        set(27, value);
        return this;
    }

    /**
     * Getter for <code>applicant.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(27);
    }

    /**
     * Setter for <code>applicant.updated_on</code>.
     */
    public ApplicantRecord setUpdatedOn(LocalDateTime value) {
        set(28, value);
        return this;
    }

    /**
     * Getter for <code>applicant.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(28);
    }

    /**
     * Setter for <code>applicant.documents</code>.
     */
    public ApplicantRecord setDocuments(List<DocumentDTO> value) {
        set(29, value);
        return this;
    }

    /**
     * Getter for <code>applicant.documents</code>.
     */
    public List<DocumentDTO> getDocuments() {
        return (List<DocumentDTO>) get(29);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ApplicantRecord
     */
    public ApplicantRecord() {
        super(Applicant.APPLICANT);
    }

    /**
     * Create a detached, initialised ApplicantRecord
     */
    public ApplicantRecord(UUID id, UUID orgId, String systemCode, String firstName, String middleName, String lastName, String preferredName, String email, LocalDate dateOfBirth, ContactInfoDTO contactInfo, SocialProfilesDTO socialProfiles, AddressesDTO addresses, EmployerDetailsDTO employerDetails, List<WorkExperienceDTO> workExperience, List<EducationDTO> education, List<CertificationsDTO> certifications, AdditionalInfoDTO additionalInfo, JSONB languages, UUID status, UUID priorityLevel, UUID assignedTo, String additionalComments, UUID gender, LocalDate applicationDate, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn, List<DocumentDTO> documents) {
        super(Applicant.APPLICANT);

        setId(id);
        setOrgId(orgId);
        setSystemCode(systemCode);
        setFirstName(firstName);
        setMiddleName(middleName);
        setLastName(lastName);
        setPreferredName(preferredName);
        setEmail(email);
        setDateOfBirth(dateOfBirth);
        setContactInfo(contactInfo);
        setSocialProfiles(socialProfiles);
        setAddresses(addresses);
        setEmployerDetails(employerDetails);
        setWorkExperience(workExperience);
        setEducation(education);
        setCertifications(certifications);
        setAdditionalInfo(additionalInfo);
        setLanguages(languages);
        setStatus(status);
        setPriorityLevel(priorityLevel);
        setAssignedTo(assignedTo);
        setAdditionalComments(additionalComments);
        setGender(gender);
        setApplicationDate(applicationDate);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setDocuments(documents);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ApplicantRecord
     */
    public ApplicantRecord(com.chidhagni.ats.db.jooq.tables.pojos.Applicant value) {
        super(Applicant.APPLICANT);

        if (value != null) {
            setId(value.getId());
            setOrgId(value.getOrgId());
            setSystemCode(value.getSystemCode());
            setFirstName(value.getFirstName());
            setMiddleName(value.getMiddleName());
            setLastName(value.getLastName());
            setPreferredName(value.getPreferredName());
            setEmail(value.getEmail());
            setDateOfBirth(value.getDateOfBirth());
            setContactInfo(value.getContactInfo());
            setSocialProfiles(value.getSocialProfiles());
            setAddresses(value.getAddresses());
            setEmployerDetails(value.getEmployerDetails());
            setWorkExperience(value.getWorkExperience());
            setEducation(value.getEducation());
            setCertifications(value.getCertifications());
            setAdditionalInfo(value.getAdditionalInfo());
            setLanguages(value.getLanguages());
            setStatus(value.getStatus());
            setPriorityLevel(value.getPriorityLevel());
            setAssignedTo(value.getAssignedTo());
            setAdditionalComments(value.getAdditionalComments());
            setGender(value.getGender());
            setApplicationDate(value.getApplicationDate());
            setIsActive(value.getIsActive());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            setCreatedOn(value.getCreatedOn());
            setUpdatedOn(value.getUpdatedOn());
            setDocuments(value.getDocuments());
            resetChangedOnNotNull();
        }
    }
}
