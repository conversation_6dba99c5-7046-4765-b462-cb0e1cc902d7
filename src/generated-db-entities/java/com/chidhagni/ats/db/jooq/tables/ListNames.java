/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables;


import com.chidhagni.ats.db.jooq.DefaultSchema;
import com.chidhagni.ats.db.jooq.Keys;
import com.chidhagni.ats.db.jooq.tables.records.ListNamesRecord;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function7;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row7;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListNames extends TableImpl<ListNamesRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>list_names</code>
     */
    public static final ListNames LIST_NAMES = new ListNames();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ListNamesRecord> getRecordType() {
        return ListNamesRecord.class;
    }

    /**
     * The column <code>list_names.id</code>.
     */
    public final TableField<ListNamesRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>list_names.name</code>.
     */
    public final TableField<ListNamesRecord, String> NAME = createField(DSL.name("name"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>list_names.is_active</code>.
     */
    public final TableField<ListNamesRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>list_names.created_by</code>.
     */
    public final TableField<ListNamesRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>list_names.updated_by</code>.
     */
    public final TableField<ListNamesRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>list_names.created_on</code>.
     */
    public final TableField<ListNamesRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    /**
     * The column <code>list_names.updated_on</code>.
     */
    public final TableField<ListNamesRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6).nullable(false), this, "");

    private ListNames(Name alias, Table<ListNamesRecord> aliased) {
        this(alias, aliased, null);
    }

    private ListNames(Name alias, Table<ListNamesRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>list_names</code> table reference
     */
    public ListNames(String alias) {
        this(DSL.name(alias), LIST_NAMES);
    }

    /**
     * Create an aliased <code>list_names</code> table reference
     */
    public ListNames(Name alias) {
        this(alias, LIST_NAMES);
    }

    /**
     * Create a <code>list_names</code> table reference
     */
    public ListNames() {
        this(DSL.name("list_names"), null);
    }

    public <O extends Record> ListNames(Table<O> child, ForeignKey<O, ListNamesRecord> key) {
        super(child, key, LIST_NAMES);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<ListNamesRecord> getPrimaryKey() {
        return Keys.LIST_NAMES_ID_PK;
    }

    @Override
    public List<UniqueKey<ListNamesRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.LIST_NAMES_NAME_KEY);
    }

    @Override
    public ListNames as(String alias) {
        return new ListNames(DSL.name(alias), this);
    }

    @Override
    public ListNames as(Name alias) {
        return new ListNames(alias, this);
    }

    @Override
    public ListNames as(Table<?> alias) {
        return new ListNames(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public ListNames rename(String name) {
        return new ListNames(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public ListNames rename(Name name) {
        return new ListNames(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public ListNames rename(Table<?> name) {
        return new ListNames(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row7 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function7<? super UUID, ? super String, ? super Boolean, ? super UUID, ? super UUID, ? super LocalDateTime, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function7<? super UUID, ? super String, ? super Boolean, ? super UUID, ? super UUID, ? super LocalDateTime, ? super LocalDateTime, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
