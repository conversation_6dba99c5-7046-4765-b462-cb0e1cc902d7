/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.daos;


import com.chidhagni.ats.db.jooq.tables.ListNames;
import com.chidhagni.ats.db.jooq.tables.records.ListNamesRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ListNamesDao extends DAOImpl<ListNamesRecord, com.chidhagni.ats.db.jooq.tables.pojos.ListNames, UUID> {

    /**
     * Create a new ListNamesDao without any configuration
     */
    public ListNamesDao() {
        super(ListNames.LIST_NAMES, com.chidhagni.ats.db.jooq.tables.pojos.ListNames.class);
    }

    /**
     * Create a new ListNamesDao with an attached configuration
     */
    @Autowired
    public ListNamesDao(Configuration configuration) {
        super(ListNames.LIST_NAMES, com.chidhagni.ats.db.jooq.tables.pojos.ListNames.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.ats.db.jooq.tables.pojos.ListNames object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchById(UUID... values) {
        return fetch(ListNames.LIST_NAMES.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.ats.db.jooq.tables.pojos.ListNames fetchOneById(UUID value) {
        return fetchOne(ListNames.LIST_NAMES.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchOptionalById(UUID value) {
        return fetchOptional(ListNames.LIST_NAMES.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByName(String... values) {
        return fetch(ListNames.LIST_NAMES.NAME, values);
    }

    /**
     * Fetch a unique record that has <code>name = value</code>
     */
    public com.chidhagni.ats.db.jooq.tables.pojos.ListNames fetchOneByName(String value) {
        return fetchOne(ListNames.LIST_NAMES.NAME, value);
    }

    /**
     * Fetch a unique record that has <code>name = value</code>
     */
    public Optional<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchOptionalByName(String value) {
        return fetchOptional(ListNames.LIST_NAMES.NAME, value);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByIsActive(Boolean... values) {
        return fetch(ListNames.LIST_NAMES.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByCreatedBy(UUID... values) {
        return fetch(ListNames.LIST_NAMES.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByUpdatedBy(UUID... values) {
        return fetch(ListNames.LIST_NAMES.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(ListNames.LIST_NAMES.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(ListNames.LIST_NAMES.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.ListNames> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(ListNames.LIST_NAMES.UPDATED_ON, values);
    }
}
