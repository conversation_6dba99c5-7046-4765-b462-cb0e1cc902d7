/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.records;


import com.chidhagni.ats.db.jooq.tables.ListNames;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record7;
import org.jooq.Row7;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListNamesRecord extends UpdatableRecordImpl<ListNamesRecord> implements Record7<UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>list_names.id</code>.
     */
    public ListNamesRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>list_names.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>list_names.name</code>.
     */
    public ListNamesRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>list_names.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>list_names.is_active</code>.
     */
    public ListNamesRecord setIsActive(Boolean value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>list_names.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(2);
    }

    /**
     * Setter for <code>list_names.created_by</code>.
     */
    public ListNamesRecord setCreatedBy(UUID value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>list_names.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(3);
    }

    /**
     * Setter for <code>list_names.updated_by</code>.
     */
    public ListNamesRecord setUpdatedBy(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>list_names.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>list_names.created_on</code>.
     */
    public ListNamesRecord setCreatedOn(LocalDateTime value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>list_names.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(5);
    }

    /**
     * Setter for <code>list_names.updated_on</code>.
     */
    public ListNamesRecord setUpdatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>list_names.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(6);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record7 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row7<UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row7) super.fieldsRow();
    }

    @Override
    public Row7<UUID, String, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row7) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ListNames.LIST_NAMES.ID;
    }

    @Override
    public Field<String> field2() {
        return ListNames.LIST_NAMES.NAME;
    }

    @Override
    public Field<Boolean> field3() {
        return ListNames.LIST_NAMES.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field4() {
        return ListNames.LIST_NAMES.CREATED_BY;
    }

    @Override
    public Field<UUID> field5() {
        return ListNames.LIST_NAMES.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field6() {
        return ListNames.LIST_NAMES.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return ListNames.LIST_NAMES.UPDATED_ON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public Boolean component3() {
        return getIsActive();
    }

    @Override
    public UUID component4() {
        return getCreatedBy();
    }

    @Override
    public UUID component5() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component6() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component7() {
        return getUpdatedOn();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public Boolean value3() {
        return getIsActive();
    }

    @Override
    public UUID value4() {
        return getCreatedBy();
    }

    @Override
    public UUID value5() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value6() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value7() {
        return getUpdatedOn();
    }

    @Override
    public ListNamesRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ListNamesRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public ListNamesRecord value3(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public ListNamesRecord value4(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public ListNamesRecord value5(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public ListNamesRecord value6(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ListNamesRecord value7(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ListNamesRecord values(UUID value1, String value2, Boolean value3, UUID value4, UUID value5, LocalDateTime value6, LocalDateTime value7) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ListNamesRecord
     */
    public ListNamesRecord() {
        super(ListNames.LIST_NAMES);
    }

    /**
     * Create a detached, initialised ListNamesRecord
     */
    public ListNamesRecord(UUID id, String name, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn) {
        super(ListNames.LIST_NAMES);

        setId(id);
        setName(name);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ListNamesRecord
     */
    public ListNamesRecord(com.chidhagni.ats.db.jooq.tables.pojos.ListNames value) {
        super(ListNames.LIST_NAMES);

        if (value != null) {
            setId(value.getId());
            setName(value.getName());
            setIsActive(value.getIsActive());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            setCreatedOn(value.getCreatedOn());
            setUpdatedOn(value.getUpdatedOn());
            resetChangedOnNotNull();
        }
    }
}
