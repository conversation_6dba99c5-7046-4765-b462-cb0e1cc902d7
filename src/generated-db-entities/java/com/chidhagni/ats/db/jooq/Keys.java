/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq;


import com.chidhagni.ats.db.jooq.tables.Applicant;
import com.chidhagni.ats.db.jooq.tables.DocumentRepo;
import com.chidhagni.ats.db.jooq.tables.ListNames;
import com.chidhagni.ats.db.jooq.tables.ListValues;
import com.chidhagni.ats.db.jooq.tables.Resource;
import com.chidhagni.ats.db.jooq.tables.records.ApplicantRecord;
import com.chidhagni.ats.db.jooq.tables.records.DocumentRepoRecord;
import com.chidhagni.ats.db.jooq.tables.records.ListNamesRecord;
import com.chidhagni.ats.db.jooq.tables.records.ListValuesRecord;
import com.chidhagni.ats.db.jooq.tables.records.ResourceRecord;

import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling foreign key relationships and constraints of tables in the
 * default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Keys {

    // -------------------------------------------------------------------------
    // UNIQUE and PRIMARY KEY definitions
    // -------------------------------------------------------------------------

    public static final UniqueKey<ApplicantRecord> CANDIDATES_PKEY = Internal.createUniqueKey(Applicant.APPLICANT, DSL.name("candidates_pkey"), new TableField[] { Applicant.APPLICANT.ID }, true);
    public static final UniqueKey<ApplicantRecord> CANDIDATES_SYSTEM_CODE_KEY = Internal.createUniqueKey(Applicant.APPLICANT, DSL.name("candidates_system_code_key"), new TableField[] { Applicant.APPLICANT.SYSTEM_CODE }, true);
    public static final UniqueKey<DocumentRepoRecord> DOCUMENT_REPO_ID_PK = Internal.createUniqueKey(DocumentRepo.DOCUMENT_REPO, DSL.name("document_repo_id_pk"), new TableField[] { DocumentRepo.DOCUMENT_REPO.ID }, true);
    public static final UniqueKey<ListNamesRecord> LIST_NAMES_ID_PK = Internal.createUniqueKey(ListNames.LIST_NAMES, DSL.name("list_names_id_pk"), new TableField[] { ListNames.LIST_NAMES.ID }, true);
    public static final UniqueKey<ListNamesRecord> LIST_NAMES_NAME_KEY = Internal.createUniqueKey(ListNames.LIST_NAMES, DSL.name("list_names_name_key"), new TableField[] { ListNames.LIST_NAMES.NAME }, true);
    public static final UniqueKey<ListValuesRecord> LIST_VALUES_ID_PK = Internal.createUniqueKey(ListValues.LIST_VALUES, DSL.name("list_values_id_pk"), new TableField[] { ListValues.LIST_VALUES.ID }, true);
    public static final UniqueKey<ResourceRecord> RESOURCE_ID_PK = Internal.createUniqueKey(Resource.RESOURCE, DSL.name("resource_id_pk"), new TableField[] { Resource.RESOURCE.ID }, true);
}
