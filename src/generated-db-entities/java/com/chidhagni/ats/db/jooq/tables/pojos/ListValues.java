/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.pojos;


import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.UUID;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListValues implements Serializable {

    private static final long serialVersionUID = 1L;

    private UUID id;
    private String name;
    private UUID listNamesId;
    private Boolean isActive;
    private UUID createdBy;
    private UUID updatedBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

    public ListValues() {}

    public ListValues(ListValues value) {
        this.id = value.id;
        this.name = value.name;
        this.listNamesId = value.listNamesId;
        this.isActive = value.isActive;
        this.createdBy = value.createdBy;
        this.updatedBy = value.updatedBy;
        this.createdOn = value.createdOn;
        this.updatedOn = value.updatedOn;
    }

    public ListValues(
        UUID id,
        String name,
        UUID listNamesId,
        Boolean isActive,
        UUID createdBy,
        UUID updatedBy,
        LocalDateTime createdOn,
        LocalDateTime updatedOn
    ) {
        this.id = id;
        this.name = name;
        this.listNamesId = listNamesId;
        this.isActive = isActive;
        this.createdBy = createdBy;
        this.updatedBy = updatedBy;
        this.createdOn = createdOn;
        this.updatedOn = updatedOn;
    }

    /**
     * Getter for <code>list_values.id</code>.
     */
    public UUID getId() {
        return this.id;
    }

    /**
     * Setter for <code>list_values.id</code>.
     */
    public ListValues setId(UUID id) {
        this.id = id;
        return this;
    }

    /**
     * Getter for <code>list_values.name</code>.
     */
    public String getName() {
        return this.name;
    }

    /**
     * Setter for <code>list_values.name</code>.
     */
    public ListValues setName(String name) {
        this.name = name;
        return this;
    }

    /**
     * Getter for <code>list_values.list_names_id</code>.
     */
    public UUID getListNamesId() {
        return this.listNamesId;
    }

    /**
     * Setter for <code>list_values.list_names_id</code>.
     */
    public ListValues setListNamesId(UUID listNamesId) {
        this.listNamesId = listNamesId;
        return this;
    }

    /**
     * Getter for <code>list_values.is_active</code>.
     */
    public Boolean getIsActive() {
        return this.isActive;
    }

    /**
     * Setter for <code>list_values.is_active</code>.
     */
    public ListValues setIsActive(Boolean isActive) {
        this.isActive = isActive;
        return this;
    }

    /**
     * Getter for <code>list_values.created_by</code>.
     */
    public UUID getCreatedBy() {
        return this.createdBy;
    }

    /**
     * Setter for <code>list_values.created_by</code>.
     */
    public ListValues setCreatedBy(UUID createdBy) {
        this.createdBy = createdBy;
        return this;
    }

    /**
     * Getter for <code>list_values.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return this.updatedBy;
    }

    /**
     * Setter for <code>list_values.updated_by</code>.
     */
    public ListValues setUpdatedBy(UUID updatedBy) {
        this.updatedBy = updatedBy;
        return this;
    }

    /**
     * Getter for <code>list_values.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return this.createdOn;
    }

    /**
     * Setter for <code>list_values.created_on</code>.
     */
    public ListValues setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
        return this;
    }

    /**
     * Getter for <code>list_values.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return this.updatedOn;
    }

    /**
     * Setter for <code>list_values.updated_on</code>.
     */
    public ListValues setUpdatedOn(LocalDateTime updatedOn) {
        this.updatedOn = updatedOn;
        return this;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        final ListValues other = (ListValues) obj;
        if (this.id == null) {
            if (other.id != null)
                return false;
        }
        else if (!this.id.equals(other.id))
            return false;
        if (this.name == null) {
            if (other.name != null)
                return false;
        }
        else if (!this.name.equals(other.name))
            return false;
        if (this.listNamesId == null) {
            if (other.listNamesId != null)
                return false;
        }
        else if (!this.listNamesId.equals(other.listNamesId))
            return false;
        if (this.isActive == null) {
            if (other.isActive != null)
                return false;
        }
        else if (!this.isActive.equals(other.isActive))
            return false;
        if (this.createdBy == null) {
            if (other.createdBy != null)
                return false;
        }
        else if (!this.createdBy.equals(other.createdBy))
            return false;
        if (this.updatedBy == null) {
            if (other.updatedBy != null)
                return false;
        }
        else if (!this.updatedBy.equals(other.updatedBy))
            return false;
        if (this.createdOn == null) {
            if (other.createdOn != null)
                return false;
        }
        else if (!this.createdOn.equals(other.createdOn))
            return false;
        if (this.updatedOn == null) {
            if (other.updatedOn != null)
                return false;
        }
        else if (!this.updatedOn.equals(other.updatedOn))
            return false;
        return true;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((this.id == null) ? 0 : this.id.hashCode());
        result = prime * result + ((this.name == null) ? 0 : this.name.hashCode());
        result = prime * result + ((this.listNamesId == null) ? 0 : this.listNamesId.hashCode());
        result = prime * result + ((this.isActive == null) ? 0 : this.isActive.hashCode());
        result = prime * result + ((this.createdBy == null) ? 0 : this.createdBy.hashCode());
        result = prime * result + ((this.updatedBy == null) ? 0 : this.updatedBy.hashCode());
        result = prime * result + ((this.createdOn == null) ? 0 : this.createdOn.hashCode());
        result = prime * result + ((this.updatedOn == null) ? 0 : this.updatedOn.hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ListValues (");

        sb.append(id);
        sb.append(", ").append(name);
        sb.append(", ").append(listNamesId);
        sb.append(", ").append(isActive);
        sb.append(", ").append(createdBy);
        sb.append(", ").append(updatedBy);
        sb.append(", ").append(createdOn);
        sb.append(", ").append(updatedOn);

        sb.append(")");
        return sb.toString();
    }
}
