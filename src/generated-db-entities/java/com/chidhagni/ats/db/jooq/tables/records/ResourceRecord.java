/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.records;


import com.chidhagni.ats.db.jooq.tables.Resource;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.JSONB;
import org.jooq.Record1;
import org.jooq.Record11;
import org.jooq.Row11;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ResourceRecord extends UpdatableRecordImpl<ResourceRecord> implements Record11<UUID, String, String, String, UUID, JSONB, Boolean, LocalDateTime, LocalDateTime, UUID, UUID> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>resource.id</code>.
     */
    public ResourceRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>resource.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>resource.name</code>.
     */
    public ResourceRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>resource.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>resource.description</code>.
     */
    public ResourceRecord setDescription(String value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>resource.description</code>.
     */
    public String getDescription() {
        return (String) get(2);
    }

    /**
     * Setter for <code>resource.type</code>.
     */
    public ResourceRecord setType(String value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>resource.type</code>.
     */
    public String getType() {
        return (String) get(3);
    }

    /**
     * Setter for <code>resource.parent_resource_id</code>.
     */
    public ResourceRecord setParentResourceId(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>resource.parent_resource_id</code>.
     */
    public UUID getParentResourceId() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>resource.validations</code>.
     */
    public ResourceRecord setValidations(JSONB value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>resource.validations</code>.
     */
    public JSONB getValidations() {
        return (JSONB) get(5);
    }

    /**
     * Setter for <code>resource.is_active</code>.
     */
    public ResourceRecord setIsActive(Boolean value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>resource.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(6);
    }

    /**
     * Setter for <code>resource.created_on</code>.
     */
    public ResourceRecord setCreatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>resource.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(7);
    }

    /**
     * Setter for <code>resource.updated_on</code>.
     */
    public ResourceRecord setUpdatedOn(LocalDateTime value) {
        set(8, value);
        return this;
    }

    /**
     * Getter for <code>resource.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(8);
    }

    /**
     * Setter for <code>resource.created_by</code>.
     */
    public ResourceRecord setCreatedBy(UUID value) {
        set(9, value);
        return this;
    }

    /**
     * Getter for <code>resource.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(9);
    }

    /**
     * Setter for <code>resource.updated_by</code>.
     */
    public ResourceRecord setUpdatedBy(UUID value) {
        set(10, value);
        return this;
    }

    /**
     * Getter for <code>resource.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(10);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record11 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row11<UUID, String, String, String, UUID, JSONB, Boolean, LocalDateTime, LocalDateTime, UUID, UUID> fieldsRow() {
        return (Row11) super.fieldsRow();
    }

    @Override
    public Row11<UUID, String, String, String, UUID, JSONB, Boolean, LocalDateTime, LocalDateTime, UUID, UUID> valuesRow() {
        return (Row11) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return Resource.RESOURCE.ID;
    }

    @Override
    public Field<String> field2() {
        return Resource.RESOURCE.NAME;
    }

    @Override
    public Field<String> field3() {
        return Resource.RESOURCE.DESCRIPTION;
    }

    @Override
    public Field<String> field4() {
        return Resource.RESOURCE.TYPE;
    }

    @Override
    public Field<UUID> field5() {
        return Resource.RESOURCE.PARENT_RESOURCE_ID;
    }

    @Override
    public Field<JSONB> field6() {
        return Resource.RESOURCE.VALIDATIONS;
    }

    @Override
    public Field<Boolean> field7() {
        return Resource.RESOURCE.IS_ACTIVE;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return Resource.RESOURCE.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field9() {
        return Resource.RESOURCE.UPDATED_ON;
    }

    @Override
    public Field<UUID> field10() {
        return Resource.RESOURCE.CREATED_BY;
    }

    @Override
    public Field<UUID> field11() {
        return Resource.RESOURCE.UPDATED_BY;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public String component3() {
        return getDescription();
    }

    @Override
    public String component4() {
        return getType();
    }

    @Override
    public UUID component5() {
        return getParentResourceId();
    }

    @Override
    public JSONB component6() {
        return getValidations();
    }

    @Override
    public Boolean component7() {
        return getIsActive();
    }

    @Override
    public LocalDateTime component8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component9() {
        return getUpdatedOn();
    }

    @Override
    public UUID component10() {
        return getCreatedBy();
    }

    @Override
    public UUID component11() {
        return getUpdatedBy();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public String value3() {
        return getDescription();
    }

    @Override
    public String value4() {
        return getType();
    }

    @Override
    public UUID value5() {
        return getParentResourceId();
    }

    @Override
    public JSONB value6() {
        return getValidations();
    }

    @Override
    public Boolean value7() {
        return getIsActive();
    }

    @Override
    public LocalDateTime value8() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value9() {
        return getUpdatedOn();
    }

    @Override
    public UUID value10() {
        return getCreatedBy();
    }

    @Override
    public UUID value11() {
        return getUpdatedBy();
    }

    @Override
    public ResourceRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ResourceRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public ResourceRecord value3(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public ResourceRecord value4(String value) {
        setType(value);
        return this;
    }

    @Override
    public ResourceRecord value5(UUID value) {
        setParentResourceId(value);
        return this;
    }

    @Override
    public ResourceRecord value6(JSONB value) {
        setValidations(value);
        return this;
    }

    @Override
    public ResourceRecord value7(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public ResourceRecord value8(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ResourceRecord value9(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ResourceRecord value10(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public ResourceRecord value11(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public ResourceRecord values(UUID value1, String value2, String value3, String value4, UUID value5, JSONB value6, Boolean value7, LocalDateTime value8, LocalDateTime value9, UUID value10, UUID value11) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ResourceRecord
     */
    public ResourceRecord() {
        super(Resource.RESOURCE);
    }

    /**
     * Create a detached, initialised ResourceRecord
     */
    public ResourceRecord(UUID id, String name, String description, String type, UUID parentResourceId, JSONB validations, Boolean isActive, LocalDateTime createdOn, LocalDateTime updatedOn, UUID createdBy, UUID updatedBy) {
        super(Resource.RESOURCE);

        setId(id);
        setName(name);
        setDescription(description);
        setType(type);
        setParentResourceId(parentResourceId);
        setValidations(validations);
        setIsActive(isActive);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ResourceRecord
     */
    public ResourceRecord(com.chidhagni.ats.db.jooq.tables.pojos.Resource value) {
        super(Resource.RESOURCE);

        if (value != null) {
            setId(value.getId());
            setName(value.getName());
            setDescription(value.getDescription());
            setType(value.getType());
            setParentResourceId(value.getParentResourceId());
            setValidations(value.getValidations());
            setIsActive(value.getIsActive());
            setCreatedOn(value.getCreatedOn());
            setUpdatedOn(value.getUpdatedOn());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            resetChangedOnNotNull();
        }
    }
}
