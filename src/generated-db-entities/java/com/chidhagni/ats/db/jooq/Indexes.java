/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq;


import com.chidhagni.ats.db.jooq.tables.Resource;

import org.jooq.Index;
import org.jooq.OrderField;
import org.jooq.impl.DSL;
import org.jooq.impl.Internal;


/**
 * A class modelling indexes of tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Indexes {

    // -------------------------------------------------------------------------
    // INDEX definitions
    // -------------------------------------------------------------------------

    public static final Index IDX_RESOURCE_IS_ACTIVE = Internal.createIndex(DSL.name("idx_resource_is_active"), Resource.RESOURCE, new OrderField[] { Resource.RESOURCE.IS_ACTIVE }, false);
    public static final Index IDX_RESOURCE_NAME = Internal.createIndex(DSL.name("idx_resource_name"), Resource.RESOURCE, new OrderField[] { Resource.RESOURCE.NAME }, false);
    public static final Index IDX_RESOURCE_PARENT_ID = Internal.createIndex(DSL.name("idx_resource_parent_id"), Resource.RESOURCE, new OrderField[] { Resource.RESOURCE.PARENT_RESOURCE_ID }, false);
    public static final Index IDX_RESOURCE_TYPE = Internal.createIndex(DSL.name("idx_resource_type"), Resource.RESOURCE, new OrderField[] { Resource.RESOURCE.TYPE }, false);
}
