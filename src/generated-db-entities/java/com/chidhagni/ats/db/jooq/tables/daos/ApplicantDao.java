/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.daos;


import com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO;
import com.chidhagni.ats.applicant.dto.request.AddressesDTO;
import com.chidhagni.ats.applicant.dto.request.CertificationsDTO;
import com.chidhagni.ats.applicant.dto.request.ContactInfoDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.applicant.dto.request.EducationDTO;
import com.chidhagni.ats.applicant.dto.request.EmployerDetailsDTO;
import com.chidhagni.ats.applicant.dto.request.SocialProfilesDTO;
import com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO;
import com.chidhagni.ats.db.jooq.tables.Applicant;
import com.chidhagni.ats.db.jooq.tables.records.ApplicantRecord;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ApplicantDao extends DAOImpl<ApplicantRecord, com.chidhagni.ats.db.jooq.tables.pojos.Applicant, UUID> {

    /**
     * Create a new ApplicantDao without any configuration
     */
    public ApplicantDao() {
        super(Applicant.APPLICANT, com.chidhagni.ats.db.jooq.tables.pojos.Applicant.class);
    }

    /**
     * Create a new ApplicantDao with an attached configuration
     */
    @Autowired
    public ApplicantDao(Configuration configuration) {
        super(Applicant.APPLICANT, com.chidhagni.ats.db.jooq.tables.pojos.Applicant.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.ats.db.jooq.tables.pojos.Applicant object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchById(UUID... values) {
        return fetch(Applicant.APPLICANT.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.ats.db.jooq.tables.pojos.Applicant fetchOneById(UUID value) {
        return fetchOne(Applicant.APPLICANT.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchOptionalById(UUID value) {
        return fetchOptional(Applicant.APPLICANT.ID, value);
    }

    /**
     * Fetch records that have <code>org_id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfOrgId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ORG_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>org_id IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByOrgId(UUID... values) {
        return fetch(Applicant.APPLICANT.ORG_ID, values);
    }

    /**
     * Fetch records that have <code>system_code BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfSystemCode(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.SYSTEM_CODE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>system_code IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchBySystemCode(String... values) {
        return fetch(Applicant.APPLICANT.SYSTEM_CODE, values);
    }

    /**
     * Fetch a unique record that has <code>system_code = value</code>
     */
    public com.chidhagni.ats.db.jooq.tables.pojos.Applicant fetchOneBySystemCode(String value) {
        return fetchOne(Applicant.APPLICANT.SYSTEM_CODE, value);
    }

    /**
     * Fetch a unique record that has <code>system_code = value</code>
     */
    public Optional<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchOptionalBySystemCode(String value) {
        return fetchOptional(Applicant.APPLICANT.SYSTEM_CODE, value);
    }

    /**
     * Fetch records that have <code>first_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfFirstName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.FIRST_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>first_name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByFirstName(String... values) {
        return fetch(Applicant.APPLICANT.FIRST_NAME, values);
    }

    /**
     * Fetch records that have <code>middle_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfMiddleName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.MIDDLE_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>middle_name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByMiddleName(String... values) {
        return fetch(Applicant.APPLICANT.MIDDLE_NAME, values);
    }

    /**
     * Fetch records that have <code>last_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfLastName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.LAST_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>last_name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByLastName(String... values) {
        return fetch(Applicant.APPLICANT.LAST_NAME, values);
    }

    /**
     * Fetch records that have <code>preferred_name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfPreferredName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.PREFERRED_NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>preferred_name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByPreferredName(String... values) {
        return fetch(Applicant.APPLICANT.PREFERRED_NAME, values);
    }

    /**
     * Fetch records that have <code>email BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfEmail(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.EMAIL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>email IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByEmail(String... values) {
        return fetch(Applicant.APPLICANT.EMAIL, values);
    }

    /**
     * Fetch records that have <code>date_of_birth BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfDateOfBirth(LocalDate lowerInclusive, LocalDate upperInclusive) {
        return fetchRange(Applicant.APPLICANT.DATE_OF_BIRTH, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>date_of_birth IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByDateOfBirth(LocalDate... values) {
        return fetch(Applicant.APPLICANT.DATE_OF_BIRTH, values);
    }

    /**
     * Fetch records that have <code>contact_info BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfContactInfo(ContactInfoDTO lowerInclusive, ContactInfoDTO upperInclusive) {
        return fetchRange(Applicant.APPLICANT.CONTACT_INFO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>contact_info IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByContactInfo(ContactInfoDTO... values) {
        return fetch(Applicant.APPLICANT.CONTACT_INFO, values);
    }

    /**
     * Fetch records that have <code>social_profiles BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfSocialProfiles(SocialProfilesDTO lowerInclusive, SocialProfilesDTO upperInclusive) {
        return fetchRange(Applicant.APPLICANT.SOCIAL_PROFILES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>social_profiles IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchBySocialProfiles(SocialProfilesDTO... values) {
        return fetch(Applicant.APPLICANT.SOCIAL_PROFILES, values);
    }

    /**
     * Fetch records that have <code>addresses BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfAddresses(AddressesDTO lowerInclusive, AddressesDTO upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ADDRESSES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>addresses IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByAddresses(AddressesDTO... values) {
        return fetch(Applicant.APPLICANT.ADDRESSES, values);
    }

    /**
     * Fetch records that have <code>employer_details BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfEmployerDetails(EmployerDetailsDTO lowerInclusive, EmployerDetailsDTO upperInclusive) {
        return fetchRange(Applicant.APPLICANT.EMPLOYER_DETAILS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>employer_details IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByEmployerDetails(EmployerDetailsDTO... values) {
        return fetch(Applicant.APPLICANT.EMPLOYER_DETAILS, values);
    }

    /**
     * Fetch records that have <code>work_experience BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfWorkExperience(List<WorkExperienceDTO> lowerInclusive, List<WorkExperienceDTO> upperInclusive) {
        return fetchRange(Applicant.APPLICANT.WORK_EXPERIENCE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>work_experience IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByWorkExperience(List<WorkExperienceDTO>... values) {
        return fetch(Applicant.APPLICANT.WORK_EXPERIENCE, values);
    }

    /**
     * Fetch records that have <code>education BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfEducation(List<EducationDTO> lowerInclusive, List<EducationDTO> upperInclusive) {
        return fetchRange(Applicant.APPLICANT.EDUCATION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>education IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByEducation(List<EducationDTO>... values) {
        return fetch(Applicant.APPLICANT.EDUCATION, values);
    }

    /**
     * Fetch records that have <code>certifications BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfCertifications(List<CertificationsDTO> lowerInclusive, List<CertificationsDTO> upperInclusive) {
        return fetchRange(Applicant.APPLICANT.CERTIFICATIONS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>certifications IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByCertifications(List<CertificationsDTO>... values) {
        return fetch(Applicant.APPLICANT.CERTIFICATIONS, values);
    }

    /**
     * Fetch records that have <code>additional_info BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfAdditionalInfo(AdditionalInfoDTO lowerInclusive, AdditionalInfoDTO upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ADDITIONAL_INFO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>additional_info IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByAdditionalInfo(AdditionalInfoDTO... values) {
        return fetch(Applicant.APPLICANT.ADDITIONAL_INFO, values);
    }

    /**
     * Fetch records that have <code>languages BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfLanguages(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(Applicant.APPLICANT.LANGUAGES, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>languages IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByLanguages(JSONB... values) {
        return fetch(Applicant.APPLICANT.LANGUAGES, values);
    }

    /**
     * Fetch records that have <code>status BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfStatus(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.STATUS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>status IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByStatus(UUID... values) {
        return fetch(Applicant.APPLICANT.STATUS, values);
    }

    /**
     * Fetch records that have <code>priority_level BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfPriorityLevel(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.PRIORITY_LEVEL, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>priority_level IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByPriorityLevel(UUID... values) {
        return fetch(Applicant.APPLICANT.PRIORITY_LEVEL, values);
    }

    /**
     * Fetch records that have <code>assigned_to BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfAssignedTo(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ASSIGNED_TO, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>assigned_to IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByAssignedTo(UUID... values) {
        return fetch(Applicant.APPLICANT.ASSIGNED_TO, values);
    }

    /**
     * Fetch records that have <code>additional_comments BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfAdditionalComments(String lowerInclusive, String upperInclusive) {
        return fetchRange(Applicant.APPLICANT.ADDITIONAL_COMMENTS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>additional_comments IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByAdditionalComments(String... values) {
        return fetch(Applicant.APPLICANT.ADDITIONAL_COMMENTS, values);
    }

    /**
     * Fetch records that have <code>gender BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfGender(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.GENDER, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>gender IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByGender(UUID... values) {
        return fetch(Applicant.APPLICANT.GENDER, values);
    }

    /**
     * Fetch records that have <code>application_date BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfApplicationDate(LocalDate lowerInclusive, LocalDate upperInclusive) {
        return fetchRange(Applicant.APPLICANT.APPLICATION_DATE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>application_date IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByApplicationDate(LocalDate... values) {
        return fetch(Applicant.APPLICANT.APPLICATION_DATE, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Applicant.APPLICANT.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByIsActive(Boolean... values) {
        return fetch(Applicant.APPLICANT.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByCreatedBy(UUID... values) {
        return fetch(Applicant.APPLICANT.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Applicant.APPLICANT.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByUpdatedBy(UUID... values) {
        return fetch(Applicant.APPLICANT.UPDATED_BY, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Applicant.APPLICANT.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Applicant.APPLICANT.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Applicant.APPLICANT.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Applicant.APPLICANT.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>documents BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchRangeOfDocuments(List<DocumentDTO> lowerInclusive, List<DocumentDTO> upperInclusive) {
        return fetchRange(Applicant.APPLICANT.DOCUMENTS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>documents IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Applicant> fetchByDocuments(List<DocumentDTO>... values) {
        return fetch(Applicant.APPLICANT.DOCUMENTS, values);
    }
}
