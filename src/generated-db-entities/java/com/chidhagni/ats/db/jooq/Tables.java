/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq;


import com.chidhagni.ats.db.jooq.tables.Applicant;
import com.chidhagni.ats.db.jooq.tables.DocumentRepo;
import com.chidhagni.ats.db.jooq.tables.ListNames;
import com.chidhagni.ats.db.jooq.tables.ListValues;
import com.chidhagni.ats.db.jooq.tables.Resource;


/**
 * Convenience access to all tables in the default schema.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Tables {

    /**
     * The table <code>applicant</code>.
     */
    public static final Applicant APPLICANT = Applicant.APPLICANT;

    /**
     * The table <code>document_repo</code>.
     */
    public static final DocumentRepo DOCUMENT_REPO = DocumentRepo.DOCUMENT_REPO;

    /**
     * The table <code>list_names</code>.
     */
    public static final ListNames LIST_NAMES = ListNames.LIST_NAMES;

    /**
     * The table <code>list_values</code>.
     */
    public static final ListValues LIST_VALUES = ListValues.LIST_VALUES;

    /**
     * The table <code>resource</code>.
     */
    public static final Resource RESOURCE = Resource.RESOURCE;
}
