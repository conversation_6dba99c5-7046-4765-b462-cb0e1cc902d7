/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq;


import com.chidhagni.ats.db.jooq.tables.Applicant;
import com.chidhagni.ats.db.jooq.tables.DocumentRepo;
import com.chidhagni.ats.db.jooq.tables.ListNames;
import com.chidhagni.ats.db.jooq.tables.ListValues;
import com.chidhagni.ats.db.jooq.tables.Resource;

import java.util.Arrays;
import java.util.List;

import org.jooq.Catalog;
import org.jooq.Table;
import org.jooq.impl.SchemaImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DefaultSchema extends SchemaImpl {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>DEFAULT_SCHEMA</code>
     */
    public static final DefaultSchema DEFAULT_SCHEMA = new DefaultSchema();

    /**
     * The table <code>applicant</code>.
     */
    public final Applicant APPLICANT = Applicant.APPLICANT;

    /**
     * The table <code>document_repo</code>.
     */
    public final DocumentRepo DOCUMENT_REPO = DocumentRepo.DOCUMENT_REPO;

    /**
     * The table <code>list_names</code>.
     */
    public final ListNames LIST_NAMES = ListNames.LIST_NAMES;

    /**
     * The table <code>list_values</code>.
     */
    public final ListValues LIST_VALUES = ListValues.LIST_VALUES;

    /**
     * The table <code>resource</code>.
     */
    public final Resource RESOURCE = Resource.RESOURCE;

    /**
     * No further instances allowed
     */
    private DefaultSchema() {
        super("", null);
    }


    @Override
    public Catalog getCatalog() {
        return DefaultCatalog.DEFAULT_CATALOG;
    }

    @Override
    public final List<Table<?>> getTables() {
        return Arrays.asList(
            Applicant.APPLICANT,
            DocumentRepo.DOCUMENT_REPO,
            ListNames.LIST_NAMES,
            ListValues.LIST_VALUES,
            Resource.RESOURCE
        );
    }
}
