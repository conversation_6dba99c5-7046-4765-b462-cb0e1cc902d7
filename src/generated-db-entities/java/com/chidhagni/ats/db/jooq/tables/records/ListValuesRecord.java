/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.records;


import com.chidhagni.ats.db.jooq.tables.ListValues;

import java.time.LocalDateTime;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record8;
import org.jooq.Row8;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ListValuesRecord extends UpdatableRecordImpl<ListValuesRecord> implements Record8<UUID, String, UUID, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>list_values.id</code>.
     */
    public ListValuesRecord setId(UUID value) {
        set(0, value);
        return this;
    }

    /**
     * Getter for <code>list_values.id</code>.
     */
    public UUID getId() {
        return (UUID) get(0);
    }

    /**
     * Setter for <code>list_values.name</code>.
     */
    public ListValuesRecord setName(String value) {
        set(1, value);
        return this;
    }

    /**
     * Getter for <code>list_values.name</code>.
     */
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>list_values.list_names_id</code>.
     */
    public ListValuesRecord setListNamesId(UUID value) {
        set(2, value);
        return this;
    }

    /**
     * Getter for <code>list_values.list_names_id</code>.
     */
    public UUID getListNamesId() {
        return (UUID) get(2);
    }

    /**
     * Setter for <code>list_values.is_active</code>.
     */
    public ListValuesRecord setIsActive(Boolean value) {
        set(3, value);
        return this;
    }

    /**
     * Getter for <code>list_values.is_active</code>.
     */
    public Boolean getIsActive() {
        return (Boolean) get(3);
    }

    /**
     * Setter for <code>list_values.created_by</code>.
     */
    public ListValuesRecord setCreatedBy(UUID value) {
        set(4, value);
        return this;
    }

    /**
     * Getter for <code>list_values.created_by</code>.
     */
    public UUID getCreatedBy() {
        return (UUID) get(4);
    }

    /**
     * Setter for <code>list_values.updated_by</code>.
     */
    public ListValuesRecord setUpdatedBy(UUID value) {
        set(5, value);
        return this;
    }

    /**
     * Getter for <code>list_values.updated_by</code>.
     */
    public UUID getUpdatedBy() {
        return (UUID) get(5);
    }

    /**
     * Setter for <code>list_values.created_on</code>.
     */
    public ListValuesRecord setCreatedOn(LocalDateTime value) {
        set(6, value);
        return this;
    }

    /**
     * Getter for <code>list_values.created_on</code>.
     */
    public LocalDateTime getCreatedOn() {
        return (LocalDateTime) get(6);
    }

    /**
     * Setter for <code>list_values.updated_on</code>.
     */
    public ListValuesRecord setUpdatedOn(LocalDateTime value) {
        set(7, value);
        return this;
    }

    /**
     * Getter for <code>list_values.updated_on</code>.
     */
    public LocalDateTime getUpdatedOn() {
        return (LocalDateTime) get(7);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<UUID> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record8 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row8<UUID, String, UUID, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> fieldsRow() {
        return (Row8) super.fieldsRow();
    }

    @Override
    public Row8<UUID, String, UUID, Boolean, UUID, UUID, LocalDateTime, LocalDateTime> valuesRow() {
        return (Row8) super.valuesRow();
    }

    @Override
    public Field<UUID> field1() {
        return ListValues.LIST_VALUES.ID;
    }

    @Override
    public Field<String> field2() {
        return ListValues.LIST_VALUES.NAME;
    }

    @Override
    public Field<UUID> field3() {
        return ListValues.LIST_VALUES.LIST_NAMES_ID;
    }

    @Override
    public Field<Boolean> field4() {
        return ListValues.LIST_VALUES.IS_ACTIVE;
    }

    @Override
    public Field<UUID> field5() {
        return ListValues.LIST_VALUES.CREATED_BY;
    }

    @Override
    public Field<UUID> field6() {
        return ListValues.LIST_VALUES.UPDATED_BY;
    }

    @Override
    public Field<LocalDateTime> field7() {
        return ListValues.LIST_VALUES.CREATED_ON;
    }

    @Override
    public Field<LocalDateTime> field8() {
        return ListValues.LIST_VALUES.UPDATED_ON;
    }

    @Override
    public UUID component1() {
        return getId();
    }

    @Override
    public String component2() {
        return getName();
    }

    @Override
    public UUID component3() {
        return getListNamesId();
    }

    @Override
    public Boolean component4() {
        return getIsActive();
    }

    @Override
    public UUID component5() {
        return getCreatedBy();
    }

    @Override
    public UUID component6() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime component7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime component8() {
        return getUpdatedOn();
    }

    @Override
    public UUID value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getName();
    }

    @Override
    public UUID value3() {
        return getListNamesId();
    }

    @Override
    public Boolean value4() {
        return getIsActive();
    }

    @Override
    public UUID value5() {
        return getCreatedBy();
    }

    @Override
    public UUID value6() {
        return getUpdatedBy();
    }

    @Override
    public LocalDateTime value7() {
        return getCreatedOn();
    }

    @Override
    public LocalDateTime value8() {
        return getUpdatedOn();
    }

    @Override
    public ListValuesRecord value1(UUID value) {
        setId(value);
        return this;
    }

    @Override
    public ListValuesRecord value2(String value) {
        setName(value);
        return this;
    }

    @Override
    public ListValuesRecord value3(UUID value) {
        setListNamesId(value);
        return this;
    }

    @Override
    public ListValuesRecord value4(Boolean value) {
        setIsActive(value);
        return this;
    }

    @Override
    public ListValuesRecord value5(UUID value) {
        setCreatedBy(value);
        return this;
    }

    @Override
    public ListValuesRecord value6(UUID value) {
        setUpdatedBy(value);
        return this;
    }

    @Override
    public ListValuesRecord value7(LocalDateTime value) {
        setCreatedOn(value);
        return this;
    }

    @Override
    public ListValuesRecord value8(LocalDateTime value) {
        setUpdatedOn(value);
        return this;
    }

    @Override
    public ListValuesRecord values(UUID value1, String value2, UUID value3, Boolean value4, UUID value5, UUID value6, LocalDateTime value7, LocalDateTime value8) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        return this;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ListValuesRecord
     */
    public ListValuesRecord() {
        super(ListValues.LIST_VALUES);
    }

    /**
     * Create a detached, initialised ListValuesRecord
     */
    public ListValuesRecord(UUID id, String name, UUID listNamesId, Boolean isActive, UUID createdBy, UUID updatedBy, LocalDateTime createdOn, LocalDateTime updatedOn) {
        super(ListValues.LIST_VALUES);

        setId(id);
        setName(name);
        setListNamesId(listNamesId);
        setIsActive(isActive);
        setCreatedBy(createdBy);
        setUpdatedBy(updatedBy);
        setCreatedOn(createdOn);
        setUpdatedOn(updatedOn);
        resetChangedOnNotNull();
    }

    /**
     * Create a detached, initialised ListValuesRecord
     */
    public ListValuesRecord(com.chidhagni.ats.db.jooq.tables.pojos.ListValues value) {
        super(ListValues.LIST_VALUES);

        if (value != null) {
            setId(value.getId());
            setName(value.getName());
            setListNamesId(value.getListNamesId());
            setIsActive(value.getIsActive());
            setCreatedBy(value.getCreatedBy());
            setUpdatedBy(value.getUpdatedBy());
            setCreatedOn(value.getCreatedOn());
            setUpdatedOn(value.getUpdatedOn());
            resetChangedOnNotNull();
        }
    }
}
