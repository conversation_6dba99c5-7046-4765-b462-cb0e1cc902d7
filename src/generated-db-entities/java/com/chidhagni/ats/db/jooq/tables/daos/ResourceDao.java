/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables.daos;


import com.chidhagni.ats.db.jooq.tables.Resource;
import com.chidhagni.ats.db.jooq.tables.records.ResourceRecord;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.jooq.Configuration;
import org.jooq.JSONB;
import org.jooq.impl.DAOImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
@Repository
public class ResourceDao extends DAOImpl<ResourceRecord, com.chidhagni.ats.db.jooq.tables.pojos.Resource, UUID> {

    /**
     * Create a new ResourceDao without any configuration
     */
    public ResourceDao() {
        super(Resource.RESOURCE, com.chidhagni.ats.db.jooq.tables.pojos.Resource.class);
    }

    /**
     * Create a new ResourceDao with an attached configuration
     */
    @Autowired
    public ResourceDao(Configuration configuration) {
        super(Resource.RESOURCE, com.chidhagni.ats.db.jooq.tables.pojos.Resource.class, configuration);
    }

    @Override
    public UUID getId(com.chidhagni.ats.db.jooq.tables.pojos.Resource object) {
        return object.getId();
    }

    /**
     * Fetch records that have <code>id BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Resource.RESOURCE.ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>id IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchById(UUID... values) {
        return fetch(Resource.RESOURCE.ID, values);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public com.chidhagni.ats.db.jooq.tables.pojos.Resource fetchOneById(UUID value) {
        return fetchOne(Resource.RESOURCE.ID, value);
    }

    /**
     * Fetch a unique record that has <code>id = value</code>
     */
    public Optional<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchOptionalById(UUID value) {
        return fetchOptional(Resource.RESOURCE.ID, value);
    }

    /**
     * Fetch records that have <code>name BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfName(String lowerInclusive, String upperInclusive) {
        return fetchRange(Resource.RESOURCE.NAME, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>name IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByName(String... values) {
        return fetch(Resource.RESOURCE.NAME, values);
    }

    /**
     * Fetch records that have <code>description BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfDescription(String lowerInclusive, String upperInclusive) {
        return fetchRange(Resource.RESOURCE.DESCRIPTION, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>description IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByDescription(String... values) {
        return fetch(Resource.RESOURCE.DESCRIPTION, values);
    }

    /**
     * Fetch records that have <code>type BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfType(String lowerInclusive, String upperInclusive) {
        return fetchRange(Resource.RESOURCE.TYPE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>type IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByType(String... values) {
        return fetch(Resource.RESOURCE.TYPE, values);
    }

    /**
     * Fetch records that have <code>parent_resource_id BETWEEN lowerInclusive
     * AND upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfParentResourceId(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Resource.RESOURCE.PARENT_RESOURCE_ID, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>parent_resource_id IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByParentResourceId(UUID... values) {
        return fetch(Resource.RESOURCE.PARENT_RESOURCE_ID, values);
    }

    /**
     * Fetch records that have <code>validations BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfValidations(JSONB lowerInclusive, JSONB upperInclusive) {
        return fetchRange(Resource.RESOURCE.VALIDATIONS, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>validations IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByValidations(JSONB... values) {
        return fetch(Resource.RESOURCE.VALIDATIONS, values);
    }

    /**
     * Fetch records that have <code>is_active BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfIsActive(Boolean lowerInclusive, Boolean upperInclusive) {
        return fetchRange(Resource.RESOURCE.IS_ACTIVE, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>is_active IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByIsActive(Boolean... values) {
        return fetch(Resource.RESOURCE.IS_ACTIVE, values);
    }

    /**
     * Fetch records that have <code>created_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfCreatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Resource.RESOURCE.CREATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByCreatedOn(LocalDateTime... values) {
        return fetch(Resource.RESOURCE.CREATED_ON, values);
    }

    /**
     * Fetch records that have <code>updated_on BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfUpdatedOn(LocalDateTime lowerInclusive, LocalDateTime upperInclusive) {
        return fetchRange(Resource.RESOURCE.UPDATED_ON, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_on IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByUpdatedOn(LocalDateTime... values) {
        return fetch(Resource.RESOURCE.UPDATED_ON, values);
    }

    /**
     * Fetch records that have <code>created_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfCreatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Resource.RESOURCE.CREATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>created_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByCreatedBy(UUID... values) {
        return fetch(Resource.RESOURCE.CREATED_BY, values);
    }

    /**
     * Fetch records that have <code>updated_by BETWEEN lowerInclusive AND
     * upperInclusive</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchRangeOfUpdatedBy(UUID lowerInclusive, UUID upperInclusive) {
        return fetchRange(Resource.RESOURCE.UPDATED_BY, lowerInclusive, upperInclusive);
    }

    /**
     * Fetch records that have <code>updated_by IN (values)</code>
     */
    public List<com.chidhagni.ats.db.jooq.tables.pojos.Resource> fetchByUpdatedBy(UUID... values) {
        return fetch(Resource.RESOURCE.UPDATED_BY, values);
    }
}
