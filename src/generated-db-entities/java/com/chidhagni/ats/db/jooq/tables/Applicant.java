/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables;


import com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO;
import com.chidhagni.ats.applicant.dto.request.AddressesDTO;
import com.chidhagni.ats.applicant.dto.request.CertificationsDTO;
import com.chidhagni.ats.applicant.dto.request.ContactInfoDTO;
import com.chidhagni.ats.applicant.dto.request.DocumentDTO;
import com.chidhagni.ats.applicant.dto.request.EducationDTO;
import com.chidhagni.ats.applicant.dto.request.EmployerDetailsDTO;
import com.chidhagni.ats.applicant.dto.request.SocialProfilesDTO;
import com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO;
import com.chidhagni.ats.applicant.jooq.AdditionalInfoJsonConverter;
import com.chidhagni.ats.applicant.jooq.AddressesJsonConverter;
import com.chidhagni.ats.applicant.jooq.CertificationsJsonConverter;
import com.chidhagni.ats.applicant.jooq.ContactInfoJsonConverter;
import com.chidhagni.ats.applicant.jooq.DocumentJsonConverter;
import com.chidhagni.ats.applicant.jooq.EducationJsonConverter;
import com.chidhagni.ats.applicant.jooq.EmployerDetailsJsonConverter;
import com.chidhagni.ats.applicant.jooq.SocialProfilesJsonConverter;
import com.chidhagni.ats.applicant.jooq.WorkExperienceJsonConverter;
import com.chidhagni.ats.db.jooq.DefaultSchema;
import com.chidhagni.ats.db.jooq.Keys;
import com.chidhagni.ats.db.jooq.tables.records.ApplicantRecord;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Applicant extends TableImpl<ApplicantRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>applicant</code>
     */
    public static final Applicant APPLICANT = new Applicant();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ApplicantRecord> getRecordType() {
        return ApplicantRecord.class;
    }

    /**
     * The column <code>applicant.id</code>.
     */
    public final TableField<ApplicantRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>applicant.org_id</code>.
     */
    public final TableField<ApplicantRecord, UUID> ORG_ID = createField(DSL.name("org_id"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.system_code</code>.
     */
    public final TableField<ApplicantRecord, String> SYSTEM_CODE = createField(DSL.name("system_code"), SQLDataType.VARCHAR(50).nullable(false), this, "");

    /**
     * The column <code>applicant.first_name</code>.
     */
    public final TableField<ApplicantRecord, String> FIRST_NAME = createField(DSL.name("first_name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>applicant.middle_name</code>.
     */
    public final TableField<ApplicantRecord, String> MIDDLE_NAME = createField(DSL.name("middle_name"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>applicant.last_name</code>.
     */
    public final TableField<ApplicantRecord, String> LAST_NAME = createField(DSL.name("last_name"), SQLDataType.VARCHAR(100).nullable(false), this, "");

    /**
     * The column <code>applicant.preferred_name</code>.
     */
    public final TableField<ApplicantRecord, String> PREFERRED_NAME = createField(DSL.name("preferred_name"), SQLDataType.VARCHAR(100), this, "");

    /**
     * The column <code>applicant.email</code>.
     */
    public final TableField<ApplicantRecord, String> EMAIL = createField(DSL.name("email"), SQLDataType.VARCHAR(255).nullable(false), this, "");

    /**
     * The column <code>applicant.date_of_birth</code>.
     */
    public final TableField<ApplicantRecord, LocalDate> DATE_OF_BIRTH = createField(DSL.name("date_of_birth"), SQLDataType.LOCALDATE, this, "");

    /**
     * The column <code>applicant.contact_info</code>.
     */
    public final TableField<ApplicantRecord, ContactInfoDTO> CONTACT_INFO = createField(DSL.name("contact_info"), SQLDataType.JSONB, this, "", new ContactInfoJsonConverter());

    /**
     * The column <code>applicant.social_profiles</code>.
     */
    public final TableField<ApplicantRecord, SocialProfilesDTO> SOCIAL_PROFILES = createField(DSL.name("social_profiles"), SQLDataType.JSONB, this, "", new SocialProfilesJsonConverter());

    /**
     * The column <code>applicant.addresses</code>.
     */
    public final TableField<ApplicantRecord, AddressesDTO> ADDRESSES = createField(DSL.name("addresses"), SQLDataType.JSONB, this, "", new AddressesJsonConverter());

    /**
     * The column <code>applicant.employer_details</code>.
     */
    public final TableField<ApplicantRecord, EmployerDetailsDTO> EMPLOYER_DETAILS = createField(DSL.name("employer_details"), SQLDataType.JSONB, this, "", new EmployerDetailsJsonConverter());

    /**
     * The column <code>applicant.work_experience</code>.
     */
    public final TableField<ApplicantRecord, List<WorkExperienceDTO>> WORK_EXPERIENCE = createField(DSL.name("work_experience"), SQLDataType.JSONB, this, "", new WorkExperienceJsonConverter());

    /**
     * The column <code>applicant.education</code>.
     */
    public final TableField<ApplicantRecord, List<EducationDTO>> EDUCATION = createField(DSL.name("education"), SQLDataType.JSONB, this, "", new EducationJsonConverter());

    /**
     * The column <code>applicant.certifications</code>.
     */
    public final TableField<ApplicantRecord, List<CertificationsDTO>> CERTIFICATIONS = createField(DSL.name("certifications"), SQLDataType.JSONB, this, "", new CertificationsJsonConverter());

    /**
     * The column <code>applicant.additional_info</code>.
     */
    public final TableField<ApplicantRecord, AdditionalInfoDTO> ADDITIONAL_INFO = createField(DSL.name("additional_info"), SQLDataType.JSONB, this, "", new AdditionalInfoJsonConverter());

    /**
     * The column <code>applicant.languages</code>.
     */
    public final TableField<ApplicantRecord, JSONB> LANGUAGES = createField(DSL.name("languages"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>applicant.status</code>.
     */
    public final TableField<ApplicantRecord, UUID> STATUS = createField(DSL.name("status"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.priority_level</code>.
     */
    public final TableField<ApplicantRecord, UUID> PRIORITY_LEVEL = createField(DSL.name("priority_level"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.assigned_to</code>.
     */
    public final TableField<ApplicantRecord, UUID> ASSIGNED_TO = createField(DSL.name("assigned_to"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.additional_comments</code>.
     */
    public final TableField<ApplicantRecord, String> ADDITIONAL_COMMENTS = createField(DSL.name("additional_comments"), SQLDataType.CLOB, this, "");

    /**
     * The column <code>applicant.gender</code>.
     */
    public final TableField<ApplicantRecord, UUID> GENDER = createField(DSL.name("gender"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.application_date</code>.
     */
    public final TableField<ApplicantRecord, LocalDate> APPLICATION_DATE = createField(DSL.name("application_date"), SQLDataType.LOCALDATE, this, "");

    /**
     * The column <code>applicant.is_active</code>.
     */
    public final TableField<ApplicantRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.nullable(false).defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    /**
     * The column <code>applicant.created_by</code>.
     */
    public final TableField<ApplicantRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.updated_by</code>.
     */
    public final TableField<ApplicantRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>applicant.created_on</code>.
     */
    public final TableField<ApplicantRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>applicant.updated_on</code>.
     */
    public final TableField<ApplicantRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>applicant.documents</code>.
     */
    public final TableField<ApplicantRecord, List<DocumentDTO>> DOCUMENTS = createField(DSL.name("documents"), SQLDataType.JSONB, this, "", new DocumentJsonConverter());

    private Applicant(Name alias, Table<ApplicantRecord> aliased) {
        this(alias, aliased, null);
    }

    private Applicant(Name alias, Table<ApplicantRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>applicant</code> table reference
     */
    public Applicant(String alias) {
        this(DSL.name(alias), APPLICANT);
    }

    /**
     * Create an aliased <code>applicant</code> table reference
     */
    public Applicant(Name alias) {
        this(alias, APPLICANT);
    }

    /**
     * Create a <code>applicant</code> table reference
     */
    public Applicant() {
        this(DSL.name("applicant"), null);
    }

    public <O extends Record> Applicant(Table<O> child, ForeignKey<O, ApplicantRecord> key) {
        super(child, key, APPLICANT);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<ApplicantRecord> getPrimaryKey() {
        return Keys.CANDIDATES_PKEY;
    }

    @Override
    public List<UniqueKey<ApplicantRecord>> getUniqueKeys() {
        return Arrays.asList(Keys.CANDIDATES_SYSTEM_CODE_KEY);
    }

    @Override
    public Applicant as(String alias) {
        return new Applicant(DSL.name(alias), this);
    }

    @Override
    public Applicant as(Name alias) {
        return new Applicant(alias, this);
    }

    @Override
    public Applicant as(Table<?> alias) {
        return new Applicant(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public Applicant rename(String name) {
        return new Applicant(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public Applicant rename(Name name) {
        return new Applicant(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public Applicant rename(Table<?> name) {
        return new Applicant(name.getQualifiedName(), null);
    }
}
