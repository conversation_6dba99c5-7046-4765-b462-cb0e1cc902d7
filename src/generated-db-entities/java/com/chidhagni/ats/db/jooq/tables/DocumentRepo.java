/*
 * This file is generated by jOOQ.
 */
package com.chidhagni.ats.db.jooq.tables;


import com.chidhagni.ats.db.jooq.DefaultSchema;
import com.chidhagni.ats.db.jooq.Keys;
import com.chidhagni.ats.db.jooq.tables.records.DocumentRepoRecord;
import com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO;
import com.chidhagni.ats.documentrepo.jooq.DocumentRepoRecipientJsonConverter;
import com.chidhagni.ats.documentrepo.jooq.DocumentRepoSenderJsonConverter;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.function.Function;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Function14;
import org.jooq.JSONB;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Records;
import org.jooq.Row14;
import org.jooq.Schema;
import org.jooq.SelectField;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.TableOptions;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.SQLDataType;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DocumentRepo extends TableImpl<DocumentRepoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>document_repo</code>
     */
    public static final DocumentRepo DOCUMENT_REPO = new DocumentRepo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DocumentRepoRecord> getRecordType() {
        return DocumentRepoRecord.class;
    }

    /**
     * The column <code>document_repo.id</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> ID = createField(DSL.name("id"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>document_repo.category</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> CATEGORY = createField(DSL.name("category"), SQLDataType.UUID.nullable(false), this, "");

    /**
     * The column <code>document_repo.sub_category</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> SUB_CATEGORY = createField(DSL.name("sub_category"), SQLDataType.UUID, this, "");

    /**
     * The column <code>document_repo.sender</code>.
     */
    public final TableField<DocumentRepoRecord, ParticipantDetailsDTO> SENDER = createField(DSL.name("sender"), SQLDataType.JSONB, this, "", new DocumentRepoSenderJsonConverter());

    /**
     * The column <code>document_repo.recipients</code>.
     */
    public final TableField<DocumentRepoRecord, List<ParticipantDetailsDTO>> RECIPIENTS = createField(DSL.name("recipients"), SQLDataType.JSONB, this, "", new DocumentRepoRecipientJsonConverter());

    /**
     * The column <code>document_repo.path</code>.
     */
    public final TableField<DocumentRepoRecord, String> PATH = createField(DSL.name("path"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>document_repo.file_date</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> FILE_DATE = createField(DSL.name("file_date"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>document_repo.created_by</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> CREATED_BY = createField(DSL.name("created_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>document_repo.updated_by</code>.
     */
    public final TableField<DocumentRepoRecord, UUID> UPDATED_BY = createField(DSL.name("updated_by"), SQLDataType.UUID, this, "");

    /**
     * The column <code>document_repo.created_on</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> CREATED_ON = createField(DSL.name("created_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>document_repo.updated_on</code>.
     */
    public final TableField<DocumentRepoRecord, LocalDateTime> UPDATED_ON = createField(DSL.name("updated_on"), SQLDataType.LOCALDATETIME(6), this, "");

    /**
     * The column <code>document_repo.remarks</code>.
     */
    public final TableField<DocumentRepoRecord, String> REMARKS = createField(DSL.name("remarks"), SQLDataType.VARCHAR(255), this, "");

    /**
     * The column <code>document_repo.tags</code>.
     */
    public final TableField<DocumentRepoRecord, JSONB> TAGS = createField(DSL.name("tags"), SQLDataType.JSONB, this, "");

    /**
     * The column <code>document_repo.is_active</code>.
     */
    public final TableField<DocumentRepoRecord, Boolean> IS_ACTIVE = createField(DSL.name("is_active"), SQLDataType.BOOLEAN.defaultValue(DSL.field(DSL.raw("true"), SQLDataType.BOOLEAN)), this, "");

    private DocumentRepo(Name alias, Table<DocumentRepoRecord> aliased) {
        this(alias, aliased, null);
    }

    private DocumentRepo(Name alias, Table<DocumentRepoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, DSL.comment(""), TableOptions.table());
    }

    /**
     * Create an aliased <code>document_repo</code> table reference
     */
    public DocumentRepo(String alias) {
        this(DSL.name(alias), DOCUMENT_REPO);
    }

    /**
     * Create an aliased <code>document_repo</code> table reference
     */
    public DocumentRepo(Name alias) {
        this(alias, DOCUMENT_REPO);
    }

    /**
     * Create a <code>document_repo</code> table reference
     */
    public DocumentRepo() {
        this(DSL.name("document_repo"), null);
    }

    public <O extends Record> DocumentRepo(Table<O> child, ForeignKey<O, DocumentRepoRecord> key) {
        super(child, key, DOCUMENT_REPO);
    }

    @Override
    public Schema getSchema() {
        return aliased() ? null : DefaultSchema.DEFAULT_SCHEMA;
    }

    @Override
    public UniqueKey<DocumentRepoRecord> getPrimaryKey() {
        return Keys.DOCUMENT_REPO_ID_PK;
    }

    @Override
    public DocumentRepo as(String alias) {
        return new DocumentRepo(DSL.name(alias), this);
    }

    @Override
    public DocumentRepo as(Name alias) {
        return new DocumentRepo(alias, this);
    }

    @Override
    public DocumentRepo as(Table<?> alias) {
        return new DocumentRepo(alias.getQualifiedName(), this);
    }

    /**
     * Rename this table
     */
    @Override
    public DocumentRepo rename(String name) {
        return new DocumentRepo(DSL.name(name), null);
    }

    /**
     * Rename this table
     */
    @Override
    public DocumentRepo rename(Name name) {
        return new DocumentRepo(name, null);
    }

    /**
     * Rename this table
     */
    @Override
    public DocumentRepo rename(Table<?> name) {
        return new DocumentRepo(name.getQualifiedName(), null);
    }

    // -------------------------------------------------------------------------
    // Row14 type methods
    // -------------------------------------------------------------------------

    @Override
    public Row14<UUID, UUID, UUID, ParticipantDetailsDTO, List<ParticipantDetailsDTO>, String, LocalDateTime, UUID, UUID, LocalDateTime, LocalDateTime, String, JSONB, Boolean> fieldsRow() {
        return (Row14) super.fieldsRow();
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Function)}.
     */
    public <U> SelectField<U> mapping(Function14<? super UUID, ? super UUID, ? super UUID, ? super ParticipantDetailsDTO, ? super List<ParticipantDetailsDTO>, ? super String, ? super LocalDateTime, ? super UUID, ? super UUID, ? super LocalDateTime, ? super LocalDateTime, ? super String, ? super JSONB, ? super Boolean, ? extends U> from) {
        return convertFrom(Records.mapping(from));
    }

    /**
     * Convenience mapping calling {@link SelectField#convertFrom(Class,
     * Function)}.
     */
    public <U> SelectField<U> mapping(Class<U> toType, Function14<? super UUID, ? super UUID, ? super UUID, ? super ParticipantDetailsDTO, ? super List<ParticipantDetailsDTO>, ? super String, ? super LocalDateTime, ? super UUID, ? super UUID, ? super LocalDateTime, ? super LocalDateTime, ? super String, ? super JSONB, ? super Boolean, ? extends U> from) {
        return convertFrom(toType, Records.mapping(from));
    }
}
