// API Types generated from OpenAPI spec for Applicants

// Common utility types
export interface Page<T> {
  items: T[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

// List Values API Types
export interface ListValuesDTO {
  id: string; // UUID
  listValue: string;
  isActive: boolean;
}

export interface GetAllListValuesDTO {
  listValues: ListValuesDTO[];
}

// New Global List Values API Types (for /list-value endpoint)
export interface GlobalListValueDTO {
  id: string; // UUID
  name: string;
}

export type GetAllGlobalListValuesDTO = GlobalListValueDTO[];

// Custom SelectOption interface for our API response transformation
export interface ApiSelectOption {
  value: string; // id from ListValuesDTO
  key: string;   // name from ListValuesDTO (changed from listValue)
}

// Contact Information
export interface ContactInfo {
  id?: string; // UUID
  homePhone?: string;
  mobilePhone?: string;
  workPhone?: string;
  otherPhone?: string;
  alternateEmail?: string;
}

// Social Profiles
export interface SocialProfiles {
  id?: string; // UUID
  linkedin?: string;
  facebook?: string;
  twitter?: string;
  skypeId?: string;
  videoReference?: string;
}

// Address
export interface Address {
  id?: string; // UUID
  country: string; // UUID
  state: string; // UUID
  city: string;
  zipcode: string;
  isPrimary: boolean;
}

// Employer Information
export interface EmployerDetails {
  id?: string; // UUID
  selectionType: string;
  vendorContact?: string; // UUID
  firstName: string;
  lastName: string;
  employerName: string;
  officeNumber?: string;
  emailId: string;
  mobileNumber?: string;
  status: boolean;
}

// Work Experience
export interface WorkExperience {
  id?: string; // UUID
  companyName: string;
  jobTitle: string;
  startDate: string; // ISO date
  endDate?: string; // ISO date
  location: string;
  employeeType: string; // UUID
  responsibilities?: string;
  isActive?: boolean; // Whether the work experience record is active or soft-deleted
}

// Education
export interface Education {
  id?: string; // UUID
  schoolName: string;
  degree: string; // UUID
  yearCompleted: string;
  minorStudy?: string;
  majorStudy?: string;
  gpa?: string;
  country: string; // UUID
  state: string; // UUID
  city: string;
  isHigherEducation: boolean;
  isActive?: boolean; // Whether the education record is active or soft-deleted
}

// Certification
export interface Certification {
  id?: string; // UUID
  certification: string;
  yearCompleted: string;
  comments?: string;
  isActive?: boolean; // Whether the certification record is active or soft-deleted
}

// Additional Information
export interface AdditionalInfo {
  id?: string; // UUID
  experience?: {
    id?: string; // UUID
    years: string; // UUID
    months: string; // UUID
  };
  jobTitle?: string;
  expectedPay?: {
    id?: string; // UUID
    currency: string; // UUID
    amount: string; // UUID
  };
  currentCTC?: {
    id?: string; // UUID
    currency: string; // UUID
    amount: string; // UUID
  };
  skills?: string[]; // Array of UUIDs
  primarySkills?: string[]; // Array of UUIDs
  technology?: string; // UUID
  industry?: string[]; // Array of UUIDs - multi-select
  function?: string[]; // Array of UUIDs - multi-select
  taxTerms?: string; // UUID
  noticePeriod?: string; // UUID
  currentCompany?: string;
  applicantStatus?: string; // UUID
  ownership?: string; // UUID
  relocation?: boolean;
  additionalComments?: string;
  panCardNumber?: string;
  aadhaarNumber?: string;
  gpa?: string;
  gender?: string; // UUID
  raceEthnicity?: string; // UUID
  veteranStatus?: string; // UUID
  veteranType?: string; // UUID
  disability?: string; // UUID
  workAuthorization?: string; // UUID
  clearance?: boolean;
  source?: string; // UUID
  referredBy?: string;
  ssn?: string; // Moved from root level to additionalInfo
}

// Document
export interface Document {
  id?: string;
  fileIndex?: number; // Simple sequential index for file reference
  title: string;
  category: string; // UUID
  subcategory: string; // UUID
  comments?: string;
  filePath?: string; // Path to the uploaded file from API
  isActive?: boolean; // Whether the document is active or soft-deleted
}

// Document with file reference for uploads
export interface DocumentWithFile {
  id: string; // Random UUID for document identification
  fileIndex: number; // System generated UUID for file reference
  title: string;
  comments?: string;
  category: string; // UUID
  subcategory?: string; // Document type UUID
}

// File upload structure
export interface FileUpload {
  fileIndex: number; // System generated UUID
  file: File;
  title: string;
  comments?: string;
  type: string; // Document type (will be mapped to subcategory)
}

// Document operations for PATCH requests
export type DocumentToAdd = Omit<Document, 'id' | 'filePath'>;

export interface DocumentToUpdate extends Document {
  id: string; // Required for updates
}

// Main Applicant interface (for API requests/responses)
export interface Applicant {
  id?: string; // UUID - optional for create requests
  orgId?: string; // UUID
  systemCode?: string; // Generated by system
  firstName: string;
  middleName?: string;
  lastName: string;
  preferredName?: string;
  email: string;
  dateOfBirth?: string; // ISO date
  contactInfo?: ContactInfo;
  socialProfiles?: SocialProfiles;
  addresses?: Address | Address[];
  employerDetails?: EmployerDetails | EmployerDetails[]; // Support both single object and array for compatibility
  workExperience?: WorkExperience[];
  education?: Education[];
  certifications?: Certification[];
  additionalInfo?: AdditionalInfo;
  documents?: Document[];
  status?: string; // UUID
  priorityLevel?: string; // UUID
  assignedTo?: string; // UUID
  additionalComments?: string;
  applicationDate?: string; // ISO date
  isActive?: boolean;
  createdBy?: string;
  updatedBy?: string;
  createdOn?: string; // ISO datetime
  updatedOn?: string; // ISO datetime
}

// Response DTOs
export interface ApplicantResponseDTO {
  id: string; // UUID
  systemCode: string;
  firstName: string;
  lastName: string;
  preferredName?: string;
  email: string;
  status: string; // UUID
  priorityLevel: string; // UUID
  assignedTo?: string; // UUID
  applicationDate: string; // ISO date
  createdOn: string; // ISO datetime
  updatedOn: string; // ISO datetime
  summary?: {
    currentTitle?: string;
    experienceYears?: number;
    primarySkills?: string[];
    currentCompany?: string;
  };
  isActive: boolean;
}

export interface ApplicantDetailResponse extends Applicant {
  id: string; // Required in responses
  systemCode: string; // Required in responses
}

// Request types
export type CreateApplicantRequest = Applicant;

export interface CreateApplicantWithFilesRequest {
  applicantData: Omit<Applicant, 'documents'> & {
    documents?: DocumentWithFile[];
  };
  files?: FileUpload[];
}

// API Response types - Updated to match backend GetAllApplicantsResponseDTO
export interface GetAllApplicantsResponse {
  applicants: ApplicantResponseDTO[];
  totalCount: number; // Updated from totalElements to match backend
  totalPages: number;
  currentPage: number;
  pageSize: number;
}

export interface CreateApplicantResponse {
  success: boolean;
  message: string;
  id: string; // UUID
}

export interface PatchApplicantResponse {
  success: boolean;
  message: string;
  id: string; // UUID
  data?: {
    updatedFields?: string[];
    updatedAt?: string;
    documentsAdded?: Array<{
      documentId: string;
      title: string;
      fileName: string;
      uploadStatus: string;
    }>;
    documentsUpdated?: Array<{
      documentId: string;
      title: string;
      updateStatus: string;
    }>;
    documentsDeleted?: string[];
  };
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface DeleteApplicantResponse {
  success: boolean;
  message: string;
}

export interface ActivateApplicantResponse {
  success: boolean;
  message: string;
}

// Request DTO for GetAllApplicants POST endpoint
export interface GetAllApplicantsRequestDTO {
  // Pagination
  page?: number;
  pageSize?: number;
  
  // Sorting (sortDirection as string to match backend)
  sortDirection?: string; // "ASC" or "DESC"
  
  // Filters
  status?: string; // status filter
  search?: string; // global search keyword
}

// Legacy interface for backward compatibility
export interface ApplicantListParams {
  // Pagination
  page?: number;
  pageSize?: number;
  
  // Sorting
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  
  // Name filters
  firstNameFilter?: string;
  lastNameFilter?: string;
  
  // Contact filters
  emailFilter?: string;
  
  // Status and organization filters
  statusFilter?: string;
  orgIdFilter?: string;
  
  // Skills and experience filters
  skills?: string; // comma-separated
  experienceMin?: number;
  experienceMax?: number;
  
  // Global search
  searchKeyWord?: string;
  search?: string; // legacy field, keep for backward compatibility
}

// File upload types
export interface ApplicantFormData {
  applicantData: string; // JSON string of applicant data
  files?: File[]; // Optional file uploads
}

export interface PatchApplicantFormData {
  patchData: string; // JSON string of patch data
  files?: File[]; // Optional file uploads
} 

// Document Tree API Response Types
export interface DocumentTreeResource {
  id: string;
  name: string;
  description: string;
  type: 'CATEGORY' | 'SUB_CATEGORY';
  parentResourceId: string;
  isActive: boolean;
  createdOn: string;
  updatedOn: string;
  children: DocumentTreeResource[];
  childCount: number;
}

export type DocumentTreeResponse = DocumentTreeResource;

// Document tree select option (for dropdown)
export interface DocumentTreeSelectOption {
  value: string; // id
  key: string; // name
} 