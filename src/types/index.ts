// Type definitions for form fields
export type CourtesyTitle = "Mr" | "Mrs" | "Ms" | "Dr" | "Prof";
export type ApplicantGroup = "Internal" | "External" | "Referral" | "Agency" | "Campus Hire";
export type Technology =
  | "Java"
  | "Python"
  | "C / C++"
  | ".NET / C#"
  | "JavaScript"
  | "TypeScript"
  | "ReactJS"
  | "Angular"
  | "Vue.js"
  | "Node.js"
  | "PHP"
  | "Ruby / Ruby on Rails"
  | "Go (Golang)"
  | "Swift (iOS)"
  | "Kotlin (Android)"
  | "SQL"
  | "PL/SQL"
  | "Oracle"
  | "MySQL"
  | "PostgreSQL"
  | "MongoDB"
  | "AWS"
  | "Azure"
  | "Google Cloud (GCP)"
  | "DevOps"
  | "Docker"
  | "Kubernetes"
  | "Salesforce"
  | "SAP"
  | "ServiceNow"
  | "Workday"
  | "Mainframe"
  | "Data Engineering"
  | "Machine Learning"
  | "Artificial Intelligence"
  | "Hadoop / Big Data"
  | "ETL / Informatica"
  | "QA / Testing (Manual & Automation)"
  | "Selenium"
  | "Cybersecurity";

// Enhanced interfaces for comprehensive candidate management
export interface PersonalInfo {
  // Basic Personal Information
  courtesyTitle?: CourtesyTitle;
  firstName: string;
  lastName: string;
  middleName?: string;
  nickName?: string;
  email: string;
  alternateEmail?: string;

  // Phone Numbers
  homePhone?: string;
  mobilePhone?: string;
  workPhone?: string;
  otherPhone?: string;

  // Personal Details
  dateOfBirth?: string;
  ssn?: string;

  // Social Media & Communication
  skypeId?: string;
  linkedinProfileUrl?: string;
  facebookProfileUrl?: string;
  twitterProfileUrl?: string;
  videoReference?: string;

  // Work Authorization & Clearance
  workAuthorization?: string;
  workAuthorizationExpiry?: string;
  clearance?: 'Yes' | 'No';

  // Address Information
  address?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  country?: string;
  state?: string;
  zipCode?: string;
}

export interface ProfessionalInfo {
  // Sourcing Information
  source?: string;
  referredBy?: string;

  // Experience
  experienceYears?: string;
  experienceMonths?: string;
  jobTitle?: string;

  // Compensation
  expectedPayCurrency?: string;
  expectedPayAmount?: string;
  currentCtcCurrency?: string;
  currentCtcAmount?: string;

  // Skills & Technology
  skills?: string[];
  primarySkills?: string[];
  technology?: Technology;

  // Categories
  industry?: string[];
  function?: string[];

  // Employment Terms
  taxTerms?: string;
  noticePeriod?: string;
  currentCompany?: string;

  // Management
  applicantStatus?: string;
  applicantGroup?: ApplicantGroup;
  ownership?: string;

  // Preferences
  relocation?: 'Yes' | 'No';
  additionalComments?: string;

  // Documents
  panCardNumber?: string;
  aadharNumber?: string;
  gpa?: string;

  // EEO Demographics
  gender?: string;
  raceEthnicity?: string;

  // Veteran Information
  veteranStatus?: string;
  veteranType?: string;

  // Accessibility
  disability?: string;
}

// Legacy interface for backward compatibility
export interface PersonalDetails {
  // Basic Personal Information
  firstName: string;
  lastName: string;
  middleName?: string;
  nickName?: string;
  email: string;
  alternateEmail?: string;

  // Phone Numbers
  homePhone?: string;
  mobilePhone?: string;
  workPhone?: string;
  otherPhone?: string;

  // Personal Details
  dateOfBirth?: string;
  ssn?: string;

  // Social Media & Communication
  skypeId?: string;
  linkedinProfileUrl?: string;
  facebookProfileUrl?: string;
  twitterProfileUrl?: string;
  videoReference?: string;

  // Work Authorization & Clearance
  workAuthorization?: string;
  clearance?: 'Yes' | 'No';

  // Address Information
  address?: string;
  city?: string;
  country?: string;
  state?: string;
  zipCode?: string;

  // Professional Information
  source?: string;
  experienceYears?: string;
  experienceMonths?: string;
  referredBy?: string;
  applicantStatus?: string;
  applicantGroup?: string[];
  ownership?: string;
  jobTitle?: string;
  expectedPayCurrency?: string;
  expectedPayAmount?: string;
  additionalComments?: string;

  // Skills & Technology
  relocation?: 'Yes' | 'No';
  skills?: string[];
  primarySkills?: string[];
  technology?: string[];

  // Employment Terms
  taxTerms?: string;
  noticePeriod?: string;
  industry?: string[];
  currentCtcCurrency?: string;
  currentCtcAmount?: string;
  currentCompany?: string;
  function?: string[];
  workAuthorizationExpiry?: string;
  panCardNumber?: string;
  gpa?: string;
  aadharNumber?: string;

  // EEO Details
  gender?: string;
  raceEthnicity?: string;
  veteranStatus?: string;
  veteranType?: string;
  disability?: string;
}

export interface DocumentDetails {
  id: string;
  fileIndex: number; // Simple sequential index
  title: string;
  category: string; // UUID
  subcategory: string; // UUID - this is what should be used for document type selection
  comments: string;
  filePath?: string; // Path to the uploaded file from API
  file?: File; // For new file uploads
  attachment?: File | string; // For backward compatibility
  fileName?: string; // Extracted filename for display
  resumeVisibility?: string; // Optional field
  isActive?: boolean; // Whether the document is active or soft-deleted
}

// File upload structure as specified in requirements
export interface FileUpload {
  id: string; // Random UUID for document identification
  fileIndex: string; // Random UUID
  file: File;
  title: string;
  comments?: string;
  type: string; // Document type UUID (will be mapped to sub_category)

}

export interface WorkExperience {
  id: string;
  companyName: string;
  jobTitle: string;
  startDate: string;
  endDate?: string;
  responsibilities: string;
  location?: string;
  employeeType?: string;
  isActive?: boolean;
}

export interface EducationDetails {
  id: string;
  schoolName: string;
  degree: string;
  yearCompleted?: string;
  majorStudy?: string;
  minorStudy?: string;
  gpa?: string;
  country?: string;
  state?: string;
  city?: string;
  higherEducation?: boolean;
  isActive?: boolean;
  // View-layer optional fields
  institution?: string;
  startYear?: string;
  endYear?: string;
  location?: string;
  fieldOfStudy?: string;
  specialization?: string;
  educationType?: string;
  isCurrentlyStudying?: boolean;
  activities?: string;
  coursework?: string[];
  honors?: string[];
  thesis?: string;
  advisor?: string;
  description?: string;
  isVerified?: boolean;
  verificationDate?: string;
}

export interface CertificationDetails {
  id: string;
  certification: string;
  yearCompleted: string;
  comments?: string;
  isActive?: boolean;
  // View-layer optional fields
  issuingOrganization?: string;
  issueDate?: string;
  certificationLevel?: string;
  score?: string | number;
  validityPeriod?: string;
  expiryDate?: string;
  renewalDate?: string;
  credentialId?: string;
  skills?: string[];
  description?: string;
  credentialUrl?: string;
  verificationUrl?: string;
  isVerified?: boolean;
  verificationDate?: string;
}

export interface LanguageDetails {
  id: string;
  language: string;
  proficiency: {
    read: boolean;
    speak: boolean;
    write: boolean;
  };
}

export interface EmployerDetails {
  isNew: boolean;
  addFromExistingVendor: boolean;
  vendorContact?: string;
  firstName?: string;
  lastName?: string;
  employerName?: string;
  officeNumber?: string;
  emailId?: string;
  extension?: string;
  mobileNumber?: string;
  status?: boolean; // true = Active, false = Inactive
}

export interface Applicant {
  id: string;
  personalDetails: PersonalDetails;
  documents: DocumentDetails[];
  workExperience: WorkExperience[];
  educationDetails: EducationDetails[];
  certifications: CertificationDetails[];
  languages: LanguageDetails[];
  employerDetails?: EmployerDetails;
  // Legacy fields for backward compatibility
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  applicationDate: string;
  status: string;
  resume?: string;
  coverLetter?: string;
  experience: number;
  skills: string[];
  interviewDate?: string;
  interviewFeedback?: string;
  onboardingStage?: 'documentation' | 'training' | 'setup' | 'completed';
  startDate?: string;
  avatar?: string;
  isActive?: boolean;
}

export interface Job {
  id: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  description: string;
  requirements: string[];
  postedDate: string;
  isActive: boolean;
}

export interface OnboardingTask {
  id: string;
  applicantId: string;
  title: string;
  description: string;
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed';
  assignedTo: string;
  category: 'documentation' | 'training' | 'setup' | 'orientation';
}

export interface Interview {
  id: string;
  applicantId: string;
  date: string;
  time: string;
  interviewer: string;
  type: 'phone' | 'video' | 'in-person';
  status: 'scheduled' | 'completed' | 'cancelled';
  feedback?: string;
  rating?: number;
}

// Re-export form builder types
export * from './formBuilder';