/**
 * Enhanced Settings Types for TalentFlow ATS
 * Comprehensive settings architecture matching enterprise-level admin panels
 */

// Base setting interfaces
export interface BaseSetting {
  id: string;
  organizationId: string;
  category: SettingCategory;
  key: string;
  value: any;
  dataType: SettingDataType;
  isEditable: boolean;
  isRequired: boolean;
  description?: string;
  validationRules?: ValidationRule[];
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  updatedBy: string;
}

export type SettingDataType = 'string' | 'number' | 'boolean' | 'json' | 'array' | 'date' | 'email' | 'url' | 'phone';

export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

// Main setting categories from Ceipal admin panel
export type SettingCategory = 
  | 'global_settings'
  | 'reminder'
  | 'leads'
  | 'whatsapp'
  | 'organization'
  | 'applicants'
  | 'jobs'
  | 'job_requisition'
  | 'security'
  | 'placements'
  | 'bench'
  | 'clients'
  | 'vendors'
  | 'texthire'
  | 'campus'
  | 'job_posting'
  | 'api_settings'
  | 'integration';

// Global Settings interfaces
export interface GlobalSettings {
  customFields: CustomFieldSettings;
  emailSettings: EmailSettings;
  contentSettings: ContentSettings;
  lookups: LookupSettings;
  mandatoryFields: MandatoryFieldSettings;
}

export interface CustomFieldSettings {
  applicantFields: CustomField[];
  jobFields: CustomField[];
  organizationFields: CustomField[];
  enableCustomFields: boolean;
  maxCustomFields: number;
}

export interface CustomField {
  id: string;
  name: string;
  label: string;
  type: CustomFieldType;
  entityType: 'applicant' | 'job' | 'organization' | 'vendor' | 'client';
  isRequired: boolean;
  isActive: boolean;
  options?: string[]; // For select/multi-select fields
  validationRules?: ValidationRule[];
  displayOrder: number;
  section?: string;
}

export type CustomFieldType = 
  | 'text'
  | 'textarea'
  | 'number'
  | 'email'
  | 'phone'
  | 'url'
  | 'date'
  | 'datetime'
  | 'select'
  | 'multiselect'
  | 'checkbox'
  | 'radio'
  | 'file'
  | 'currency';

export interface EmailSettings {
  smtpConfiguration: SMTPConfiguration;
  emailTemplates: EmailTemplate[];
  defaultSender: string;
  enableEmailTracking: boolean;
  emailSignature: string;
  autoReplySettings: AutoReplySettings;
}

export interface SMTPConfiguration {
  host: string;
  port: number;
  username: string;
  password: string;
  encryption: 'none' | 'tls' | 'ssl';
  fromEmail: string;
  fromName: string;
  isActive: boolean;
}

export interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  type: EmailTemplateType;
  isActive: boolean;
  variables: string[];
  createdAt: string;
  updatedAt: string;
}

export type EmailTemplateType = 
  | 'application_received'
  | 'interview_scheduled'
  | 'interview_reminder'
  | 'status_update'
  | 'rejection'
  | 'offer_letter'
  | 'welcome'
  | 'password_reset'
  | 'vendor_notification'
  | 'client_notification';

export interface AutoReplySettings {
  enabled: boolean;
  subject: string;
  message: string;
  businessHoursOnly: boolean;
  businessHours: BusinessHours;
}

export interface BusinessHours {
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
  sunday: DaySchedule;
}

export interface DaySchedule {
  isWorkingDay: boolean;
  startTime: string;
  endTime: string;
}

export interface ContentSettings {
  companyBranding: CompanyBranding;
  applicationForm: ApplicationFormSettings;
  jobBoard: JobBoardSettings;
  careerSite: CareerSiteSettings;
}

export interface CompanyBranding {
  logo: string;
  favicon: string;
  primaryColor: string;
  secondaryColor: string;
  fontFamily: string;
  customCSS?: string;
}

export interface ApplicationFormSettings {
  enableCoverLetter: boolean;
  enablePortfolio: boolean;
  enableCustomQuestions: boolean;
  maxFileSize: number;
  allowedFileTypes: string[];
  requiredFields: string[];
}

export interface JobBoardSettings {
  isPublic: boolean;
  enableSearch: boolean;
  enableFilters: boolean;
  jobsPerPage: number;
  enableSocialSharing: boolean;
  seoSettings: SEOSettings;
}

export interface SEOSettings {
  metaTitle: string;
  metaDescription: string;
  keywords: string[];
  enableSitemap: boolean;
}

export interface CareerSiteSettings {
  isEnabled: boolean;
  customDomain?: string;
  theme: string;
  aboutUs: string;
  contactInfo: ContactInfo;
  socialLinks: SocialLinks;
}

export interface ContactInfo {
  address: string;
  phone: string;
  email: string;
  workingHours: string;
}

export interface SocialLinks {
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  youtube?: string;
}

export interface LookupSettings {
  systemLookups: SystemLookup[];
  customLookups: CustomLookup[];
}

export interface SystemLookup {
  id: string;
  category: string;
  name: string;
  values: LookupValue[];
  isEditable: boolean;
  isActive: boolean;
}

export interface CustomLookup {
  id: string;
  name: string;
  description: string;
  values: LookupValue[];
  entityType: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LookupValue {
  id: string;
  value: string;
  label: string;
  isActive: boolean;
  displayOrder: number;
  parentId?: string; // For hierarchical lookups
}

export interface MandatoryFieldSettings {
  applicantFields: MandatoryField[];
  jobFields: MandatoryField[];
  organizationFields: MandatoryField[];
}

export interface MandatoryField {
  fieldName: string;
  isRequired: boolean;
  entityType: string;
  validationMessage?: string;
}

// Reminder Settings
export interface ReminderSettings {
  reminderSettings: ReminderConfiguration;
  applicantProfile: ApplicantProfileSettings;
  clientReminder: ClientReminderSettings;
  vendors: VendorReminderSettings;
  jobPosting: JobPostingReminderSettings;
  placements: PlacementReminderSettings;
  emailStatistics: EmailStatisticsSettings;
  eventReminder: EventReminderSettings;
  general: GeneralReminderSettings;
}

export interface ReminderConfiguration {
  enableReminders: boolean;
  defaultReminderTime: number; // minutes before
  reminderTypes: ReminderType[];
  escalationRules: EscalationRule[];
}

export interface ReminderType {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  defaultEnabled: boolean;
  reminderTimes: number[]; // minutes before event
}

export interface EscalationRule {
  id: string;
  triggerAfter: number; // minutes
  escalateTo: string[]; // user IDs
  message: string;
  isActive: boolean;
}

export interface ApplicantProfileSettings {
  enableProfileReminders: boolean;
  incompleteProfileReminder: number; // days
  profileUpdateReminder: number; // days
  documentExpiryReminder: number; // days before expiry
}

export interface ClientReminderSettings {
  feedbackReminder: number; // hours after interview
  decisionReminder: number; // days after application
  contractRenewalReminder: number; // days before expiry
  paymentReminder: number; // days after due date
}

export interface VendorReminderSettings {
  submissionDeadlineReminder: number; // hours before deadline
  documentSubmissionReminder: number; // days
  performanceReviewReminder: number; // days
  contractComplianceReminder: number; // days
}

export interface JobPostingReminderSettings {
  jobExpiryReminder: number; // days before expiry
  lowApplicationsReminder: number; // days after posting
  interviewSchedulingReminder: number; // hours
  offerExpiryReminder: number; // days before expiry
}

export interface PlacementReminderSettings {
  startDateReminder: number; // days before start
  probationEndReminder: number; // days before end
  documentCollectionReminder: number; // days
  feedbackCollectionReminder: number; // days after placement
}

export interface EmailStatisticsSettings {
  enableTracking: boolean;
  trackOpens: boolean;
  trackClicks: boolean;
  trackBounces: boolean;
  reportingFrequency: 'daily' | 'weekly' | 'monthly';
  recipients: string[]; // email addresses for reports
}

export interface EventReminderSettings {
  interviewReminders: InterviewReminderSettings;
  meetingReminders: MeetingReminderSettings;
  deadlineReminders: DeadlineReminderSettings;
}

export interface InterviewReminderSettings {
  candidateReminder: number[]; // hours before [24, 2]
  interviewerReminder: number[]; // hours before [24, 1]
  panelReminder: number[]; // hours before [24, 4]
  enableSMSReminders: boolean;
  enableEmailReminders: boolean;
}

export interface MeetingReminderSettings {
  defaultReminder: number; // minutes before
  enableCalendarIntegration: boolean;
  reminderMethods: ('email' | 'sms' | 'push')[];
}

export interface DeadlineReminderSettings {
  applicationDeadlines: number[]; // days before [7, 3, 1]
  documentDeadlines: number[]; // days before [5, 2, 1]
  contractDeadlines: number[]; // days before [14, 7, 3]
}

export interface GeneralReminderSettings {
  timezone: string;
  businessHours: BusinessHours;
  excludeWeekends: boolean;
  excludeHolidays: boolean;
  holidays: Holiday[];
}

export interface Holiday {
  id: string;
  name: string;
  date: string;
  isRecurring: boolean;
  country?: string;
  region?: string;
}

// Leads Management Settings
export interface LeadsSettings {
  leadCallType: LeadCallTypeSettings;
  leadStatuses: LeadStatusSettings;
  leadSources: LeadSourceSettings;
  leadSettings: LeadGeneralSettings;
}

export interface LeadCallTypeSettings {
  callTypes: LeadCallType[];
  defaultCallType: string;
  enableCallLogging: boolean;
  requireCallNotes: boolean;
}

export interface LeadCallType {
  id: string;
  name: string;
  description: string;
  color: string;
  isActive: boolean;
  displayOrder: number;
}

export interface LeadStatusSettings {
  statuses: LeadStatus[];
  defaultStatus: string;
  enableStatusHistory: boolean;
  autoProgressRules: StatusProgressRule[];
}

export interface LeadStatus {
  id: string;
  name: string;
  description: string;
  color: string;
  isActive: boolean;
  isFinal: boolean;
  displayOrder: number;
  nextStatuses: string[]; // allowed next status IDs
}

export interface StatusProgressRule {
  id: string;
  fromStatus: string;
  toStatus: string;
  condition: string; // JSON condition
  triggerAfter: number; // days
  isActive: boolean;
}

export interface LeadSourceSettings {
  sources: LeadSource[];
  enableSourceTracking: boolean;
  requireSourceSelection: boolean;
  customSources: boolean;
}

export interface LeadSource {
  id: string;
  name: string;
  description: string;
  type: 'organic' | 'paid' | 'referral' | 'social' | 'email' | 'other';
  isActive: boolean;
  trackingCode?: string;
  cost?: number;
  displayOrder: number;
}

export interface LeadGeneralSettings {
  enableLeadScoring: boolean;
  scoringCriteria: LeadScoringCriteria[];
  autoAssignmentRules: LeadAssignmentRule[];
  duplicateDetection: DuplicateDetectionSettings;
  leadNurturing: LeadNurturingSettings;
}

export interface LeadScoringCriteria {
  id: string;
  name: string;
  field: string;
  condition: string;
  points: number;
  isActive: boolean;
}

export interface LeadAssignmentRule {
  id: string;
  name: string;
  conditions: AssignmentCondition[];
  assignTo: string; // user ID or team ID
  priority: number;
  isActive: boolean;
}

export interface AssignmentCondition {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
}

export interface DuplicateDetectionSettings {
  enabled: boolean;
  matchFields: string[];
  autoMerge: boolean;
  notifyOnDuplicate: boolean;
  mergeStrategy: 'keep_latest' | 'keep_oldest' | 'manual';
}

export interface LeadNurturingSettings {
  enabled: boolean;
  campaigns: NurturingCampaign[];
  defaultCampaign?: string;
}

export interface NurturingCampaign {
  id: string;
  name: string;
  description: string;
  triggers: CampaignTrigger[];
  actions: CampaignAction[];
  isActive: boolean;
}

export interface CampaignTrigger {
  type: 'status_change' | 'time_based' | 'activity' | 'score_change';
  condition: any;
  delay?: number; // minutes
}

export interface CampaignAction {
  type: 'send_email' | 'assign_user' | 'update_status' | 'create_task' | 'send_sms';
  parameters: any;
  delay?: number; // minutes
}

// WhatsApp Settings
export interface WhatsAppSettings {
  whatsappIntegration: WhatsAppIntegrationSettings;
  whatsappTemplatesCategory: WhatsAppTemplatesCategorySettings;
  whatsappTemplates: WhatsAppTemplatesSettings;
}

export interface WhatsAppIntegrationSettings {
  isEnabled: boolean;
  apiKey: string;
  phoneNumber: string;
  businessAccountId: string;
  webhookUrl: string;
  verifyToken: string;
  enableAutomation: boolean;
  businessHours: BusinessHours;
  autoReplyMessage: string;
}

export interface WhatsAppTemplatesCategorySettings {
  categories: WhatsAppTemplateCategory[];
  defaultCategory: string;
  enableCustomCategories: boolean;
}

export interface WhatsAppTemplateCategory {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  displayOrder: number;
}

export interface WhatsAppTemplatesSettings {
  templates: WhatsAppTemplate[];
  enableTemplateApproval: boolean;
  defaultLanguage: string;
  supportedLanguages: string[];
}

export interface WhatsAppTemplate {
  id: string;
  name: string;
  category: string;
  language: string;
  status: 'pending' | 'approved' | 'rejected';
  type: 'text' | 'media' | 'interactive';
  content: WhatsAppTemplateContent;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WhatsAppTemplateContent {
  header?: {
    type: 'text' | 'image' | 'video' | 'document';
    content: string;
  };
  body: string;
  footer?: string;
  buttons?: WhatsAppButton[];
}

export interface WhatsAppButton {
  type: 'quick_reply' | 'url' | 'phone_number';
  text: string;
  value: string;
}

// Organization Settings
export interface OrganizationSettings {
  billingDetails: BillingDetailsSettings;
  businessUnits: BusinessUnitsSettings;
  customEmailTemplates: CustomEmailTemplatesSettings;
  emailTemplates: EmailTemplatesSettings;
  hierarchy: HierarchySettings;
  listViewSorting: ListViewSortingSettings;
  organizationLookups: OrganizationLookupsSettings;
  settings: OrganizationGeneralSettings;
  sslClientCertificate: SSLClientCertificateSettings;
  submissionTabsDisplay: SubmissionTabsDisplaySettings;
  dataBackup: DataBackupSettings;
  targetSettings: TargetSettings;
}

export interface BillingDetailsSettings {
  companyName: string;
  billingAddress: Address;
  taxId: string;
  paymentMethods: PaymentMethod[];
  billingCycle: 'monthly' | 'quarterly' | 'annually';
  currency: string;
  invoiceSettings: InvoiceSettings;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'bank_transfer' | 'paypal' | 'stripe';
  details: any;
  isDefault: boolean;
  isActive: boolean;
}

export interface InvoiceSettings {
  autoGenerate: boolean;
  emailInvoices: boolean;
  invoicePrefix: string;
  invoiceNumbering: 'sequential' | 'date_based';
  paymentTerms: number; // days
  lateFeePercentage: number;
}

export interface BusinessUnitsSettings {
  units: BusinessUnit[];
  enableHierarchy: boolean;
  defaultUnit: string;
  requireUnitAssignment: boolean;
}

export interface BusinessUnit {
  id: string;
  name: string;
  description: string;
  parentId?: string;
  managerId: string;
  budget: number;
  costCenter: string;
  isActive: boolean;
  settings: BusinessUnitSettings;
}

export interface BusinessUnitSettings {
  allowCrossUnitAccess: boolean;
  requireApproval: boolean;
  budgetAlerts: boolean;
  budgetThreshold: number; // percentage
}

export interface CustomEmailTemplatesSettings {
  templates: CustomEmailTemplate[];
  enableCustomTemplates: boolean;
  templateCategories: string[];
  approvalRequired: boolean;
}

export interface CustomEmailTemplate {
  id: string;
  name: string;
  category: string;
  subject: string;
  body: string;
  variables: TemplateVariable[];
  isActive: boolean;
  createdBy: string;
  approvedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateVariable {
  name: string;
  description: string;
  type: 'text' | 'date' | 'number' | 'boolean';
  defaultValue?: any;
  isRequired: boolean;
}

export interface EmailTemplatesSettings {
  systemTemplates: SystemEmailTemplate[];
  customization: TemplateCustomizationSettings;
  localization: TemplateLocalizationSettings;
}

export interface SystemEmailTemplate {
  id: string;
  name: string;
  type: EmailTemplateType;
  subject: string;
  body: string;
  isCustomizable: boolean;
  variables: string[];
  lastModified: string;
}

export interface TemplateCustomizationSettings {
  allowSubjectCustomization: boolean;
  allowBodyCustomization: boolean;
  allowVariableCustomization: boolean;
  requireApproval: boolean;
  backupOriginal: boolean;
}

export interface TemplateLocalizationSettings {
  enableLocalization: boolean;
  supportedLanguages: string[];
  defaultLanguage: string;
  fallbackToDefault: boolean;
}
