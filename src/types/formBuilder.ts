// TypeScript types for Form Builder functionality
// Following the existing pattern from types/index.ts

export type FieldType =
  | 'text'
  | 'email'
  | 'phone'
  | 'select'
  | 'multiselect'
  | 'date'
  | 'datetime'
  | 'time'
  | 'daterange'
  | 'textarea'
  | 'richtext'
  | 'file'
  | 'number'
  | 'url'
  | 'radio'
  | 'checkbox'
  | 'rating'
  | 'slider'
  | 'color'
  | 'signature'
  | 'address'
  | 'matrix'
  | 'image_choice'
  | 'section_break'
  | 'page_break';

export type FormStatus = 'draft' | 'published' | 'archived';

export type TemplateCategory = 
  | 'candidate-screening' 
  | 'job-application' 
  | 'feedback' 
  | 'survey' 
  | 'custom';

export interface FormFieldValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  required?: boolean;
  fileTypes?: string[];
  maxFileSize?: number; // in MB
}

export interface FormFieldOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface FormField {
  id: string;
  type: FieldType;
  label: string;
  placeholder?: string;
  required: boolean;
  description?: string;
  options?: FormFieldOption[];
  validation?: FormFieldValidation;
  order: number;
  width?: 'full' | 'half' | 'third'; // For layout purposes
  conditional?: {
    dependsOn: string; // Field ID
    showWhen: string | string[]; // Value(s) that trigger visibility
  };

  // Complex field configurations
  richTextConfig?: {
    toolbar?: string[];
    height?: number;
    allowImages?: boolean;
  };
  ratingConfig?: {
    maxRating: number;
    icon?: 'star' | 'heart' | 'thumbs';
    allowHalf?: boolean;
    labels?: string[];
  };
  sliderConfig?: {
    min: number;
    max: number;
    step?: number;
    showLabels?: boolean;
    leftLabel?: string;
    rightLabel?: string;
  };
  fileConfig?: {
    maxFiles?: number;
    maxSize?: number; // in MB
    allowedTypes?: string[];
    allowMultiple?: boolean;
    showPreview?: boolean;
  };
  matrixConfig?: {
    rows: string[];
    columns: string[];
    inputType: 'radio' | 'checkbox' | 'text';
    required?: boolean;
  };
  addressConfig?: {
    enableAutocomplete?: boolean;
    requiredFields?: ('street' | 'city' | 'state' | 'zip' | 'country')[];
    defaultCountry?: string;
  };
  signatureConfig?: {
    width?: number;
    height?: number;
    penColor?: string;
    backgroundColor?: string;
  };
}

export interface FormSettings {
  allowMultipleSubmissions: boolean;
  requireAuthentication: boolean;
  showProgressBar: boolean;
  customTheme?: {
    primaryColor: string;
    backgroundColor: string;
    fontFamily: string;
    logoUrl?: string;
  };
  notifications: {
    sendToEmail: string[];
    sendOnSubmission: boolean;
    emailTemplate?: string;
  };
  redirectUrl?: string;
  thankYouMessage?: string;
  collectMetadata: boolean; // IP, user agent, etc.
}

export interface FormSchema {
  id: string;
  organizationId: string;
  title: string;
  description?: string;
  fields: FormField[];
  settings: FormSettings;
  status: FormStatus;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  submissionCount: number;
  shareUrl?: string;
  embedCode?: string;
  tags?: string[];
  category?: TemplateCategory;
}

export interface FormResponse {
  id: string;
  formId: string;
  responses: Record<string, string | string[] | number | boolean>;
  submittedAt: string;
  submitterEmail?: string;
  submitterName?: string;
  ipAddress?: string;
  userAgent?: string;
  metadata?: Record<string, string | number | boolean>;
}

export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  thumbnail: string;
  fields: FormField[];
  usageCount: number;
  tags: string[];
  isPublic: boolean;
  createdBy?: string;
  organizationId?: string;
}

export interface FieldTypeDefinition {
  type: FieldType;
  label: string;
  icon: string;
  description: string;
  defaultProps: Partial<FormField>;
  validationOptions: (keyof FormFieldValidation)[];
  supportsOptions: boolean;
}

// Form Builder UI State
export interface FormBuilderState {
  currentForm: FormSchema | null;
  selectedFieldId: string | null;
  isDirty: boolean;
  isPreviewMode: boolean;
  draggedField: FieldType | null;
}

// Form Builder Actions
export type FormBuilderAction =
  | { type: 'SET_FORM'; payload: FormSchema }
  | { type: 'ADD_FIELD'; payload: FormField }
  | { type: 'UPDATE_FIELD'; payload: { id: string; updates: Partial<FormField> } }
  | { type: 'DELETE_FIELD'; payload: string }
  | { type: 'REORDER_FIELDS'; payload: { fromIndex: number; toIndex: number } }
  | { type: 'SELECT_FIELD'; payload: string | null }
  | { type: 'SET_PREVIEW_MODE'; payload: boolean }
  | { type: 'SET_DIRTY'; payload: boolean }
  | { type: 'UPDATE_FORM_SETTINGS'; payload: Partial<FormSettings> }
  | { type: 'UPDATE_FORM_META'; payload: { title?: string; description?: string } };

// Response Management
export interface ResponseFilters {
  dateRange?: {
    start: string;
    end: string;
  };
  searchTerm?: string;
  sortBy?: 'submittedAt' | 'submitterName' | 'submitterEmail';
  sortOrder?: 'asc' | 'desc';
}

export interface ResponseStats {
  totalResponses: number;
  responseRate: number;
  averageCompletionTime: number;
  dropOffPoints: Array<{
    fieldId: string;
    fieldLabel: string;
    dropOffRate: number;
  }>;
  submissionsByDate: Array<{
    date: string;
    count: number;
  }>;
}

// Export formats
export type ExportFormat = 'csv' | 'excel' | 'pdf' | 'json';

export interface ExportOptions {
  format: ExportFormat;
  includeMetadata: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  fields?: string[]; // Field IDs to include
}

// Form sharing and embedding
export interface ShareSettings {
  isPublic: boolean;
  allowAnonymous: boolean;
  expiresAt?: string;
  maxSubmissions?: number;
  password?: string;
  domains?: string[]; // Allowed domains for embedding
}

// Form analytics
export interface FormAnalytics {
  views: number;
  submissions: number;
  conversionRate: number;
  averageTimeToComplete: number;
  fieldAnalytics: Array<{
    fieldId: string;
    fieldLabel: string;
    completionRate: number;
    averageTimeSpent: number;
    validationErrors: number;
  }>;
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  browserBreakdown: Record<string, number>;
  locationBreakdown: Record<string, number>;
}

// Validation errors
export interface FormValidationError {
  fieldId: string;
  message: string;
  type: 'required' | 'format' | 'length' | 'custom';
}

export interface FormSubmissionResult {
  success: boolean;
  errors?: FormValidationError[];
  responseId?: string;
  message?: string;
}

// Form builder component props
export interface FormBuilderProps {
  initialForm?: FormSchema;
  onSave: (form: FormSchema) => void;
  onPublish: (form: FormSchema) => void;
  onPreview: (form: FormSchema) => void;
  readOnly?: boolean;
}

export interface FormRendererProps {
  form: FormSchema;
  onSubmit: (responses: Record<string, string | string[] | number | boolean>) => Promise<FormSubmissionResult>;
  initialValues?: Record<string, string | string[] | number | boolean>;
  showProgress?: boolean;
  customTheme?: FormSettings['customTheme'];
}

export interface ResponseViewerProps {
  formId: string;
  responses: FormResponse[];
  onExport: (options: ExportOptions) => void;
  onDelete: (responseId: string) => void;
  filters?: ResponseFilters;
  onFiltersChange: (filters: ResponseFilters) => void;
}
