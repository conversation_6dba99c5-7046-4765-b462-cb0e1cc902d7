export type OrganizationType = 'client' | 'vendor';
export type UserRole = 'admin' | 'recruiter' | 'hiring_manager' | 'interviewer' | 'employee' | 'super_admin';
export type SubscriptionPlan = 'free' | 'basic' | 'professional' | 'enterprise';

export interface Organization {
  id: string;
  name: string;
  type: OrganizationType;
  subscriptionPlan: SubscriptionPlan;
  industry: string;
  size: string;
  website?: string;
  address: string;
  phone: string;
  email: string;
  logo?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
  settings: OrganizationSettings;
  // Vendor-specific fields
  vendorProfile?: VendorProfile;
  // Client-specific fields
  clientProfile?: ClientProfile;
}

export interface OrganizationSettings {
  timezone: string;
  currency: string;
  dateFormat: string;
  allowPublicJobBoard: boolean;
  requireApprovalForJobs: boolean;
  maxUsers: number;
  maxJobPostings: number;
  customBranding: boolean;
}

export interface VendorProfile {
  specializations: string[];
  certifications: string[];
  yearsInBusiness: number;
  numberOfRecruiters: number;
  successRate: number;
  averageTimeToFill: number;
  clientReviews: Review[];
  connectedClients: string[]; // Client organization IDs
  pendingClientRequests: string[]; // Client organization IDs with pending connection requests
  rejectedByClients: string[]; // Client organization IDs that rejected this vendor
  vendorTier: 'bronze' | 'silver' | 'gold' | 'platinum';
  minimumRate: number;
  preferredIndustries: string[];
  serviceAreas: string[]; // Geographic areas they serve
}

export interface ClientProfile {
  departments: Department[];
  preferredVendors: string[]; // Organization IDs
  approvedVendors: string[]; // Organization IDs that are approved to work with this client
  pendingVendorRequests: string[]; // Organization IDs with pending approval requests
  hiringBudget: number;
  annualHires: number;
  companySize: string;
  vendorSettings: ClientVendorSettings;
}

export interface ClientVendorSettings {
  autoApproveVendors: boolean;
  requireVendorApproval: boolean;
  maxVendorsPerJob: number;
  allowPublicJobAccess: boolean;
  vendorCommissionRate: number; // Percentage
}

export interface Department {
  id: string;
  name: string;
  description: string;
  managerId: string;
  budgetAllocated: number;
}

export interface User {
  id: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  avatar?: string;
  phone?: string;
  department?: string;
  title: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  invitedBy?: string;
  onboardingCompleted: boolean;
  // Enhanced authentication fields
  invitationStatus: 'pending' | 'accepted' | 'expired' | 'none';
  invitationToken?: string;
  invitationExpiresAt?: string;
  lastPasswordChange?: string;
  failedLoginAttempts: number;
  accountLocked: boolean;
  accountLockedUntil?: string;
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpiresAt?: string;
  // Social login connections
  socialConnections: SocialConnection[];
  // Session management
  activeSessions: UserSession[];
  // Preferences
  preferences: UserPreferences;
}

export interface Permission {
  module: 'applicants' | 'jobs' | 'interviews' | 'onboarding' | 'analytics' | 'settings' | 'users' | 'vendors' | 'form_builder' | 'communications';
  actions: ('create' | 'read' | 'update' | 'delete' | 'approve' | 'invite' | 'manage_roles')[];
}

// Enhanced authentication interfaces
export interface SocialConnection {
  id: string;
  provider: 'google' | 'linkedin' | 'microsoft' | 'github';
  providerId: string;
  email: string;
  connectedAt: string;
  isActive: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
}

export interface UserSession {
  id: string;
  userId: string;
  deviceInfo: string;
  ipAddress: string;
  location: string;
  userAgent: string;
  createdAt: string;
  lastActivity: string;
  expiresAt: string;
  isActive: boolean;
  isCurrent: boolean;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  emailNotifications: {
    newApplications: boolean;
    interviewReminders: boolean;
    systemUpdates: boolean;
    weeklyReports: boolean;
  };
  dashboardLayout: string[];
  defaultPageSize: number;
}

// User invitation system
export interface UserInvitation {
  id: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  department?: string;
  title: string;
  invitedBy: string;
  invitedAt: string;
  expiresAt: string;
  status: 'pending' | 'accepted' | 'expired' | 'cancelled';
  token: string;
  message?: string;
  acceptedAt?: string;
}

// Role template system
export interface RoleTemplate {
  id: string;
  name: string;
  description: string;
  permissions: Permission[];
  organizationType: 'client' | 'vendor' | 'both';
  isCustom: boolean;
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface Review {
  id: string;
  clientId: string;
  rating: number;
  comment: string;
  createdAt: string;
  reviewer: string;
}

// Vendor-Client Relationship Management
export interface VendorClientRelationship {
  id: string;
  vendorId: string;
  clientId: string;
  status: 'pending' | 'approved' | 'rejected' | 'suspended';
  requestedBy: 'vendor' | 'client';
  requestedAt: string;
  approvedAt?: string;
  approvedBy?: string;
  rejectedAt?: string;
  rejectedBy?: string;
  rejectionReason?: string;
  contractTerms?: RelationshipTerms;
  performanceMetrics?: VendorPerformanceMetrics;
}

export interface RelationshipTerms {
  commissionRate: number;
  paymentTerms: string;
  exclusivityPeriod?: number; // days
  guaranteePeriod?: number; // days
  cancellationNotice: number; // days
}

export interface VendorPerformanceMetrics {
  totalSubmissions: number;
  totalHires: number;
  averageTimeToSubmit: number; // days
  averageTimeToHire: number; // days
  clientSatisfactionRating: number;
  lastUpdated: string;
}

// Enhanced Job interface for multi-tenant
export interface MultiTenantJob extends Omit<Job, 'id'> {
  id: string;
  organizationId: string;
  createdBy: string;
  assignedVendors: string[]; // Vendor organization IDs
  invitedVendors: string[]; // Vendors invited but not yet accepted
  clientDepartment?: string;
  budget?: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isPublic: boolean;
  visibility: 'public' | 'private' | 'vendor-only' | 'assigned-vendors';
  maxVendors?: number;
  requiredClearanceLevel?: string;
  jobCode: string;
  approvalStatus: 'draft' | 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: string;
  vendorDeadline?: string; // Deadline for vendor submissions
  clientFeedbackRequired: boolean;
  autoApproveVendorCandidates: boolean;
  jobMetrics?: JobMetrics;
}

export interface JobMetrics {
  totalApplications: number;
  vendorSubmissions: number;
  directApplications: number;
  interviewsScheduled: number;
  offersExtended: number;
  hires: number;
  averageTimeToHire?: number;
  topPerformingVendor?: string;
}

// Enhanced Applicant interface for multi-tenant
export interface MultiTenantApplicant extends Omit<Applicant, 'id'> {
  id: string;
  organizationId: string; // Which organization manages this applicant
  sourceOrganizationId?: string; // If submitted by a vendor
  submittedBy: string; // User ID who added the applicant
  jobId: string;
  vendorId?: string; // If applicant is from a vendor
  submissionStatus: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'withdrawn';
  clientFeedback?: ApplicantFeedback[];
  vendorFeedback?: ApplicantFeedback[];
  vendorNotes?: string;
  clientNotes?: string;
  rateCard?: RateCard;
  clearanceLevel?: string;
  availability: string;
  noticePeriod?: string;
  currentEmployer?: string;
  referralSource?: string;
  submittedAt?: string;
  lastUpdatedBy?: string;
  exclusivityPeriod?: number; // days
}

export interface ApplicantFeedback {
  id: string;
  userId: string;
  userName: string;
  feedback: string;
  rating: number;
  createdAt: string;
  stage: string;
}

export interface RateCard {
  hourlyRate?: number;
  dailyRate?: number;
  annualSalary?: number;
  currency: string;
  negotiable: boolean;
}

// Contract Management
export interface Contract {
  id: string;
  clientId: string;
  vendorId: string;
  applicantId?: string;
  jobId?: string;
  type: 'master_service' | 'statement_of_work' | 'individual_contract';
  startDate: string;
  endDate?: string;
  value: number;
  currency: string;
  status: 'draft' | 'active' | 'completed' | 'terminated';
  terms: ContractTerms;
  createdBy: string;
  createdAt: string;
}

export interface ContractTerms {
  paymentTerms: string;
  deliverables: string[];
  penalties: string;
  cancellationPolicy: string;
  markupPercentage?: number;
}

// Billing and Invoicing
export interface Invoice {
  id: string;
  organizationId: string;
  clientId?: string;
  vendorId?: string;
  amount: number;
  currency: string;
  status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
  dueDate: string;
  createdAt: string;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  rate: number;
  total: number;
  candidateId?: string;
  jobId?: string;
}

// Communication & Collaboration
export interface Message {
  id: string;
  fromOrganizationId: string;
  toOrganizationId: string;
  fromUserId: string;
  toUserId?: string; // If null, message is to organization
  subject: string;
  content: string;
  messageType: 'general' | 'job-related' | 'candidate-related' | 'system';
  relatedJobId?: string;
  relatedCandidateId?: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
  attachments?: MessageAttachment[];
}

export interface MessageAttachment {
  id: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  url: string;
}

export interface Notification {
  id: string;
  userId: string;
  organizationId: string;
  type: 'job_assigned' | 'candidate_submitted' | 'candidate_approved' | 'candidate_rejected' | 'message_received' | 'deadline_reminder';
  title: string;
  message: string;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
  actionUrl?: string;
  relatedJobId?: string;
  relatedCandidateId?: string;
}