import axios, { AxiosError } from 'axios';
import { SecureRandom } from '@/lib/secureRandom';
import { generateUUID } from '@/lib/utils';
import { apiConfig, buildUrl, withQuery, getAuthorizationHeaders, getMultipartAuthorizationHeaders, createApiError, ApiError, defaultAxiosConfig } from '@/lib/apiConfig';
import { logError } from '@/lib/errorHandling';
import { errorRecoveryService, ErrorRecoveryService } from '@/lib/errorRecovery';
import { resourceCleanup, withCleanup, FormDataResource } from '@/lib/resourceCleanup';
import { API_CONSTANTS } from '@/constants/apiConstants';
import {
  ApplicantListParams,
  GetAllApplicantsRequestDTO,
  GetAllApplicantsResponse,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  PatchApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  PatchApplicantFormData,
  GetAllListValuesDTO,
  ApiSelectOption,
  FileUpload,
  DocumentWithFile,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
  Document as ApplicantDocument
} from '@/types/applicants';

/**
 * Utility function to check if an error is retryable
 */
const isRetryableError = (error: AxiosError): boolean => {
  if (!error.response) return true; // Network errors are retryable
  
  const status = error.response.status;
  // Retry on server errors (5xx) and rate limiting (429)
  return status >= 500 || status === 429;
};

/**
 * Utility function to create a delay
 */
const delay = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Retry strategy with exponential backoff
 */
const retryWithBackoff = async <T>(
  operation: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000,
  signal?: AbortSignal
): Promise<T> => {
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      // Check if request was aborted before each attempt
      if (signal?.aborted) {
        throw new Error('Request aborted');
      }
      
      return await operation();
    } catch (error) {
      const axiosError = error as AxiosError;
      
      // Don't retry if request was aborted
      if (signal?.aborted || error instanceof Error && error.message === 'Request aborted') {
        throw error;
      }
      
      // Don't retry if it's the last attempt or error is not retryable
      if (attempt === maxRetries || !isRetryableError(axiosError)) {
        throw error;
      }
      
      // Wait before retrying with exponential backoff
      const delayMs = baseDelay * Math.pow(2, attempt);
      await delay(delayMs);
    }
  }
  
  throw new Error('Max retries exceeded');
};

/**
 * Higher-order function to handle error logging, recovery, and callbacks consistently across all API functions
 * Eliminates DRY violation by centralizing error handling logic with global error recovery
 */
const withErrorHandling = <T extends unknown[], R>(
  operation: string,
  apiCall: (...args: T) => Promise<R>,
  errorCallback?: (error: ApiError) => void,
  enableErrorRecovery = true
) => {
  return async (...args: T): Promise<R | undefined> => {
    const operationId = SecureRandom.generateOperationId(operation);
    
    try {
      // First attempt - direct API call
      return await apiCall(...args);
    } catch (err) {
      const apiError = createApiError(err as AxiosError);
      logError(operation, apiError, { params: args, operationId });
      
      // Attempt error recovery if enabled
      if (enableErrorRecovery) {
        try {
          // Extract signal from args if present (usually last parameter)
          const signal = args[args.length - 1] as AbortSignal | undefined;
          const isAbortSignal = signal && typeof signal === 'object' && 'aborted' in signal;
          const abortSignal = isAbortSignal ? signal : undefined;
          
          // Attempt recovery
          const recoveredResult = await errorRecoveryService.handleApiError(
            apiError,
            () => apiCall(...args),
            operationId,
            abortSignal
          );
          
          return recoveredResult;
        } catch (recoveryError) {
          // Recovery failed, proceed with original error handling
          logError(`${operation}-recovery-failed`, recoveryError as Error, { 
            operationId, 
            originalError: apiError 
          });
        }
      }
      
      // Call error callback if provided
      if (errorCallback) {
        errorCallback(apiError);
      }
      
      return undefined;
    }
  };
};

/**
 * Class responsible for processing files and building document structures
 * Follows Single Responsibility Principle - only handles file/document processing
 */
export class ApplicantFileProcessor {
  /**
   * Process files and ensure each has a sequential fileIndex
   */
  processFiles(files: FileUpload[]): FileUpload[] {
    return files.map((file, index) => ({
      ...file,
      fileIndex: index // Use sequential index as number instead of string
    }));
  }

  /**
   * Build documents array with fileIndex references and proper structure
   */
  buildDocuments(files: FileUpload[]): ApplicantDocument[] {
    return files.map(file => ({
      id: generateUUID(), // Random UUID for document identification
      fileIndex: file.fileIndex,
      title: file.title,
      comments: file.comments,
      category: API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT, // Fixed category ID
      subcategory: file.type || API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT, // Ensure subcategory is always a string
      isActive: true, // Mark document as active
    }));
  }

  /**
   * Combine applicant data with processed documents
   */
  combineApplicantData(
    applicantData: CreateApplicantRequest, 
    documentsWithFiles: ApplicantDocument[]
  ): CreateApplicantRequest {
    // Ensure we have a clean CreateApplicantRequest with proper Document[] type
    const cleanApplicantData: CreateApplicantRequest = {
      ...applicantData,
      id: applicantData.id || generateUUID(), // Ensure applicant has an id
      documents: [
        ...(applicantData.documents || []).map(doc => ({
          ...doc,
          subcategory: doc.subcategory || API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT // Ensure subcategory is always a string
        })),
        ...documentsWithFiles
      ]
    };
    
    return cleanApplicantData;
  }

  /**
   * Combine applicant data from CreateApplicantWithFilesRequest with processed documents
   */
  combineApplicantDataWithFiles(
    applicantData: Omit<CreateApplicantRequest, 'documents'> & { documents?: DocumentWithFile[] },
    documentsWithFiles: ApplicantDocument[]
  ): CreateApplicantRequest {
    // Convert DocumentWithFile[] to Document[] by ensuring subcategory is always a string
    const convertedDocuments: ApplicantDocument[] = (applicantData.documents || []).map(doc => ({
      ...doc,
      subcategory: doc.subcategory || API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT // Ensure subcategory is always a string
    }));

    // Create clean CreateApplicantRequest
    const cleanApplicantData: CreateApplicantRequest = {
      ...applicantData,
      id: applicantData.id || generateUUID(), // Ensure applicant has an id
      documents: [
        ...convertedDocuments,
        ...documentsWithFiles
      ]
    };
    
    return cleanApplicantData;
  }

  /**
   * Create FormData for multipart requests with proper resource management
   */
  createFormDataWithCleanup(
    applicantData: CreateApplicantRequest, 
    processedFiles: FileUpload[],
    operationId?: string
  ): FormDataResource {
    const formDataResource = resourceCleanup.createFormDataResource(operationId);
    const formData = formDataResource.getFormData();
    
    formData.append('applicantData', JSON.stringify(applicantData));
    
    // Append files as List<MultipartFile> with RequestPart name "files"
    // Backend expects @RequestPart(value = "files", required = false) List<MultipartFile> files
    processedFiles.forEach(file => {
      formData.append('files', file.file);
    });
    
    return formDataResource;
  }

  /**
   * Create FormData for multipart requests (legacy method for backward compatibility)
   */
  createFormData(
    applicantData: CreateApplicantRequest, 
    processedFiles: FileUpload[]
  ): FormData {
    const formData = new FormData();
    formData.append('applicantData', JSON.stringify(applicantData));
    
    // Append files as List<MultipartFile> with RequestPart name "files"
    // Backend expects @RequestPart(value = "files", required = false) List<MultipartFile> files
    processedFiles.forEach(file => {
      formData.append('files', file.file);
    });
    
    return formData;
  }
}

/**
 * Class responsible for making HTTP API calls
 * Follows Single Responsibility Principle - only handles API communication
 */
export class ApplicantApiClient {
  /**
   * Make a POST request for listing applicants
   */
  async listApplicants(
    requestBody: GetAllApplicantsRequestDTO, 
    signal?: AbortSignal
  ): Promise<GetAllApplicantsResponse> {
    const url = buildUrl(apiConfig.applicants.list);
    const headers = {
      'Content-Type': 'application/vnd-chidhagni-ats.applicant.get.all.req-v1+json',
      'Accept': 'application/vnd-chidhagni-ats.applicant.get.all.res-v1+json'
    };
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'post',
        url,
        headers,
        data: requestBody,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a GET request for applicant details
   */
  async getApplicant(applicantId: string, signal?: AbortSignal): Promise<ApplicantDetailResponse> {
    const url = buildUrl(apiConfig.applicants.detail, { id: applicantId });
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/vnd-chidhagni-ats.applicant.get.by-id.res-v1+json'
    };
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'get',
        url,
        headers,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a POST request to create applicant (multipart format, no files)
   */
  async createApplicant(formData: FormData, signal?: AbortSignal): Promise<CreateApplicantResponse> {
    const url = buildUrl(apiConfig.applicants.create);
    const headers = getMultipartAuthorizationHeaders(
      'application/vnd-chidhagni-ats.applicant.create.res-v1+json'
    );
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'post',
        url,
        data: formData,
        headers,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a POST request to create applicant with files
   */
  async createApplicantWithFiles(formData: FormData, signal?: AbortSignal): Promise<CreateApplicantResponse> {
    const url = buildUrl(apiConfig.applicants.create);
    const headers = getMultipartAuthorizationHeaders(
      'application/vnd-chidhagni-ats.applicant.create.res-v1+json'
    );
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'post',
        url,
        data: formData,
        headers,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a PUT request to update applicant with files
   */
  async patchApplicantWithFiles(
    applicantId: string, 
    formData: FormData, 
    signal?: AbortSignal
  ): Promise<PatchApplicantResponse> {
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'put',
        url: buildUrl(apiConfig.applicants.update, { id: applicantId }),
        data: formData,
        headers: getMultipartAuthorizationHeaders(
          'application/vnd.ats.applicant.update.response.v1+json'
        ),
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a DELETE request to deactivate applicant
   */
  async deleteApplicant(applicantId: string, signal?: AbortSignal): Promise<DeleteApplicantResponse> {
    const url = buildUrl(apiConfig.applicants.deactivate, { id: applicantId });
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/vnd-chidhagni-ats.applicant.deactivate.res-v1+json'
    };
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'delete',
        url,
        headers,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a PATCH request to activate applicant
   */
  async activateApplicant(applicantId: string, signal?: AbortSignal): Promise<ActivateApplicantResponse> {
    const url = buildUrl(apiConfig.applicants.activate, { id: applicantId });
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/vnd-chidhagni-ats.applicant.activate.res-v1+json'
    };
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'patch',
        url,
        headers,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a GET request for list values
   */
  async getListValues(listNameId: string, signal?: AbortSignal): Promise<GetAllListValuesDTO> {
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'get',
        url: `${buildUrl(apiConfig.applicants.listValues)}?id=${encodeURIComponent(listNameId)}`,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a GET request for document tree
   */
  async getDocumentTree(resourceId: string, signal?: AbortSignal): Promise<DocumentTreeResponse> {
    const url = `${buildUrl('/resources')}/${resourceId}/tree`;
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'get',
        url,
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }

  /**
   * Make a GET request for all list values
   */
  async getAllListValues(signal?: AbortSignal): Promise<GetAllGlobalListValuesDTO> {
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'get',
        url: buildUrl(apiConfig.listValue),
        signal,
        ...defaultAxiosConfig,
      });
      
      return response.data;
    }, 3, 1000, signal);
  }
}

// Create singleton instances
const fileProcessor = new ApplicantFileProcessor();
const apiClient = new ApplicantApiClient();

// Refactored API functions using the new architecture

// List all applicants with pagination and filtering - Updated to use POST method
export async function listApplicants(
  params?: { query?: ApplicantListParams },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<GetAllApplicantsResponse | undefined> {
  // Convert legacy ApplicantListParams to GetAllApplicantsRequestDTO
  const requestBody: GetAllApplicantsRequestDTO = {};
  
  if (params?.query) {
    const query = params.query;
    
    // Map pagination
    if (query.page !== undefined) requestBody.page = query.page;
    if (query.pageSize !== undefined) requestBody.pageSize = query.pageSize;
    
    // Map sorting
    if (query.sortDirection) requestBody.sortDirection = query.sortDirection;
    
    // Map filters - use searchKeyWord as primary search, fall back to search
    if (query.searchKeyWord) requestBody.search = query.searchKeyWord;
    else if (query.search) requestBody.search = query.search;
    
    // Map status filter
    if (query.statusFilter) requestBody.status = query.statusFilter;
  }
  
  return withErrorHandling('listApplicants', (signal?: AbortSignal) => 
    apiClient.listApplicants(requestBody, signal), errorCallback
  )(signal);
}

// Get applicant by ID
export async function getApplicant(
  params: { pathParams: { applicantId: string } },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<ApplicantDetailResponse | undefined> {
  return withErrorHandling('getApplicant', (signal?: AbortSignal) => 
    apiClient.getApplicant(params.pathParams.applicantId, signal), errorCallback
  )(signal);
}

// Create new applicant (multipart format, no files)
// Backend only accepts multipart requests, even without files
export async function createApplicant(
  params: { body: CreateApplicantRequest },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<CreateApplicantResponse | undefined> {
  return withCleanup(async (cleanup) => {
    // Create FormData with resource management
    const formDataResource = cleanup.createFormDataResource('createApplicant');
    const formData = formDataResource.getFormData();
    formData.append('applicantData', JSON.stringify(params.body));
    // No files to append
    
    return withErrorHandling('createApplicant', (signal?: AbortSignal) => 
      apiClient.createApplicant(formData, signal), errorCallback
    )(signal);
  }, 'createApplicant');
}

// Create new applicant with files (using FormData)
// Matches backend API specification: applicantData as JSON string + files as List<MultipartFile>
export async function createApplicantWithFiles(
  params: CreateApplicantWithFilesRequest,
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<CreateApplicantResponse | undefined> {
  return withCleanup(async (cleanup) => {
    const { applicantData, files = [] } = params;
    
    // Use file processor to handle file processing and document creation
    const processedFiles = fileProcessor.processFiles(files);
    const documentsWithFiles = fileProcessor.buildDocuments(processedFiles);
    const finalApplicantData = fileProcessor.combineApplicantDataWithFiles(applicantData, documentsWithFiles);
    const formDataResource = fileProcessor.createFormDataWithCleanup(finalApplicantData, processedFiles, 'createApplicantWithFiles');
    const formData = formDataResource.getFormData();
    
    return withErrorHandling('createApplicantWithFiles', (signal?: AbortSignal) => 
      apiClient.createApplicantWithFiles(formData, signal), errorCallback
    )(signal);
  }, 'createApplicantWithFiles');
}

// Update applicant with optional file uploads using PUT method
export async function patchApplicantWithFiles(
  params: { pathParams: { applicantId: string }; body: PatchApplicantFormData },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<PatchApplicantResponse | undefined> {
  return withCleanup(async (cleanup) => {
    // Use PUT with FormData for file uploads with proper resource management
    const formDataResource = cleanup.createFormDataResource('patchApplicantWithFiles');
    const formData = formDataResource.getFormData();
    
    // Ensure patchData is properly stringified for FormData
    formData.append('applicantData', 
      typeof params.body.patchData === 'string' 
        ? params.body.patchData 
        : JSON.stringify(params.body.patchData)
    );
    
    if (params.body.files && params.body.files.length > 0) {
      params.body.files.forEach(file => {
        formData.append('files', file);
      });
    }
    
    return withErrorHandling('patchApplicantWithFiles', (signal?: AbortSignal) => 
      apiClient.patchApplicantWithFiles(params.pathParams.applicantId, formData, signal), errorCallback
    )(signal);
  }, 'patchApplicantWithFiles');
}

// Deactivate applicant (soft delete)
export async function deleteApplicant(
  params: { pathParams: { applicantId: string } },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<DeleteApplicantResponse | undefined> {
  return withErrorHandling('deleteApplicant', (signal?: AbortSignal) => 
    apiClient.deleteApplicant(params.pathParams.applicantId, signal), errorCallback
  )(signal);
}

// Activate applicant
export async function activateApplicant(
  params: { pathParams: { applicantId: string } },
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<ActivateApplicantResponse | undefined> {
  return withErrorHandling('activateApplicant', (signal?: AbortSignal) => 
    apiClient.activateApplicant(params.pathParams.applicantId, signal), errorCallback
  )(signal);
}

// Get list values by ID (no authorization required)
export async function getListValuesById(
  listNameId: string,
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<ApiSelectOption[] | undefined> {
  const processResponse = async (signal?: AbortSignal): Promise<ApiSelectOption[]> => {
    const response = await apiClient.getListValues(listNameId, signal);
    
    // Convert response to SelectOption format
    if (response?.listValues) {
      const options = response.listValues
        .filter(item => item.isActive) // Only return active items
        .map(item => ({
          value: item.id,         // id -> value
          key: item.listValue,    // listValue -> key
        }));

      // Sort options based on the content type
      return options.sort((a, b) => {
        // Check if both values are purely numeric
        const aIsNumeric = /^\d+(\.\d+)?$/.test(a.key);
        const bIsNumeric = /^\d+(\.\d+)?$/.test(b.key);
        
        if (aIsNumeric && bIsNumeric) {
          // Both are numeric - sort numerically
          return parseFloat(a.key) - parseFloat(b.key);
        }
        
        // Check if values contain numbers (like ranges: "0-50000", "50000-100000")
        const aHasNumbers = /\d/.test(a.key);
        const bHasNumbers = /\d/.test(b.key);
        
        if (aHasNumbers && bHasNumbers) {
          // Extract first number for range comparison
          const aFirstNum = parseFloat(a.key.match(/\d+(\.\d+)?/)?.[0] || '0');
          const bFirstNum = parseFloat(b.key.match(/\d+(\.\d+)?/)?.[0] || '0');
          
          if (aFirstNum !== bFirstNum) {
            return aFirstNum - bFirstNum;
          }
        }
        
        // Default to alphabetical sorting
        return a.key.localeCompare(b.key);
      });
    }
    
    return [];
  };

  return withErrorHandling('getListValuesById', processResponse, errorCallback)(signal);
}

// Get document tree structure for document type dropdown
export async function getDocumentTree(
  resourceId: string,
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<DocumentTreeResponse | undefined> {
  return withErrorHandling('getDocumentTree', (signal?: AbortSignal) => 
    apiClient.getDocumentTree(resourceId, signal), errorCallback
  )(signal);
}

// Get all list values (no authorization required)
export async function getAllListValues(
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<GetAllGlobalListValuesDTO | undefined> {
  return withErrorHandling('getAllListValues', (signal?: AbortSignal) => 
    apiClient.getAllListValues(signal), errorCallback
  )(signal);

} 

/**
 * Get file content by location/path for document viewing
 */
export async function getFileByLocation(
  location: string,
  errorCallback?: (error: ApiError) => void,
  signal?: AbortSignal
): Promise<{ data: string; contentType?: string } | undefined> {
  const processResponse = async (signal?: AbortSignal): Promise<{ data: string; contentType?: string }> => {
    const url = buildUrl('/files/get-file-content');
    
    return retryWithBackoff(async () => {
      const response = await axios({
        method: 'get',
        url,
        params: { location },
        headers: getAuthorizationHeaders(),
        signal,
        ...defaultAxiosConfig,
      });
      
      return {
        data: response.data?.data || response.data,
        contentType: response.headers['content-type']
      };
    }, 3, 1000, signal);
  };

  return withErrorHandling('getFileByLocation', processResponse, errorCallback)(signal);
} 