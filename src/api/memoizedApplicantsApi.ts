import { memoizeApiCall, invalidateCache } from '@/lib/memoization';
import {
  listApplicants as _listApplicants,
  getApplicant as _getApplicant,
  getListValuesById as _getListValuesById,
  getDocumentTree as _getDocumentTree,
  createApplicant,
  createApplicantWithFiles,
  patchApplicantWithFiles,
  deleteApplicant,
  activateApplicant
} from './applicantsApi';

// Memoize read-only operations with appropriate TTL
export const listApplicants = memoizeApiCall(_listApplicants, {
  ttl: 2 * 60 * 1000, // 2 minutes for list data
  keyGenerator: (params) => `listApplicants:${JSON.stringify(params?.query || {})}`,
  // Cache only when the response shape is valid; allow empty arrays without forcing true
  shouldCache: (params, result) => !!result && Array.isArray((result as { applicants?: unknown[] }).applicants)
});

export const getApplicant = memoizeApiCall(_getApplicant, {
  ttl: 5 * 60 * 1000, // 5 minutes for individual applicant data
  keyGenerator: (params) => `getApplicant:${params.pathParams.applicantId}`,
  shouldCache: (params, result) => !!result
});

export const getListValuesById = memoizeApiCall(_getListValuesById, {
  ttl: 15 * 60 * 1000, // 15 minutes for dropdown values (they change rarely)
  keyGenerator: (listNameId) => `listValues:${listNameId}`,
  shouldCache: (listNameId, result) => result && result.length > 0
});

export const getDocumentTree = memoizeApiCall(_getDocumentTree, {
  ttl: 30 * 60 * 1000, // 30 minutes for document tree (very stable data)
  keyGenerator: (resourceId) => `documentTree:${resourceId}`,
  shouldCache: (resourceId, result) => !!result
});

// Export write operations without memoization (they should invalidate cache)
export { createApplicant, createApplicantWithFiles, patchApplicantWithFiles };

// Enhanced write operations that invalidate relevant cache entries
export async function createApplicantAndInvalidateCache(
  params: Parameters<typeof createApplicant>[0],
  errorCallback?: Parameters<typeof createApplicant>[1]
) {
  const result = await createApplicant(params, errorCallback);
  if (result) {
    // Invalidate list cache since we added a new applicant
    invalidateCache('listApplicants:');
  }
  return result;
}

export async function createApplicantWithFilesAndInvalidateCache(
  params: Parameters<typeof createApplicantWithFiles>[0],
  errorCallback?: Parameters<typeof createApplicantWithFiles>[1]
) {
  const result = await createApplicantWithFiles(params, errorCallback);
  if (result) {
    // Invalidate list cache since we added a new applicant
    invalidateCache('listApplicants:');
  }
  return result;
}

export async function patchApplicantAndInvalidateCache(
  params: Parameters<typeof patchApplicantWithFiles>[0],
  errorCallback?: Parameters<typeof patchApplicantWithFiles>[1]
) {
  const result = await patchApplicantWithFiles(params, errorCallback);
  if (result) {
    const applicantId = params.pathParams.applicantId;
    // Invalidate specific applicant cache and list cache
    invalidateCache(`getApplicant:${applicantId}`);
    invalidateCache('listApplicants:');
  }
  return result;
}

export async function deleteApplicantAndInvalidateCache(
  params: Parameters<typeof deleteApplicant>[0],
  errorCallback?: Parameters<typeof deleteApplicant>[1]
) {
  const result = await deleteApplicant(params, errorCallback);
  if (result) {
    const applicantId = params.pathParams.applicantId;
    // Invalidate specific applicant cache and list cache
    invalidateCache(`getApplicant:${applicantId}`);
    invalidateCache('listApplicants:');
  }
  return result;
}

export async function activateApplicantAndInvalidateCache(
  params: Parameters<typeof activateApplicant>[0],
  errorCallback?: Parameters<typeof activateApplicant>[1]
) {
  const result = await activateApplicant(params, errorCallback);
  if (result) {
    const applicantId = params.pathParams.applicantId;
    // Invalidate specific applicant cache and list cache
    invalidateCache(`getApplicant:${applicantId}`);
    invalidateCache('listApplicants:');
  }
  return result;
}

// Utility function to clear all applicant-related cache
export function clearApplicantCache(): void {
  invalidateCache('listApplicants:');
  invalidateCache('getApplicant:');
}

// Utility function to clear dropdown cache
export function clearDropdownCache(): void {
  invalidateCache('listValues:');
  invalidateCache('documentTree:');
} 