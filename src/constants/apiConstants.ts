// API Constants - Extract hard-coded values for better maintainability
export const API_CONSTANTS = {
  // Document categories
  DOCUMENT_CATEGORIES: {
    DEFAULT: "bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e"
  },
  
  // Request timeouts
  TIMEOUTS: {
    DEFAULT: 30000, // 30 seconds
    FILE_UPLOAD: 60000, // 60 seconds for file uploads
  },
  
  // Pagination defaults
  PAGINATION: {
    DEFAULT_PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
  },
  
  // Error messages for user-friendly display
  ERROR_MESSAGES: {
    NETWORK_ERROR: "Unable to connect to the server. Please check your internet connection and try again.",
    SERVER_ERROR: "Something went wrong on our end. Please try again in a few moments.",
    VALIDATION_ERROR: "Please check your input and try again.",
    UNAUTHORIZED: "Your session has expired. Please log in again.",
    FOR<PERSON><PERSON><PERSON><PERSON>: "You don't have permission to perform this action.",
    NOT_FOUND: "The requested resource was not found.",
    TIMEOUT: "The request took too long to complete. Please try again.",
    FILE_UPLOAD_ERROR: "Failed to upload file. Please check the file size and format.",
    GENERIC_ERROR: "An unexpected error occurred. Please try again.",
  },
  
  // Loading states
  LOADING_MESSAGES: {
    FETCHING_DATA: "Loading...",
    SAVING_DATA: "Saving...",
    UPLOADING_FILES: "Uploading files...",
    PROCESSING: "Processing...",
  }
} as const;

// Type for error message keys
export type ErrorMessageKey = keyof typeof API_CONSTANTS.ERROR_MESSAGES;
export type LoadingMessageKey = keyof typeof API_CONSTANTS.LOADING_MESSAGES; 