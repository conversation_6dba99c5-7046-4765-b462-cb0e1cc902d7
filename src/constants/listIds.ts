// List IDs for API endpoints
// These UUIDs should be obtained from your backend team
export const LIST_IDS = {
  // Personal Info related lists
  WORK_AUTHORIZATION: 'a0e8c6ef-1ad3-456d-a6de-3f86f48c9f13',
  COUNTRIES: '30aaf845-f823-4fb6-ade1-c0a1dde31cc9',
  DOCUMENT_TYPE: '9883822c-df6a-42ab-ad9d-a1f0d53b4083',
  CITIES: '550e8400-e29b-41d4-a716-************',
  
  // Professional Info related lists
  EXPERIENCE_YEARS: 'e789f150-e016-44cc-bf03-76a637844164',
  EXPERIENCE_MONTHS: '60426fcb-efa1-4283-91ce-c6af8e02aadc',
  SKILLS: '9b4079d1-837a-48a6-8e3e-17e20d3a78b7',
  PRIMARY_SKILLS: '9b4079d1-837a-48a6-8e3e-17e20d3a78b7',
  TECHNOLOGIES: '57850cea-7f33-49a0-a907-6aa45dd0d1ef',
//   INDUSTRIES: '550e8400-e29b-41d4-a716-************',
//   FUNCTIONS: '550e8400-e29b-41d4-a716-************',
  
  // Financial related lists
  CURRENCIES: 'e5e189d0-4cc4-4d05-a0d2-41b3ce8f0909',
  SALARY_RANGES: '6fc23d82-81b2-481d-a83d-2a55d81f2c31',
  
  // Status and Categories
  APPLICANT_STATUS: 'c8e6217d-943d-4576-9b48-68f2a8c8fae1',
  PRIORITY_LEVELS: '550e8400-e29b-41d4-a716-446655440015',
  APPLICATION_SOURCES: 'd2d99b08-1e0c-46db-b0ea-46d1eb31a7aa',
//   OWNERSHIP: '550e8400-e29b-41d4-a716-446655440035',
  
  // Employment related lists
  EMPLOYEE_TYPES: '79ae67b4-2666-47d7-8d87-dc44639b8474',
  TAX_TERMS: 'eaa385ef-3d45-4b2a-9bd5-edbc1170b36f',
  NOTICE_PERIODS: '77f88dbd-4680-4cd9-9a82-9b5455834648',
  
  // Education related lists
  DEGREES: '3f8bb3ed-85e9-42cc-b683-e70fbdedeadd',
  EDUCATION_LEVELS: '550e8400-e29b-41d4-a716-446655440021',
  
  // Demographics (EEO)
  GENDERS: '7ab28fef-eeb4-4a4a-a8c3-333795d73714',
  RACE_ETHNICITIES: '441b1df8-18ba-48fe-884a-f0f6e7607b76',
  VETERAN_STATUS: '89431873-f5b4-4577-9478-cf8097f21146',
  VETERAN_TYPES: 'bec2991b-146b-45ea-b743-ac6e8ed9db5a',
  DISABILITIES: '31b32906-38ee-4579-8d99-f13bd1019e59',
  
  // Document categories
  DOCUMENT_CATEGORIES: '550e8400-e29b-41d4-a716-446655440027',
  DOCUMENT_SUBCATEGORIES: '550e8400-e29b-41d4-a716-446655440028',
  
  // Document tree resource (for tree API endpoint)
  DOCUMENT_TREE_RESOURCE: 'bfa87b49-0b4c-489d-8f4e-3b1d61b8c01e',
} as const;

// Helper function to get all list IDs as an array
export function getAllListIds(): string[] {
  return Object.values(LIST_IDS);
}

// Helper function to get commonly used list IDs for forms
export function getFormListIds(): string[] {
  return [
    LIST_IDS.WORK_AUTHORIZATION,
    LIST_IDS.COUNTRIES,
    LIST_IDS.EXPERIENCE_YEARS,
    LIST_IDS.EXPERIENCE_MONTHS,
    LIST_IDS.CURRENCIES,
    LIST_IDS.SALARY_RANGES,
    LIST_IDS.TAX_TERMS,
    LIST_IDS.NOTICE_PERIODS,
    LIST_IDS.APPLICANT_STATUS,
    LIST_IDS.SKILLS,
    LIST_IDS.TECHNOLOGIES,
    LIST_IDS.APPLICATION_SOURCES,
    LIST_IDS.DOCUMENT_TYPE,
    LIST_IDS.DEGREES,
  ];
} 