/**
 * Settings Configuration for Enhanced Admin Panel
 * Defines the structure and navigation for the comprehensive settings system
 */

import {
  <PERSON><PERSON>s,
  Bell,
  Users,
  Shield,
  Database,
  Mail,
  Palette,
  MessageSquare,
  Building2,
  UserCheck,
  Briefcase,
  FileText,
  Lock,
  MapPin,
  Bench,
  UserPlus,
  GraduationCap,
  Globe,
  Zap,
  Link,
  Phone,
  Target,
  Calendar,
  TrendingUp,
  DollarSign,
  FileSpreadsheet,
  Workflow,
  Eye,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RotateCcw,
  Archive,
  Download,
  Upload,
  Search,
  Filter,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart
} from 'lucide-react';

export interface SettingsMenuItem {
  id: string;
  name: string;
  icon: any;
  category: string;
  items: SettingsSubItem[];
  description?: string;
  requiresPermission?: string[];
  isActive: boolean;
}

export interface SettingsSubItem {
  id: string;
  name: string;
  description: string;
  icon: any;
  path: string;
  category: 'left' | 'middle' | 'right';
  requiresPermission?: string[];
  isActive: boolean;
  badge?: string;
  isNew?: boolean;
}

// Main settings navigation structure matching Ceipal admin panel
export const SETTINGS_NAVIGATION: SettingsMenuItem[] = [
  {
    id: 'global_settings',
    name: 'Global Settings',
    icon: Settings,
    category: 'system',
    description: 'Configure application-wide settings',
    items: [
      {
        id: 'custom_fields',
        name: 'Custom Fields',
        description: 'Manage custom fields for various entities',
        icon: Database,
        path: '/settings/global/custom-fields',
        category: 'left',
        isActive: true
      },
      {
        id: 'email_settings',
        name: 'Email Settings',
        description: 'Configure email templates and notifications',
        icon: Mail,
        path: '/settings/global/email-settings',
        category: 'middle',
        isActive: true
      },
      {
        id: 'content_settings',
        name: 'Content Settings',
        description: 'Manage user content and data privacy',
        icon: FileText,
        path: '/settings/global/content-settings',
        category: 'right',
        isActive: true
      },
      {
        id: 'lookups',
        name: 'Lookups',
        description: 'Define and manage lookup values',
        icon: Search,
        path: '/settings/global/lookups',
        category: 'left',
        isActive: true
      },
      {
        id: 'mandatory_fields',
        name: 'Mandatory Fields',
        description: 'Set mandatory fields for data entry',
        icon: CheckCircle,
        path: '/settings/global/mandatory-fields',
        category: 'middle',
        isActive: true
      }
    ],
    isActive: true
  },
  {
    id: 'reminder',
    name: 'Reminder',
    icon: Bell,
    category: 'workflow',
    description: 'Configure automated reminders',
    items: [
      {
        id: 'reminder_settings',
        name: 'Reminder Settings',
        description: 'Configure reminder options',
        icon: Settings,
        path: '/settings/reminder/settings',
        category: 'left',
        isActive: true
      },
      {
        id: 'applicant_profile',
        name: 'Applicant Profile',
        description: 'Manage applicant profiles',
        icon: UserCheck,
        path: '/settings/reminder/applicant-profile',
        category: 'middle',
        isActive: true
      },
      {
        id: 'client_reminder',
        name: 'Client Reminder',
        description: 'Manage client-specific settings for statements',
        icon: Building2,
        path: '/settings/reminder/client-reminder',
        category: 'right',
        isActive: true
      },
      {
        id: 'vendors',
        name: 'Vendors',
        description: 'Manage vendor relationships for placements',
        icon: Users,
        path: '/settings/reminder/vendors',
        category: 'left',
        isActive: true
      },
      {
        id: 'job_posting',
        name: 'Job Posting',
        description: 'Manage job posting reminders',
        icon: Briefcase,
        path: '/settings/reminder/job-posting',
        category: 'middle',
        isActive: true
      },
      {
        id: 'placements',
        name: 'Placements',
        description: 'Manage placement reminders',
        icon: MapPin,
        path: '/settings/reminder/placements',
        category: 'right',
        isActive: true
      },
      {
        id: 'email_statistics',
        name: 'Email Statistics',
        description: 'View email statistics for placements',
        icon: BarChart3,
        path: '/settings/reminder/email-statistics',
        category: 'left',
        isActive: true
      },
      {
        id: 'event_reminder',
        name: 'Event Reminder',
        description: 'Set event reminders for placements',
        icon: Calendar,
        path: '/settings/reminder/event-reminder',
        category: 'middle',
        isActive: true
      },
      {
        id: 'general',
        name: 'General',
        description: 'General reminder settings',
        icon: Settings,
        path: '/settings/reminder/general',
        category: 'right',
        isActive: true
      }
    ],
    isActive: true
  },
  {
    id: 'leads',
    name: 'Leads',
    icon: TrendingUp,
    category: 'business',
    description: 'Lead management and tracking',
    items: [
      {
        id: 'lead_call_type',
        name: 'Lead Call Type',
        description: 'Define types of lead calls',
        icon: Phone,
        path: '/settings/leads/call-type',
        category: 'left',
        isActive: true
      },
      {
        id: 'lead_statuses',
        name: 'Lead Statuses',
        description: 'Manage lead statuses',
        icon: Target,
        path: '/settings/leads/statuses',
        category: 'middle',
        isActive: true
      },
      {
        id: 'lead_sources',
        name: 'Lead Sources',
        description: 'Manage lead sources',
        icon: Globe,
        path: '/settings/leads/sources',
        category: 'right',
        isActive: true
      },
      {
        id: 'lead_settings',
        name: 'Lead Settings',
        description: 'Configure general lead settings',
        icon: Settings,
        path: '/settings/leads/settings',
        category: 'left',
        isActive: true
      }
    ],
    isActive: true
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    icon: MessageSquare,
    category: 'communication',
    description: 'WhatsApp integration and messaging',
    items: [
      {
        id: 'whatsapp_integration',
        name: 'WhatsApp Integration',
        description: 'Integrate your WhatsApp business number',
        icon: Link,
        path: '/settings/whatsapp/integration',
        category: 'left',
        isActive: true
      },
      {
        id: 'whatsapp_templates_category',
        name: 'WhatsApp Templates Category',
        description: 'Categorize WhatsApp message templates',
        icon: FileText,
        path: '/settings/whatsapp/templates-category',
        category: 'middle',
        isActive: true
      },
      {
        id: 'whatsapp_templates',
        name: 'WhatsApp Templates',
        description: 'Create and manage WhatsApp message templates',
        icon: MessageSquare,
        path: '/settings/whatsapp/templates',
        category: 'right',
        isActive: true
      }
    ],
    isActive: true
  },
  {
    id: 'organization',
    name: 'Organization',
    icon: Building2,
    category: 'company',
    description: 'Organization-wide settings and configuration',
    items: [
      {
        id: 'billing_details',
        name: 'Billing Details',
        description: 'View and manage billing information',
        icon: DollarSign,
        path: '/settings/organization/billing-details',
        category: 'left',
        isActive: true
      },
      {
        id: 'business_units',
        name: 'Business Units',
        description: 'Define and manage different business units',
        icon: Building2,
        path: '/settings/organization/business-units',
        category: 'middle',
        isActive: true
      },
      {
        id: 'custom_email_templates',
        name: 'Custom Email Templates',
        description: 'Create and manage custom email templates',
        icon: Mail,
        path: '/settings/organization/custom-email-templates',
        category: 'right',
        isActive: true
      },
      {
        id: 'email_templates',
        name: 'Email Templates',
        description: 'Manage system email templates',
        icon: FileText,
        path: '/settings/organization/email-templates',
        category: 'left',
        isActive: true
      },
      {
        id: 'hierarchy',
        name: 'Hierarchy',
        description: 'Define organizational hierarchy',
        icon: Workflow,
        path: '/settings/organization/hierarchy',
        category: 'middle',
        isActive: true
      },
      {
        id: 'list_view_sorting',
        name: 'List View Sorting',
        description: 'Configure sorting for list views',
        icon: Filter,
        path: '/settings/organization/list-view-sorting',
        category: 'right',
        isActive: true
      },
      {
        id: 'organization_lookups',
        name: 'Organization Lookups',
        description: 'Manage organization-specific lookup values',
        icon: Search,
        path: '/settings/organization/lookups',
        category: 'left',
        isActive: true
      },
      {
        id: 'settings',
        name: 'Settings',
        description: 'General organization settings',
        icon: Settings,
        path: '/settings/organization/settings',
        category: 'middle',
        isActive: true
      },
      {
        id: 'ssl_client_certificate',
        name: 'SSL Client Certificate',
        description: 'Manage SSL client certificates',
        icon: Lock,
        path: '/settings/organization/ssl-client-certificate',
        category: 'right',
        isActive: true
      },
      {
        id: 'submission_tabs_display',
        name: 'Submission Tabs Display',
        description: 'Configure display of submission tabs',
        icon: Eye,
        path: '/settings/organization/submission-tabs-display',
        category: 'left',
        isActive: true
      },
      {
        id: 'data_backup',
        name: 'Data Backup',
        description: 'Manage data backup and recovery',
        icon: Archive,
        path: '/settings/organization/data-backup',
        category: 'middle',
        isActive: true
      },
      {
        id: 'target_settings',
        name: 'Target Settings',
        description: 'Set target goals and configurations',
        icon: Target,
        path: '/settings/organization/target-settings',
        category: 'right',
        isActive: true
      }
    ],
    isActive: true
  }
];

// Settings permissions mapping
export const SETTINGS_PERMISSIONS = {
  ADMIN: 'admin',
  SUPER_ADMIN: 'super_admin',
  HR_MANAGER: 'hr_manager',
  RECRUITER: 'recruiter',
  HIRING_MANAGER: 'hiring_manager'
} as const;

// Settings categories for organization
export const SETTINGS_CATEGORIES = {
  SYSTEM: 'system',
  WORKFLOW: 'workflow', 
  BUSINESS: 'business',
  COMMUNICATION: 'communication',
  COMPANY: 'company',
  SECURITY: 'security',
  INTEGRATION: 'integration'
} as const;
