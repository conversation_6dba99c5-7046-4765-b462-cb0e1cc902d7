/**
 * File-Based Routing Menu Configuration
 * This configuration defines the menu items and their corresponding routes
 * Each path corresponds to a folder structure in src/pages/
 *
 * Convention:
 * - path: '/dashboard' -> src/pages/dashboard/index.tsx
 * - path: '/candidates' -> src/pages/candidates/index.tsx
 * - path: '/jobs' -> src/pages/jobs/index.tsx
 */

export interface MenuConfigurationItem {
  name: string;
  title: string;
  icon: string;
  path: string;
}

export const LEFT_MENU_CONFIGURATION: MenuConfigurationItem[] = [
  {
    name: 'Dashboard',
    title: 'Dashboard',
    icon: 'BarChart3',
    path: '/dashboard'
  },
  {
    name: 'Applicants',
    title: 'Applicants',
    icon: 'Users',
    path: '/applicants'
  },
  {
    name: 'Jobs',
    title: 'Jobs',
    icon: 'Briefcase',
    path: '/jobs'
  },
  {
    name: 'VendorJobs',
    title: 'Vendor Jobs',
    icon: 'Briefcase',
    path: '/vendor-jobs'
  },
  {
    name: 'Messages',
    title: 'Messages',
    icon: 'MessageSquare',
    path: '/messages'
  },
  {
    name: 'Interviews',
    title: 'Interviews',
    icon: 'Calendar',
    path: '/interviews'
  },
  {
    name: 'FormBuilder',
    title: 'Form Builder',
    icon: 'FileText',
    path: '/form-builder'
  },
  {
    name: 'Analytics',
    title: 'Analytics',
    icon: 'TrendingUp',
    path: '/analytics'
  },
  {
    name: 'VendorManagement',
    title: 'Vendor Management',
    icon: 'Building2',
    path: '/vendors'
  },
  {
    name: 'VendorSubmissions',
    title: 'Vendor Submissions',
    icon: 'UserCheck',
    path: '/vendor-submissions'
  },
  {
    name: 'Onboarding',
    title: 'Onboarding',
    icon: 'FileText',
    path: '/onboarding'
  },
  {
    name: 'Users',
    title: 'Users',
    icon: 'Users',
    path: '/users'
  },
  {
    name: 'Roles',
    title: 'Role Management',
    icon: 'Shield',
    path: '/roles'
  },
  {
    name: 'Settings',
    title: 'Settings',
    icon: 'Settings',
    path: '/settings'
  },
  {
    name: 'SuperAdmin',
    title: 'Super Admin',
    icon: 'Shield',
    path: '/super-admin'
  }
];

/**
 * Export the menu configuration as staticLeftMenu for backward compatibility
 */
export const staticLeftMenu = LEFT_MENU_CONFIGURATION;

/**
 * Convert menu path to file path for automatic route discovery
 * Examples:
 * - '/dashboard' -> 'dashboard'
 * - '/candidates' -> 'candidates'
 * - '/vendor-jobs' -> 'vendor-jobs'
 */
export const pathToFolderName = (path: string): string => {
  return path.replace(/^\//, '');
};

/**
 * Get all folder names that should exist based on menu configuration
 */
export const getRequiredFolders = (): string[] => {
  return LEFT_MENU_CONFIGURATION.map(item => pathToFolderName(item.path));
};

/**
 * Find menu configuration by path
 */
export const findMenuConfigByPath = (path: string): MenuConfigurationItem | undefined => {
  return LEFT_MENU_CONFIGURATION.find((item) => item.path === path);
};

/**
 * Check if a folder name corresponds to a menu item
 */
export const isValidMenuFolder = (folderName: string): boolean => {
  return getRequiredFolders().includes(folderName);
};
