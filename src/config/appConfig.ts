/**
 * Application Configuration Management
 * 
 * This module provides environment-specific configuration management
 * with type safety, feature flags, and validation.
 */

// Configuration interfaces
export interface ApiConfig {
  baseUrl: string;
  timeout: number;
  maxRetries: number;
  retryDelay: number;
  validateStatus: (status: number) => boolean;
}

export interface FeatureFlags {
  enableMocking: boolean;
  enableLogging: boolean;
  enableDebugMode: boolean;
  enablePerformanceMonitoring: boolean;
  enableErrorReporting: boolean;
}

export interface SecurityConfig {
  enableCSP: boolean;
  enableHTTPS: boolean;
  allowInsecureConnections: boolean;
  corsEnabled: boolean;
}

export interface AppConfig {
  environment: 'development' | 'staging' | 'production' | 'test' | 'dev-local';
  api: ApiConfig;
  features: FeatureFlags;
  security: SecurityConfig;
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error';
    enableConsole: boolean;
    enableRemote: boolean;
  };
  performance: {
    enableCaching: boolean;
    cacheTimeout: number;
    enableCompression: boolean;
    enableCodeSplitting: boolean;
    preloadStrategy: 'aggressive' | 'lazy' | 'user-interaction';
    chunkSize: 'small' | 'medium' | 'large';
  };
}

// Environment-specific configurations
const developmentConfig: AppConfig = {
  environment: 'development',
  api: {
    baseUrl: import.meta.env.VITE_API_URL ,
    timeout: 30000,
    maxRetries: 3,
    retryDelay: 1000,
    validateStatus: (status: number) => status >= 200 && status < 300,
  },
  features: {
    enableMocking: import.meta.env.VITE_ENABLE_MOCKING === 'true',
    enableLogging: true,
    enableDebugMode: true,
    enablePerformanceMonitoring: true,
    enableErrorReporting: false,
  },
  security: {
    enableCSP: false,
    enableHTTPS: false,
    allowInsecureConnections: true,
    corsEnabled: true,
  },
  logging: {
    level: 'debug',
    enableConsole: true,
    enableRemote: false,
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableCompression: false,
    enableCodeSplitting: true,
    preloadStrategy: 'user-interaction',
    chunkSize: 'medium',
  },
};
;
const stagingConfig: AppConfig = {
  environment: 'staging',
  api: {
    baseUrl: import.meta.env.VITE_API_URL ,
    timeout: 45000,
    maxRetries: 3,
    retryDelay: 2000,
    validateStatus: (status: number) => status >= 200 && status < 300,
  },
  features: {
    enableMocking: false,
    enableLogging: true,
    enableDebugMode: false,
    enablePerformanceMonitoring: true,
    enableErrorReporting: true,
  },
  security: {
    enableCSP: true,
    enableHTTPS: true,
    allowInsecureConnections: false,
    corsEnabled: true,
  },
  logging: {
    level: 'info',
    enableConsole: false,
    enableRemote: true,
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 600000, // 10 minutes
    enableCompression: true,
    enableCodeSplitting: true,
    preloadStrategy: 'lazy',
    chunkSize: 'medium',
  },
};

const productionConfig: AppConfig = {
  environment: 'production',
  api: {
    baseUrl: import.meta.env.VITE_API_URL ,
    timeout: 60000,
    maxRetries: 5,
    retryDelay: 3000,
    validateStatus: (status: number) => status >= 200 && status < 300,
  },
  features: {
    enableMocking: false,
    enableLogging: false,
    enableDebugMode: false,
    enablePerformanceMonitoring: true,
    enableErrorReporting: true,
  },
  security: {
    enableCSP: true,
    enableHTTPS: true,
    allowInsecureConnections: false,
    corsEnabled: false,
  },
  logging: {
    level: 'error',
    enableConsole: false,
    enableRemote: true,
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 1800000, // 30 minutes
    enableCompression: true,
    enableCodeSplitting: true,
    preloadStrategy: 'aggressive',
    chunkSize: 'large',
  },
};

// Dev-local configuration for local development with specific settings
const devLocalConfig: AppConfig = {
  environment: 'dev-local',
  api: {
    baseUrl: import.meta.env.VITE_API_URL,
    timeout: 30000,
    maxRetries: 3,
    retryDelay: 1000,
    validateStatus: (status: number) => status >= 200 && status < 300,
  },
  features: {
    enableMocking: true,
    enableLogging: true,
    enableDebugMode: true,
    enablePerformanceMonitoring: true,
    enableErrorReporting: false,
  },
  security: {
    enableCSP: false,
    enableHTTPS: false,
    allowInsecureConnections: true,
    corsEnabled: true,
  },
  logging: {
    level: 'debug',
    enableConsole: true,
    enableRemote: false,
  },
  performance: {
    enableCaching: true,
    cacheTimeout: 300000, // 5 minutes
    enableCompression: false,
    enableCodeSplitting: true,
    preloadStrategy: 'user-interaction',
    chunkSize: 'small',
  },
};

const testConfig: AppConfig = {
  environment: 'test',
  api: {
    baseUrl: 'http://localhost:8080/ats/api/v1',
    timeout: 10000,
    maxRetries: 1,
    retryDelay: 500,
    validateStatus: (status: number) => status >= 200 && status < 300,
  },
  features: {
    enableMocking: true,
    enableLogging: false,
    enableDebugMode: false,
    enablePerformanceMonitoring: false,
    enableErrorReporting: false,
  },
  security: {
    enableCSP: false,
    enableHTTPS: false,
    allowInsecureConnections: true,
    corsEnabled: true,
  },
  logging: {
    level: 'warn',
    enableConsole: false,
    enableRemote: false,
  },
  performance: {
    enableCaching: false,
    cacheTimeout: 0,
    enableCompression: false,
    enableCodeSplitting: false,
    preloadStrategy: 'lazy',
    chunkSize: 'small',
  },
};

// Configuration validation
function validateConfig(config: AppConfig): void {
  if (!config.api.baseUrl) {
    throw new Error('API base URL is required');
  }
  
  if (config.api.timeout <= 0) {
    throw new Error('API timeout must be positive');
  }
  
  if (config.api.maxRetries < 0) {
    throw new Error('Max retries cannot be negative');
  }
  
  if (config.environment === 'production' && config.features.enableDebugMode) {
    console.warn('Debug mode is enabled in production environment');
  }
  
  if (config.environment === 'production' && config.security.allowInsecureConnections) {
    throw new Error('Insecure connections are not allowed in production');
  }
}

// Configuration factory
export function createConfig(environment?: string): AppConfig {
  // Use MODE for environment-specific config, while keeping NODE_ENV for React's build mode
  const env = environment || import.meta.env.VITE_ENV_MODE || import.meta.env.MODE || 'development';
  console.log('Current environment mode:', env);
  
  let config: AppConfig;
  
  switch (env) {
    case 'dev-local':
      config = devLocalConfig;
      break;
    case 'development':
      config = developmentConfig;
      break;
    case 'staging':
      config = stagingConfig;
      break;
    case 'production':
      config = productionConfig;
      break;
    case 'test':
      config = testConfig;
      break;
    default:
      // Check if env contains 'local' and use devLocalConfig
      if (env.toLowerCase().includes('local')) {
        config = devLocalConfig;
      } else {
        config = developmentConfig;
      }
  }
  
  // Validate configuration
  validateConfig(config);
  
  return config;
}

// Singleton configuration instance
export const config = createConfig();

// Configuration utilities
export const isProduction = () => config.environment === 'production';
export const isDevelopment = () => config.environment === 'development';
export const isTest = () => config.environment === 'test';
export const isStaging = () => config.environment === 'staging';
export const isDevLocal = () => config.environment === 'dev-local';

// Feature flag utilities
export const isFeatureEnabled = (feature: keyof FeatureFlags): boolean => {
  return config.features[feature];
};

// API configuration getters
export const getApiConfig = (): ApiConfig => config.api;
export const getSecurityConfig = (): SecurityConfig => config.security;

// Export specific configurations for backward compatibility
export const API_BASE_URL = config.api.baseUrl;
export const API_TIMEOUT = config.api.timeout;
export const ENABLE_MOCKING = config.features.enableMocking;
export const ENABLE_LOGGING = config.features.enableLogging;

// Runtime configuration updates (for testing purposes)
export function updateConfig(updates: Partial<AppConfig>): void {
  if (config.environment === 'production') {
    console.warn('Configuration updates are not recommended in production');
    return;
  }
  
  Object.assign(config, updates);
  validateConfig(config);
}

// Configuration debugging
export function debugConfig(): void {
  if (config.features.enableDebugMode) {
    console.group('🔧 Application Configuration');
    console.log('Environment:', config.environment);
    console.log('API Base URL:', config.api.baseUrl);
    console.log('Features:', config.features);
    console.log('Security:', config.security);
    console.groupEnd();
  }
}

// Initialize debug logging if enabled
if (config.features.enableDebugMode) {
  debugConfig();
} 