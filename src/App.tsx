import React, { Suspense, useEffect } from "react";
import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import Layout from "./components/Layout";
import ErrorBoundary from "./components/ErrorBoundary";
import Login from "./pages/Login";
import ProtectedRoute from "./components/ProtectedRoute";
import { AuthProvider } from "./contexts/AuthContext";
import { PerformanceMonitor } from "./lib/performance";
import {
  generateFileBasedRoutes,
  logDiscoveredRoutes,
  validateRoutes,
} from "./utils/FileBasedNavigation";

// Special pages that are not part of file-based routing
const ApplicantSubmission = React.lazy(
  () => import("./pages/ApplicantSubmission")
);
const Profile = React.lazy(() => import("./pages/profile"));
const NotFound = React.lazy(() => import("./pages/NotFound"));



// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-3 text-gray-600">Loading...</span>
  </div>
);

const queryClient = new QueryClient();

const App = () => {
  // Initialize performance monitoring and navigation system
  useEffect(() => {
    PerformanceMonitor.init();

    // Log file-based navigation discovery for debugging
    logDiscoveredRoutes();

    // Validate routes and log any issues
    const validation = validateRoutes();
    if (!validation.valid) {
      console.error(
        "❌ Navigation validation failed. Missing files:",
        validation.missing
      );
    }

    // Cleanup on unmount
    return () => {
      PerformanceMonitor.cleanup();
    };
  }, []);

  // Generate file-based routes automatically
  const fileBasedRoutes = generateFileBasedRoutes();

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <TooltipProvider>
            <Toaster />

            <BrowserRouter>
              <Routes>
                {/* Public routes */}
                <Route path="/login" element={<Login />} />

                {/* Protected routes with automatic file-based routing */}
                <Route
                  path="/"
                  element={
                    <ProtectedRoute>
                      <Layout />
                    </ProtectedRoute>
                  }
                >
                  {/* Default redirect from root to dashboard */}
                  <Route index element={<Navigate to="/dashboard" replace />} />

                  {/* Automatically generated routes from LEFT_MENU_CONFIGURATION and file structure */}
                  {fileBasedRoutes.map((route, index) => {
                    const RouteComponent = route.Component;
                    return (
                      <Route
                        key={index}
                        path={route.path}
                        index={route.index}
                        element={
                          <Suspense fallback={<PageLoader />}>
                            {RouteComponent && <RouteComponent />}
                          </Suspense>
                        }
                      />
                    );
                  })}

                  {/* Special routes that don't follow file-based pattern */}
                  <Route
                    path="submit-applicant/:jobId"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        <ApplicantSubmission />
                      </Suspense>
                    }
                  />
                  <Route
                    path="profile"
                    element={
                      <Suspense fallback={<PageLoader />}>
                        <Profile />
                      </Suspense>
                    }
                  />


                </Route>

                {/* 404 route */}
                <Route
                  path="*"
                  element={
                    <Suspense fallback={<PageLoader />}>
                      <NotFound />
                    </Suspense>
                  }
                />
              </Routes>
            </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
