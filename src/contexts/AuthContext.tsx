import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Organization } from '@/types/multiTenant';
import { 
  AuthAPI, 
  TokenManager, 
  LoginCredentials, 
  RegisterData, 
  AuthResponse 
} from '@/lib/auth';
import { AuditLogger } from '@/lib/security';
import { getAllListValues } from '@/api/applicantsApi';
import { GlobalListValueDTO } from '@/types/applicants';

/**
 * AuthContext now provides list values from the /list-value API endpoint.
 * 
 * Usage in components:
 * const { listValues, isListValuesLoading } = useAuth();
 * 
 * The listValues array contains all list values from the API and is automatically
 * fetched when the application starts and after successful login/registration.
 */

// Auth context interface
interface AuthContextType {
  // State
  user: User | null;
  organization: Organization | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  listValues: GlobalListValueDTO[];
  isListValuesLoading: boolean;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  
  // Permissions
  hasPermission: (module: string, action: string) => boolean;
  canAccessModule: (module: string) => boolean;
  
  // Organization helpers
  isVendorOrganization: () => boolean;
  isClientOrganization: () => boolean;
  getUserRole: () => string;
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [listValues, setListValues] = useState<GlobalListValueDTO[]>([]);
  const [isListValuesLoading, setIsListValuesLoading] = useState(false);

  // Computed state
  const isAuthenticated = !!user && !!organization;

  // Initialize auth state on mount
  useEffect(() => {
    initializeAuth();
  }, []);

  // Initialize authentication state
  const initializeAuth = async () => {
    try {
      setIsLoading(true);

      // Check if we have a valid access token
      if (!TokenManager.isAccessTokenValid()) {
        // Try to refresh token if we have a refresh token
        const refreshToken = TokenManager.getRefreshToken();
        if (refreshToken) {
          try {
            const { accessToken } = await AuthAPI.refreshToken();
            TokenManager.setTokens(accessToken, refreshToken);
          } catch (error) {
            console.error('Token refresh failed:', error);
            TokenManager.clearTokens();
            setIsLoading(false);
            return;
          }
        } else {
          setIsLoading(false);
          return;
        }
      }

      // Fetch current user and organization, and load list values
      await Promise.all([
        loadUserData(),
        loadListValues()
      ]);
    } catch (error) {
      console.error('Auth initialization failed:', error);
      TokenManager.clearTokens();
    } finally {
      setIsLoading(false);
    }
  };

  // Load user and organization data
  const loadUserData = async () => {
    try {
      const [userData, orgData] = await Promise.all([
        AuthAPI.getCurrentUser(),
        AuthAPI.getCurrentOrganization(),
      ]);

      setUser(userData);
      setOrganization(orgData);

      // Log successful authentication
      AuditLogger.log('user_authenticated', userData.id, {
        organizationId: orgData.id,
        role: userData.role,
      });
    } catch (error) {
      console.error('Failed to load user data:', error);
      throw error;
    }
  };

  // Load list values data
  const loadListValues = async () => {
    try {
      setIsListValuesLoading(true);
      const response = await getAllListValues();
      
      if (response) {
        // Response is now a direct array, not wrapped in an object
        setListValues(response);
      }
    } catch (error) {
      console.error('Failed to load list values:', error);
      // Don't throw error as this is not critical for app functionality
    } finally {
      setIsListValuesLoading(false);
    }
  };

  // Login function
  const login = async (credentials: LoginCredentials) => {
    try {
      setIsLoading(true);

      const response: AuthResponse = await AuthAPI.login(credentials);
      
      // Store tokens
      TokenManager.setTokens(response.accessToken, response.refreshToken);
      
      // Set user and organization state
      setUser(response.user);
      setOrganization(response.organization);

      // Load list values after successful login
      await loadListValues();

      // Log successful login
      AuditLogger.log('user_login', response.user.id, {
        organizationId: response.organization.id,
        email: credentials.email,
      });
    } catch (error) {
      // Log failed login attempt
      AuditLogger.log('login_failed', 'unknown', {
        email: credentials.email,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Register function
  const register = async (data: RegisterData) => {
    try {
      setIsLoading(true);

      const response: AuthResponse = await AuthAPI.register(data);
      
      // Store tokens
      TokenManager.setTokens(response.accessToken, response.refreshToken);
      
      // Set user and organization state
      setUser(response.user);
      setOrganization(response.organization);

      // Load list values after successful registration
      await loadListValues();

      // Log successful registration
      AuditLogger.log('user_registered', response.user.id, {
        organizationId: response.organization.id,
        email: data.email,
      });
    } catch (error) {
      // Log failed registration attempt
      AuditLogger.log('registration_failed', 'unknown', {
        email: data.email,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = async () => {
    try {
      const userId = user?.id || 'unknown';
      
      // Call logout API
      await AuthAPI.logout();
      
      // Clear local state
      setUser(null);
      setOrganization(null);
      
      // Log logout
      AuditLogger.log('user_logout', userId);
    } catch (error) {
      console.error('Logout failed:', error);
      // Clear local state even if API call fails
      setUser(null);
      setOrganization(null);
      TokenManager.clearTokens();
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    if (!isAuthenticated) return;
    
    try {
      await loadUserData();
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      // If refresh fails, logout user
      await logout();
    }
  };

  // Permission checking
  const hasPermission = (module: string, action: string): boolean => {
    if (!user) return false;

    const permission = user.permissions.find(p => p.module === module);
    return permission ? permission.actions.includes(action as 'create' | 'read' | 'update' | 'delete' | 'approve') : false;
  };

  // Module access checking
  const canAccessModule = (module: string): boolean => {
    return hasPermission(module, 'read');
  };

  // Organization type helpers
  const isVendorOrganization = (): boolean => {
    return organization?.type === 'vendor';
  };

  const isClientOrganization = (): boolean => {
    return organization?.type === 'client';
  };

  // Get user role
  const getUserRole = (): string => {
    return user?.role ?? 'employee';
  };

  // Context value
  const contextValue: AuthContextType = {
    // State
    user,
    organization,
    isLoading,
    isAuthenticated,
    listValues,
    isListValuesLoading,
    
    // Actions
    login,
    register,
    logout,
    refreshUser,
    
    // Permissions
    hasPermission,
    canAccessModule,
    
    // Organization helpers
    isVendorOrganization,
    isClientOrganization,
    getUserRole,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// HOC for components that require authentication
export const withAuth = <P extends object>(
  Component: React.ComponentType<P>
): React.FC<P> => {
  return (props: P) => {
    const { isAuthenticated, isLoading } = useAuth();

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading...</span>
        </div>
      );
    }

    if (!isAuthenticated) {
      // Redirect to login page
      window.location.href = '/login';
      return null;
    }

    return <Component {...props} />;
  };
};
