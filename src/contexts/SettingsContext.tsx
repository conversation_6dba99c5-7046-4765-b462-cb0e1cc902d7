/**
 * Settings Context Provider
 * Manages global settings state and operations
 */

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react';
import { BaseSetting, SettingCategory } from '@/types/settings';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';

interface SettingsState {
  settings: Record<string, BaseSetting>;
  loading: boolean;
  error: string | null;
  lastUpdated: string | null;
}

interface SettingsContextType extends SettingsState {
  getSetting: (category: SettingCategory, key: string) => BaseSetting | null;
  getSettingValue: <T = any>(category: SettingCategory, key: string, defaultValue?: T) => T;
  updateSetting: (category: SettingCategory, key: string, value: any) => Promise<void>;
  createSetting: (setting: Omit<BaseSetting, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  deleteSetting: (category: SettingCategory, key: string) => Promise<void>;
  refreshSettings: () => Promise<void>;
  resetToDefaults: (category?: SettingCategory) => Promise<void>;
}

type SettingsAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_SETTINGS'; payload: BaseSetting[] }
  | { type: 'UPDATE_SETTING'; payload: BaseSetting }
  | { type: 'DELETE_SETTING'; payload: { category: SettingCategory; key: string } }
  | { type: 'RESET_SETTINGS'; payload: BaseSetting[] };

const initialState: SettingsState = {
  settings: {},
  loading: false,
  error: null,
  lastUpdated: null,
};

function settingsReducer(state: SettingsState, action: SettingsAction): SettingsState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };
    
    case 'SET_SETTINGS':
      const settingsMap = action.payload.reduce((acc, setting) => {
        acc[`${setting.category}_${setting.key}`] = setting;
        return acc;
      }, {} as Record<string, BaseSetting>);
      return {
        ...state,
        settings: settingsMap,
        loading: false,
        error: null,
        lastUpdated: new Date().toISOString(),
      };
    
    case 'UPDATE_SETTING':
      return {
        ...state,
        settings: {
          ...state.settings,
          [`${action.payload.category}_${action.payload.key}`]: action.payload,
        },
        lastUpdated: new Date().toISOString(),
      };
    
    case 'DELETE_SETTING':
      const { [action.payload.category + '_' + action.payload.key]: deleted, ...remainingSettings } = state.settings;
      return {
        ...state,
        settings: remainingSettings,
        lastUpdated: new Date().toISOString(),
      };
    
    case 'RESET_SETTINGS':
      const resetSettingsMap = action.payload.reduce((acc, setting) => {
        acc[`${setting.category}_${setting.key}`] = setting;
        return acc;
      }, {} as Record<string, BaseSetting>);
      return {
        ...state,
        settings: resetSettingsMap,
        lastUpdated: new Date().toISOString(),
      };
    
    default:
      return state;
  }
}

const SettingsContext = createContext<SettingsContextType | null>(null);

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(settingsReducer, initialState);
  const { user, organization } = useAuth();
  const { toast } = useToast();

  // Mock API functions - replace with actual API calls
  const fetchSettings = useCallback(async (): Promise<BaseSetting[]> => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: '1',
            organizationId: organization?.id || '',
            category: 'global_settings',
            key: 'company_name',
            value: organization?.name || 'TalentFlow ATS',
            dataType: 'string',
            isEditable: true,
            isRequired: true,
            description: 'Company name displayed across the application',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: user?.id || '',
            updatedBy: user?.id || '',
          },
          {
            id: '2',
            organizationId: organization?.id || '',
            category: 'global_settings',
            key: 'enable_custom_fields',
            value: true,
            dataType: 'boolean',
            isEditable: true,
            isRequired: false,
            description: 'Enable custom fields functionality',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            createdBy: user?.id || '',
            updatedBy: user?.id || '',
          },
        ]);
      }, 1000);
    });
  }, [organization?.id, organization?.name, user?.id]);

  const saveSettingToAPI = useCallback(async (setting: BaseSetting): Promise<BaseSetting> => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          ...setting,
          updatedAt: new Date().toISOString(),
          updatedBy: user?.id || '',
        });
      }, 500);
    });
  }, [user?.id]);

  const deleteSettingFromAPI = useCallback(async (category: SettingCategory, key: string): Promise<void> => {
    // Mock implementation - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve();
      }, 500);
    });
  }, []);

  // Context methods
  const getSetting = useCallback((category: SettingCategory, key: string): BaseSetting | null => {
    return state.settings[`${category}_${key}`] || null;
  }, [state.settings]);

  const getSettingValue = useCallback(<T = any>(
    category: SettingCategory,
    key: string,
    defaultValue?: T
  ): T => {
    const setting = getSetting(category, key);
    return setting ? setting.value : defaultValue;
  }, [getSetting]);

  const updateSetting = useCallback(async (
    category: SettingCategory,
    key: string,
    value: any
  ): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const existingSetting = getSetting(category, key);
      if (!existingSetting) {
        throw new Error(`Setting ${category}_${key} not found`);
      }

      const updatedSetting = {
        ...existingSetting,
        value,
        updatedAt: new Date().toISOString(),
        updatedBy: user?.id || '',
      };

      const savedSetting = await saveSettingToAPI(updatedSetting);
      dispatch({ type: 'UPDATE_SETTING', payload: savedSetting });
      
      toast({
        title: 'Setting Updated',
        description: `${key} has been updated successfully.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update setting';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast({
        title: 'Update Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [getSetting, saveSettingToAPI, user?.id, toast]);

  const createSetting = useCallback(async (
    settingData: Omit<BaseSetting, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      const newSetting: BaseSetting = {
        ...settingData,
        id: `setting_${Date.now()}`, // Mock ID generation
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const savedSetting = await saveSettingToAPI(newSetting);
      dispatch({ type: 'UPDATE_SETTING', payload: savedSetting });
      
      toast({
        title: 'Setting Created',
        description: `${settingData.key} has been created successfully.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create setting';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast({
        title: 'Creation Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [saveSettingToAPI, toast]);

  const deleteSetting = useCallback(async (
    category: SettingCategory,
    key: string
  ): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      
      await deleteSettingFromAPI(category, key);
      dispatch({ type: 'DELETE_SETTING', payload: { category, key } });
      
      toast({
        title: 'Setting Deleted',
        description: `${key} has been deleted successfully.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete setting';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast({
        title: 'Deletion Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [deleteSettingFromAPI, toast]);

  const refreshSettings = useCallback(async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      const settings = await fetchSettings();
      dispatch({ type: 'SET_SETTINGS', payload: settings });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh settings';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
    }
  }, [fetchSettings]);

  const resetToDefaults = useCallback(async (category?: SettingCategory): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      // Mock implementation - replace with actual API call to get default settings
      const defaultSettings = await fetchSettings();
      const filteredSettings = category 
        ? defaultSettings.filter(s => s.category === category)
        : defaultSettings;
      
      dispatch({ type: 'RESET_SETTINGS', payload: filteredSettings });
      
      toast({
        title: 'Settings Reset',
        description: category 
          ? `${category} settings have been reset to defaults.`
          : 'All settings have been reset to defaults.',
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to reset settings';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      toast({
        title: 'Reset Failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, [fetchSettings, toast]);

  // Load settings on mount
  useEffect(() => {
    if (organization?.id) {
      refreshSettings();
    }
  }, [organization?.id, refreshSettings]);

  const contextValue: SettingsContextType = {
    ...state,
    getSetting,
    getSettingValue,
    updateSetting,
    createSetting,
    deleteSetting,
    refreshSettings,
    resetToDefaults,
  };

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
}

export function useSettings() {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
}
