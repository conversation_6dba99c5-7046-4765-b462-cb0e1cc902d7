import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogClose, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';
import { mockApplicants } from '@/data/mockData';
import { mockOrganizations } from '@/data/multiTenantData';
import { getSecureRandomArrayItem, getSecureRandomFloat, getSecureRandomInt } from '@/lib/utils';
import {
  Briefcase,
  Building2,
  Calendar,
  CheckCircle,
  Clock,
  Download,
  Search,
  Star,
  User,
  XCircle
} from 'lucide-react';
import { useState } from 'react';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'interview-scheduled':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Define the vendor submission type
type VendorSubmission = {
  submissionId: string;
  vendorId: string;
  vendorName: string;
  submittedAt: string;
  submissionStatus: 'pending' | 'approved' | 'rejected' | 'interview-scheduled';
  vendorNotes: string;
  clientFeedback: string;
  rating: number;
  expectedSalary: string;
  noticePeriod: string;
  resumeUrl: string;
} & typeof mockApplicants[0];

export default function VendorSubmissions() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [feedback, setFeedback] = useState('');
  const { isClientOrganization, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Vendor Submissions...</h2>
          <p className="text-gray-600">Please wait while we load submission data.</p>
        </div>
      </div>
    );
  }

  // Only show this page to client organizations
  if (!isClientOrganization()) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
          <p className="text-gray-600">This page is only available to client organizations.</p>
        </div>
      </div>
    );
  }

  // Mock vendor submissions - in real app, this would come from API
  const vendorSubmissions: VendorSubmission[] = mockApplicants.map((candidate, index) => ({
    ...candidate,
    submissionId: `sub-${index + 1}`,
    vendorId: mockOrganizations.find(org => org.type === 'vendor')?.id ?? 'org-2',
    vendorName: mockOrganizations.find(org => org.type === 'vendor')?.name ?? 'Elite Recruiters Inc',
    submittedAt: new Date(Date.now() - getSecureRandomFloat(0, 7) * 24 * 60 * 60 * 1000).toISOString(),
    submissionStatus: getSecureRandomArrayItem(['pending', 'approved', 'rejected', 'interview-scheduled']),
    vendorNotes: 'Strong technical background with excellent communication skills. Available for immediate start.',
    clientFeedback: index < 2 ? 'Great profile, scheduling interview' : '',
    rating: index < 2 ? getSecureRandomFloat(4, 5) : 0,
    expectedSalary: '$' + getSecureRandomInt(80000, 140000).toFixed(0),
    noticePeriod: getSecureRandomArrayItem(['Immediate', '2 weeks', '1 month']),
    resumeUrl: '#',
  }));

  const filteredSubmissions = vendorSubmissions.filter(submission =>
    (`${submission.firstName} ${submission.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
     submission.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
     submission.vendorName.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (statusFilter === 'all' || submission.submissionStatus === statusFilter)
  );

  const pendingSubmissions = filteredSubmissions.filter(s => s.submissionStatus === 'pending');
  const reviewedSubmissions = filteredSubmissions.filter(s => s.submissionStatus !== 'pending');

  const handleApprove = (submissionId: string) => {
    // In real app, this would call API
    alert('Applicant approved! Vendor has been notified.');
  };

  const handleReject = (submissionId: string, reason: string) => {
    console.log('Rejecting submission:', submissionId, 'Reason:', reason);
    // In real app, this would call API
    alert('Applicant rejected. Vendor has been notified with feedback.');
  };

  const handleScheduleInterview = (submissionId: string) => {
    console.log('Scheduling interview for:', submissionId);
    // Navigate to interviews page where they can schedule
    window.location.href = '/interviews';
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Submissions</h1>
          <p className="text-gray-600">Review applicants submitted by your vendor partners</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Review</p>
                <p className="text-2xl font-semibold text-gray-900">{pendingSubmissions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Approved</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {filteredSubmissions.filter(s => s.submissionStatus === 'approved').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Calendar className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Interviews</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {filteredSubmissions.filter(s => s.submissionStatus === 'interview-scheduled').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Star className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {reviewedSubmissions.length > 0 ? (reviewedSubmissions.reduce((acc, s) => acc + s.rating, 0) / reviewedSubmissions.length).toFixed(1) : '0.0'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">Pending Review ({pendingSubmissions.length})</TabsTrigger>
          <TabsTrigger value="reviewed">Reviewed ({reviewedSubmissions.length})</TabsTrigger>
          <TabsTrigger value="all">All Submissions ({filteredSubmissions.length})</TabsTrigger>
        </TabsList>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search applicants or vendors..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="interview-scheduled">Interview Scheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <TabsContent value="pending" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {pendingSubmissions.map((submission) => (
              <Card key={submission.submissionId} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={submission.avatar} />
                        <AvatarFallback>
                          {submission.firstName[0]}{submission.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{submission.firstName} {submission.lastName}</CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Briefcase className="mr-1 h-3 w-3" />
                            {submission.position}
                          </div>
                          <div className="flex items-center">
                            <Building2 className="mr-1 h-3 w-3" />
                            {submission.vendorName}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            {new Date(submission.submittedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(submission.submissionStatus)}>
                      {submission.submissionStatus.replace('-', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Experience:</span>
                        <p className="font-medium">{submission.experience}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Expected Salary:</span>
                        <p className="font-medium">{submission.expectedSalary}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Notice Period:</span>
                        <p className="font-medium">{submission.noticePeriod}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Department:</span>
                        <p className="font-medium">{submission.department}</p>
                      </div>
                    </div>

                    <div>
                      <span className="text-sm text-gray-500">Skills:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {submission.skills.slice(0, 5).map((skill) => (
                          <Badge key={skill} variant="secondary" className="text-xs">
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {submission.vendorNotes && (
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <h4 className="text-sm font-medium text-blue-900 mb-1">Vendor Notes:</h4>
                        <p className="text-sm text-blue-800">{submission.vendorNotes}</p>
                      </div>
                    )}

                    <div className="flex space-x-2">
                      <Button 
                        size="sm" 
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => handleApprove(submission.submissionId)}
                      >
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Approve
                      </Button>
                      
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                            <XCircle className="mr-1 h-3 w-3" />
                            Reject
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Reject Applicant</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div>
                              <Label htmlFor="rejection-reason">Reason for Rejection</Label>
                              <Textarea 
                                id="rejection-reason"
                                placeholder="Please provide feedback for the vendor..."
                                value={feedback}
                                onChange={(e) => setFeedback(e.target.value)}
                                rows={4}
                              />
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => {
                                  handleReject(submission.submissionId, feedback);
                                  setFeedback('');
                                }}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Reject Applicant
                              </Button>
                              <DialogClose asChild>
                                <Button variant="outline">Cancel</Button>
                              </DialogClose>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>

                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => handleScheduleInterview(submission.submissionId)}
                      >
                        <Calendar className="mr-1 h-3 w-3" />
                        Schedule Interview
                      </Button>

                      <Button size="sm" variant="outline">
                        <Download className="mr-1 h-3 w-3" />
                        Resume
                      </Button>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button size="sm" variant="outline">
                            <User className="mr-1 h-3 w-3" />
                            View Details
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl">
                          <DialogHeader>
                            <DialogTitle>{submission.firstName} {submission.lastName} - Applicant Details</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="text-gray-500">Email:</span>
                                <p className="font-medium">{submission.email}</p>
                              </div>
                              <div>
                                <span className="text-gray-500">Phone:</span>
                                <p className="font-medium">{submission.phone}</p>
                              </div>
                              <div>
                                <span className="text-gray-500">Current Company:</span>
                                <p className="font-medium">TechCorp Inc</p>
                              </div>
                              <div>
                                <span className="text-gray-500">Education:</span>
                                <p className="font-medium">BS Computer Science, MIT</p>
                              </div>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500">Full Skill Set:</span>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {submission.skills.map((skill) => (
                                  <Badge key={skill} variant="secondary" className="text-xs">
                                    {skill}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          </div>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="reviewed" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {reviewedSubmissions.map((submission) => (
              <Card key={submission.submissionId} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={submission.avatar} />
                        <AvatarFallback>
                          {submission.firstName[0]}{submission.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{submission.firstName} {submission.lastName}</CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Briefcase className="mr-1 h-3 w-3" />
                            {submission.position}
                          </div>
                          <div className="flex items-center">
                            <Building2 className="mr-1 h-3 w-3" />
                            {submission.vendorName}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                      <Badge className={getStatusColor(submission.submissionStatus)}>
                        {submission.submissionStatus.replace('-', ' ')}
                      </Badge>
                      {submission.rating > 0 && (
                        <div className="flex items-center">
                          <Star className="h-3 w-3 text-yellow-400 fill-current" />
                          <span className="ml-1 text-sm">{submission.rating.toFixed(1)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {submission.clientFeedback && (
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <h4 className="text-sm font-medium text-gray-900 mb-1">Your Feedback:</h4>
                      <p className="text-sm text-gray-700">{submission.clientFeedback}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {filteredSubmissions.map((submission) => (
              <Card key={submission.submissionId} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={submission.avatar} />
                        <AvatarFallback>
                          {submission.firstName[0]}{submission.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{submission.firstName} {submission.lastName}</CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Briefcase className="mr-1 h-3 w-3" />
                            {submission.position}
                          </div>
                          <div className="flex items-center">
                            <Building2 className="mr-1 h-3 w-3" />
                            {submission.vendorName}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            {new Date(submission.submittedAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(submission.submissionStatus)}>
                      {submission.submissionStatus.replace('-', ' ')}
                    </Badge>
                  </div>
                </CardHeader>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
