import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { mockApplicants, mockOnboardingTasks } from '@/data/mockData';
import { OnboardingTask } from '@/types';
import {
  AlertCircle,
  BookOpen,
  CheckCircle,
  Clock,
  FileText,
  Plus,
  Settings,
  Users
} from 'lucide-react';
import { useState } from 'react';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'in-progress':
      return 'bg-blue-100 text-blue-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'documentation':
      return FileText;
    case 'training':
      return BookOpen;
    case 'setup':
      return Settings;
    case 'orientation':
      return Users;
    default:
      return FileText;
  }
};

const getOnboardingProgress = (stage?: string) => {
  switch (stage) {
    case 'documentation':
      return 25;
    case 'training':
      return 50;
    case 'setup':
      return 75;
    case 'completed':
      return 100;
    default:
      return 0;
  }
};

export default function Onboarding() {
  const [tasks] = useState<OnboardingTask[]>(mockOnboardingTasks);
  const onboardingApplicants = mockApplicants.filter(c => c.status === 'onboarding');

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Onboarding</h1>
          <p className="text-gray-600">Manage new hire onboarding process</p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Task
        </Button>
      </div>

      <Tabs defaultValue="applicants" className="space-y-4">
        <TabsList>
          <TabsTrigger value="applicants">Applicants</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="applicants" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {onboardingApplicants.map((applicant) => (
              <Card key={applicant.id}>
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src={applicant.avatar} />
                      <AvatarFallback>
                        {applicant.firstName[0]}{applicant.lastName[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <CardTitle className="text-lg">
                        {applicant.firstName} {applicant.lastName}
                      </CardTitle>
                      <p className="text-sm text-gray-600">{applicant.position}</p>
                    </div>
                    <Badge className="bg-green-100 text-green-800">
                      {applicant.onboardingStage}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Onboarding Progress</span>
                        <span>{getOnboardingProgress(applicant.onboardingStage)}%</span>
                      </div>
                      <Progress value={getOnboardingProgress(applicant.onboardingStage)} />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Start Date:</span>
                        <p className="font-medium">{applicant.startDate}</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Department:</span>
                        <p className="font-medium">{applicant.department}</p>
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">View Tasks</Button>
                      <Button size="sm">Update Progress</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            {tasks.map((task) => {
              const applicant = mockApplicants.find(c => c.id === task.candidateId);
              const CategoryIcon = getCategoryIcon(task.category);
              
              return (
                <Card key={task.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        <CategoryIcon className="h-5 w-5 text-gray-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="space-y-1">
                            <h3 className="font-semibold text-gray-900">{task.title}</h3>
                            <p className="text-sm text-gray-600">{task.description}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Assigned to: {task.assignedTo}</span>
                              <span>Due: {new Date(task.dueDate).toLocaleDateString()}</span>
                            </div>
                            {applicant && (
                              <div className="flex items-center space-x-2 mt-2">
                                <Avatar className="h-6 w-6">
                                  <AvatarImage src={applicant.avatar} />
                                  <AvatarFallback className="text-xs">
                                    {applicant.firstName[0]}{applicant.lastName[0]}
                                  </AvatarFallback>
                                </Avatar>
                                <span className="text-sm text-gray-600">
                                  {applicant.firstName} {applicant.lastName}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className={getStatusColor(task.status)}>
                              {task.status}
                            </Badge>
                            {task.status === 'completed' && (
                              <CheckCircle className="h-5 w-5 text-green-500" />
                            )}
                            {task.status === 'in-progress' && (
                              <Clock className="h-5 w-5 text-blue-500" />
                            )}
                            {task.status === 'pending' && new Date(task.dueDate) < new Date() && (
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Engineering Onboarding</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Complete onboarding template for software engineers
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <span>Documentation (5 tasks)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Settings className="h-4 w-4 text-gray-400" />
                    <span>Setup (7 tasks)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-gray-400" />
                    <span>Training (4 tasks)</span>
                  </div>
                </div>
                <Button size="sm" className="mt-4" variant="outline">
                  Use Template
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales Onboarding</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 mb-4">
                  Onboarding process for sales team members
                </p>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-4 w-4 text-gray-400" />
                    <span>Documentation (3 tasks)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <BookOpen className="h-4 w-4 text-gray-400" />
                    <span>Training (6 tasks)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span>Orientation (3 tasks)</span>
                  </div>
                </div>
                <Button size="sm" className="mt-4" variant="outline">
                  Use Template
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}