import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { User } from '@/types/multiTenant';
import { toast } from '@/hooks/use-toast';
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Users, 
  Briefcase, 
  Calendar,
  MessageSquare,
  Save,
  Loader2,
} from 'lucide-react';

interface NotificationsTabProps {
  user: User;
}

interface NotificationSettings {
  email: {
    newApplicants: boolean;
    interviewReminders: boolean;
    jobUpdates: boolean;
    teamMessages: boolean;
    systemUpdates: boolean;
    weeklyReports: boolean;
  };
  push: {
    newApplicants: boolean;
    interviewReminders: boolean;
    jobUpdates: boolean;
    teamMessages: boolean;
    urgentAlerts: boolean;
  };
  frequency: {
    emailDigest: 'immediate' | 'daily' | 'weekly' | 'never';
    reminderTiming: '15min' | '30min' | '1hour' | '2hours';
  };
  doNotDisturb: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
}

export default function NotificationsTab({ user }: NotificationsTabProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings>({
    email: {
      newApplicants: true,
      interviewReminders: true,
      jobUpdates: true,
      teamMessages: false,
      systemUpdates: true,
      weeklyReports: true,
    },
    push: {
      newApplicants: true,
      interviewReminders: true,
      jobUpdates: false,
      teamMessages: true,
      urgentAlerts: true,
    },
    frequency: {
      emailDigest: 'daily',
      reminderTiming: '30min',
    },
    doNotDisturb: {
      enabled: false,
      startTime: '18:00',
      endTime: '09:00',
    },
  });

  const handleEmailToggle = (key: keyof NotificationSettings['email']) => {
    setSettings(prev => ({
      ...prev,
      email: {
        ...prev.email,
        [key]: !prev.email[key],
      },
    }));
  };

  const handlePushToggle = (key: keyof NotificationSettings['push']) => {
    setSettings(prev => ({
      ...prev,
      push: {
        ...prev.push,
        [key]: !prev.push[key],
      },
    }));
  };

  const handleFrequencyChange = (key: keyof NotificationSettings['frequency'], value: string) => {
    setSettings(prev => ({
      ...prev,
      frequency: {
        ...prev.frequency,
        [key]: value,
      },
    }));
  };

  const handleDoNotDisturbToggle = () => {
    setSettings(prev => ({
      ...prev,
      doNotDisturb: {
        ...prev.doNotDisturb,
        enabled: !prev.doNotDisturb.enabled,
      },
    }));
  };

  const handleDoNotDisturbTimeChange = (field: 'startTime' | 'endTime', value: string) => {
    setSettings(prev => ({
      ...prev,
      doNotDisturb: {
        ...prev.doNotDisturb,
        [field]: value,
      },
    }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Here you would call the API to save notification settings
      // await updateNotificationSettings(user.id, settings);

      toast({
        title: "Notification settings saved",
        description: "Your notification preferences have been updated successfully.",
      });

    } catch (error) {
      toast({
        title: "Error saving settings",
        description: "There was a problem saving your notification preferences. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Email Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Mail className="mr-2 h-5 w-5" />
            Email Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4 text-gray-500" />
                  <Label>New Applicants</Label>
                </div>
                <p className="text-sm text-gray-600">Get notified when new candidates apply</p>
              </div>
              <Switch
                checked={settings.email.newApplicants}
                onCheckedChange={() => handleEmailToggle('newApplicants')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <Label>Interview Reminders</Label>
                </div>
                <p className="text-sm text-gray-600">Reminders for upcoming interviews</p>
              </div>
              <Switch
                checked={settings.email.interviewReminders}
                onCheckedChange={() => handleEmailToggle('interviewReminders')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Briefcase className="h-4 w-4 text-gray-500" />
                  <Label>Job Updates</Label>
                </div>
                <p className="text-sm text-gray-600">Updates on job postings and applications</p>
              </div>
              <Switch
                checked={settings.email.jobUpdates}
                onCheckedChange={() => handleEmailToggle('jobUpdates')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4 text-gray-500" />
                  <Label>Team Messages</Label>
                </div>
                <p className="text-sm text-gray-600">Messages from team members</p>
              </div>
              <Switch
                checked={settings.email.teamMessages}
                onCheckedChange={() => handleEmailToggle('teamMessages')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Bell className="h-4 w-4 text-gray-500" />
                  <Label>System Updates</Label>
                </div>
                <p className="text-sm text-gray-600">Important system announcements</p>
              </div>
              <Switch
                checked={settings.email.systemUpdates}
                onCheckedChange={() => handleEmailToggle('systemUpdates')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <Label>Weekly Reports</Label>
                </div>
                <p className="text-sm text-gray-600">Weekly summary of activities</p>
              </div>
              <Switch
                checked={settings.email.weeklyReports}
                onCheckedChange={() => handleEmailToggle('weeklyReports')}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Smartphone className="mr-2 h-5 w-5" />
            Push Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>New Applicants</Label>
                <p className="text-sm text-gray-600">Instant notifications for new applications</p>
              </div>
              <Switch
                checked={settings.push.newApplicants}
                onCheckedChange={() => handlePushToggle('newApplicants')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Interview Reminders</Label>
                <p className="text-sm text-gray-600">Push reminders for interviews</p>
              </div>
              <Switch
                checked={settings.push.interviewReminders}
                onCheckedChange={() => handlePushToggle('interviewReminders')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Job Updates</Label>
                <p className="text-sm text-gray-600">Push notifications for job changes</p>
              </div>
              <Switch
                checked={settings.push.jobUpdates}
                onCheckedChange={() => handlePushToggle('jobUpdates')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Team Messages</Label>
                <p className="text-sm text-gray-600">Instant team communication alerts</p>
              </div>
              <Switch
                checked={settings.push.teamMessages}
                onCheckedChange={() => handlePushToggle('teamMessages')}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label>Urgent Alerts</Label>
                <p className="text-sm text-gray-600">Critical system alerts</p>
              </div>
              <Switch
                checked={settings.push.urgentAlerts}
                onCheckedChange={() => handlePushToggle('urgentAlerts')}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Notification Frequency */}
      <Card>
        <CardHeader>
          <CardTitle>Notification Frequency</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Email Digest</Label>
              <Select
                value={settings.frequency.emailDigest}
                onValueChange={(value) => handleFrequencyChange('emailDigest', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Immediate</SelectItem>
                  <SelectItem value="daily">Daily Digest</SelectItem>
                  <SelectItem value="weekly">Weekly Digest</SelectItem>
                  <SelectItem value="never">Never</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Reminder Timing</Label>
              <Select
                value={settings.frequency.reminderTiming}
                onValueChange={(value) => handleFrequencyChange('reminderTiming', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15min">15 minutes before</SelectItem>
                  <SelectItem value="30min">30 minutes before</SelectItem>
                  <SelectItem value="1hour">1 hour before</SelectItem>
                  <SelectItem value="2hours">2 hours before</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Do Not Disturb */}
      <Card>
        <CardHeader>
          <CardTitle>Do Not Disturb</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label>Enable Do Not Disturb</Label>
              <p className="text-sm text-gray-600">Pause notifications during specified hours</p>
            </div>
            <Switch
              checked={settings.doNotDisturb.enabled}
              onCheckedChange={handleDoNotDisturbToggle}
            />
          </div>

          {settings.doNotDisturb.enabled && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="space-y-2">
                <Label>Start Time</Label>
                <Select
                  value={settings.doNotDisturb.startTime}
                  onValueChange={(value) => handleDoNotDisturbTimeChange('startTime', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => {
                      const hour = i.toString().padStart(2, '0');
                      return (
                        <SelectItem key={`${hour}:00`} value={`${hour}:00`}>
                          {hour}:00
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>End Time</Label>
                <Select
                  value={settings.doNotDisturb.endTime}
                  onValueChange={(value) => handleDoNotDisturbTimeChange('endTime', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 24 }, (_, i) => {
                      const hour = i.toString().padStart(2, '0');
                      return (
                        <SelectItem key={`${hour}:00`} value={`${hour}:00`}>
                          {hour}:00
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Notification Settings
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
