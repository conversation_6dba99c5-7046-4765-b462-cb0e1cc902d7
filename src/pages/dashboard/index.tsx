import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DefaultDashboard from '@/components/dashboard/DefaultDashboard';
import ClientDashboard from '@/components/dashboard/ClientDashboard';
import VendorDashboard from '@/components/dashboard/VendorDashboard';

const DashboardPage: React.FC = () => {
  const { organization } = useAuth();

  // Show appropriate dashboard based on organization type
  if (organization?.type === 'client') {
    return <ClientDashboard />;
  }
  
  if (organization?.type === 'vendor') {
    return <VendorDashboard />;
  }
  
  // Default dashboard for other cases
  return <DefaultDashboard />;
};

export default DashboardPage;
