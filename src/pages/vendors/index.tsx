import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '@/contexts/AuthContext';
import { mockOrganizations, mockVendorClientRelationships } from '@/data/multiTenantData';
import {
  CheckCircle,
  Clock,
  Mail,
  MapPin,
  Plus,
  Search,
  Star,
  TrendingUp,
  Users,
  XCircle
} from 'lucide-react';
import { useState } from 'react';

const getStatusColor = (status: string) => {
  switch (status) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    case 'suspended':
      return 'bg-gray-100 text-gray-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getTierColor = (tier: string) => {
  switch (tier) {
    case 'platinum':
      return 'bg-purple-100 text-purple-800';
    case 'gold':
      return 'bg-yellow-100 text-yellow-800';
    case 'silver':
      return 'bg-gray-100 text-gray-800';
    case 'bronze':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function VendorManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const { organization, isClientOrganization, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Vendor Management...</h2>
          <p className="text-gray-600">Please wait while we load your vendor data.</p>
        </div>
      </div>
    );
  }

  // Only show this page to client organizations
  if (!isClientOrganization()) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Access Denied</h2>
          <p className="text-gray-600">This page is only available to client organizations.</p>
        </div>
      </div>
    );
  }

  const vendorOrganizations = mockOrganizations.filter(org => org.type === 'vendor');
  const relationships = mockVendorClientRelationships.filter(rel => rel.clientId === organization?.id);
  
  const connectedVendors = vendorOrganizations.filter(vendor => 
    relationships.some(rel => rel.vendorId === vendor.id && rel.status === 'approved')
  );
  
  const pendingVendors = vendorOrganizations.filter(vendor => 
    relationships.some(rel => rel.vendorId === vendor.id && rel.status === 'pending')
  );

  const availableVendors = vendorOrganizations.filter(vendor => 
    !relationships.some(rel => rel.vendorId === vendor.id)
  );

  const filteredVendors = connectedVendors.filter(vendor =>
    vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.vendorProfile?.specializations.some(spec => 
      spec.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Vendor Management</h1>
          <p className="text-gray-600">Manage your recruiting vendor partnerships</p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Invite Vendor
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Invite New Vendor</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="vendorEmail">Vendor Email</Label>
                <Input id="vendorEmail" placeholder="<EMAIL>" />
              </div>
              <div>
                <Label htmlFor="message">Invitation Message</Label>
                <Textarea 
                  id="message" 
                  placeholder="We'd like to invite you to partner with us for our recruiting needs..."
                  rows={4}
                />
              </div>
              <div className="flex space-x-2">
                <Button>Send Invitation</Button>
                <Button variant="outline">Cancel</Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Connected Vendors</p>
                <p className="text-2xl font-semibold text-gray-900">{connectedVendors.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-yellow-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending Requests</p>
                <p className="text-2xl font-semibold text-gray-900">{pendingVendors.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Success Rate</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {connectedVendors.length > 0 ? Math.round(connectedVendors.reduce((acc, v) => acc + (v.vendorProfile?.successRate ?? 0), 0) / connectedVendors.length) : 0}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Recruiters</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {connectedVendors.reduce((acc, v) => acc + (v.vendorProfile?.numberOfRecruiters ?? 0), 0)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="connected" className="space-y-4">
        <TabsList>
          <TabsTrigger value="connected">Connected Vendors</TabsTrigger>
          <TabsTrigger value="pending">Pending Requests</TabsTrigger>
          <TabsTrigger value="available">Available Vendors</TabsTrigger>
        </TabsList>

        <TabsContent value="connected" className="space-y-4">
          {/* Search and Filter */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search vendors..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filter by specialization" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Specializations</SelectItem>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Connected Vendors Grid */}
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {filteredVendors.map((vendor) => {
              const relationship = relationships.find(rel => rel.vendorId === vendor.id);
              const profile = vendor.vendorProfile;
              
              return (
                <Card key={vendor.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={vendor.logo} />
                          <AvatarFallback>
                            {vendor.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-lg">{vendor.name}</CardTitle>
                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <MapPin className="h-3 w-3" />
                            <span>{vendor.address.split(',').slice(-2).join(',').trim()}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        <Badge className={getTierColor(profile?.vendorTier ?? 'bronze')}>
                          {profile?.vendorTier ?? 'Bronze'}
                        </Badge>
                        <Badge className={getStatusColor(relationship?.status ?? 'pending')}>
                          {relationship?.status ?? 'Pending'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-500">Success Rate:</span>
                          <p className="font-medium">{profile?.successRate ?? 0}%</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Avg Time to Fill:</span>
                          <p className="font-medium">{profile?.averageTimeToFill ?? 0} days</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Recruiters:</span>
                          <p className="font-medium">{profile?.numberOfRecruiters ?? 0}</p>
                        </div>
                        <div>
                          <span className="text-gray-500">Years in Business:</span>
                          <p className="font-medium">{profile?.yearsInBusiness ?? 0}</p>
                        </div>
                      </div>
                      
                      <div>
                        <span className="text-sm text-gray-500">Specializations:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {profile?.specializations.map((spec) => (
                            <Badge key={spec} variant="secondary" className="text-xs">
                              {spec}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      {relationship?.performanceMetrics && (
                        <div className="bg-gray-50 p-3 rounded-lg">
                          <h4 className="text-sm font-medium mb-2">Performance Metrics</h4>
                          <div className="grid grid-cols-2 gap-2 text-xs">
                            <div>
                              <span className="text-gray-500">Submissions:</span>
                              <span className="ml-1 font-medium">{relationship.performanceMetrics.totalSubmissions}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Hires:</span>
                              <span className="ml-1 font-medium">{relationship.performanceMetrics.totalHires}</span>
                            </div>
                            <div>
                              <span className="text-gray-500">Satisfaction:</span>
                              <div className="flex items-center ml-1">
                                <Star className="h-3 w-3 text-yellow-400 fill-current" />
                                <span className="ml-1 font-medium">{relationship.performanceMetrics.clientSatisfactionRating}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Mail className="mr-1 h-3 w-3" />
                          Message
                        </Button>
                        <Button size="sm" variant="outline">
                          View Details
                        </Button>
                        <Button size="sm">
                          Assign Job
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="pending" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {pendingVendors.map((vendor) => (
              <Card key={vendor.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={vendor.logo} />
                        <AvatarFallback>
                          {vendor.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{vendor.name}</CardTitle>
                        <p className="text-sm text-gray-600">{vendor.industry}</p>
                      </div>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-800">
                      Pending Approval
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Success Rate:</span>
                        <p className="font-medium">{vendor.vendorProfile?.successRate ?? 0}%</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Experience:</span>
                        <p className="font-medium">{vendor.vendorProfile?.yearsInBusiness ?? 0} years</p>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-sm text-gray-500">Specializations:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {vendor.vendorProfile?.specializations.map((spec) => (
                          <Badge key={spec} variant="secondary" className="text-xs">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <CheckCircle className="mr-1 h-3 w-3" />
                        Approve
                      </Button>
                      <Button size="sm" variant="outline" className="text-red-600 border-red-600 hover:bg-red-50">
                        <XCircle className="mr-1 h-3 w-3" />
                        Reject
                      </Button>
                      <Button size="sm" variant="outline">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="available" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
            {availableVendors.map((vendor) => (
              <Card key={vendor.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src={vendor.logo} />
                        <AvatarFallback>
                          {vendor.name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <CardTitle className="text-lg">{vendor.name}</CardTitle>
                        <p className="text-sm text-gray-600">{vendor.industry}</p>
                      </div>
                    </div>
                    <Badge className={getTierColor(vendor.vendorProfile?.vendorTier ?? 'bronze')}>
                      {vendor.vendorProfile?.vendorTier ?? 'Bronze'}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Success Rate:</span>
                        <p className="font-medium">{vendor.vendorProfile?.successRate ?? 0}%</p>
                      </div>
                      <div>
                        <span className="text-gray-500">Min Rate:</span>
                        <p className="font-medium">${vendor.vendorProfile?.minimumRate ?? 0}/hr</p>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-sm text-gray-500">Specializations:</span>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {vendor.vendorProfile?.specializations.map((spec) => (
                          <Badge key={spec} variant="secondary" className="text-xs">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div className="flex space-x-2">
                      <Button size="sm">
                        <Plus className="mr-1 h-3 w-3" />
                        Connect
                      </Button>
                      <Button size="sm" variant="outline">
                        View Profile
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
