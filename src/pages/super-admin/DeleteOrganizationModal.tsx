import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { AlertTriangle, Building2, Users, Briefcase } from 'lucide-react';
import { Organization } from '@/types/multiTenant';

interface DeleteOrganizationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organization: Organization | null;
  onOrganizationDeleted: (organizationId: string) => void;
}

export default function DeleteOrganizationModal({
  open,
  onOpenChange,
  organization,
  onOrganizationDeleted,
}: DeleteOrganizationModalProps) {
  const [confirmationText, setConfirmationText] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  if (!organization) return null;

  const isConfirmationValid = confirmationText === organization.name;

  const handleDelete = async () => {
    if (!isConfirmationValid) {
      toast.error('Please type the organization name to confirm deletion');
      return;
    }

    setIsDeleting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Call the callback to delete the organization
      onOrganizationDeleted(organization.id);

      // Reset state and close modal
      setConfirmationText('');
      onOpenChange(false);

      toast.success(`Organization "${organization.name}" has been deleted successfully`);
    } catch (error) {
      console.error('Error deleting organization:', error);
      toast.error('Failed to delete organization. Please try again.');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancel = () => {
    setConfirmationText('');
    onOpenChange(false);
  };

  // Mock data for demonstration - in real app, this would come from API
  const organizationStats = {
    userCount: Math.floor(Math.random() * 50) + 1,
    jobCount: Math.floor(Math.random() * 20) + 1,
    activeJobs: Math.floor(Math.random() * 10) + 1,
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="h-6 w-6 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-xl text-red-900">Delete Organization</DialogTitle>
              <DialogDescription className="text-red-700">
                This action cannot be undone. This will permanently delete the organization.
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Organization Info */}
          <Card className="border-red-200">
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                  {organization.logo ? (
                    <img
                      src={organization.logo}
                      alt={organization.name}
                      className="w-12 h-12 rounded-lg object-cover"
                    />
                  ) : (
                    <Building2 className="h-8 w-8 text-blue-600" />
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{organization.name}</h3>
                  <div className="flex items-center space-x-2 mt-1">
                    <Badge variant={organization.type === 'client' ? 'default' : 'secondary'}>
                      {organization.type}
                    </Badge>
                    <Badge variant="outline">
                      {organization.subscriptionPlan}
                    </Badge>
                    <Badge variant={organization.isActive ? 'outline' : 'destructive'}>
                      {organization.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {organization.industry} • {organization.size} employees
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Impact Warning */}
          <Card className="border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-900 mb-2">
                    Deleting this organization will also remove:
                  </h4>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-amber-600" />
                      <span className="text-amber-800">
                        {organizationStats.userCount} users
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4 text-amber-600" />
                      <span className="text-amber-800">
                        {organizationStats.jobCount} total jobs
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Briefcase className="h-4 w-4 text-amber-600" />
                      <span className="text-amber-800">
                        {organizationStats.activeJobs} active jobs
                      </span>
                    </div>
                  </div>
                  <p className="text-amber-800 mt-3 text-sm">
                    All associated data including user accounts, job postings, applications, 
                    and organization settings will be permanently deleted.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Confirmation Input */}
          <div className="space-y-3">
            <Label htmlFor="confirmation" className="text-sm font-medium">
              To confirm deletion, type the organization name:{' '}
              <span className="font-semibold text-red-600">{organization.name}</span>
            </Label>
            <Input
              id="confirmation"
              value={confirmationText}
              onChange={(e) => setConfirmationText(e.target.value)}
              placeholder={`Type "${organization.name}" to confirm`}
              className={`${
                confirmationText && !isConfirmationValid 
                  ? 'border-red-500 focus:border-red-500' 
                  : ''
              }`}
            />
            {confirmationText && !isConfirmationValid && (
              <p className="text-sm text-red-500">
                Organization name doesn't match. Please type "{organization.name}" exactly.
              </p>
            )}
          </div>

          {/* Additional Warnings */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h5 className="font-medium text-gray-900 mb-2">Before you delete:</h5>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• Consider deactivating the organization instead of deleting it</li>
              <li>• Export any important data you might need later</li>
              <li>• Notify users about the upcoming deletion</li>
              <li>• Ensure all pending transactions are completed</li>
            </ul>
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button variant="outline" onClick={handleCancel} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleDelete}
            disabled={!isConfirmationValid || isDeleting}
          >
            {isDeleting ? 'Deleting...' : 'Delete Organization'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
