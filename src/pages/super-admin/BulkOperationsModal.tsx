import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import {
  Building2,
  Users,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Zap,
  Download,
  Upload,
  Mail,
  Settings,
} from 'lucide-react';
import { Organization, SubscriptionPlan } from '@/types/multiTenant';
import { toast } from 'sonner';

interface BulkOperationsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organizations: Organization[];
  onBulkUpdate: (organizationIds: string[], updates: Partial<Organization>) => void;
}

type BulkOperation = 
  | 'activate'
  | 'deactivate'
  | 'change-plan'
  | 'export-data'
  | 'send-notification'
  | 'update-settings';

interface BulkOperationConfig {
  id: BulkOperation;
  name: string;
  description: string;
  icon: React.ReactNode;
  requiresInput: boolean;
  destructive?: boolean;
}

const bulkOperations: BulkOperationConfig[] = [
  {
    id: 'activate',
    name: 'Activate Organizations',
    description: 'Enable selected organizations and restore access',
    icon: <CheckCircle className="h-4 w-4" />,
    requiresInput: false,
  },
  {
    id: 'deactivate',
    name: 'Deactivate Organizations',
    description: 'Disable selected organizations and suspend access',
    icon: <XCircle className="h-4 w-4" />,
    requiresInput: false,
    destructive: true,
  },
  {
    id: 'change-plan',
    name: 'Change Subscription Plan',
    description: 'Update subscription plan for selected organizations',
    icon: <Zap className="h-4 w-4" />,
    requiresInput: true,
  },
  {
    id: 'export-data',
    name: 'Export Organization Data',
    description: 'Download data for selected organizations as CSV',
    icon: <Download className="h-4 w-4" />,
    requiresInput: false,
  },
  {
    id: 'send-notification',
    name: 'Send Notification',
    description: 'Send email notification to selected organizations',
    icon: <Mail className="h-4 w-4" />,
    requiresInput: true,
  },
  {
    id: 'update-settings',
    name: 'Update Settings',
    description: 'Apply common settings to selected organizations',
    icon: <Settings className="h-4 w-4" />,
    requiresInput: true,
  },
];

export default function BulkOperationsModal({
  open,
  onOpenChange,
  organizations,
  onBulkUpdate,
}: BulkOperationsModalProps) {
  const [selectedOrganizations, setSelectedOrganizations] = useState<string[]>([]);
  const [selectedOperation, setSelectedOperation] = useState<BulkOperation | null>(null);
  const [newSubscriptionPlan, setNewSubscriptionPlan] = useState<SubscriptionPlan>('basic');
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);

  const handleOrganizationToggle = (organizationId: string) => {
    setSelectedOrganizations(prev => 
      prev.includes(organizationId)
        ? prev.filter(id => id !== organizationId)
        : [...prev, organizationId]
    );
  };

  const handleSelectAll = () => {
    if (selectedOrganizations.length === organizations.length) {
      setSelectedOrganizations([]);
    } else {
      setSelectedOrganizations(organizations.map(org => org.id));
    }
  };

  const handleExecuteOperation = async () => {
    if (!selectedOperation || selectedOrganizations.length === 0) {
      toast.error('Please select an operation and organizations');
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      // Simulate processing with progress
      for (let i = 0; i <= 100; i += 10) {
        setProgress(i);
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      let updates: Partial<Organization> = {};
      let successMessage = '';

      switch (selectedOperation) {
        case 'activate':
          updates = { isActive: true };
          successMessage = `Activated ${selectedOrganizations.length} organizations`;
          break;
        case 'deactivate':
          updates = { isActive: false };
          successMessage = `Deactivated ${selectedOrganizations.length} organizations`;
          break;
        case 'change-plan':
          updates = { subscriptionPlan: newSubscriptionPlan };
          successMessage = `Updated subscription plan for ${selectedOrganizations.length} organizations`;
          break;
        case 'export-data': {
          // Simulate export
          const csvData = generateCSVData();
          downloadCSV(csvData, 'organizations-export.csv');
          successMessage = `Exported data for ${selectedOrganizations.length} organizations`;
          break;
        }
        case 'send-notification':
          successMessage = `Sent notifications to ${selectedOrganizations.length} organizations`;
          break;
        case 'update-settings':
          successMessage = `Updated settings for ${selectedOrganizations.length} organizations`;
          break;
      }

      if (selectedOperation !== 'export-data') {
        onBulkUpdate(selectedOrganizations, updates);
      }

      toast.success(successMessage);
      
      // Reset state
      setSelectedOrganizations([]);
      setSelectedOperation(null);
      onOpenChange(false);
    } catch (error) {
      console.error('Bulk operation failed:', error);
      toast.error('Bulk operation failed. Please try again.');
    } finally {
      setIsProcessing(false);
      setProgress(0);
    }
  };

  const generateCSVData = () => {
    const selectedOrgs = organizations.filter(org => selectedOrganizations.includes(org.id));
    const headers = ['Name', 'Type', 'Subscription Plan', 'Industry', 'Size', 'Email', 'Status', 'Created At'];
    const rows = selectedOrgs.map(org => [
      org.name,
      org.type,
      org.subscriptionPlan,
      org.industry,
      org.size,
      org.email,
      org.isActive ? 'Active' : 'Inactive',
      org.createdAt,
    ]);
    
    return [headers, ...rows].map(row => row.join(',')).join('\n');
  };

  const downloadCSV = (csvData: string, filename: string) => {
    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const selectedOperation_config = selectedOperation ? bulkOperations.find(op => op.id === selectedOperation) : null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Zap className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <DialogTitle className="text-xl">Bulk Operations</DialogTitle>
              <DialogDescription>
                Perform actions on multiple organizations at once
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Organization Selection */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Building2 className="mr-2 h-4 w-4" />
                  Select Organizations ({selectedOrganizations.length} of {organizations.length})
                </CardTitle>
                <Button variant="outline" size="sm" onClick={handleSelectAll}>
                  {selectedOrganizations.length === organizations.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="max-h-60 overflow-y-auto space-y-2">
                {organizations.map((org) => (
                  <div key={org.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50">
                    <Checkbox
                      checked={selectedOrganizations.includes(org.id)}
                      onCheckedChange={() => handleOrganizationToggle(org.id)}
                    />
                    <div className="flex-1 flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Building2 className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">{org.name}</p>
                          <p className="text-sm text-gray-600">{org.industry}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={org.type === 'client' ? 'default' : 'secondary'}>
                          {org.type}
                        </Badge>
                        <Badge variant={org.isActive ? 'outline' : 'destructive'}>
                          {org.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Operation Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="mr-2 h-4 w-4" />
                Select Operation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {bulkOperations.map((operation) => (
                  <div
                    key={operation.id}
                    className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                      selectedOperation === operation.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${operation.destructive ? 'hover:border-red-300' : ''}`}
                    onClick={() => setSelectedOperation(operation.id)}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        operation.destructive ? 'bg-red-100 text-red-600' : 'bg-blue-100 text-blue-600'
                      }`}>
                        {operation.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium">{operation.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{operation.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Operation Configuration */}
          {selectedOperation_config?.requiresInput && (
            <Card>
              <CardHeader>
                <CardTitle>Configuration</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedOperation === 'change-plan' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">New Subscription Plan</label>
                    <Select value={newSubscriptionPlan} onValueChange={(value) => setNewSubscriptionPlan(value as SubscriptionPlan)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="basic">Basic</SelectItem>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="enterprise">Enterprise</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                {selectedOperation === 'send-notification' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Notification Type</label>
                    <Select defaultValue="maintenance">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="maintenance">Maintenance Notice</SelectItem>
                        <SelectItem value="feature-update">Feature Update</SelectItem>
                        <SelectItem value="billing">Billing Reminder</SelectItem>
                        <SelectItem value="security">Security Alert</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                {selectedOperation === 'update-settings' && (
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Settings Template</label>
                    <Select defaultValue="default">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="default">Default Settings</SelectItem>
                        <SelectItem value="strict">Strict Security</SelectItem>
                        <SelectItem value="relaxed">Relaxed Permissions</SelectItem>
                        <SelectItem value="enterprise">Enterprise Template</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Processing Progress */}
          {isProcessing && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Processing...</span>
                    <span className="text-sm text-gray-600">{progress}%</span>
                  </div>
                  <Progress value={progress} className="w-full" />
                  <p className="text-sm text-gray-600">
                    Applying {selectedOperation_config?.name.toLowerCase()} to {selectedOrganizations.length} organizations
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Warning for Destructive Operations */}
          {selectedOperation_config?.destructive && selectedOrganizations.length > 0 && (
            <Card className="border-amber-200 bg-amber-50">
              <CardContent className="pt-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-amber-900">Warning</h4>
                    <p className="text-sm text-amber-800 mt-1">
                      This operation will affect {selectedOrganizations.length} organizations and may impact their users' access. 
                      Please ensure you want to proceed with this action.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isProcessing}>
            Cancel
          </Button>
          <Button
            onClick={handleExecuteOperation}
            disabled={!selectedOperation || selectedOrganizations.length === 0 || isProcessing}
            variant={selectedOperation_config?.destructive ? 'destructive' : 'default'}
          >
            {isProcessing ? 'Processing...' : `Execute Operation (${selectedOrganizations.length})`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
