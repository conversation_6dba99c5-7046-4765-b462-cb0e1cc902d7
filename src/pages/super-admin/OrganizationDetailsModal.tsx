import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  Mail,
  Phone,
  Globe,
  MapPin,
  Calendar,
  Users,
  Settings,
  CreditCard,
  Shield,
  Clock,
  DollarSign,
} from 'lucide-react';
import { Organization, OrganizationType, SubscriptionPlan } from '@/types/multiTenant';

interface OrganizationDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organization: Organization | null;
  onEdit?: (organization: Organization) => void;
  onDelete?: (organization: Organization) => void;
}

export default function OrganizationDetailsModal({
  open,
  onOpenChange,
  organization,
  onEdit,
  onDelete,
}: OrganizationDetailsModalProps) {
  if (!organization) return null;

  const getSubscriptionBadgeVariant = (plan: SubscriptionPlan) => {
    switch (plan) {
      case 'enterprise':
        return 'default';
      case 'professional':
        return 'secondary';
      case 'basic':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getTypeBadgeVariant = (type: OrganizationType) => {
    return type === 'client' ? 'default' : 'secondary';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
              {organization.logo ? (
                <img
                  src={organization.logo}
                  alt={organization.name}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              ) : (
                <Building2 className="h-8 w-8 text-blue-600" />
              )}
            </div>
            <div>
              <DialogTitle className="text-2xl">{organization.name}</DialogTitle>
              <DialogDescription className="flex items-center space-x-2 mt-1">
                <Badge variant={getTypeBadgeVariant(organization.type)}>
                  {organization.type}
                </Badge>
                <Badge variant={getSubscriptionBadgeVariant(organization.subscriptionPlan)}>
                  {organization.subscriptionPlan}
                </Badge>
                {organization.isActive ? (
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    Active
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-red-600 border-red-600">
                    Inactive
                  </Badge>
                )}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="contact">Contact</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Building2 className="mr-2 h-4 w-4" />
                    Organization Info
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Industry</span>
                    <span className="font-medium">{organization.industry}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Size</span>
                    <span className="font-medium">{organization.size} employees</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Website</span>
                    {organization.website ? (
                      <a
                        href={organization.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline flex items-center"
                      >
                        <Globe className="mr-1 h-3 w-3" />
                        Visit
                      </a>
                    ) : (
                      <span className="text-gray-400">Not provided</span>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Created</span>
                    <span className="font-medium">{formatDate(organization.createdAt)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Last Updated</span>
                    <span className="font-medium">{formatDate(organization.updatedAt)}</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CreditCard className="mr-2 h-4 w-4" />
                    Subscription Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Plan</span>
                    <Badge variant={getSubscriptionBadgeVariant(organization.subscriptionPlan)}>
                      {organization.subscriptionPlan}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Max Users</span>
                    <span className="font-medium">{organization.settings.maxUsers}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Max Job Postings</span>
                    <span className="font-medium">{organization.settings.maxJobPostings}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-gray-600">Custom Branding</span>
                    <span className="font-medium">
                      {organization.settings.customBranding ? 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contact" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Mail className="mr-2 h-4 w-4" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Mail className="mr-2 h-4 w-4" />
                      Email
                    </div>
                    <p className="font-medium">{organization.email}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Phone className="mr-2 h-4 w-4" />
                      Phone
                    </div>
                    <p className="font-medium">{organization.phone}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex items-center text-sm text-gray-600">
                    <MapPin className="mr-2 h-4 w-4" />
                    Address
                  </div>
                  <p className="font-medium">{organization.address}</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="mr-2 h-4 w-4" />
                  Organization Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="mr-2 h-4 w-4" />
                      Timezone
                    </div>
                    <p className="font-medium">{organization.settings.timezone}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <DollarSign className="mr-2 h-4 w-4" />
                      Currency
                    </div>
                    <p className="font-medium">{organization.settings.currency}</p>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="mr-2 h-4 w-4" />
                      Date Format
                    </div>
                    <p className="font-medium">{organization.settings.dateFormat}</p>
                  </div>
                </div>

                <div className="space-y-3 pt-4 border-t">
                  <h4 className="font-medium">Permissions & Features</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Public Job Board</span>
                      <Badge variant={organization.settings.allowPublicJobBoard ? 'default' : 'outline'}>
                        {organization.settings.allowPublicJobBoard ? 'Enabled' : 'Disabled'}
                      </Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Require Job Approval</span>
                      <Badge variant={organization.settings.requireApprovalForJobs ? 'default' : 'outline'}>
                        {organization.settings.requireApprovalForJobs ? 'Required' : 'Not Required'}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-4">
            {organization.clientProfile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    Client Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Hiring Volume</span>
                      <span className="font-medium capitalize">{organization.clientProfile.hiringVolume}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Max Vendors per Job</span>
                      <span className="font-medium">{organization.clientProfile.maxVendorsPerJob}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Auto Approve Vendors</span>
                      <Badge variant={organization.clientProfile.autoApproveVendors ? 'default' : 'outline'}>
                        {organization.clientProfile.autoApproveVendors ? 'Yes' : 'No'}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Payment Terms</span>
                      <span className="font-medium">{organization.clientProfile.vendorPaymentTerms}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {organization.vendorProfile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Shield className="mr-2 h-4 w-4" />
                    Vendor Profile
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Years in Business</span>
                      <span className="font-medium">{organization.vendorProfile.yearsInBusiness}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Team Size</span>
                      <span className="font-medium">{organization.vendorProfile.teamSize}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Vendor Tier</span>
                      <Badge variant="outline" className="capitalize">
                        {organization.vendorProfile.vendorTier}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Minimum Rate</span>
                      <span className="font-medium">${organization.vendorProfile.minimumRate}/hr</span>
                    </div>
                  </div>
                  
                  {organization.vendorProfile.preferredIndustries.length > 0 && (
                    <div className="pt-3 border-t">
                      <h5 className="text-sm font-medium mb-2">Preferred Industries</h5>
                      <div className="flex flex-wrap gap-2">
                        {organization.vendorProfile.preferredIndustries.map((industry) => (
                          <Badge key={industry} variant="outline">
                            {industry}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {organization.vendorProfile.serviceAreas.length > 0 && (
                    <div className="pt-3 border-t">
                      <h5 className="text-sm font-medium mb-2">Service Areas</h5>
                      <div className="flex flex-wrap gap-2">
                        {organization.vendorProfile.serviceAreas.map((area) => (
                          <Badge key={area} variant="outline">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        <div className="flex justify-between pt-4 border-t">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <div className="space-x-2">
            {onEdit && (
              <Button variant="outline" onClick={() => onEdit(organization)}>
                Edit Organization
              </Button>
            )}
            {onDelete && (
              <Button variant="destructive" onClick={() => onDelete(organization)}>
                Delete Organization
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
