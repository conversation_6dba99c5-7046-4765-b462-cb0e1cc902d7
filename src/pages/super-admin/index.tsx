import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Building2,
  Users,
  TrendingUp,
  DollarSign,
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Shield,
  Activity,
  Calendar,
  Globe,
  Zap,
} from 'lucide-react';
import { mockOrganizations, mockUsers } from '@/data/multiTenantData';
import { Organization, OrganizationType, SubscriptionPlan } from '@/types/multiTenant';
import { useAuth } from '@/contexts/AuthContext';
import CreateOrganizationModal from './CreateOrganizationModal';
import OrganizationDetailsModal from './OrganizationDetailsModal';
import EditOrganizationModal from './EditOrganizationModal';
import DeleteOrganizationModal from './DeleteOrganizationModal';
import OrganizationUsersModal from './OrganizationUsersModal';
import BulkOperationsModal from './BulkOperationsModal';

export default function SuperAdminDashboard() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<OrganizationType | 'all'>('all');
  const [planFilter, setPlanFilter] = useState<SubscriptionPlan | 'all'>('all');
  const [statusFilter, setStatusFilter] = useState<'active' | 'inactive' | 'all'>('all');
  const [organizations, setOrganizations] = useState<Organization[]>(mockOrganizations);
  const [createModalOpen, setCreateModalOpen] = useState(false);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [usersModalOpen, setUsersModalOpen] = useState(false);
  const [bulkModalOpen, setBulkModalOpen] = useState(false);
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);

  // Check if user is super admin
  const isSuperAdmin = user?.role === 'super_admin';

  // Calculate dashboard metrics
  const totalOrganizations = organizations.length;
  const activeOrganizations = organizations.filter(org => org.isActive).length;
  const clientOrganizations = organizations.filter(org => org.type === 'client').length;
  const vendorOrganizations = organizations.filter(org => org.type === 'vendor').length;
  const totalUsers = mockUsers.length;
  const activeUsers = mockUsers.filter(user => user.isActive).length;

  // Calculate revenue (mock calculation based on subscription plans)
  const monthlyRevenue = organizations.reduce((total, org) => {
    const planRevenue = {
      free: 0,
      basic: 99,
      professional: 299,
      enterprise: 999,
    };
    return total + (org.isActive ? planRevenue[org.subscriptionPlan] : 0);
  }, 0);

  // Filter organizations
  const filteredOrganizations = useMemo(() => {
    return organizations.filter(org => {
      const matchesSearch = !searchTerm ||
        org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.industry.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesType = typeFilter === 'all' || org.type === typeFilter;
      const matchesPlan = planFilter === 'all' || org.subscriptionPlan === planFilter;
      const matchesStatus = statusFilter === 'all' ||
        (statusFilter === 'active' && org.isActive) ||
        (statusFilter === 'inactive' && !org.isActive);

      return matchesSearch && matchesType && matchesPlan && matchesStatus;
    });
  }, [searchTerm, typeFilter, planFilter, statusFilter, organizations]);

  const getSubscriptionBadgeVariant = (plan: SubscriptionPlan) => {
    switch (plan) {
      case 'enterprise':
        return 'default';
      case 'professional':
        return 'secondary';
      case 'basic':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getTypeBadgeVariant = (type: OrganizationType) => {
    return type === 'client' ? 'default' : 'secondary';
  };

  const handleOrganizationCreated = (newOrganization: Organization) => {
    setOrganizations(prev => [...prev, newOrganization]);
  };

  const handleViewOrganization = (organization: Organization) => {
    setSelectedOrganization(organization);
    setDetailsModalOpen(true);
  };

  const handleEditOrganization = (organization: Organization) => {
    setSelectedOrganization(organization);
    setEditModalOpen(true);
    setDetailsModalOpen(false); // Close details modal if open
  };

  const handleDeleteOrganization = (organization: Organization) => {
    setSelectedOrganization(organization);
    setDeleteModalOpen(true);
    setDetailsModalOpen(false); // Close details modal if open
  };

  const handleOrganizationUpdated = (updatedOrganization: Organization) => {
    setOrganizations(prev =>
      prev.map(org => org.id === updatedOrganization.id ? updatedOrganization : org)
    );
    setSelectedOrganization(updatedOrganization); // Update selected organization
  };

  const handleOrganizationDeleted = (organizationId: string) => {
    setOrganizations(prev => prev.filter(org => org.id !== organizationId));
    setSelectedOrganization(null);
  };

  const handleManageUsers = (organization: Organization) => {
    setSelectedOrganization(organization);
    setUsersModalOpen(true);
    setDetailsModalOpen(false); // Close details modal if open
  };

  const handleBulkOperations = () => {
    setBulkModalOpen(true);
  };

  const handleBulkUpdate = (organizationIds: string[], updates: Partial<Organization>) => {
    setOrganizations(prev =>
      prev.map(org =>
        organizationIds.includes(org.id)
          ? { ...org, ...updates, updatedAt: new Date().toISOString() }
          : org
      )
    );
  };

  if (!isSuperAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Shield className="h-12 w-12 text-red-500 mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Access Denied
            </h2>
            <p className="text-sm text-gray-600 text-center">
              Super Admin privileges required to access this page.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Super Admin Dashboard</h1>
          <p className="text-gray-600">Manage organizations and system-wide settings</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline" onClick={handleBulkOperations}>
            <Zap className="mr-2 h-4 w-4" />
            Bulk Operations
          </Button>
          <Button onClick={() => setCreateModalOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create Organization
          </Button>
        </div>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Organizations</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalOrganizations}</div>
            <p className="text-xs text-muted-foreground">
              {activeOrganizations} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {activeUsers} active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${monthlyRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              From {activeOrganizations} active orgs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12.5%</div>
            <p className="text-xs text-muted-foreground">
              vs last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Organization Management */}
      <Tabs defaultValue="organizations" className="space-y-4">
        <TabsList>
          <TabsTrigger value="organizations">Organizations</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="system">System Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="organizations" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle>Organization Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search organizations..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={typeFilter} onValueChange={(value) => setTypeFilter(value as OrganizationType | 'all')}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="client">Client</SelectItem>
                    <SelectItem value="vendor">Vendor</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={planFilter} onValueChange={(value) => setPlanFilter(value as SubscriptionPlan | 'all')}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Plan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Plans</SelectItem>
                    <SelectItem value="free">Free</SelectItem>
                    <SelectItem value="basic">Basic</SelectItem>
                    <SelectItem value="professional">Professional</SelectItem>
                    <SelectItem value="enterprise">Enterprise</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as 'active' | 'inactive' | 'all')}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Organizations List */}
              <div className="space-y-4">
                {filteredOrganizations.map((org) => (
                  <div key={org.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium">{org.name}</h3>
                          <Badge variant={getTypeBadgeVariant(org.type)}>
                            {org.type}
                          </Badge>
                          <Badge variant={getSubscriptionBadgeVariant(org.subscriptionPlan)}>
                            {org.subscriptionPlan}
                          </Badge>
                          {org.isActive ? (
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="outline" className="text-red-600 border-red-600">
                              Inactive
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Globe className="h-3 w-3 mr-1" />
                            {org.industry}
                          </span>
                          <span className="flex items-center">
                            <Users className="h-3 w-3 mr-1" />
                            {org.size}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {new Date(org.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button variant="outline" size="sm" onClick={() => handleViewOrganization(org)}>
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleManageUsers(org)}>
                        <Users className="h-4 w-4 mr-1" />
                        Users
                      </Button>
                      <Button variant="outline" size="sm" onClick={() => handleEditOrganization(org)}>
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteOrganization(org)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {filteredOrganizations.length === 0 && (
                <div className="text-center py-8">
                  <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No organizations found</h3>
                  <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {/* Analytics Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 text-green-600" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Active Rate</p>
                    <p className="text-2xl font-bold text-green-600">
                      {Math.round((activeOrganizations / totalOrganizations) * 100)}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Growth Rate</p>
                    <p className="text-2xl font-bold text-blue-600">+12%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 text-purple-600" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Avg Revenue</p>
                    <p className="text-2xl font-bold text-purple-600">$2.4K</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center">
                  <Users className="h-4 w-4 text-orange-600" />
                  <div className="ml-2">
                    <p className="text-sm font-medium">Avg Users/Org</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {Math.round(totalUsers / totalOrganizations)}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Organization Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Client Organizations</span>
                      <span className="font-medium">{clientOrganizations} ({Math.round((clientOrganizations / totalOrganizations) * 100)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${(clientOrganizations / totalOrganizations) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Vendor Organizations</span>
                      <span className="font-medium">{vendorOrganizations} ({Math.round((vendorOrganizations / totalOrganizations) * 100)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${(vendorOrganizations / totalOrganizations) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Subscription Plans</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {['free', 'basic', 'professional', 'enterprise'].map(plan => {
                    const count = organizations.filter(org => org.subscriptionPlan === plan).length;
                    const percentage = Math.round((count / totalOrganizations) * 100);
                    const colors = {
                      free: 'bg-gray-500',
                      basic: 'bg-blue-500',
                      professional: 'bg-purple-500',
                      enterprise: 'bg-gold-500'
                    };
                    return (
                      <div key={plan}>
                        <div className="flex justify-between mb-2">
                          <span className="capitalize">{plan}</span>
                          <span className="font-medium">{count} ({percentage}%)</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`${colors[plan as keyof typeof colors]} h-2 rounded-full`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Industry Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Industry Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {Array.from(new Set(organizations.map(org => org.industry))).map(industry => {
                  const count = organizations.filter(org => org.industry === industry).length;
                  const percentage = Math.round((count / totalOrganizations) * 100);
                  return (
                    <div key={industry} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{industry}</h4>
                        <Badge variant="outline">{count}</Badge>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-indigo-600 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{percentage}% of total</p>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {organizations
                  .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                  .slice(0, 5)
                  .map(org => (
                    <div key={org.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building2 className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{org.name}</p>
                        <p className="text-sm text-gray-600">
                          Updated {new Date(org.updatedAt).toLocaleDateString()}
                        </p>
                      </div>
                      <Badge variant={org.isActive ? 'outline' : 'destructive'}>
                        {org.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600">System-wide configuration options will be available here.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Create Organization Modal */}
      <CreateOrganizationModal
        open={createModalOpen}
        onOpenChange={setCreateModalOpen}
        onOrganizationCreated={handleOrganizationCreated}
      />

      {/* Organization Details Modal */}
      <OrganizationDetailsModal
        open={detailsModalOpen}
        onOpenChange={setDetailsModalOpen}
        organization={selectedOrganization}
        onEdit={handleEditOrganization}
        onDelete={handleDeleteOrganization}
      />

      {/* Edit Organization Modal */}
      <EditOrganizationModal
        open={editModalOpen}
        onOpenChange={setEditModalOpen}
        organization={selectedOrganization}
        onOrganizationUpdated={handleOrganizationUpdated}
      />

      {/* Delete Organization Modal */}
      <DeleteOrganizationModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        organization={selectedOrganization}
        onOrganizationDeleted={handleOrganizationDeleted}
      />

      {/* Organization Users Modal */}
      <OrganizationUsersModal
        open={usersModalOpen}
        onOpenChange={setUsersModalOpen}
        organization={selectedOrganization}
      />

      {/* Bulk Operations Modal */}
      <BulkOperationsModal
        open={bulkModalOpen}
        onOpenChange={setBulkModalOpen}
        organizations={organizations}
        onBulkUpdate={handleBulkUpdate}
      />
    </div>
  );
}
