import React, { useState, useMemo } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { SETTINGS_NAVIGATION } from '@/config/settingsConfig';
import { SettingsProvider, useSettings } from '@/contexts/SettingsContext';
import {
  Search,
  ChevronRight,
  Settings as SettingsIcon,
  Loader2,
  AlertCircle,
  RefreshCw
} from 'lucide-react';

// Settings Layout Component
function SettingsLayout() {
  const [selectedCategory, setSelectedCategory] = useState('global_settings');
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const { loading, error, refreshSettings } = useSettings();

  // Filter settings based on search query
  const filteredNavigation = useMemo(() => {
    if (!searchQuery) return SETTINGS_NAVIGATION;

    return SETTINGS_NAVIGATION.map(category => ({
      ...category,
      items: category.items.filter(item =>
        item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    })).filter(category => category.items.length > 0);
  }, [searchQuery]);

  const selectedCategoryData = filteredNavigation.find(cat => cat.id === selectedCategory);
  const selectedItemData = selectedCategoryData?.items.find(item => item.id === selectedItem);

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Sidebar - Categories */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Admin Setup Panel</h1>
          <p className="text-sm text-gray-600">Configure your ATS settings and preferences</p>

          {/* Search */}
          <div className="relative mt-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search settings..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <ScrollArea className="flex-1">
          <div className="p-4 space-y-2">
            {filteredNavigation.map((category) => {
              const Icon = category.icon;
              const isSelected = selectedCategory === category.id;

              return (
                <button
                  key={category.id}
                  onClick={() => {
                    setSelectedCategory(category.id);
                    setSelectedItem(null);
                  }}
                  className={cn(
                    "w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors",
                    isSelected
                      ? "bg-blue-50 text-blue-700 border border-blue-200"
                      : "hover:bg-gray-50 text-gray-700"
                  )}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{category.name}</div>
                    <div className="text-xs text-gray-500 truncate">{category.description}</div>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {category.items.length}
                  </Badge>
                </button>
              );
            })}
          </div>
        </ScrollArea>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshSettings}
            disabled={loading}
            className="w-full"
          >
            {loading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh Settings
          </Button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {selectedCategoryData?.name || 'Select a Category'}
              </h2>
              <p className="text-sm text-gray-600 mt-1">
                {selectedCategoryData?.description || 'Choose a settings category from the left panel'}
              </p>
            </div>
            {error && (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span className="text-sm">Error loading settings</span>
              </div>
            )}
          </div>
        </div>

        {/* Content Grid */}
        <div className="flex-1 overflow-auto">
          {selectedCategoryData ? (
            <SettingsCategoryView
              category={selectedCategoryData}
              selectedItem={selectedItem}
              onItemSelect={setSelectedItem}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <SettingsIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Welcome to Settings</h3>
                <p className="text-gray-600">Select a category from the left panel to get started</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Settings Category View Component
interface SettingsCategoryViewProps {
  category: any;
  selectedItem: string | null;
  onItemSelect: (itemId: string | null) => void;
}

function SettingsCategoryView({ category, selectedItem, onItemSelect }: SettingsCategoryViewProps) {
  // Group items by category (left, middle, right)
  const groupedItems = useMemo(() => {
    const groups = {
      left: category.items.filter((item: any) => item.category === 'left'),
      middle: category.items.filter((item: any) => item.category === 'middle'),
      right: category.items.filter((item: any) => item.category === 'right'),
    };
    return groups;
  }, [category.items]);

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 mb-4">Primary Settings</h3>
          {groupedItems.left.map((item: any) => (
            <SettingsItemCard
              key={item.id}
              item={item}
              isSelected={selectedItem === item.id}
              onSelect={() => onItemSelect(item.id)}
            />
          ))}
        </div>

        {/* Middle Column */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 mb-4">Configuration</h3>
          {groupedItems.middle.map((item: any) => (
            <SettingsItemCard
              key={item.id}
              item={item}
              isSelected={selectedItem === item.id}
              onSelect={() => onItemSelect(item.id)}
            />
          ))}
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <h3 className="font-medium text-gray-900 mb-4">Advanced Options</h3>
          {groupedItems.right.map((item: any) => (
            <SettingsItemCard
              key={item.id}
              item={item}
              isSelected={selectedItem === item.id}
              onSelect={() => onItemSelect(item.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

// Settings Item Card Component
interface SettingsItemCardProps {
  item: any;
  isSelected: boolean;
  onSelect: () => void;
}

function SettingsItemCard({ item, isSelected, onSelect }: SettingsItemCardProps) {
  const Icon = item.icon;

  return (
    <Card
      className={cn(
        "cursor-pointer transition-all duration-200 hover:shadow-md",
        isSelected && "ring-2 ring-blue-500 shadow-md"
      )}
      onClick={onSelect}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className={cn(
            "p-2 rounded-lg",
            isSelected ? "bg-blue-100 text-blue-600" : "bg-gray-100 text-gray-600"
          )}>
            <Icon className="h-5 w-5" />
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="font-medium text-gray-900 truncate">{item.name}</h4>
              <div className="flex items-center space-x-2">
                {item.isNew && (
                  <Badge variant="secondary" className="text-xs">New</Badge>
                )}
                {item.badge && (
                  <Badge variant="outline" className="text-xs">{item.badge}</Badge>
                )}
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-1 line-clamp-2">{item.description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Main Settings Component with Provider
export default function Settings() {
  return (
    <SettingsProvider>
      <SettingsLayout />
    </SettingsProvider>
  );
}