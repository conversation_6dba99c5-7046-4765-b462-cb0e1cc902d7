import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { UserInvitation, RoleTemplate, Permission } from '@/types/multiTenant';
import { UserManagementAPI, InviteUserRequest } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Loader2, 
  Mail, 
  User as UserIcon, 
  Building, 
  Shield,
  Clock,
  Send,
  Plus,
  Minus
} from 'lucide-react';

interface InviteUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUserInvited: (invitation: UserInvitation) => void;
}

interface InviteUserForm {
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  department: string;
  title: string;
  message: string;
  expiresInDays: number;
  permissions: Permission[];
  useRoleTemplate: boolean;
  selectedTemplate?: string;
}

const initialFormData: InviteUserForm = {
  firstName: '',
  lastName: '',
  email: '',
  role: '',
  department: '',
  title: '',
  message: '',
  expiresInDays: 7,
  permissions: [],
  useRoleTemplate: true,
  selectedTemplate: '',
};

const defaultRoles = [
  { value: 'admin', label: 'Administrator' },
  { value: 'hiring_manager', label: 'Hiring Manager' },
  { value: 'recruiter', label: 'Recruiter' },
  { value: 'interviewer', label: 'Interviewer' },
  { value: 'employee', label: 'Employee' },
];

const availableModules = [
  { value: 'applicants', label: 'Applicants' },
  { value: 'jobs', label: 'Jobs' },
  { value: 'interviews', label: 'Interviews' },
  { value: 'onboarding', label: 'Onboarding' },
  { value: 'analytics', label: 'Analytics' },
  { value: 'settings', label: 'Settings' },
  { value: 'users', label: 'Users' },
  { value: 'vendors', label: 'Vendors' },
  { value: 'form_builder', label: 'Form Builder' },
  { value: 'communications', label: 'Communications' },
];

const availableActions = [
  { value: 'create', label: 'Create' },
  { value: 'read', label: 'Read' },
  { value: 'update', label: 'Update' },
  { value: 'delete', label: 'Delete' },
  { value: 'approve', label: 'Approve' },
  { value: 'invite', label: 'Invite' },
  { value: 'manage_roles', label: 'Manage Roles' },
];

export default function InviteUserDialog({ open, onOpenChange, onUserInvited }: InviteUserDialogProps) {
  const { organization } = useAuth();
  const [formData, setFormData] = useState<InviteUserForm>(initialFormData);
  const [isLoading, setIsLoading] = useState(false);
  const [roleTemplates, setRoleTemplates] = useState<RoleTemplate[]>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);

  // Load role templates when dialog opens
  useEffect(() => {
    if (open && organization) {
      loadRoleTemplates();
    }
  }, [open, organization]);

  const loadRoleTemplates = async () => {
    try {
      setLoadingTemplates(true);
      const templates = await UserManagementAPI.getRoleTemplates(organization?.type);
      setRoleTemplates(templates);
    } catch (error) {
      console.error('Failed to load role templates:', error);
      toast({
        title: "Error",
        description: "Failed to load role templates. Using default roles.",
        variant: "destructive",
      });
    } finally {
      setLoadingTemplates(false);
    }
  };

  const handleInputChange = (field: keyof InviteUserForm, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRoleChange = (role: string) => {
    setFormData(prev => ({ ...prev, role }));
    
    // If using role template, apply template permissions
    if (formData.useRoleTemplate) {
      const template = roleTemplates.find(t => t.name === role);
      if (template) {
        setFormData(prev => ({ ...prev, permissions: template.permissions }));
      }
    }
  };

  const handlePermissionChange = (moduleIndex: number, action: string, checked: boolean) => {
    setFormData(prev => {
      const newPermissions = [...prev.permissions];
      const permission = newPermissions[moduleIndex];
      
      if (checked) {
        if (!permission.actions.includes(action as 'create' | 'read' | 'update' | 'delete' | 'approve' | 'invite' | 'manage_roles')) {
          permission.actions.push(action as 'create' | 'read' | 'update' | 'delete' | 'approve' | 'invite' | 'manage_roles');
        }
      } else {
        permission.actions = permission.actions.filter(a => a !== action);
      }
      
      return { ...prev, permissions: newPermissions };
    });
  };

  const addPermissionModule = (module: string) => {
    setFormData(prev => ({
      ...prev,
      permissions: [...prev.permissions, {
        module: module as 'applicants' | 'jobs' | 'interviews' | 'onboarding' | 'analytics' | 'settings' | 'users' | 'vendors' | 'form_builder' | 'communications',
        actions: ['read']
      }]
    }));
  };

  const removePermissionModule = (index: number) => {
    setFormData(prev => ({
      ...prev,
      permissions: prev.permissions.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!organization) {
      toast({
        title: "Error",
        description: "Organization context is required.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const invitationData: InviteUserRequest = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        role: formData.role,
        permissions: formData.permissions.map(p => ({
          module: p.module,
          actions: p.actions
        })),
        department: formData.department || undefined,
        title: formData.title,
        message: formData.message || undefined,
        expiresInDays: formData.expiresInDays,
      };

      const invitation = await UserManagementAPI.inviteUser(organization.id, invitationData);
      
      onUserInvited(invitation);
      onOpenChange(false);
      setFormData(initialFormData);
      
      toast({
        title: "Invitation Sent",
        description: `Invitation sent to ${formData.email}. They have ${formData.expiresInDays} days to accept.`,
      });
    } catch (error) {
      console.error('Failed to send invitation:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send invitation.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const isFormValid = formData.firstName && formData.lastName && formData.email && formData.role && formData.title;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Invite New User
          </DialogTitle>
          <DialogDescription>
            Send an invitation to join {organization?.name}. The user will receive an email with instructions to set up their account.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <UserIcon className="h-4 w-4" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name *</Label>
                  <Input
                    id="firstName"
                    value={formData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="Enter first name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name *</Label>
                  <Input
                    id="lastName"
                    value={formData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Enter last name"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email Address *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="department">Department</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="department"
                      value={formData.department}
                      onChange={(e) => handleInputChange('department', e.target.value)}
                      placeholder="Enter department"
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="title">Job Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter job title"
                    required
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Role and Permissions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Shield className="h-4 w-4" />
                Role and Permissions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="role">Role *</Label>
                <Select value={formData.role} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    {loadingTemplates ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2">Loading roles...</span>
                      </div>
                    ) : (
                      <>
                        {roleTemplates.map((template) => (
                          <SelectItem key={template.id} value={template.name}>
                            <div className="flex items-center gap-2">
                              <span>{template.name}</span>
                              {template.isCustom && <Badge variant="outline" className="text-xs">Custom</Badge>}
                            </div>
                          </SelectItem>
                        ))}
                        {roleTemplates.length === 0 && defaultRoles.map((role) => (
                          <SelectItem key={role.value} value={role.value}>
                            {role.label}
                          </SelectItem>
                        ))}
                      </>
                    )}
                  </SelectContent>
                </Select>
              </div>

              {!formData.useRoleTemplate && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Custom Permissions</Label>
                    <Select onValueChange={addPermissionModule}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Add module" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableModules
                          .filter(module => !formData.permissions.some(p => p.module === module.value))
                          .map((module) => (
                            <SelectItem key={module.value} value={module.value}>
                              {module.label}
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.permissions.map((permission, index) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium capitalize">
                          {availableModules.find(m => m.value === permission.module)?.label || permission.module}
                        </h4>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removePermissionModule(index)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        {availableActions.map((action) => (
                          <div key={action.value} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${permission.module}-${action.value}`}
                              checked={permission.actions.includes(action.value as 'create' | 'read' | 'update' | 'delete' | 'approve' | 'invite' | 'manage_roles')}
                              onCheckedChange={(checked) =>
                                handlePermissionChange(index, action.value, checked as boolean)
                              }
                            />
                            <Label 
                              htmlFor={`${permission.module}-${action.value}`}
                              className="text-sm"
                            >
                              {action.label}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Invitation Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Clock className="h-4 w-4" />
                Invitation Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="expiresInDays">Expires In (Days)</Label>
                <Select 
                  value={formData.expiresInDays.toString()} 
                  onValueChange={(value) => handleInputChange('expiresInDays', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Day</SelectItem>
                    <SelectItem value="3">3 Days</SelectItem>
                    <SelectItem value="7">7 Days</SelectItem>
                    <SelectItem value="14">14 Days</SelectItem>
                    <SelectItem value="30">30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Personal Message (Optional)</Label>
                <Textarea
                  id="message"
                  value={formData.message}
                  onChange={(e) => handleInputChange('message', e.target.value)}
                  placeholder="Add a personal message to the invitation email..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={!isFormValid || isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending Invitation...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send Invitation
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
