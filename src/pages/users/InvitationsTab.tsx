import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useAuth } from '@/contexts/AuthContext';
import { UserInvitation } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Send,
  Search,
  MoreHorizontal,
  RefreshCw,
  X,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Mail,
  Calendar,
  User,
  Loader2
} from 'lucide-react';

interface InvitationsTabProps {
  invitations: UserInvitation[];
  onInvitationsUpdate: (invitations: UserInvitation[]) => void;
}

export default function InvitationsTab({ invitations, onInvitationsUpdate }: InvitationsTabProps) {
  const { organization } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [resendingId, setResendingId] = useState<string | null>(null);
  const [cancelDialogOpen, setCancelDialogOpen] = useState(false);
  const [selectedInvitation, setSelectedInvitation] = useState<UserInvitation | null>(null);

  // Load invitations when component mounts
  useEffect(() => {
    if (organization) {
      loadInvitations();
    }
  }, [organization]);

  const loadInvitations = async () => {
    if (!organization) return;

    try {
      setIsLoading(true);
      const fetchedInvitations = await UserManagementAPI.getInvitations(organization.id);
      onInvitationsUpdate(fetchedInvitations);
    } catch (error) {
      console.error('Failed to load invitations:', error);
      toast({
        title: "Error",
        description: "Failed to load invitations.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendInvitation = async (invitation: UserInvitation) => {
    try {
      setResendingId(invitation.id);
      await UserManagementAPI.resendInvitation(invitation.id);
      
      // Update invitation status
      const updatedInvitations = invitations.map(inv => 
        inv.id === invitation.id 
          ? { ...inv, invitedAt: new Date().toISOString(), status: 'pending' as const }
          : inv
      );
      onInvitationsUpdate(updatedInvitations);
      
      toast({
        title: "Invitation Resent",
        description: `Invitation resent to ${invitation.email}.`,
      });
    } catch (error) {
      console.error('Failed to resend invitation:', error);
      toast({
        title: "Error",
        description: "Failed to resend invitation.",
        variant: "destructive",
      });
    } finally {
      setResendingId(null);
    }
  };

  const handleCancelInvitation = async () => {
    if (!selectedInvitation) return;

    try {
      await UserManagementAPI.cancelInvitation(selectedInvitation.id);
      
      // Update invitation status
      const updatedInvitations = invitations.map(inv => 
        inv.id === selectedInvitation.id 
          ? { ...inv, status: 'cancelled' as const }
          : inv
      );
      onInvitationsUpdate(updatedInvitations);
      
      toast({
        title: "Invitation Cancelled",
        description: `Invitation to ${selectedInvitation.email} has been cancelled.`,
      });
    } catch (error) {
      console.error('Failed to cancel invitation:', error);
      toast({
        title: "Error",
        description: "Failed to cancel invitation.",
        variant: "destructive",
      });
    } finally {
      setCancelDialogOpen(false);
      setSelectedInvitation(null);
    }
  };

  const openCancelDialog = (invitation: UserInvitation) => {
    setSelectedInvitation(invitation);
    setCancelDialogOpen(true);
  };

  // Filter invitations
  const filteredInvitations = invitations.filter(invitation => {
    const matchesSearch = !searchTerm || 
      invitation.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invitation.role.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || invitation.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status: UserInvitation['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="text-orange-600 border-orange-600"><Clock className="w-3 h-3 mr-1" />Pending</Badge>;
      case 'accepted':
        return <Badge variant="default" className="bg-green-600"><CheckCircle className="w-3 h-3 mr-1" />Accepted</Badge>;
      case 'expired':
        return <Badge variant="destructive"><XCircle className="w-3 h-3 mr-1" />Expired</Badge>;
      case 'cancelled':
        return <Badge variant="secondary"><X className="w-3 h-3 mr-1" />Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date();
  };

  const canResend = (invitation: UserInvitation) => {
    return invitation.status === 'expired' || (invitation.status === 'pending' && isExpired(invitation.expiresAt));
  };

  const canCancel = (invitation: UserInvitation) => {
    return invitation.status === 'pending' && !isExpired(invitation.expiresAt);
  };

  // Statistics
  const stats = {
    total: invitations.length,
    pending: invitations.filter(inv => inv.status === 'pending').length,
    accepted: invitations.filter(inv => inv.status === 'accepted').length,
    expired: invitations.filter(inv => inv.status === 'expired').length,
    cancelled: invitations.filter(inv => inv.status === 'cancelled').length,
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Mail className="h-4 w-4 text-blue-500" />
              <div>
                <p className="text-sm font-medium">Total Invitations</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Pending</p>
                <p className="text-2xl font-bold">{stats.pending}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <div>
                <p className="text-sm font-medium">Accepted</p>
                <p className="text-2xl font-bold">{stats.accepted}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <XCircle className="h-4 w-4 text-red-500" />
              <div>
                <p className="text-sm font-medium">Expired</p>
                <p className="text-2xl font-bold">{stats.expired}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <X className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Cancelled</p>
                <p className="text-2xl font-bold">{stats.cancelled}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between space-x-4">
            <div className="flex items-center space-x-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search invitations by name, email, or role..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="accepted">Accepted</option>
                <option value="expired">Expired</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
            
            <Button
              variant="outline"
              onClick={loadInvitations}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Invitations Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Send className="h-5 w-5" />
            Invitations ({filteredInvitations.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredInvitations.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Invitee</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Department</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Invited</TableHead>
                  <TableHead>Expires</TableHead>
                  <TableHead>Invited By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInvitations.map((invitation) => (
                  <TableRow key={invitation.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                          <User className="w-4 h-4 text-gray-600" />
                        </div>
                        <div>
                          <p className="font-medium">{invitation.firstName} {invitation.lastName}</p>
                          <p className="text-sm text-gray-500">{invitation.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="capitalize">{invitation.role.replace('_', ' ')}</span>
                    </TableCell>
                    <TableCell>{invitation.department || '-'}</TableCell>
                    <TableCell>{getStatusBadge(invitation.status)}</TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3 text-gray-400" />
                        <span className="text-sm">{formatDate(invitation.invitedAt)}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3 text-gray-400" />
                        <span className={`text-sm ${isExpired(invitation.expiresAt) ? 'text-red-600' : ''}`}>
                          {formatDate(invitation.expiresAt)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-600">{invitation.invitedBy}</span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {canResend(invitation) && (
                            <DropdownMenuItem 
                              onClick={() => handleResendInvitation(invitation)}
                              disabled={resendingId === invitation.id}
                            >
                              {resendingId === invitation.id ? (
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              ) : (
                                <Send className="mr-2 h-4 w-4" />
                              )}
                              Resend Invitation
                            </DropdownMenuItem>
                          )}
                          {canCancel(invitation) && (
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => openCancelDialog(invitation)}
                            >
                              <X className="mr-2 h-4 w-4" />
                              Cancel Invitation
                            </DropdownMenuItem>
                          )}
                          {invitation.message && (
                            <DropdownMenuItem>
                              <AlertTriangle className="mr-2 h-4 w-4" />
                              View Message
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <Send className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No invitations found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Try adjusting your search criteria.' 
                  : 'Start by inviting new users to join your organization.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Cancel Invitation Dialog */}
      <AlertDialog open={cancelDialogOpen} onOpenChange={setCancelDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Cancel Invitation
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to cancel the invitation for {selectedInvitation?.firstName} {selectedInvitation?.lastName} ({selectedInvitation?.email})? 
              This action cannot be undone and they will not be able to accept the invitation.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Keep Invitation</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleCancelInvitation}
              className="bg-red-600 hover:bg-red-700"
            >
              Cancel Invitation
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
