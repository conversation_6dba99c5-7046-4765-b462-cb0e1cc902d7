import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { User, UserSession } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Loader2, 
  Monitor,
  Smartphone,
  Tablet,
  Globe,
  MapPin,
  Clock,
  Shield,
  X,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

interface UserSessionsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User | null;
}

export default function UserSessionsDialog({ open, onOpenChange, user }: UserSessionsDialogProps) {
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [terminatingSession, setTerminatingSession] = useState<string | null>(null);
  const [terminateAllDialogOpen, setTerminateAllDialogOpen] = useState(false);

  // Load user sessions when dialog opens
  useEffect(() => {
    if (open && user) {
      loadUserSessions();
    }
  }, [open, user]);

  const loadUserSessions = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      const userSessions = await UserManagementAPI.getUserSessions(user.id);
      setSessions(userSessions);
    } catch (error) {
      console.error('Failed to load user sessions:', error);
      toast({
        title: "Error",
        description: "Failed to load user sessions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      setTerminatingSession(sessionId);
      await UserManagementAPI.terminateSession(sessionId);
      
      // Remove the terminated session from the list
      setSessions(prev => prev.filter(session => session.id !== sessionId));
      
      toast({
        title: "Session Terminated",
        description: "The user session has been terminated successfully.",
      });
    } catch (error) {
      console.error('Failed to terminate session:', error);
      toast({
        title: "Error",
        description: "Failed to terminate session.",
        variant: "destructive",
      });
    } finally {
      setTerminatingSession(null);
    }
  };

  const handleTerminateAllSessions = async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      await UserManagementAPI.terminateAllSessions(user.id, true);
      
      // Reload sessions to reflect changes
      await loadUserSessions();
      
      toast({
        title: "All Sessions Terminated",
        description: "All user sessions except the current one have been terminated.",
      });
    } catch (error) {
      console.error('Failed to terminate all sessions:', error);
      toast({
        title: "Error",
        description: "Failed to terminate all sessions.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setTerminateAllDialogOpen(false);
    }
  };

  const getDeviceIcon = (deviceInfo: string) => {
    const device = deviceInfo.toLowerCase();
    if (device.includes('mobile') || device.includes('android') || device.includes('iphone')) {
      return <Smartphone className="h-4 w-4" />;
    }
    if (device.includes('tablet') || device.includes('ipad')) {
      return <Tablet className="h-4 w-4" />;
    }
    return <Monitor className="h-4 w-4" />;
  };

  const formatLastActivity = (lastActivity: string) => {
    const date = new Date(lastActivity);
    const now = new Date();
    const diffInMinutes = (now.getTime() - date.getTime()) / (1000 * 60);

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${Math.floor(diffInMinutes)} minutes ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`;
    return date.toLocaleDateString();
  };

  const formatSessionDuration = (createdAt: string) => {
    const start = new Date(createdAt);
    const now = new Date();
    const diffInHours = (now.getTime() - start.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) return `${Math.floor(diffInHours * 60)} minutes`;
    if (diffInHours < 24) return `${Math.floor(diffInHours)} hours`;
    return `${Math.floor(diffInHours / 24)} days`;
  };

  const activeSessions = sessions.filter(session => session.isActive);
  const inactiveSessions = sessions.filter(session => !session.isActive);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              User Sessions - {user?.firstName} {user?.lastName}
            </DialogTitle>
            <DialogDescription>
              Manage active and recent sessions for this user. You can terminate suspicious or unauthorized sessions.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Session Summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-green-500" />
                    <div>
                      <p className="text-sm font-medium">Active Sessions</p>
                      <p className="text-2xl font-bold">{activeSessions.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-4 w-4 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium">Recent Sessions</p>
                      <p className="text-2xl font-bold">{inactiveSessions.length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center space-x-2">
                    <Globe className="h-4 w-4 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium">Unique Locations</p>
                      <p className="text-2xl font-bold">
                        {new Set(sessions.map(s => s.location)).size}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Actions */}
            <div className="flex justify-between items-center">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={loadUserSessions}
                  disabled={isLoading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </div>
              
              {activeSessions.length > 1 && (
                <Button
                  variant="destructive"
                  onClick={() => setTerminateAllDialogOpen(true)}
                  disabled={isLoading}
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Terminate All Sessions
                </Button>
              )}
            </div>

            {/* Active Sessions */}
            {activeSessions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Shield className="h-4 w-4 text-green-500" />
                    Active Sessions ({activeSessions.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Device</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>IP Address</TableHead>
                        <TableHead>Last Activity</TableHead>
                        <TableHead>Duration</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {activeSessions.map((session) => (
                        <TableRow key={session.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getDeviceIcon(session.deviceInfo)}
                              <div>
                                <p className="font-medium">{session.deviceInfo}</p>
                                <p className="text-sm text-gray-500 truncate max-w-48">
                                  {session.userAgent}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span>{session.location}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                              {session.ipAddress}
                            </code>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {formatLastActivity(session.lastActivity)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <span className="text-sm">
                              {formatSessionDuration(session.createdAt)}
                            </span>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Badge variant={session.isCurrent ? 'default' : 'secondary'}>
                                {session.isCurrent ? 'Current' : 'Active'}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {!session.isCurrent && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleTerminateSession(session.id)}
                                disabled={terminatingSession === session.id}
                              >
                                {terminatingSession === session.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            )}

            {/* Recent Sessions */}
            {inactiveSessions.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Clock className="h-4 w-4 text-gray-500" />
                    Recent Sessions ({inactiveSessions.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Device</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>IP Address</TableHead>
                        <TableHead>Session Period</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {inactiveSessions.slice(0, 10).map((session) => (
                        <TableRow key={session.id}>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {getDeviceIcon(session.deviceInfo)}
                              <div>
                                <p className="font-medium">{session.deviceInfo}</p>
                                <p className="text-sm text-gray-500 truncate max-w-48">
                                  {session.userAgent}
                                </p>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-3 w-3 text-gray-400" />
                              <span>{session.location}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                              {session.ipAddress}
                            </code>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <p>{new Date(session.createdAt).toLocaleDateString()}</p>
                              <p className="text-gray-500">
                                {formatSessionDuration(session.createdAt)} duration
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">Ended</Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                  
                  {inactiveSessions.length > 10 && (
                    <div className="text-center mt-4">
                      <p className="text-sm text-gray-500">
                        Showing 10 of {inactiveSessions.length} recent sessions
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Empty State */}
            {sessions.length === 0 && !isLoading && (
              <div className="text-center py-8">
                <Shield className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No sessions found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  This user has no active or recent sessions.
                </p>
              </div>
            )}

            {/* Loading State */}
            {isLoading && (
              <div className="text-center py-8">
                <Loader2 className="mx-auto h-8 w-8 animate-spin text-gray-400" />
                <p className="mt-2 text-sm text-gray-500">Loading sessions...</p>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Terminate All Sessions Confirmation */}
      <AlertDialog open={terminateAllDialogOpen} onOpenChange={setTerminateAllDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              Terminate All Sessions
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will terminate all active sessions for {user?.firstName} {user?.lastName} except their current session. 
              They will need to log in again on all other devices. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleTerminateAllSessions}
              className="bg-red-600 hover:bg-red-700"
            >
              Terminate All Sessions
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
