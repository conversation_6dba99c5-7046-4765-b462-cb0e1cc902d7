import React, { useState, useMemo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { mockUsers } from '@/data/multiTenantData';
import { User } from '@/types/multiTenant';
import AddUserDialog from './AddUserDialog';
import EditUserDialog from './EditUserDialog';
import DeleteUserDialog from './DeleteUserDialog';
import InviteUserDialog from './InviteUserDialog';
import UserSessionsDialog from './UserSessionsDialog';
import InvitationsTab from './InvitationsTab';
import { UserInvitation } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import {
  Users as UsersIcon,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Send,
  Shield,
  Filter,
  Download,
  Upload,
  Clock,
} from 'lucide-react';

export default function Users() {
  const { organization } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [addUserDialogOpen, setAddUserDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [deleteUserDialogOpen, setDeleteUserDialogOpen] = useState(false);
  const [inviteUserDialogOpen, setInviteUserDialogOpen] = useState(false);
  const [userSessionsDialogOpen, setUserSessionsDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [invitations, setInvitations] = useState<UserInvitation[]>([]);
  const [activeTab, setActiveTab] = useState('users');

  // Filter users by current organization
  const organizationUsers = useMemo(() => {
    return users.filter(user => user.organizationId === organization?.id);
  }, [users, organization?.id]);

  // Filter users based on search term and filters
  const filteredUsers = useMemo(() => {
    let filtered = organizationUsers;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.department?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => {
        switch (statusFilter) {
          case 'active':
            return user.isActive && !user.accountLocked;
          case 'inactive':
            return !user.isActive;
          case 'locked':
            return user.accountLocked;
          case 'pending':
            return user.invitationStatus === 'pending';
          default:
            return true;
        }
      });
    }

    // Apply role filter
    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    return filtered;
  }, [organizationUsers, searchTerm, statusFilter, roleFilter]);

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default';
      case 'hiring_manager':
        return 'secondary';
      case 'recruiter':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatRole = (role: string) => {
    return role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatLastLogin = (lastLogin?: string) => {
    if (!lastLogin) return 'Never';
    return new Date(lastLogin).toLocaleDateString();
  };

  const handleAddUser = (newUser: User) => {
    setUsers(prev => [...prev, newUser]);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setEditUserDialogOpen(true);
  };

  const handleUpdateUser = (updatedUser: User) => {
    setUsers(prev => prev.map(user => user.id === updatedUser.id ? updatedUser : user));
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteUserDialogOpen(true);
  };

  const handleUserDeleted = (userId: string) => {
    setUsers(prev => prev.filter(user => user.id !== userId));
  };

  const handleUserInvited = (invitation: UserInvitation) => {
    setInvitations(prev => [...prev, invitation]);
    // Switch to invitations tab to show the new invitation
    setActiveTab('invitations');
  };

  const handleInvitationsUpdate = (updatedInvitations: UserInvitation[]) => {
    setInvitations(updatedInvitations);
  };

  const handleViewSessions = (user: User) => {
    setSelectedUser(user);
    setUserSessionsDialogOpen(true);
  };

  const getAccountStatus = (user: User): { status: string; variant: 'default' | 'secondary' | 'destructive' | 'outline' } => {
    if (user.invitationStatus === 'pending') return { status: 'Pending', variant: 'outline' };
    if (user.accountLocked) return { status: 'Locked', variant: 'destructive' };
    if (!user.isActive) return { status: 'Inactive', variant: 'secondary' };
    return { status: 'Active', variant: 'default' };
  };

  const uniqueRoles = Array.from(new Set(organizationUsers.map(user => user.role)));

  // Load initial invitations data
  useEffect(() => {
    const loadInitialInvitations = async () => {
      if (organization) {
        try {
          const fetchedInvitations = await UserManagementAPI.getInvitations(organization.id);
          setInvitations(fetchedInvitations);
        } catch (error) {
          console.error('Failed to load initial invitations:', error);
        }
      }
    };

    loadInitialInvitations();
  }, [organization]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
          <p className="text-gray-600">
            Manage users and their roles within {organization?.name}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => setInviteUserDialogOpen(true)}>
            <Send className="mr-2 h-4 w-4" />
            Invite User
          </Button>
          <Button onClick={() => setAddUserDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Add User
          </Button>
        </div>
      </div>

      {/* Tabs Navigation */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-fit grid-cols-2">
            <TabsTrigger value="users" className="flex items-center gap-2">
              <UsersIcon className="h-4 w-4" />
              Users ({filteredUsers.length})
            </TabsTrigger>
            <TabsTrigger value="invitations" className="flex items-center gap-2">
              <Send className="h-4 w-4" />
              Invitations ({invitations.length})
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-6">
          {/* Search and Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search users by name, email, role, or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Filter className="h-4 w-4 text-gray-400" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                    <option value="locked">Locked</option>
                    <option value="pending">Pending</option>
                  </select>

                  <select
                    value={roleFilter}
                    onChange={(e) => setRoleFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="all">All Roles</option>
                    {uniqueRoles.map((role) => (
                      <option key={role} value={role}>
                        {formatRole(role)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <UsersIcon className="mr-2 h-5 w-5" />
                Team Members ({filteredUsers.length})
              </CardTitle>
            </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Department</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar} alt={`${user.firstName} ${user.lastName}`} />
                        <AvatarFallback>
                          {user.firstName[0]}{user.lastName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium text-gray-900">
                          {user.firstName} {user.lastName}
                        </p>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <Mail className="h-3 w-3" />
                          <span>{user.email}</span>
                        </div>
                        {user.phone && (
                          <div className="flex items-center space-x-2 text-sm text-gray-500">
                            <Phone className="h-3 w-3" />
                            <span>{user.phone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getRoleBadgeVariant(user.role)}>
                      {formatRole(user.role)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <p className="font-medium text-gray-900">{user.department}</p>
                      <p className="text-sm text-gray-500">{user.title}</p>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {(() => {
                        const { status, variant } = getAccountStatus(user);
                        return (
                          <>
                            {status === 'Active' && <UserCheck className="h-4 w-4 text-green-600" />}
                            {status === 'Inactive' && <UserX className="h-4 w-4 text-gray-600" />}
                            {status === 'Locked' && <Shield className="h-4 w-4 text-red-600" />}
                            {status === 'Pending' && <Clock className="h-4 w-4 text-orange-600" />}
                            <Badge variant={variant}>{status}</Badge>
                          </>
                        );
                      })()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm text-gray-600">
                      {formatLastLogin(user.lastLogin)}
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleEditUser(user)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit User
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleViewSessions(user)}>
                          <Shield className="mr-2 h-4 w-4" />
                          View Sessions
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className="text-red-600"
                          onClick={() => handleDeleteUser(user)}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete User
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredUsers.length === 0 && (
            <div className="text-center py-8">
              <UsersIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by adding a new user.'}
              </p>
            </div>
          )}
          </CardContent>
        </Card>
      </TabsContent>

      {/* Invitations Tab */}
      <TabsContent value="invitations">
        <InvitationsTab
          invitations={invitations}
          onInvitationsUpdate={handleInvitationsUpdate}
        />
      </TabsContent>
    </Tabs>

      {/* Add User Dialog */}
      <AddUserDialog
        open={addUserDialogOpen}
        onOpenChange={setAddUserDialogOpen}
        onUserAdded={handleAddUser}
      />

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editUserDialogOpen}
        onOpenChange={setEditUserDialogOpen}
        user={selectedUser}
        onUserUpdated={handleUpdateUser}
      />

      {/* Delete User Dialog */}
      <DeleteUserDialog
        open={deleteUserDialogOpen}
        onOpenChange={setDeleteUserDialogOpen}
        user={selectedUser}
        onUserDeleted={handleUserDeleted}
      />

      {/* Invite User Dialog */}
      <InviteUserDialog
        open={inviteUserDialogOpen}
        onOpenChange={setInviteUserDialogOpen}
        onUserInvited={handleUserInvited}
      />

      {/* User Sessions Dialog */}
      <UserSessionsDialog
        open={userSessionsDialogOpen}
        onOpenChange={setUserSessionsDialogOpen}
        user={selectedUser}
      />
    </div>
  );
}
