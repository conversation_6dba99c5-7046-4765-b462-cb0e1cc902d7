import React, { useState, use<PERSON>emo, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '@/components/ui/resizable';
import {
  Plus,
  Search,
  Filter,
  Eye,
  Settings,
  Save,
  Share,
  FileText,
  Users,
  Calendar,
  TrendingUp,
  Sparkles,
  BarChart3,
  Type,
  Mail,
  Phone,
  Hash,
  ChevronDown,
  CheckSquare,
  Upload,
  Link,
  AlignLeft,
  Star,
  Minus,
  Clock,
  Circle,
  Square,
  Image,
  Grid3X3,
  PenTool,
  Palette,
  MapPin,
  Minus as SeparatorIcon,
  FileX as FileBreak,
  CalendarDays,
  GripVertical,
  Trash2,
  MessageSquare,
  ShoppingCart,
  CheckCircle,
} from 'lucide-react';
import { mockForms } from '@/data/formBuilderMockData';
import { FormSchema, FormField } from '@/types/formBuilder';
import { useToast } from '@/hooks/use-toast';

// Complex field types with enhanced capabilities
const complexFieldTypes = [
  // Basic Fields
  { type: 'text', label: 'Text Input', icon: Type, description: 'Single line text input', category: 'basic' },
  { type: 'textarea', label: 'Text Area', icon: AlignLeft, description: 'Multi-line text input', category: 'basic' },
  { type: 'richtext', label: 'Rich Text Editor', icon: FileText, description: 'Rich text with formatting', category: 'advanced' },
  
  // Contact Fields
  { type: 'email', label: 'Email', icon: Mail, description: 'Email address input', category: 'contact' },
  { type: 'phone', label: 'Phone', icon: Phone, description: 'Phone number input', category: 'contact' },
  { type: 'address', label: 'Address', icon: MapPin, description: 'Address with autocomplete', category: 'contact' },
  
  // Number & Date Fields
  { type: 'number', label: 'Number', icon: Hash, description: 'Numeric input', category: 'number' },
  { type: 'slider', label: 'Slider', icon: Minus, description: 'Range slider input', category: 'number' },
  { type: 'date', label: 'Date', icon: Calendar, description: 'Date picker', category: 'date' },
  { type: 'datetime', label: 'Date & Time', icon: Clock, description: 'Date and time picker', category: 'date' },
  { type: 'time', label: 'Time', icon: Clock, description: 'Time picker', category: 'date' },
  { type: 'daterange', label: 'Date Range', icon: CalendarDays, description: 'Date range picker', category: 'date' },
  
  // Choice Fields
  { type: 'select', label: 'Dropdown', icon: ChevronDown, description: 'Single selection dropdown', category: 'choice' },
  { type: 'radio', label: 'Radio Buttons', icon: Circle, description: 'Single selection radio buttons', category: 'choice' },
  { type: 'multiselect', label: 'Multi Select', icon: CheckSquare, description: 'Multiple selection dropdown', category: 'choice' },
  { type: 'checkbox', label: 'Checkboxes', icon: Square, description: 'Multiple selection checkboxes', category: 'choice' },
  { type: 'image_choice', label: 'Image Choice', icon: Image, description: 'Select from image options', category: 'choice' },
  
  // Advanced Fields
  { type: 'rating', label: 'Rating', icon: Star, description: 'Star rating input', category: 'advanced' },
  { type: 'matrix', label: 'Matrix/Grid', icon: Grid3X3, description: 'Grid of questions and answers', category: 'advanced' },
  { type: 'signature', label: 'Signature', icon: PenTool, description: 'Digital signature capture', category: 'advanced' },
  { type: 'file', label: 'File Upload', icon: Upload, description: 'File upload with preview', category: 'advanced' },
  { type: 'color', label: 'Color Picker', icon: Palette, description: 'Color selection input', category: 'advanced' },
  { type: 'url', label: 'URL', icon: Link, description: 'Website URL input', category: 'basic' },
  
  // Layout Fields
  { type: 'section_break', label: 'Section Break', icon: SeparatorIcon, description: 'Visual section separator', category: 'layout' },
  { type: 'page_break', label: 'Page Break', icon: FileBreak, description: 'Multi-page form break', category: 'layout' },
];

const fieldCategories = [
  { id: 'all', label: 'All Fields', count: complexFieldTypes.length },
  { id: 'basic', label: 'Basic', count: complexFieldTypes.filter(f => f.category === 'basic').length },
  { id: 'contact', label: 'Contact', count: complexFieldTypes.filter(f => f.category === 'contact').length },
  { id: 'choice', label: 'Choice', count: complexFieldTypes.filter(f => f.category === 'choice').length },
  { id: 'number', label: 'Number & Date', count: complexFieldTypes.filter(f => f.category === 'number' || f.category === 'date').length },
  { id: 'advanced', label: 'Advanced', count: complexFieldTypes.filter(f => f.category === 'advanced').length },
  { id: 'layout', label: 'Layout', count: complexFieldTypes.filter(f => f.category === 'layout').length },
];

interface IntegratedFormBuilderProps {
  isOpen: boolean;
  onClose: () => void;
  editingForm?: FormSchema | null;
  onSave?: (form: FormSchema, status: 'draft' | 'published') => void;
}

const IntegratedFormBuilder: React.FC<IntegratedFormBuilderProps> = ({
  isOpen,
  onClose,
  editingForm = null,
  onSave,
}) => {
  const [activeTab, setActiveTab] = useState('start');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentForm, setCurrentForm] = useState<FormSchema | null>(editingForm);
  const [formTitle, setFormTitle] = useState(editingForm?.title || '');
  const [formDescription, setFormDescription] = useState(editingForm?.description || '');
  const [formFields, setFormFields] = useState<FormField[]>([]);
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);
  const [draggedFieldId, setDraggedFieldId] = useState<string | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [previewData, setPreviewData] = useState<Record<string, unknown>>({});
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [currentPage, setCurrentPage] = useState(0);
  const { toast } = useToast();

  // Initialize form data when editing form changes
  useEffect(() => {
    if (editingForm) {
      setFormTitle(editingForm.title);
      setFormDescription(editingForm.description || '');
      setFormFields(editingForm.fields || []);
      setCurrentForm(editingForm);
    } else {
      setFormTitle('');
      setFormDescription('');
      setFormFields([]);
      setCurrentForm(null);
    }
    // Reset other states when form changes
    setSelectedFieldId(null);
    setPreviewData({});
    setValidationErrors({});
    setCurrentPage(0);
  }, [editingForm]);

  // Save and publish handlers
  const handleSaveDraft = () => {
    if (!formTitle.trim()) {
      toast({
        title: "Form Title Required",
        description: "Please enter a form title before saving.",
        variant: "destructive",
      });
      return;
    }

    const formData: FormSchema = {
      id: editingForm?.id || `form-${Date.now()}`,
      title: formTitle,
      description: formDescription,
      fields: formFields,
      status: 'draft',
      submissionCount: editingForm?.submissionCount || 0,
      createdAt: editingForm?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      shareUrl: editingForm?.shareUrl,
    };

    onSave?.(formData, 'draft');
    toast({
      title: "Draft Saved",
      description: "Your form has been saved as a draft.",
    });
    onClose();
  };

  const handlePublishForm = () => {
    if (!formTitle.trim()) {
      toast({
        title: "Form Title Required",
        description: "Please enter a form title before publishing.",
        variant: "destructive",
      });
      return;
    }

    if (formFields.length === 0) {
      toast({
        title: "No Fields Added",
        description: "Please add at least one field before publishing.",
        variant: "destructive",
      });
      return;
    }

    const formData: FormSchema = {
      id: editingForm?.id || `form-${Date.now()}`,
      title: formTitle,
      description: formDescription,
      fields: formFields,
      status: 'published',
      submissionCount: editingForm?.submissionCount || 0,
      createdAt: editingForm?.createdAt || new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      shareUrl: `https://forms.talentflow.com/${editingForm?.id || `form-${Date.now()}`}`,
    };

    onSave?.(formData, 'published');
    toast({
      title: "Form Published",
      description: "Your form is now live and ready to collect responses.",
    });
    onClose();
  };

  // Filter field types based on category and search
  const filteredFieldTypes = useMemo(() => {
    let filtered = complexFieldTypes;
    
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(field => 
        field.category === selectedCategory || 
        (selectedCategory === 'number' && (field.category === 'number' || field.category === 'date'))
      );
    }
    
    if (searchTerm) {
      filtered = filtered.filter(field =>
        field.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        field.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    return filtered;
  }, [selectedCategory, searchTerm]);

  const handleAddField = (fieldType: { type: string; label: string; icon: React.ComponentType; description: string; category: string }) => {
    const newField = {
      id: `field-${Date.now()}`,
      type: fieldType.type,
      label: `New ${fieldType.label}`,
      required: false,
      order: formFields.length,
      placeholder: fieldType.type === 'text' ? 'Enter text...' : undefined,
      options: ['select', 'multiselect', 'radio', 'checkbox'].includes(fieldType.type) 
        ? [{ value: 'option1', label: 'Option 1' }, { value: 'option2', label: 'Option 2' }] 
        : undefined,
    };
    
    setFormFields([...formFields, newField]);
    toast({
      title: "Field Added",
      description: `${fieldType.label} has been added to your form.`,
    });
  };

  // Template system
  const formTemplates = [
    {
      id: 'job_application',
      name: 'Job Application',
      description: 'Complete job application form with all standard fields',
      icon: Users,
      color: 'blue',
      fields: [
        { type: 'text', label: 'Full Name', required: true, placeholder: 'Enter your full name' },
        { type: 'email', label: 'Email Address', required: true, placeholder: '<EMAIL>' },
        { type: 'phone', label: 'Phone Number', required: true, placeholder: '+****************' },
        { type: 'address', label: 'Address', required: true },
        { type: 'section_break', label: 'Professional Information' },
        { type: 'text', label: 'Current Job Title', placeholder: 'e.g., Software Engineer' },
        { type: 'text', label: 'Current Company', placeholder: 'e.g., Tech Corp Inc.' },
        { type: 'number', label: 'Years of Experience', placeholder: '5', validation: { min: 0, max: 50 } },
        { type: 'select', label: 'Education Level', required: true, options: [
          { label: 'High School', value: 'high_school' },
          { label: 'Bachelor\'s Degree', value: 'bachelors' },
          { label: 'Master\'s Degree', value: 'masters' },
          { label: 'PhD', value: 'phd' }
        ]},
        { type: 'textarea', label: 'Why are you interested in this position?', required: true, placeholder: 'Tell us about your motivation...' },
        { type: 'file', label: 'Resume/CV', required: true, fileConfig: { allowMultiple: false, maxFiles: 1, maxSize: 5 } },
        { type: 'file', label: 'Cover Letter', fileConfig: { allowMultiple: false, maxFiles: 1, maxSize: 5 } },
        { type: 'checkbox', label: 'Availability', options: [
          { label: 'Available to start immediately', value: 'immediate' },
          { label: 'Available for remote work', value: 'remote' },
          { label: 'Available for travel', value: 'travel' }
        ]},
        { type: 'date', label: 'Earliest Start Date', required: true }
      ]
    },
    {
      id: 'feedback_survey',
      name: 'Feedback Survey',
      description: 'Customer feedback and satisfaction survey',
      icon: BarChart3,
      color: 'green',
      fields: [
        { type: 'text', label: 'Name (Optional)', placeholder: 'Your name' },
        { type: 'email', label: 'Email (Optional)', placeholder: '<EMAIL>' },
        { type: 'section_break', label: 'Service Experience' },
        { type: 'rating', label: 'Overall Satisfaction', required: true, ratingConfig: { maxRating: 5, allowHalf: false } },
        { type: 'rating', label: 'Product Quality', required: true, ratingConfig: { maxRating: 5, allowHalf: false } },
        { type: 'rating', label: 'Customer Service', required: true, ratingConfig: { maxRating: 5, allowHalf: false } },
        { type: 'select', label: 'How did you hear about us?', options: [
          { label: 'Google Search', value: 'google' },
          { label: 'Social Media', value: 'social' },
          { label: 'Friend/Family', value: 'referral' },
          { label: 'Advertisement', value: 'ad' },
          { label: 'Other', value: 'other' }
        ]},
        { type: 'textarea', label: 'What did you like most?', placeholder: 'Tell us what you enjoyed...' },
        { type: 'textarea', label: 'What could we improve?', placeholder: 'Your suggestions for improvement...' },
        { type: 'radio', label: 'Would you recommend us to others?', required: true, options: [
          { label: 'Definitely', value: 'definitely' },
          { label: 'Probably', value: 'probably' },
          { label: 'Not sure', value: 'not_sure' },
          { label: 'Probably not', value: 'probably_not' },
          { label: 'Definitely not', value: 'definitely_not' }
        ]}
      ]
    },
    {
      id: 'event_registration',
      name: 'Event Registration',
      description: 'Registration form for events and workshops',
      icon: Calendar,
      color: 'purple',
      fields: [
        { type: 'text', label: 'Full Name', required: true, placeholder: 'Enter your full name' },
        { type: 'email', label: 'Email Address', required: true, placeholder: '<EMAIL>' },
        { type: 'phone', label: 'Phone Number', required: true, placeholder: '+****************' },
        { type: 'text', label: 'Organization/Company', placeholder: 'Your company name' },
        { type: 'text', label: 'Job Title', placeholder: 'Your position' },
        { type: 'section_break', label: 'Event Preferences' },
        { type: 'radio', label: 'Attendance Type', required: true, options: [
          { label: 'In-person', value: 'in_person' },
          { label: 'Virtual', value: 'virtual' },
          { label: 'Hybrid (both)', value: 'hybrid' }
        ]},
        { type: 'multiselect', label: 'Sessions of Interest', options: [
          { label: 'Keynote Presentation', value: 'keynote' },
          { label: 'Technical Workshops', value: 'workshops' },
          { label: 'Networking Session', value: 'networking' },
          { label: 'Panel Discussion', value: 'panel' },
          { label: 'Q&A Session', value: 'qa' }
        ]},
        { type: 'select', label: 'Dietary Restrictions', options: [
          { label: 'None', value: 'none' },
          { label: 'Vegetarian', value: 'vegetarian' },
          { label: 'Vegan', value: 'vegan' },
          { label: 'Gluten-free', value: 'gluten_free' },
          { label: 'Other', value: 'other' }
        ]},
        { type: 'textarea', label: 'Special Requirements', placeholder: 'Any accessibility needs or special requests...' },
        { type: 'checkbox', label: 'Communication Preferences', options: [
          { label: 'Send me event updates', value: 'updates' },
          { label: 'Add me to mailing list', value: 'mailing_list' },
          { label: 'Send me future event notifications', value: 'future_events' }
        ]}
      ]
    },
    {
      id: 'contact_form',
      name: 'Contact Form',
      description: 'Simple contact form for inquiries',
      icon: MessageSquare,
      color: 'orange',
      fields: [
        { type: 'text', label: 'Name', required: true, placeholder: 'Your full name' },
        { type: 'email', label: 'Email', required: true, placeholder: '<EMAIL>' },
        { type: 'phone', label: 'Phone Number', placeholder: '+****************' },
        { type: 'text', label: 'Subject', required: true, placeholder: 'What is this regarding?' },
        { type: 'select', label: 'Inquiry Type', required: true, options: [
          { label: 'General Question', value: 'general' },
          { label: 'Technical Support', value: 'support' },
          { label: 'Sales Inquiry', value: 'sales' },
          { label: 'Partnership', value: 'partnership' },
          { label: 'Other', value: 'other' }
        ]},
        { type: 'textarea', label: 'Message', required: true, placeholder: 'Please describe your inquiry in detail...', validation: { minLength: 10, maxLength: 1000 } },
        { type: 'checkbox', label: 'Preferences', options: [
          { label: 'I would like to receive a response via email', value: 'email_response' },
          { label: 'I would like to receive a phone call', value: 'phone_response' },
          { label: 'Subscribe to newsletter', value: 'newsletter' }
        ]}
      ]
    },
    {
      id: 'order_form',
      name: 'Order Form',
      description: 'Product order form with customer details',
      icon: ShoppingCart,
      color: 'indigo',
      fields: [
        { type: 'section_break', label: 'Customer Information' },
        { type: 'text', label: 'Full Name', required: true, placeholder: 'Enter your full name' },
        { type: 'email', label: 'Email Address', required: true, placeholder: '<EMAIL>' },
        { type: 'phone', label: 'Phone Number', required: true, placeholder: '+****************' },
        { type: 'address', label: 'Billing Address', required: true },
        { type: 'checkbox', label: 'Shipping Options', options: [
          { label: 'Same as billing address', value: 'same_address' },
          { label: 'Different shipping address', value: 'different_address' }
        ]},
        { type: 'page_break' },
        { type: 'section_break', label: 'Order Details' },
        { type: 'select', label: 'Product Category', required: true, options: [
          { label: 'Electronics', value: 'electronics' },
          { label: 'Clothing', value: 'clothing' },
          { label: 'Books', value: 'books' },
          { label: 'Home & Garden', value: 'home_garden' },
          { label: 'Sports', value: 'sports' }
        ]},
        { type: 'text', label: 'Product Name/SKU', required: true, placeholder: 'Enter product name or SKU' },
        { type: 'number', label: 'Quantity', required: true, placeholder: '1', validation: { min: 1, max: 100 } },
        { type: 'select', label: 'Size (if applicable)', options: [
          { label: 'Small', value: 'S' },
          { label: 'Medium', value: 'M' },
          { label: 'Large', value: 'L' },
          { label: 'Extra Large', value: 'XL' }
        ]},
        { type: 'color', label: 'Color Preference' },
        { type: 'textarea', label: 'Special Instructions', placeholder: 'Any special requests or notes...' },
        { type: 'radio', label: 'Shipping Speed', required: true, options: [
          { label: 'Standard (5-7 days)', value: 'standard' },
          { label: 'Express (2-3 days)', value: 'express' },
          { label: 'Overnight', value: 'overnight' }
        ]}
      ]
    }
  ];

  const handleUseTemplate = (templateId: string) => {
    const template = formTemplates.find(t => t.id === templateId);
    if (!template) return;

    const templateFields: FormField[] = template.fields.map((field, index) => ({
      id: `field_${Date.now()}_${index}`,
      order: index,
      ...field
    }));

    setFormFields(templateFields);
    setCurrentPage(0);
    setPreviewData({});
    setValidationErrors({});
    setSelectedFieldId(null);
    setActiveTab('build');

    toast({
      title: "Template Applied",
      description: `${template.name} template has been loaded with ${templateFields.length} fields.`,
    });
  };

  const handleDeleteField = (fieldId: string) => {
    setFormFields(formFields.filter(f => f.id !== fieldId));
    if (selectedFieldId === fieldId) {
      setSelectedFieldId(null);
    }
    toast({
      title: "Field Deleted",
      description: "The field has been removed from your form.",
    });
  };

  const handleSelectField = (fieldId: string) => {
    setSelectedFieldId(fieldId);
  };

  const handleUpdateField = (fieldId: string, updates: Partial<FormField>) => {
    setFormFields(fields =>
      fields.map(field =>
        field.id === fieldId ? { ...field, ...updates } : field
      )
    );
  };

  // Drag and Drop handlers
  const handleDragStart = (e: React.DragEvent, fieldId: string) => {
    setDraggedFieldId(fieldId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', fieldId);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (!draggedFieldId) return;

    const draggedIndex = formFields.findIndex(f => f.id === draggedFieldId);
    if (draggedIndex === -1 || draggedIndex === dropIndex) return;

    const newFields = [...formFields];
    const draggedField = newFields[draggedIndex];

    // Remove dragged field
    newFields.splice(draggedIndex, 1);

    // Insert at new position
    const insertIndex = draggedIndex < dropIndex ? dropIndex - 1 : dropIndex;
    newFields.splice(insertIndex, 0, draggedField);

    // Update order property
    const updatedFields = newFields.map((field, index) => ({
      ...field,
      order: index
    }));

    setFormFields(updatedFields);
    setDraggedFieldId(null);
    setDragOverIndex(null);

    toast({
      title: "Field Reordered",
      description: "Field has been moved to new position.",
    });
  };

  const handleDragEnd = () => {
    setDraggedFieldId(null);
    setDragOverIndex(null);
  };

  // Validation functions
  const validateField = (field: FormField, value: unknown): string | null => {
    // Required field validation
    if (field.required && (!value || (Array.isArray(value) && value.length === 0) || value === '')) {
      return `${field.label} is required`;
    }

    // Skip validation if field is empty and not required
    if (!value || value === '') return null;

    const validation = field.validation;
    if (!validation) return null;

    // String length validation
    if (typeof value === 'string') {
      if (validation.minLength && value.length < validation.minLength) {
        return `${field.label} must be at least ${validation.minLength} characters`;
      }
      if (validation.maxLength && value.length > validation.maxLength) {
        return `${field.label} must be no more than ${validation.maxLength} characters`;
      }
    }

    // Number validation
    if (field.type === 'number' || field.type === 'slider') {
      const numValue = parseFloat(value);
      if (isNaN(numValue)) {
        return `${field.label} must be a valid number`;
      }
      if (validation.min !== undefined && numValue < validation.min) {
        return `${field.label} must be at least ${validation.min}`;
      }
      if (validation.max !== undefined && numValue > validation.max) {
        return `${field.label} must be no more than ${validation.max}`;
      }
    }

    // Pattern validation
    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return `${field.label} format is invalid`;
      }
    }

    // Email validation
    if (field.type === 'email' && typeof value === 'string') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return `${field.label} must be a valid email address`;
      }
    }

    // URL validation
    if (field.type === 'url' && typeof value === 'string') {
      try {
        new URL(value);
      } catch {
        return `${field.label} must be a valid URL`;
      }
    }

    // Phone validation (basic)
    if (field.type === 'phone' && typeof value === 'string') {
      const phoneRegex = /^[+]?[1-9][\d]{0,15}$/;
      if (!phoneRegex.test(value.replace(/[\s\-()]/g, ''))) {
        return `${field.label} must be a valid phone number`;
      }
    }

    return null;
  };

  const validateAllFields = (): boolean => {
    const errors: Record<string, string> = {};
    let hasErrors = false;

    formFields.forEach(field => {
      if (field.type === 'section_break' || field.type === 'page_break') return;

      const error = validateField(field, previewData[field.id]);
      if (error) {
        errors[field.id] = error;
        hasErrors = true;
      }
    });

    setValidationErrors(errors);
    return !hasErrors;
  };

  const handlePreviewSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateAllFields()) {
      toast({
        title: "Form Validation Passed!",
        description: "All fields are valid. In a real form, this would submit the data.",
      });
    } else {
      toast({
        title: "Validation Errors",
        description: "Please fix the errors below before submitting.",
        variant: "destructive",
      });
    }
  };

  // Multi-page form functions
  const splitFieldsIntoPages = (): FormField[][] => {
    const pages: FormField[][] = [];
    let currentPageFields: FormField[] = [];

    formFields.forEach(field => {
      if (field.type === 'page_break') {
        if (currentPageFields.length > 0) {
          pages.push(currentPageFields);
          currentPageFields = [];
        }
      } else {
        currentPageFields.push(field);
      }
    });

    // Add the last page if it has fields
    if (currentPageFields.length > 0) {
      pages.push(currentPageFields);
    }

    // If no pages, return all fields as one page
    return pages.length > 0 ? pages : [formFields.filter(f => f.type !== 'page_break')];
  };

  const validateCurrentPage = (): boolean => {
    const pages = splitFieldsIntoPages();
    const currentPageFields = pages[currentPage] || [];

    const errors: Record<string, string> = {};
    let hasErrors = false;

    currentPageFields.forEach(field => {
      if (field.type === 'section_break') return;

      const error = validateField(field, previewData[field.id]);
      if (error) {
        errors[field.id] = error;
        hasErrors = true;
      }
    });

    // Update validation errors for current page only
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      // Clear errors for current page fields first
      currentPageFields.forEach(field => {
        delete newErrors[field.id];
      });
      // Add new errors
      return { ...newErrors, ...errors };
    });

    return !hasErrors;
  };

  const handleNextPage = () => {
    if (validateCurrentPage()) {
      const pages = splitFieldsIntoPages();
      if (currentPage < pages.length - 1) {
        setCurrentPage(currentPage + 1);
      }
    } else {
      toast({
        title: "Validation Errors",
        description: "Please fix the errors on this page before continuing.",
        variant: "destructive",
      });
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    }
  };

  const handleGoToPage = (pageIndex: number) => {
    const pages = splitFieldsIntoPages();
    if (pageIndex >= 0 && pageIndex < pages.length) {
      // Validate current page before allowing navigation
      if (pageIndex > currentPage && !validateCurrentPage()) {
        toast({
          title: "Validation Errors",
          description: "Please fix the errors on the current page before navigating.",
          variant: "destructive",
        });
        return;
      }
      setCurrentPage(pageIndex);
    }
  };

  const renderFieldPreview = (field: FormField, index: number) => {
    const Icon = complexFieldTypes.find(f => f.type === field.type)?.icon || Type;
    const isSelected = selectedFieldId === field.id;
    const isDragging = draggedFieldId === field.id;
    const isDragOver = dragOverIndex === index;

    // Determine which page this field belongs to
    const getFieldPage = () => {
      const pages = splitFieldsIntoPages();
      for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
        if (pages[pageIndex].some(f => f.id === field.id)) {
          return pageIndex + 1;
        }
      }
      return 1;
    };

    const fieldPage = field.type === 'page_break' ? null : getFieldPage();

    return (
      <div
        key={field.id}
        draggable
        onDragStart={(e) => handleDragStart(e, field.id)}
        onDragOver={(e) => handleDragOver(e, index)}
        onDragLeave={handleDragLeave}
        onDrop={(e) => handleDrop(e, index)}
        onDragEnd={handleDragEnd}
        className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
          isDragging
            ? 'opacity-50 scale-95'
            : isDragOver
            ? 'border-primary bg-primary/10 scale-105'
            : isSelected
            ? 'border-primary bg-primary/5'
            : 'border-border bg-card hover:border-primary/50'
        }`}
        onClick={() => handleSelectField(field.id)}
      >
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <GripVertical className="h-4 w-4 text-muted-foreground cursor-move hover:text-primary" />
            <Icon className="h-4 w-4" />
            <span className="font-medium">{field.label}</span>
            {field.required && <Badge variant="destructive" className="text-xs">Required</Badge>}
            {fieldPage && (
              <Badge variant="secondary" className="text-xs">
                Page {fieldPage}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteField(field.id);
            }}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <div className="ml-6">
          <div className="p-3 border rounded bg-muted/50 text-sm text-muted-foreground">
            {field.type} field preview - {field.description || 'No description'}
          </div>
        </div>
      </div>
    );
  };

  const renderPropertiesPanel = () => {
    const selectedField = formFields.find(f => f.id === selectedFieldId);

    if (!selectedField) {
      return (
        <div className="text-center py-8 text-muted-foreground">
          <Settings className="h-8 w-8 mx-auto mb-2" />
          <p>Select a field to edit its properties</p>
        </div>
      );
    }

    const fieldType = complexFieldTypes.find(f => f.type === selectedField.type);
    const Icon = fieldType?.icon || Type;

    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2 pb-4 border-b">
          <Icon className="h-5 w-5" />
          <div>
            <h3 className="font-semibold">{fieldType?.label || selectedField.type}</h3>
            <p className="text-sm text-muted-foreground">{fieldType?.description}</p>
          </div>
        </div>

        {/* Basic Properties */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Field Label</label>
            <Input
              value={selectedField.label}
              onChange={(e) => handleUpdateField(selectedField.id, { label: e.target.value })}
              placeholder="Enter field label"
            />
          </div>

          <div>
            <label className="text-sm font-medium">Description</label>
            <Input
              value={selectedField.description || ''}
              onChange={(e) => handleUpdateField(selectedField.id, { description: e.target.value })}
              placeholder="Optional field description"
            />
          </div>

          {selectedField.type !== 'section_break' && selectedField.type !== 'page_break' && (
            <>
              <div>
                <label className="text-sm font-medium">Placeholder</label>
                <Input
                  value={selectedField.placeholder || ''}
                  onChange={(e) => handleUpdateField(selectedField.id, { placeholder: e.target.value })}
                  placeholder="Enter placeholder text"
                />
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="required"
                  checked={selectedField.required}
                  onChange={(e) => handleUpdateField(selectedField.id, { required: e.target.checked })}
                />
                <label htmlFor="required" className="text-sm font-medium">
                  Required field
                </label>
              </div>
            </>
          )}
        </div>

        {/* Validation Rules */}
        {selectedField.type !== 'section_break' && selectedField.type !== 'page_break' && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">Validation Rules</h4>

            {/* String length validation */}
            {(['text', 'textarea', 'richtext', 'email', 'url', 'phone'].includes(selectedField.type)) && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-sm font-medium">Min Length</label>
                  <Input
                    type="number"
                    min="0"
                    value={selectedField.validation?.minLength || ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        minLength: e.target.value ? parseInt(e.target.value) : undefined
                      }
                    })}
                    placeholder="0"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max Length</label>
                  <Input
                    type="number"
                    min="1"
                    value={selectedField.validation?.maxLength || ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        maxLength: e.target.value ? parseInt(e.target.value) : undefined
                      }
                    })}
                    placeholder="No limit"
                  />
                </div>
              </div>
            )}

            {/* Number validation */}
            {(['number', 'slider'].includes(selectedField.type)) && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <label className="text-sm font-medium">Min Value</label>
                  <Input
                    type="number"
                    value={selectedField.validation?.min ?? ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        min: e.target.value ? parseFloat(e.target.value) : undefined
                      }
                    })}
                    placeholder="No minimum"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max Value</label>
                  <Input
                    type="number"
                    value={selectedField.validation?.max ?? ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        max: e.target.value ? parseFloat(e.target.value) : undefined
                      }
                    })}
                    placeholder="No maximum"
                  />
                </div>
              </div>
            )}

            {/* Pattern validation */}
            {(['text', 'email', 'phone', 'url'].includes(selectedField.type)) && (
              <div>
                <label className="text-sm font-medium">Custom Pattern (Regex)</label>
                <Input
                  value={selectedField.validation?.pattern || ''}
                  onChange={(e) => handleUpdateField(selectedField.id, {
                    validation: {
                      ...selectedField.validation,
                      pattern: e.target.value || undefined
                    }
                  })}
                  placeholder="e.g., ^[A-Z]{2}[0-9]{4}$"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Regular expression pattern for custom validation
                </p>
              </div>
            )}

            {/* File validation */}
            {selectedField.type === 'file' && (
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium">Allowed File Types</label>
                  <Input
                    value={selectedField.validation?.fileTypes?.join(', ') || ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        fileTypes: e.target.value ? e.target.value.split(',').map(t => t.trim()) : undefined
                      }
                    })}
                    placeholder="e.g., .pdf, .doc, .jpg"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Max File Size (MB)</label>
                  <Input
                    type="number"
                    min="0.1"
                    step="0.1"
                    value={selectedField.validation?.maxFileSize || ''}
                    onChange={(e) => handleUpdateField(selectedField.id, {
                      validation: {
                        ...selectedField.validation,
                        maxFileSize: e.target.value ? parseFloat(e.target.value) : undefined
                      }
                    })}
                    placeholder="10"
                  />
                </div>
              </div>
            )}
          </div>
        )}

        {/* Field-specific Properties */}
        {(['select', 'multiselect', 'radio', 'checkbox', 'image_choice'].includes(selectedField.type)) && (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h4 className="font-medium mb-3">Options</h4>
              <div className="space-y-2">
                {selectedField.options?.map((option, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={option.label}
                      onChange={(e) => {
                        const newOptions = [...(selectedField.options || [])];
                        newOptions[index] = { ...option, label: e.target.value, value: e.target.value.toLowerCase().replace(/\s+/g, '_') };
                        handleUpdateField(selectedField.id, { options: newOptions });
                      }}
                      placeholder={`Option ${index + 1}`}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newOptions = selectedField.options?.filter((_, i) => i !== index);
                        handleUpdateField(selectedField.id, { options: newOptions });
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOptions = [...(selectedField.options || []), { value: `option_${Date.now()}`, label: 'New Option' }];
                    handleUpdateField(selectedField.id, { options: newOptions });
                  }}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Option
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Rating Configuration */}
        {selectedField.type === 'rating' && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">Rating Settings</h4>
            <div>
              <label className="text-sm font-medium">Maximum Rating</label>
              <Input
                type="number"
                min="1"
                max="10"
                value={selectedField.ratingConfig?.maxRating || 5}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  ratingConfig: { ...selectedField.ratingConfig, maxRating: parseInt(e.target.value) }
                })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="allowHalf"
                checked={selectedField.ratingConfig?.allowHalf || false}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  ratingConfig: { ...selectedField.ratingConfig, allowHalf: e.target.checked }
                })}
              />
              <label htmlFor="allowHalf" className="text-sm">Allow half ratings</label>
            </div>
          </div>
        )}

        {/* Slider Configuration */}
        {selectedField.type === 'slider' && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">Slider Settings</h4>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="text-sm font-medium">Min Value</label>
                <Input
                  type="number"
                  value={selectedField.sliderConfig?.min || 0}
                  onChange={(e) => handleUpdateField(selectedField.id, {
                    sliderConfig: { ...selectedField.sliderConfig, min: parseInt(e.target.value) }
                  })}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Max Value</label>
                <Input
                  type="number"
                  value={selectedField.sliderConfig?.max || 100}
                  onChange={(e) => handleUpdateField(selectedField.id, {
                    sliderConfig: { ...selectedField.sliderConfig, max: parseInt(e.target.value) }
                  })}
                />
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">Step</label>
              <Input
                type="number"
                min="1"
                value={selectedField.sliderConfig?.step || 1}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  sliderConfig: { ...selectedField.sliderConfig, step: parseInt(e.target.value) }
                })}
              />
            </div>
          </div>
        )}

        {/* File Upload Configuration */}
        {selectedField.type === 'file' && (
          <div className="space-y-4 border-t pt-4">
            <h4 className="font-medium">File Upload Settings</h4>
            <div>
              <label className="text-sm font-medium">Max Files</label>
              <Input
                type="number"
                min="1"
                value={selectedField.fileConfig?.maxFiles || 1}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  fileConfig: { ...selectedField.fileConfig, maxFiles: parseInt(e.target.value) }
                })}
              />
            </div>
            <div>
              <label className="text-sm font-medium">Max Size (MB)</label>
              <Input
                type="number"
                min="1"
                value={selectedField.fileConfig?.maxSize || 10}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  fileConfig: { ...selectedField.fileConfig, maxSize: parseInt(e.target.value) }
                })}
              />
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="allowMultiple"
                checked={selectedField.fileConfig?.allowMultiple || false}
                onChange={(e) => handleUpdateField(selectedField.id, {
                  fileConfig: { ...selectedField.fileConfig, allowMultiple: e.target.checked }
                })}
              />
              <label htmlFor="allowMultiple" className="text-sm">Allow multiple files</label>
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderLivePreviewField = (field: FormField) => {
    const handlePreviewChange = (value: unknown) => {
      setPreviewData(prev => ({ ...prev, [field.id]: value }));

      // Clear validation error when user starts typing
      if (validationErrors[field.id]) {
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[field.id];
          return newErrors;
        });
      }

      // Real-time validation for immediate feedback
      const error = validateField(field, value);
      if (error) {
        setValidationErrors(prev => ({ ...prev, [field.id]: error }));
      }
    };

    const fieldValue = previewData[field.id] || '';
    const hasError = !!validationErrors[field.id];
    const errorMessage = validationErrors[field.id];

    switch (field.type) {
      case 'text':
      case 'email':
      case 'url':
        return (
          <div>
            <Input
              type={field.type === 'email' ? 'email' : field.type === 'url' ? 'url' : 'text'}
              placeholder={field.placeholder}
              value={fieldValue}
              onChange={(e) => handlePreviewChange(e.target.value)}
              required={field.required}
              className={hasError ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {hasError && (
              <p className="text-sm text-red-500 mt-1">{errorMessage}</p>
            )}
          </div>
        );

      case 'textarea':
        return (
          <div>
            <textarea
              className={`flex min-h-[80px] w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${
                hasError
                  ? 'border-red-500 focus-visible:ring-red-500'
                  : 'border-input focus-visible:ring-ring'
              }`}
              placeholder={field.placeholder}
              value={fieldValue}
              onChange={(e) => handlePreviewChange(e.target.value)}
              required={field.required}
            />
            {hasError && (
              <p className="text-sm text-red-500 mt-1">{errorMessage}</p>
            )}
          </div>
        );

      case 'richtext':
        return (
          <div className="border rounded-md p-3 min-h-[120px] bg-background">
            <div className="text-sm text-muted-foreground mb-2 border-b pb-2">
              Rich Text Editor (Preview Mode)
            </div>
            <textarea
              className="w-full border-0 outline-none resize-none bg-transparent"
              placeholder={field.placeholder || "Enter rich text content..."}
              value={fieldValue}
              onChange={(e) => handlePreviewChange(e.target.value)}
              rows={4}
            />
          </div>
        );

      case 'number':
        return (
          <div>
            <Input
              type="number"
              placeholder={field.placeholder}
              value={fieldValue}
              onChange={(e) => handlePreviewChange(e.target.value)}
              required={field.required}
              className={hasError ? 'border-red-500 focus-visible:ring-red-500' : ''}
            />
            {hasError && (
              <p className="text-sm text-red-500 mt-1">{errorMessage}</p>
            )}
          </div>
        );

      case 'phone':
        return (
          <Input
            type="tel"
            placeholder={field.placeholder || "Enter phone number"}
            value={fieldValue}
            onChange={(e) => handlePreviewChange(e.target.value)}
            required={field.required}
          />
        );

      case 'date':
        return (
          <Input
            type="date"
            value={fieldValue}
            onChange={(e) => handlePreviewChange(e.target.value)}
            required={field.required}
          />
        );

      case 'datetime':
        return (
          <Input
            type="datetime-local"
            value={fieldValue}
            onChange={(e) => handlePreviewChange(e.target.value)}
            required={field.required}
          />
        );

      case 'time':
        return (
          <Input
            type="time"
            value={fieldValue}
            onChange={(e) => handlePreviewChange(e.target.value)}
            required={field.required}
          />
        );

      case 'select':
        return (
          <select
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            value={fieldValue}
            onChange={(e) => handlePreviewChange(e.target.value)}
            required={field.required}
          >
            <option value="">{field.placeholder || "Select an option"}</option>
            {field.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'multiselect':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`${field.id}-${option.value}`}
                  checked={Array.isArray(fieldValue) && fieldValue.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(fieldValue) ? fieldValue : [];
                    if (e.target.checked) {
                      handlePreviewChange([...currentValues, option.value]);
                    } else {
                      handlePreviewChange(currentValues.filter((v: string) => v !== option.value));
                    }
                  }}
                />
                <label htmlFor={`${field.id}-${option.value}`} className="text-sm">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="radio"
                  id={`${field.id}-${option.value}`}
                  name={field.id}
                  value={option.value}
                  checked={fieldValue === option.value}
                  onChange={(e) => handlePreviewChange(e.target.value)}
                />
                <label htmlFor={`${field.id}-${option.value}`} className="text-sm">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'checkbox':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.value} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id={`${field.id}-${option.value}`}
                  checked={Array.isArray(fieldValue) && fieldValue.includes(option.value)}
                  onChange={(e) => {
                    const currentValues = Array.isArray(fieldValue) ? fieldValue : [];
                    if (e.target.checked) {
                      handlePreviewChange([...currentValues, option.value]);
                    } else {
                      handlePreviewChange(currentValues.filter((v: string) => v !== option.value));
                    }
                  }}
                />
                <label htmlFor={`${field.id}-${option.value}`} className="text-sm">
                  {option.label}
                </label>
              </div>
            ))}
          </div>
        );

      case 'file':
        return (
          <div className="space-y-2">
            <Input
              type="file"
              multiple={field.fileConfig?.allowMultiple}
              onChange={(e) => handlePreviewChange(e.target.files)}
              required={field.required}
            />
            <div className="text-xs text-muted-foreground">
              {field.fileConfig?.maxFiles && `Max files: ${field.fileConfig.maxFiles}`}
              {field.fileConfig?.maxSize && ` • Max size: ${field.fileConfig.maxSize}MB`}
            </div>
          </div>
        );

      case 'rating': {
        const maxRating = field.ratingConfig?.maxRating || 5;
        return (
          <div className="flex items-center space-x-1">
            {Array.from({ length: maxRating }, (_, i) => (
              <button
                key={i}
                type="button"
                onClick={() => handlePreviewChange(i + 1)}
                className={`text-2xl ${
                  fieldValue > i ? 'text-yellow-400' : 'text-gray-300'
                } hover:text-yellow-400 transition-colors`}
              >
                ★
              </button>
            ))}
            <span className="ml-2 text-sm text-muted-foreground">
              {fieldValue || 0} / {maxRating}
            </span>
          </div>
        );
      }

      case 'slider': {
        const min = field.sliderConfig?.min || 0;
        const max = field.sliderConfig?.max || 100;
        const step = field.sliderConfig?.step || 1;
        return (
          <div className="space-y-2">
            <input
              type="range"
              min={min}
              max={max}
              step={step}
              value={fieldValue || min}
              onChange={(e) => handlePreviewChange(parseInt(e.target.value))}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{min}</span>
              <span className="font-medium">{fieldValue || min}</span>
              <span>{max}</span>
            </div>
          </div>
        );
      }

      case 'color':
        return (
          <div className="flex items-center space-x-2">
            <Input
              type="color"
              value={fieldValue || '#000000'}
              onChange={(e) => handlePreviewChange(e.target.value)}
              className="w-16 h-10 p-1 border rounded"
            />
            <Input
              type="text"
              value={fieldValue || '#000000'}
              onChange={(e) => handlePreviewChange(e.target.value)}
              placeholder="#000000"
              className="flex-1"
            />
          </div>
        );

      case 'address':
        return (
          <div className="space-y-2">
            <Input
              placeholder="Street Address"
              value={fieldValue?.street || ''}
              onChange={(e) => handlePreviewChange({ ...fieldValue, street: e.target.value })}
            />
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="City"
                value={fieldValue?.city || ''}
                onChange={(e) => handlePreviewChange({ ...fieldValue, city: e.target.value })}
              />
              <Input
                placeholder="State"
                value={fieldValue?.state || ''}
                onChange={(e) => handlePreviewChange({ ...fieldValue, state: e.target.value })}
              />
            </div>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="ZIP Code"
                value={fieldValue?.zip || ''}
                onChange={(e) => handlePreviewChange({ ...fieldValue, zip: e.target.value })}
              />
              <Input
                placeholder="Country"
                value={fieldValue?.country || ''}
                onChange={(e) => handlePreviewChange({ ...fieldValue, country: e.target.value })}
              />
            </div>
          </div>
        );

      case 'signature':
        return (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
            <PenTool className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">Signature Pad (Preview Mode)</p>
            <p className="text-xs text-muted-foreground mt-1">
              Click and drag to sign in the actual form
            </p>
          </div>
        );

      case 'matrix': {
        const rows = field.matrixConfig?.rows || [];
        const columns = field.matrixConfig?.columns || [];
        const inputType = field.matrixConfig?.inputType || 'radio';

        return (
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr>
                  <th className="border border-gray-300 p-2 bg-gray-50"></th>
                  {columns.map((col, colIndex) => (
                    <th key={colIndex} className="border border-gray-300 p-2 bg-gray-50 text-sm">
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    <td className="border border-gray-300 p-2 font-medium text-sm">{row}</td>
                    {columns.map((col, colIndex) => (
                      <td key={colIndex} className="border border-gray-300 p-2 text-center">
                        <input
                          type={inputType}
                          name={inputType === 'radio' ? `${field.id}-${rowIndex}` : undefined}
                          value={`${rowIndex}-${colIndex}`}
                          checked={
                            inputType === 'radio'
                              ? fieldValue?.[rowIndex] === colIndex
                              : fieldValue?.[rowIndex]?.includes(colIndex)
                          }
                          onChange={(e) => {
                            if (inputType === 'radio') {
                              handlePreviewChange({ ...fieldValue, [rowIndex]: colIndex });
                            } else {
                              const currentRow = fieldValue?.[rowIndex] || [];
                              const newRow = e.target.checked
                                ? [...currentRow, colIndex]
                                : currentRow.filter((c: number) => c !== colIndex);
                              handlePreviewChange({ ...fieldValue, [rowIndex]: newRow });
                            }
                          }}
                        />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );
      }

      case 'section_break':
        return (
          <div className="border-t-2 border-gray-200 pt-4">
            <div className="text-center text-muted-foreground">
              <SeparatorIcon className="h-6 w-6 mx-auto mb-2" />
              <p className="text-sm">Section Break</p>
            </div>
          </div>
        );

      case 'page_break':
        return (
          <div className="border-2 border-dashed border-primary/50 rounded-lg p-6 text-center bg-primary/5">
            <FileBreak className="h-8 w-8 mx-auto mb-2 text-primary" />
            <p className="text-sm font-medium text-primary">Page Break</p>
            <p className="text-xs text-muted-foreground mt-1">
              New page starts here in multi-page forms
            </p>
          </div>
        );

      default:
        return (
          <div className="p-3 border rounded-md bg-muted/50 text-sm text-muted-foreground">
            {field.type} field preview - Interactive preview not yet implemented
          </div>
        );
    }
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center gap-3">
            <div className="h-8 w-8 bg-primary/10 rounded-lg flex items-center justify-center">
              <FileText className="h-4 w-4 text-primary" />
            </div>
            {editingForm ? 'Edit Form' : 'Create New Form'}
          </DialogTitle>
          <DialogDescription>
            Build your form using our comprehensive field library and live preview
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="px-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="start" className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4" />
                  Start
                </TabsTrigger>
                <TabsTrigger value="build" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Build
                </TabsTrigger>
                <TabsTrigger value="preview" className="flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Preview
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>

            <div className="flex-1 overflow-hidden">
              <TabsContent value="start" className="h-full p-6 pt-4">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Choose a Starting Point</h3>
                    <p className="text-muted-foreground">Select a template or start from scratch</p>
                  </div>
                  
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 border-dashed border-primary/20 hover:border-primary/40">
                      <CardContent className="p-6 text-center">
                        <div className="h-12 w-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                          <Plus className="h-6 w-6 text-primary" />
                        </div>
                        <h4 className="font-semibold mb-2">Start from Scratch</h4>
                        <p className="text-sm text-muted-foreground">Build your form from the ground up</p>
                        <Button 
                          className="mt-4" 
                          onClick={() => setActiveTab('build')}
                        >
                          Start Building
                        </Button>
                      </CardContent>
                    </Card>
                    
                    {formTemplates.map((template) => {
                      const Icon = template.icon;
                      const colorClasses = {
                        blue: 'bg-blue-100 text-blue-600',
                        green: 'bg-green-100 text-green-600',
                        purple: 'bg-purple-100 text-purple-600',
                        orange: 'bg-orange-100 text-orange-600',
                        indigo: 'bg-indigo-100 text-indigo-600',
                      };

                      return (
                        <Card
                          key={template.id}
                          className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleUseTemplate(template.id)}
                        >
                          <CardContent className="p-6 text-center">
                            <div className={`h-12 w-12 ${colorClasses[template.color]} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                              <Icon className="h-6 w-6" />
                            </div>
                            <h4 className="font-semibold mb-2">{template.name}</h4>
                            <p className="text-sm text-muted-foreground">{template.description}</p>
                            <Button variant="outline" className="mt-4">
                              Use Template
                            </Button>
                            <div className="mt-2 text-xs text-muted-foreground">
                              {template.fields.length} fields
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>

                  {/* Template Features */}
                  <div className="mt-8 p-6 bg-muted/50 rounded-lg">
                    <h4 className="font-semibold mb-3">Template Features</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Pre-configured field types</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Built-in validation rules</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Professional layouts</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Multi-page support</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Fully customizable</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span>Ready to use</span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="build" className="h-full p-0">
                <ResizablePanelGroup direction="horizontal" className="h-full">
                  {/* Field Library */}
                  <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
                    <div className="h-full p-4 border-r">
                      <div className="space-y-4">
                        <div>
                          <h3 className="font-semibold mb-3">Field Library</h3>
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                            <Input
                              placeholder="Search fields..."
                              value={searchTerm}
                              onChange={(e) => setSearchTerm(e.target.value)}
                              className="pl-10"
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          {fieldCategories.map((category) => (
                            <Button
                              key={category.id}
                              variant={selectedCategory === category.id ? "default" : "ghost"}
                              className="w-full justify-between"
                              onClick={() => setSelectedCategory(category.id)}
                            >
                              <span>{category.label}</span>
                              <Badge variant="secondary" className="text-xs">
                                {category.count}
                              </Badge>
                            </Button>
                          ))}
                        </div>

                        <div className="space-y-2 max-h-96 overflow-y-auto">
                          {filteredFieldTypes.map((fieldType) => {
                            const Icon = fieldType.icon;
                            return (
                              <Button
                                key={fieldType.type}
                                variant="ghost"
                                className="w-full justify-start h-auto p-3"
                                onClick={() => handleAddField(fieldType)}
                              >
                                <Icon className="h-4 w-4 mr-3 flex-shrink-0" />
                                <div className="text-left">
                                  <div className="font-medium">{fieldType.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {fieldType.description}
                                  </div>
                                </div>
                              </Button>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  </ResizablePanel>

                  <ResizableHandle />

                  {/* Form Canvas */}
                  <ResizablePanel defaultSize={50}>
                    <div className="h-full p-6 overflow-y-auto">
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-semibold mb-4">Form Builder</h3>
                          
                          {formFields.length === 0 ? (
                            <div className="text-center py-12 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                              <Plus className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                              <p className="text-muted-foreground">
                                Add fields from the library to start building your form
                              </p>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              {/* Multi-page overview */}
                              {(() => {
                                const pages = splitFieldsIntoPages();
                                return pages.length > 1 && (
                                  <div className="p-4 bg-muted/50 rounded-lg border">
                                    <h4 className="font-medium mb-2 flex items-center gap-2">
                                      <FileBreak className="h-4 w-4" />
                                      Multi-page Form ({pages.length} pages)
                                    </h4>
                                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 text-sm">
                                      {pages.map((pageFields, index) => (
                                        <div key={index} className="p-2 bg-background rounded border">
                                          <div className="font-medium">Page {index + 1}</div>
                                          <div className="text-muted-foreground">
                                            {pageFields.length} field{pageFields.length !== 1 ? 's' : ''}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  </div>
                                );
                              })()}

                              {formFields.map((field, index) => renderFieldPreview(field, index))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </ResizablePanel>

                  <ResizableHandle />

                  {/* Properties Panel */}
                  <ResizablePanel defaultSize={25} minSize={20} maxSize={35}>
                    <div className="h-full p-4 border-l overflow-y-auto">
                      <h3 className="font-semibold mb-4">Properties</h3>
                      {renderPropertiesPanel()}
                    </div>
                  </ResizablePanel>
                </ResizablePanelGroup>
              </TabsContent>

              <TabsContent value="preview" className="h-full p-6 pt-4">
                <div className="max-w-2xl mx-auto">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Eye className="h-5 w-5" />
                        Live Form Preview
                      </CardTitle>
                      <p className="text-muted-foreground">
                        Interactive preview - try filling out the form as users would
                      </p>
                    </CardHeader>
                    <CardContent>
                      {formFields.length === 0 ? (
                        <div className="text-center py-12 text-muted-foreground">
                          <Eye className="h-8 w-8 mx-auto mb-2" />
                          <p>Add fields to see live preview</p>
                        </div>
                      ) : (() => {
                        const pages = splitFieldsIntoPages();
                        const currentPageFields = pages[currentPage] || [];
                        const isMultiPage = pages.length > 1;
                        const isLastPage = currentPage === pages.length - 1;

                        return (
                          <div className="space-y-6">
                            {/* Page indicator for multi-page forms */}
                            {isMultiPage && (
                              <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium">
                                    Page {currentPage + 1} of {pages.length}
                                  </span>
                                </div>
                                <div className="flex gap-1">
                                  {pages.map((_, index) => (
                                    <button
                                      key={index}
                                      type="button"
                                      onClick={() => handleGoToPage(index)}
                                      className={`w-3 h-3 rounded-full transition-colors ${
                                        index === currentPage
                                          ? 'bg-primary'
                                          : index < currentPage
                                          ? 'bg-primary/50'
                                          : 'bg-muted-foreground/30'
                                      }`}
                                      title={`Go to page ${index + 1}`}
                                    />
                                  ))}
                                </div>
                              </div>
                            )}

                            <form className="space-y-6" onSubmit={handlePreviewSubmit}>
                              {currentPageFields.map((field) => (
                                <div key={field.id} className="space-y-2">
                                  {field.type !== 'section_break' && (
                                    <label className="text-sm font-medium block">
                                      {field.label}
                                      {field.required && <span className="text-red-500 ml-1">*</span>}
                                    </label>
                                  )}
                                  {field.description && (
                                    <p className="text-sm text-muted-foreground">
                                      {field.description}
                                    </p>
                                  )}
                                  {renderLivePreviewField(field)}
                                </div>
                              ))}

                              {/* Navigation buttons for multi-page forms */}
                              {isMultiPage ? (
                                <div className="flex justify-between pt-6 border-t">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    onClick={handlePrevPage}
                                    disabled={currentPage === 0}
                                  >
                                    Previous
                                  </Button>

                                  {isLastPage ? (
                                    <Button type="submit">
                                      Submit Form
                                    </Button>
                                  ) : (
                                    <Button
                                      type="button"
                                      onClick={handleNextPage}
                                    >
                                      Next
                                    </Button>
                                  )}
                                </div>
                              ) : (
                                currentPageFields.length > 0 && (
                                  <div className="pt-6 border-t">
                                    <Button type="submit" className="w-full">
                                      Submit Form (Preview Mode)
                                    </Button>
                                  </div>
                                )
                              )}
                            </form>
                          </div>
                        );
                      })()}
                    </CardContent>
                  </Card>

                  {/* Preview Data Debug Panel */}
                  {Object.keys(previewData).length > 0 && (
                    <Card className="mt-6">
                      <CardHeader>
                        <CardTitle className="text-sm">Form Data (Debug)</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <pre className="text-xs bg-muted p-3 rounded overflow-auto max-h-40">
                          {JSON.stringify(previewData, null, 2)}
                        </pre>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </TabsContent>

              <TabsContent value="settings" className="h-full p-6 pt-4">
                <div className="max-w-2xl mx-auto space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Form Settings</h3>
                  </div>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Basic Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Form Title</label>
                        <Input
                          placeholder="Enter form title"
                          value={formTitle}
                          onChange={(e) => setFormTitle(e.target.value)}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Description</label>
                        <Input
                          placeholder="Enter form description"
                          value={formDescription}
                          onChange={(e) => setFormDescription(e.target.value)}
                        />
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle>Form Behavior</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="multiple-submissions" />
                        <label htmlFor="multiple-submissions" className="text-sm">
                          Allow multiple submissions
                        </label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="show-progress" />
                        <label htmlFor="show-progress" className="text-sm">
                          Show progress bar
                        </label>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        <div className="p-6 pt-0 border-t">
          <div className="flex justify-between">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleSaveDraft}>
                <Save className="h-4 w-4 mr-2" />
                Save Draft
              </Button>
              <Button onClick={handlePublishForm}>
                <Share className="h-4 w-4 mr-2" />
                Publish Form
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default IntegratedFormBuilder;
