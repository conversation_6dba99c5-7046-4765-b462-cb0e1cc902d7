import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Copy,
  Share,
  BarChart3,
  Trash2,
  Eye,
  FileText,
  Calendar,
  Users,
  TrendingUp,
  Sparkles,
  Upload,
  Star,
} from 'lucide-react';
import { mockForms } from '@/data/formBuilderMockData';
import { FormSchema, FormStatus } from '@/types/formBuilder';
import { useToast } from '@/hooks/use-toast';
import IntegratedFormBuilder from './IntegratedFormBuilder';

const FormBuilderDashboard: React.FC = () => {
  const [forms, setForms] = useState<FormSchema[]>(mockForms);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<FormStatus | 'all'>('all');
  const [deleteFormId, setDeleteFormId] = useState<string | null>(null);
  const [builderOpen, setBuilderOpen] = useState(false);
  const [editingForm, setEditingForm] = useState<FormSchema | null>(null);
  const [previewForm, setPreviewForm] = useState<FormSchema | null>(null);
  const [analyticsForm, setAnalyticsForm] = useState<FormSchema | null>(null);
  const [shareForm, setShareForm] = useState<FormSchema | null>(null);
  const { toast } = useToast();

  // Filter forms based on search and status
  const filteredForms = useMemo(() => {
    return forms.filter(form => {
      const matchesSearch = form.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           form.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesStatus = statusFilter === 'all' || form.status === statusFilter;
      return matchesSearch && matchesStatus;
    });
  }, [forms, searchTerm, statusFilter]);

  // Calculate dashboard stats
  const dashboardStats = useMemo(() => {
    const totalForms = forms.length;
    const publishedForms = forms.filter(f => f.status === 'published').length;
    const totalSubmissions = forms.reduce((sum, form) => sum + form.submissionCount, 0);
    const avgSubmissions = totalForms > 0 ? Math.round(totalSubmissions / totalForms) : 0;

    return {
      totalForms,
      publishedForms,
      totalSubmissions,
      avgSubmissions,
    };
  }, [forms]);



  const handleEditFormById = (formId: string) => {
    const form = forms.find(f => f.id === formId);
    if (form) {
      setEditingForm(form);
      setBuilderOpen(true);
    }
  };

  const handleDuplicateForm = (form: FormSchema) => {
    const duplicatedForm: FormSchema = {
      ...form,
      id: `form-${Date.now()}`,
      title: `${form.title} (Copy)`,
      status: 'draft',
      submissionCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      shareUrl: undefined,
    };

    setForms(prev => [duplicatedForm, ...prev]);
    toast({
      title: "Form Duplicated",
      description: `"${form.title}" has been duplicated successfully.`,
    });
  };

  const handleDeleteForm = (formId: string) => {
    setForms(prev => prev.filter(form => form.id !== formId));
    setDeleteFormId(null);
    toast({
      title: "Form Deleted",
      description: "The form has been deleted successfully.",
    });
  };

  const handleCreateForm = () => {
    setEditingForm(null);
    setBuilderOpen(true);
  };

  const handleEditForm = (form: FormSchema) => {
    setEditingForm(form);
    setBuilderOpen(true);
  };

  const handleCloseBuilder = () => {
    setBuilderOpen(false);
    setEditingForm(null);
  };

  const handleSaveForm = (formData: FormSchema, status: 'draft' | 'published') => {
    const existingFormIndex = forms.findIndex(f => f.id === formData.id);

    if (existingFormIndex >= 0) {
      // Update existing form
      const updatedForms = [...forms];
      updatedForms[existingFormIndex] = formData;
      setForms(updatedForms);
    } else {
      // Add new form
      setForms([formData, ...forms]);
    }
  };

  const handleViewForm = (form: FormSchema) => {
    setPreviewForm(form);
  };

  const handleViewAnalytics = (form: FormSchema) => {
    setAnalyticsForm(form);
  };

  const handleShareForm = (form: FormSchema) => {
    setShareForm(form);
  };

  const getStatusBadge = (status: FormStatus) => {
    const variants = {
      draft: 'secondary',
      published: 'default',
      archived: 'outline',
    } as const;

    return (
      <Badge variant={variants[status]} className="capitalize">
        {status}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Form Builder</h1>
          <p className="text-muted-foreground">
            Create and manage custom forms for candidate screening and data collection
          </p>
        </div>
        <Button onClick={handleCreateForm} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Create Form
        </Button>
      </div>

      {/* Dashboard Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Forms</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.totalForms}</div>
            <p className="text-xs text-muted-foreground">
              {dashboardStats.publishedForms} published
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Submissions</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.totalSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              Across all forms
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Submissions</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats.avgSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              Per form
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              New submissions
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search forms..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={statusFilter} onValueChange={(value: FormStatus | 'all') => setStatusFilter(value)}>
          <SelectTrigger className="w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="published">Published</SelectItem>
            <SelectItem value="archived">Archived</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Forms Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredForms.map((form) => (
          <Card key={form.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <CardTitle className="text-lg line-clamp-1">{form.title}</CardTitle>
                  {form.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {form.description}
                    </p>
                  )}
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleEditFormById(form.id)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDuplicateForm(form)}>
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleShareForm(form)}>
                      <Share className="h-4 w-4 mr-2" />
                      Share
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleViewForm(form)}>
                      <Eye className="h-4 w-4 mr-2" />
                      {form.status === 'published' ? 'View Live' : 'Preview'}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleViewAnalytics(form)}>
                      <BarChart3 className="h-4 w-4 mr-2" />
                      Analytics
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => {
                        const updatedForm = { ...form, status: form.status === 'published' ? 'draft' : 'published' as FormStatus };
                        if (updatedForm.status === 'published' && !updatedForm.shareUrl) {
                          updatedForm.shareUrl = `https://forms.talentflow.com/${form.id}`;
                        }
                        handleSaveForm(updatedForm, updatedForm.status);
                        toast({
                          title: updatedForm.status === 'published' ? "Form Published" : "Form Unpublished",
                          description: updatedForm.status === 'published'
                            ? "Your form is now live and ready to collect responses."
                            : "Your form has been moved to draft status.",
                        });
                      }}
                    >
                      {form.status === 'published' ? '📝 Move to Draft' : '🚀 Publish Form'}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => setDeleteFormId(form.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              <div className="flex items-center justify-between mb-3">
                {getStatusBadge(form.status)}
                <span className="text-sm text-muted-foreground">
                  {form.submissionCount} submissions
                </span>
              </div>

              <div className="text-xs text-muted-foreground mb-4">
                <div>Created: {formatDate(form.createdAt)}</div>
                <div>Updated: {formatDate(form.updatedAt)}</div>
                <div>{form.fields.length} fields</div>
              </div>

              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="flex-1"
                  onClick={() => handleEditFormById(form.id)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewForm(form)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredForms.length === 0 && (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No forms found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || statusFilter !== 'all' 
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first form'
            }
          </p>
          {!searchTerm && statusFilter === 'all' && (
            <Button onClick={handleCreateForm}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Form
            </Button>
          )}
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteFormId} onOpenChange={() => setDeleteFormId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Form</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this form? This action cannot be undone and will
              also delete all associated responses.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteFormId && handleDeleteForm(deleteFormId)}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Form Preview Modal */}
      <Dialog open={!!previewForm} onOpenChange={() => setPreviewForm(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              {previewForm?.status === 'published' ? 'Live Form Preview' : 'Draft Form Preview'}
            </DialogTitle>
            <DialogDescription>
              {previewForm?.status === 'published'
                ? 'This is how your published form appears to users'
                : 'This is a preview of your draft form'
              }
            </DialogDescription>
          </DialogHeader>

          {previewForm && (
            <div className="space-y-6">
              {/* Form Header */}
              <div className="text-center space-y-2 pb-6 border-b">
                <h1 className="text-2xl font-bold">{previewForm.title}</h1>
                {previewForm.description && (
                  <p className="text-muted-foreground">{previewForm.description}</p>
                )}
                <div className="flex items-center justify-center gap-4 text-sm text-muted-foreground">
                  <span>{previewForm.fields.length} fields</span>
                  <span>•</span>
                  <span className="capitalize">{previewForm.status}</span>
                  {previewForm.status === 'published' && previewForm.shareUrl && (
                    <>
                      <span>•</span>
                      <button
                        onClick={() => {
                          navigator.clipboard.writeText(previewForm.shareUrl!);
                          toast({
                            title: "Link Copied",
                            description: "Form share link copied to clipboard.",
                          });
                        }}
                        className="text-primary hover:underline"
                      >
                        Copy Link
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Form Fields Preview */}
              <div className="space-y-4">
                {previewForm.fields.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No fields added to this form yet.</p>
                  </div>
                ) : (
                  previewForm.fields.map((field, index) => (
                    <div key={field.id} className="space-y-2">
                      <label className="text-sm font-medium flex items-center gap-1">
                        {field.label}
                        {field.required && <span className="text-destructive">*</span>}
                      </label>
                      {field.description && (
                        <p className="text-xs text-muted-foreground">{field.description}</p>
                      )}

                      {/* Render different field types */}
                      {field.type === 'text' && (
                        <Input placeholder={field.placeholder} disabled />
                      )}
                      {field.type === 'textarea' && (
                        <textarea
                          className="w-full p-2 border rounded-md resize-none"
                          rows={3}
                          placeholder={field.placeholder}
                          disabled
                        />
                      )}
                      {field.type === 'email' && (
                        <Input type="email" placeholder={field.placeholder || "Enter email address"} disabled />
                      )}
                      {field.type === 'phone' && (
                        <Input type="tel" placeholder={field.placeholder || "Enter phone number"} disabled />
                      )}
                      {field.type === 'number' && (
                        <Input type="number" placeholder={field.placeholder || "Enter number"} disabled />
                      )}
                      {field.type === 'dropdown' && (
                        <select className="w-full p-2 border rounded-md" disabled>
                          <option>Select an option...</option>
                          {field.options?.map((option, i) => (
                            <option key={i} value={option}>{option}</option>
                          ))}
                        </select>
                      )}
                      {field.type === 'radio' && (
                        <div className="space-y-2">
                          {field.options?.map((option, i) => (
                            <div key={i} className="flex items-center space-x-2">
                              <input type="radio" name={field.id} disabled />
                              <span className="text-sm">{option}</span>
                            </div>
                          ))}
                        </div>
                      )}
                      {field.type === 'checkbox' && (
                        <div className="space-y-2">
                          {field.options?.map((option, i) => (
                            <div key={i} className="flex items-center space-x-2">
                              <input type="checkbox" disabled />
                              <span className="text-sm">{option}</span>
                            </div>
                          ))}
                        </div>
                      )}
                      {field.type === 'file' && (
                        <div className="border-2 border-dashed border-muted rounded-lg p-4 text-center">
                          <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">Click to upload or drag and drop</p>
                        </div>
                      )}
                      {field.type === 'date' && (
                        <Input type="date" disabled />
                      )}
                      {field.type === 'rating' && (
                        <div className="flex items-center space-x-1">
                          {Array.from({ length: field.ratingConfig?.maxRating || 5 }, (_, i) => (
                            <Star key={i} className="h-5 w-5 text-muted-foreground" />
                          ))}
                        </div>
                      )}
                      {/* Add more field types as needed */}

                      {index < previewForm.fields.length - 1 && (
                        <div className="border-b my-4" />
                      )}
                    </div>
                  ))
                )}
              </div>

              {/* Form Footer */}
              {previewForm.fields.length > 0 && (
                <div className="pt-6 border-t">
                  <Button className="w-full" disabled>
                    Submit Form
                  </Button>
                  <p className="text-xs text-center text-muted-foreground mt-2">
                    This is a preview - form submission is disabled
                  </p>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Analytics Modal */}
      <Dialog open={!!analyticsForm} onOpenChange={() => setAnalyticsForm(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Form Analytics - {analyticsForm?.title}
            </DialogTitle>
            <DialogDescription>
              Insights and statistics for your form performance
            </DialogDescription>
          </DialogHeader>

          {analyticsForm && (
            <div className="space-y-6">
              {/* Overview Stats */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Eye className="h-4 w-4 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium">Total Views</p>
                        <p className="text-2xl font-bold">{Math.floor(analyticsForm.submissionCount * 3.2)}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-green-500" />
                      <div>
                        <p className="text-sm font-medium">Submissions</p>
                        <p className="text-2xl font-bold">{analyticsForm.submissionCount}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-sm font-medium">Conversion Rate</p>
                        <p className="text-2xl font-bold">{Math.round((analyticsForm.submissionCount / Math.max(analyticsForm.submissionCount * 3.2, 1)) * 100)}%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-purple-500" />
                      <div>
                        <p className="text-sm font-medium">Avg. Time</p>
                        <p className="text-2xl font-bold">{Math.floor(analyticsForm.fields.length * 0.8 + 2)}m</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Form Status & Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Form Details</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Status</span>
                      <Badge variant={analyticsForm.status === 'published' ? 'default' : 'secondary'} className="capitalize">
                        {analyticsForm.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Fields</span>
                      <span className="text-sm font-medium">{analyticsForm.fields.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Created</span>
                      <span className="text-sm font-medium">{formatDate(analyticsForm.createdAt)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-muted-foreground">Last Updated</span>
                      <span className="text-sm font-medium">{formatDate(analyticsForm.updatedAt)}</span>
                    </div>
                    {analyticsForm.shareUrl && (
                      <div className="pt-2 border-t">
                        <span className="text-sm text-muted-foreground">Share URL</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Input
                            value={analyticsForm.shareUrl}
                            readOnly
                            className="text-xs"
                          />
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              navigator.clipboard.writeText(analyticsForm.shareUrl!);
                              toast({
                                title: "Link Copied",
                                description: "Form share link copied to clipboard.",
                              });
                            }}
                          >
                            Copy
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Field Completion Rates</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {analyticsForm.fields.slice(0, 5).map((field, index) => {
                        const completionRate = Math.max(85 - index * 3 - Math.random() * 10, 60);
                        return (
                          <div key={field.id} className="space-y-1">
                            <div className="flex justify-between text-sm">
                              <span className="truncate">{field.label}</span>
                              <span className="font-medium">{Math.round(completionRate)}%</span>
                            </div>
                            <div className="w-full bg-muted rounded-full h-2">
                              <div
                                className="bg-primary h-2 rounded-full transition-all duration-300"
                                style={{ width: `${completionRate}%` }}
                              />
                            </div>
                          </div>
                        );
                      })}
                      {analyticsForm.fields.length > 5 && (
                        <p className="text-xs text-muted-foreground text-center pt-2">
                          Showing top 5 fields. {analyticsForm.fields.length - 5} more fields available.
                        </p>
                      )}
                      {analyticsForm.fields.length === 0 && (
                        <p className="text-sm text-muted-foreground text-center py-4">
                          No fields added to analyze yet.
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Recent Submissions</CardTitle>
                </CardHeader>
                <CardContent>
                  {analyticsForm.submissionCount > 0 ? (
                    <div className="space-y-3">
                      {Array.from({ length: Math.min(analyticsForm.submissionCount, 5) }, (_, index) => {
                        const daysAgo = Math.floor(Math.random() * 7) + 1;
                        const timeAgo = Math.floor(Math.random() * 24) + 1;
                        return (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <Users className="h-4 w-4 text-primary" />
                              </div>
                              <div>
                                <p className="text-sm font-medium">Submission #{analyticsForm.submissionCount - index}</p>
                                <p className="text-xs text-muted-foreground">
                                  {daysAgo === 1 ? `${timeAgo} hours ago` : `${daysAgo} days ago`}
                                </p>
                              </div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              Completed
                            </Badge>
                          </div>
                        );
                      })}
                      <div className="text-center pt-2">
                        <Button variant="outline" size="sm">
                          View All Submissions
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No submissions yet.</p>
                      <p className="text-sm">Share your form to start collecting responses.</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Share Modal */}
      <Dialog open={!!shareForm} onOpenChange={() => setShareForm(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Share className="h-5 w-5" />
              Share Form - {shareForm?.title}
            </DialogTitle>
            <DialogDescription>
              Share your form with others to start collecting responses
            </DialogDescription>
          </DialogHeader>

          {shareForm && (
            <div className="space-y-6">
              {/* Form Status Check */}
              {shareForm.status !== 'published' ? (
                <div className="p-4 border border-orange-200 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-2 text-orange-800">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="font-medium">Form Not Published</span>
                  </div>
                  <p className="text-sm text-orange-700 mt-1">
                    This form is currently in {shareForm.status} status. Publish it first to enable sharing.
                  </p>
                  <Button
                    size="sm"
                    className="mt-3"
                    onClick={() => {
                      const updatedForm = { ...shareForm, status: 'published' as FormStatus };
                      updatedForm.shareUrl = `https://forms.talentflow.com/f/${shareForm.id}`;
                      handleSaveForm(updatedForm, 'published');
                      setShareForm(updatedForm);
                      toast({
                        title: "Form Published",
                        description: "Your form is now live and ready to be shared.",
                      });
                    }}
                  >
                    Publish Form Now
                  </Button>
                </div>
              ) : (
                <>
                  {/* Share Link */}
                  <div className="space-y-3">
                    <h3 className="font-medium">Share Link</h3>
                    <div className="flex items-center gap-2">
                      <Input
                        value={shareForm.shareUrl || ''}
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        onClick={() => {
                          if (shareForm.shareUrl) {
                            navigator.clipboard.writeText(shareForm.shareUrl);
                            toast({
                              title: "Link Copied",
                              description: "Form share link copied to clipboard.",
                            });
                          }
                        }}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Anyone with this link can access and submit your form
                    </p>
                  </div>

                  {/* Embed Code */}
                  <div className="space-y-3">
                    <h3 className="font-medium">Embed Code</h3>
                    <div className="relative">
                      <textarea
                        value={`<iframe src="${shareForm.shareUrl}" width="100%" height="600" frameborder="0"></iframe>`}
                        readOnly
                        className="w-full p-3 text-xs font-mono bg-muted border rounded-md resize-none"
                        rows={3}
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        className="absolute top-2 right-2"
                        onClick={() => {
                          const embedCode = `<iframe src="${shareForm.shareUrl}" width="100%" height="600" frameborder="0"></iframe>`;
                          navigator.clipboard.writeText(embedCode);
                          toast({
                            title: "Embed Code Copied",
                            description: "HTML embed code copied to clipboard.",
                          });
                        }}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Embed this form directly into your website or blog
                    </p>
                  </div>

                  {/* QR Code Placeholder */}
                  <div className="space-y-3">
                    <h3 className="font-medium">QR Code</h3>
                    <div className="flex items-center gap-4">
                      <div className="w-24 h-24 bg-muted border-2 border-dashed rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <div className="w-8 h-8 bg-primary/20 rounded mx-auto mb-1"></div>
                          <span className="text-xs text-muted-foreground">QR Code</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-muted-foreground mb-2">
                          Generate a QR code for easy mobile access to your form
                        </p>
                        <Button variant="outline" size="sm">
                          Generate QR Code
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Social Sharing */}
                  <div className="space-y-3">
                    <h3 className="font-medium">Social Sharing</h3>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const text = `Check out this form: ${shareForm.title}`;
                          const url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareForm.shareUrl || '')}`;
                          window.open(url, '_blank');
                        }}
                      >
                        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                        </svg>
                        Twitter
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareForm.shareUrl || '')}`;
                          window.open(url, '_blank');
                        }}
                      >
                        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                        </svg>
                        LinkedIn
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const subject = `Form: ${shareForm.title}`;
                          const body = `Hi,\n\nI'd like to share this form with you: ${shareForm.title}\n\nYou can access it here: ${shareForm.shareUrl}\n\nThanks!`;
                          const url = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                          window.location.href = url;
                        }}
                      >
                        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                          <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
                          <polyline points="22,6 12,13 2,6"/>
                        </svg>
                        Email
                      </Button>
                    </div>
                  </div>

                  {/* Form Statistics */}
                  <div className="pt-4 border-t">
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div>
                        <div className="text-2xl font-bold text-primary">{shareForm.submissionCount}</div>
                        <div className="text-xs text-muted-foreground">Submissions</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-primary">{Math.floor(shareForm.submissionCount * 3.2)}</div>
                        <div className="text-xs text-muted-foreground">Views</div>
                      </div>
                      <div>
                        <div className="text-2xl font-bold text-primary">{shareForm.fields.length}</div>
                        <div className="text-xs text-muted-foreground">Fields</div>
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Integrated Form Builder */}
      <IntegratedFormBuilder
        isOpen={builderOpen}
        onClose={handleCloseBuilder}
        editingForm={editingForm}
        onSave={handleSaveForm}
      />
    </div>
  );
};

export default FormBuilderDashboard;
