import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  Shield,
  Users,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Target,
  Award,
  Settings,
  FileText,
  Eye,
  Download,
  RefreshCw,
  UserCheck,
  UserX,
  Briefcase,
  Calendar,
  MessageSquare,
  Building
} from 'lucide-react';

interface RoleUsageData {
  roleId: string;
  roleName: string;
  userCount: number;
  activeUsers: number;
  avgSessionDuration: number;
  permissionUtilization: number;
  effectivenessScore: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  lastModified: string;
  isCustom: boolean;
}

interface PermissionUsageData {
  module: string;
  action: string;
  usageCount: number;
  usagePercentage: number;
  roleDistribution: { [roleName: string]: number };
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  icon: React.ReactNode;
}

interface RoleTransitionData {
  fromRole: string;
  toRole: string;
  transitionCount: number;
  successRate: number;
  avgTransitionTime: number;
  reason: string;
  trend: 'up' | 'down' | 'stable';
}

interface RoleOptimizationRecommendation {
  type: 'underutilized' | 'overloaded' | 'permission_gap' | 'transition_issue';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  affectedRoles: string[];
}

export default function RoleEffectivenessReporting() {
  const { organization } = useAuth();
  const [dateRange, setDateRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);
  const [roleUsageData, setRoleUsageData] = useState<RoleUsageData[]>([]);
  const [permissionUsageData, setPermissionUsageData] = useState<PermissionUsageData[]>([]);
  const [roleTransitions, setRoleTransitions] = useState<RoleTransitionData[]>([]);
  const [recommendations, setRecommendations] = useState<RoleOptimizationRecommendation[]>([]);

  useEffect(() => {
    if (organization) {
      loadRoleEffectivenessData();
    }
  }, [organization, dateRange]);

  const loadRoleEffectivenessData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock role usage data
      const mockRoleUsage: RoleUsageData[] = [
        {
          roleId: 'admin',
          roleName: 'Administrator',
          userCount: 8,
          activeUsers: 7,
          avgSessionDuration: 145,
          permissionUtilization: 89,
          effectivenessScore: 92,
          trend: 'up',
          trendPercentage: 5.2,
          lastModified: '2024-01-15',
          isCustom: false
        },
        {
          roleId: 'recruiter',
          roleName: 'Recruiter',
          userCount: 24,
          activeUsers: 21,
          avgSessionDuration: 98,
          permissionUtilization: 76,
          effectivenessScore: 85,
          trend: 'up',
          trendPercentage: 8.1,
          lastModified: '2024-01-10',
          isCustom: false
        },
        {
          roleId: 'hiring_manager',
          roleName: 'Hiring Manager',
          userCount: 15,
          activeUsers: 12,
          avgSessionDuration: 67,
          permissionUtilization: 64,
          effectivenessScore: 78,
          trend: 'stable',
          trendPercentage: 0.5,
          lastModified: '2024-01-08',
          isCustom: false
        },
        {
          roleId: 'interviewer',
          roleName: 'Interviewer',
          userCount: 32,
          activeUsers: 18,
          avgSessionDuration: 45,
          permissionUtilization: 42,
          effectivenessScore: 65,
          trend: 'down',
          trendPercentage: -3.2,
          lastModified: '2024-01-12',
          isCustom: false
        },
        {
          roleId: 'custom_senior_recruiter',
          roleName: 'Senior Recruiter',
          userCount: 6,
          activeUsers: 6,
          avgSessionDuration: 112,
          permissionUtilization: 82,
          effectivenessScore: 88,
          trend: 'up',
          trendPercentage: 12.3,
          lastModified: '2024-01-14',
          isCustom: true
        }
      ];

      // Mock permission usage data
      const mockPermissionUsage: PermissionUsageData[] = [
        {
          module: 'users',
          action: 'read',
          usageCount: 1456,
          usagePercentage: 95,
          roleDistribution: { 'Administrator': 45, 'Recruiter': 35, 'Hiring Manager': 20 },
          trend: 'up',
          trendPercentage: 8.2,
          icon: <Users className="h-4 w-4" />
        },
        {
          module: 'jobs',
          action: 'create',
          usageCount: 234,
          usagePercentage: 78,
          roleDistribution: { 'Administrator': 25, 'Recruiter': 65, 'Hiring Manager': 10 },
          trend: 'up',
          trendPercentage: 15.7,
          icon: <Briefcase className="h-4 w-4" />
        },
        {
          module: 'interviews',
          action: 'manage',
          usageCount: 567,
          usagePercentage: 82,
          roleDistribution: { 'Hiring Manager': 55, 'Interviewer': 35, 'Recruiter': 10 },
          trend: 'stable',
          trendPercentage: 2.1,
          icon: <Calendar className="h-4 w-4" />
        },
        {
          module: 'analytics',
          action: 'read',
          usageCount: 89,
          usagePercentage: 34,
          roleDistribution: { 'Administrator': 70, 'Hiring Manager': 20, 'Recruiter': 10 },
          trend: 'up',
          trendPercentage: 22.5,
          icon: <BarChart3 className="h-4 w-4" />
        },
        {
          module: 'settings',
          action: 'update',
          usageCount: 45,
          usagePercentage: 18,
          roleDistribution: { 'Administrator': 90, 'Hiring Manager': 10 },
          trend: 'down',
          trendPercentage: -5.3,
          icon: <Settings className="h-4 w-4" />
        }
      ];

      // Mock role transition data
      const mockRoleTransitions: RoleTransitionData[] = [
        {
          fromRole: 'Interviewer',
          toRole: 'Hiring Manager',
          transitionCount: 8,
          successRate: 87.5,
          avgTransitionTime: 45,
          reason: 'Promotion',
          trend: 'up'
        },
        {
          fromRole: 'Recruiter',
          toRole: 'Senior Recruiter',
          transitionCount: 6,
          successRate: 100,
          avgTransitionTime: 30,
          reason: 'Role Enhancement',
          trend: 'up'
        },
        {
          fromRole: 'Employee',
          toRole: 'Interviewer',
          transitionCount: 12,
          successRate: 75,
          avgTransitionTime: 60,
          reason: 'Skill Development',
          trend: 'stable'
        },
        {
          fromRole: 'Hiring Manager',
          toRole: 'Administrator',
          transitionCount: 3,
          successRate: 66.7,
          avgTransitionTime: 90,
          reason: 'Leadership Role',
          trend: 'down'
        }
      ];

      // Mock optimization recommendations
      const mockRecommendations: RoleOptimizationRecommendation[] = [
        {
          type: 'underutilized',
          severity: 'medium',
          title: 'Interviewer Role Underutilization',
          description: 'The Interviewer role shows low permission utilization (42%) and declining effectiveness.',
          impact: 'Reduced productivity and potential role confusion among 32 users.',
          recommendation: 'Review and streamline interviewer permissions. Consider role consolidation or additional training.',
          affectedRoles: ['Interviewer']
        },
        {
          type: 'permission_gap',
          severity: 'high',
          title: 'Analytics Access Gap',
          description: 'Analytics permissions are underutilized (34%) across roles, limiting data-driven decisions.',
          impact: 'Missed opportunities for performance optimization and strategic insights.',
          recommendation: 'Expand analytics access to Hiring Managers and Senior Recruiters. Provide analytics training.',
          affectedRoles: ['Hiring Manager', 'Senior Recruiter']
        },
        {
          type: 'overloaded',
          severity: 'low',
          title: 'Administrator Role Concentration',
          description: 'Administrators handle 70% of analytics access, creating potential bottlenecks.',
          impact: 'Single point of failure and delayed decision-making processes.',
          recommendation: 'Delegate analytics permissions to senior roles. Implement role-based analytics dashboards.',
          affectedRoles: ['Administrator']
        },
        {
          type: 'transition_issue',
          severity: 'medium',
          title: 'Hiring Manager Promotion Challenges',
          description: 'Hiring Manager to Administrator transitions show 66.7% success rate with 90-day average.',
          impact: 'Extended transition periods and potential leadership gaps.',
          recommendation: 'Implement structured leadership development program and mentorship for promotion candidates.',
          affectedRoles: ['Hiring Manager', 'Administrator']
        }
      ];

      setRoleUsageData(mockRoleUsage);
      setPermissionUsageData(mockPermissionUsage);
      setRoleTransitions(mockRoleTransitions);
      setRecommendations(mockRecommendations);

    } catch (error) {
      console.error('Failed to load role effectiveness data:', error);
      toast({
        title: "Error",
        description: "Failed to load role effectiveness data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getTrendIcon = (trend: string, percentage: number) => {
    if (trend === 'up') return <ArrowUp className="h-4 w-4 text-green-600" />;
    if (trend === 'down') return <ArrowDown className="h-4 w-4 text-red-600" />;
    return <ArrowRight className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = (trend: string) => {
    if (trend === 'up') return 'text-green-600';
    if (trend === 'down') return 'text-red-600';
    return 'text-gray-600';
  };

  const getEffectivenessColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-blue-100 text-blue-700 border-blue-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'high': return <AlertTriangle className="h-4 w-4" />;
      case 'medium': return <Clock className="h-4 w-4" />;
      default: return <CheckCircle className="h-4 w-4" />;
    }
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Role effectiveness report export will be available shortly.",
    });
  };

  const handleRefreshData = () => {
    loadRoleEffectivenessData();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Role Effectiveness Reporting</h1>
          <p className="text-gray-600 mt-1">
            Analyze role usage, permission utilization, and optimization opportunities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <Tabs defaultValue="usage" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="usage">Role Usage</TabsTrigger>
          <TabsTrigger value="permissions">Permission Analytics</TabsTrigger>
          <TabsTrigger value="transitions">Role Transitions</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
        </TabsList>

        {/* Role Usage Tab */}
        <TabsContent value="usage" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{roleUsageData.length}</div>
                <p className="text-xs text-muted-foreground">
                  {roleUsageData.filter(r => r.isCustom).length} custom roles
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Effectiveness</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(roleUsageData.reduce((acc, role) => acc + role.effectivenessScore, 0) / roleUsageData.length)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all roles
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Permission Utilization</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.round(roleUsageData.reduce((acc, role) => acc + role.permissionUtilization, 0) / roleUsageData.length)}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Average utilization
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <UserCheck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {roleUsageData.reduce((acc, role) => acc + role.activeUsers, 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Of {roleUsageData.reduce((acc, role) => acc + role.userCount, 0)} total users
                </p>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Performance Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roleUsageData.map((role) => (
                  <div key={role.roleId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{role.roleName}</span>
                          {role.isCustom && (
                            <Badge variant="outline" className="text-xs">Custom</Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600">
                          {role.activeUsers}/{role.userCount} active users
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className={`font-medium ${getEffectivenessColor(role.effectivenessScore)}`}>
                          {role.effectivenessScore}%
                        </div>
                        <div className="text-gray-500">Effectiveness</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{role.permissionUtilization}%</div>
                        <div className="text-gray-500">Permissions</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatDuration(role.avgSessionDuration)}</div>
                        <div className="text-gray-500">Avg Session</div>
                      </div>
                      <div className="text-center">
                        <div className={`flex items-center space-x-1 ${getTrendColor(role.trend)}`}>
                          {getTrendIcon(role.trend, role.trendPercentage)}
                          <span className="font-medium">
                            {role.trend !== 'stable' && (role.trendPercentage > 0 ? '+' : '')}{role.trendPercentage}%
                          </span>
                        </div>
                        <div className="text-gray-500">Trend</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Permission Analytics Tab */}
        <TabsContent value="permissions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Permission Usage Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {permissionUsageData.map((permission, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {permission.icon}
                      <div>
                        <div className="font-medium capitalize">
                          {permission.module} - {permission.action}
                        </div>
                        <div className="text-sm text-gray-600">
                          {permission.usageCount} total uses
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="font-medium">{permission.usagePercentage}%</div>
                        <div className="text-sm text-gray-500">Usage Rate</div>
                      </div>
                      <div className="text-center">
                        <div className={`flex items-center space-x-1 ${getTrendColor(permission.trend)}`}>
                          {getTrendIcon(permission.trend, permission.trendPercentage)}
                          <span className="font-medium">
                            {permission.trend !== 'stable' && (permission.trendPercentage > 0 ? '+' : '')}{permission.trendPercentage}%
                          </span>
                        </div>
                        <div className="text-sm text-gray-500">Trend</div>
                      </div>
                      <div className="w-48">
                        <div className="text-sm font-medium mb-1">Role Distribution</div>
                        <div className="space-y-1">
                          {Object.entries(permission.roleDistribution).map(([role, percentage]) => (
                            <div key={role} className="flex items-center justify-between text-xs">
                              <span>{role}</span>
                              <div className="flex items-center space-x-2">
                                <div className="w-16 bg-gray-200 rounded-full h-1">
                                  <div
                                    className="bg-blue-600 h-1 rounded-full"
                                    style={{ width: `${percentage}%` }}
                                  ></div>
                                </div>
                                <span>{percentage}%</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Role Transitions Tab */}
        <TabsContent value="transitions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ArrowRight className="h-5 w-5" />
                Role Transition Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {roleTransitions.map((transition, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{transition.fromRole}</Badge>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                        <Badge variant="outline">{transition.toRole}</Badge>
                      </div>
                      <div className="text-sm text-gray-600">
                        {transition.reason}
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{transition.transitionCount}</div>
                        <div className="text-gray-500">Transitions</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-medium ${transition.successRate >= 80 ? 'text-green-600' : transition.successRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                          {transition.successRate}%
                        </div>
                        <div className="text-gray-500">Success Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{transition.avgTransitionTime} days</div>
                        <div className="text-gray-500">Avg Time</div>
                      </div>
                      <div className="text-center">
                        <div className={`flex items-center space-x-1 ${getTrendColor(transition.trend)}`}>
                          {getTrendIcon(transition.trend, 0)}
                          <span className="font-medium capitalize">{transition.trend}</span>
                        </div>
                        <div className="text-gray-500">Trend</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recommendations Tab */}
        <TabsContent value="recommendations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Role Optimization Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recommendations.map((rec, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${getSeverityColor(rec.severity)}`}>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        {getSeverityIcon(rec.severity)}
                        <h3 className="font-medium">{rec.title}</h3>
                      </div>
                      <Badge variant="outline" className="capitalize">
                        {rec.severity} Priority
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-medium mb-1">Description</h4>
                        <p className="text-sm">{rec.description}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-1">Impact</h4>
                        <p className="text-sm">{rec.impact}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-1">Recommendation</h4>
                        <p className="text-sm">{rec.recommendation}</p>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-1">Affected Roles</h4>
                        <div className="flex flex-wrap gap-1">
                          {rec.affectedRoles.map((role, roleIndex) => (
                            <Badge key={roleIndex} variant="secondary" className="text-xs">
                              {role}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
