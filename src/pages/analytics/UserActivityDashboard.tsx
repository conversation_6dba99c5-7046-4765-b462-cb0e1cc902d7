import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  Activity,
  Users,
  Clock,
  TrendingUp,
  Calendar,
  Bar<PERSON>hart3,
  <PERSON><PERSON>hart,
  LineChart,
  Download,
  Filter,
  RefreshCw,
  Eye,
  MousePointer,
  Smartphone,
  Monitor,
  Globe,
  UserCheck,
  LogIn,
  LogOut,
  Settings,
  FileText,
  MessageSquare,
  Briefcase
} from 'lucide-react';

interface UserActivityData {
  userId: string;
  userName: string;
  email: string;
  role: string;
  lastActive: string;
  sessionDuration: number;
  actionsCount: number;
  loginCount: number;
  deviceType: 'desktop' | 'mobile' | 'tablet';
  browser: string;
  location: string;
  features: string[];
}

interface ActivityMetrics {
  totalUsers: number;
  activeUsers: number;
  avgSessionDuration: number;
  totalSessions: number;
  totalActions: number;
  bounceRate: number;
  retentionRate: number;
  growthRate: number;
}

interface FeatureUsage {
  feature: string;
  usage: number;
  growth: number;
  icon: React.ReactNode;
}

interface LoginPattern {
  hour: number;
  count: number;
  day: string;
}

export default function UserActivityDashboard() {
  const { organization, user } = useAuth();
  const [dateRange, setDateRange] = useState('7d');
  const [isLoading, setIsLoading] = useState(false);
  const [userActivities, setUserActivities] = useState<UserActivityData[]>([]);
  const [activityMetrics, setActivityMetrics] = useState<ActivityMetrics | null>(null);
  const [featureUsage, setFeatureUsage] = useState<FeatureUsage[]>([]);
  const [loginPatterns, setLoginPatterns] = useState<LoginPattern[]>([]);

  // Load analytics data when component mounts
  useEffect(() => {
    if (organization) {
      loadAnalyticsData();
    }
  }, [organization, dateRange]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call with mock data
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Generate mock user activity data
      const mockUserActivities: UserActivityData[] = [
        {
          userId: '1',
          userName: 'Sarah Johnson',
          email: '<EMAIL>',
          role: 'admin',
          lastActive: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          sessionDuration: 145,
          actionsCount: 23,
          loginCount: 5,
          deviceType: 'desktop',
          browser: 'Chrome',
          location: 'New York, US',
          features: ['users', 'roles', 'analytics', 'settings']
        },
        {
          userId: '2',
          userName: 'Michael Chen',
          email: '<EMAIL>',
          role: 'recruiter',
          lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          sessionDuration: 89,
          actionsCount: 15,
          loginCount: 3,
          deviceType: 'mobile',
          browser: 'Safari',
          location: 'San Francisco, US',
          features: ['applicants', 'jobs', 'interviews']
        },
        {
          userId: '3',
          userName: 'Emily Rodriguez',
          email: '<EMAIL>',
          role: 'hiring_manager',
          lastActive: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          sessionDuration: 67,
          actionsCount: 12,
          loginCount: 2,
          deviceType: 'desktop',
          browser: 'Firefox',
          location: 'Austin, US',
          features: ['interviews', 'analytics', 'communications']
        },
        {
          userId: '4',
          userName: 'David Kim',
          email: '<EMAIL>',
          role: 'interviewer',
          lastActive: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          sessionDuration: 45,
          actionsCount: 8,
          loginCount: 1,
          deviceType: 'tablet',
          browser: 'Chrome',
          location: 'Seattle, US',
          features: ['interviews', 'communications']
        },
        {
          userId: '5',
          userName: 'Lisa Wang',
          email: '<EMAIL>',
          role: 'employee',
          lastActive: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
          sessionDuration: 32,
          actionsCount: 6,
          loginCount: 1,
          deviceType: 'mobile',
          browser: 'Chrome',
          location: 'Los Angeles, US',
          features: ['profile', 'onboarding']
        }
      ];

      // Generate mock activity metrics
      const mockMetrics: ActivityMetrics = {
        totalUsers: 156,
        activeUsers: 89,
        avgSessionDuration: 76,
        totalSessions: 234,
        totalActions: 1456,
        bounceRate: 12.5,
        retentionRate: 87.3,
        growthRate: 15.2
      };

      // Generate mock feature usage data
      const mockFeatureUsage: FeatureUsage[] = [
        { feature: 'User Management', usage: 89, growth: 12.5, icon: <Users className="h-4 w-4" /> },
        { feature: 'Job Postings', usage: 76, growth: 8.3, icon: <Briefcase className="h-4 w-4" /> },
        { feature: 'Interviews', usage: 65, growth: 15.7, icon: <Calendar className="h-4 w-4" /> },
        { feature: 'Analytics', usage: 54, growth: 22.1, icon: <BarChart3 className="h-4 w-4" /> },
        { feature: 'Communications', usage: 43, growth: 6.9, icon: <MessageSquare className="h-4 w-4" /> },
        { feature: 'Settings', usage: 32, growth: 4.2, icon: <Settings className="h-4 w-4" /> },
        { feature: 'Reports', usage: 28, growth: 18.5, icon: <FileText className="h-4 w-4" /> }
      ];

      // Generate mock login patterns
      const mockLoginPatterns: LoginPattern[] = [
        { hour: 8, count: 45, day: 'Monday' },
        { hour: 9, count: 67, day: 'Monday' },
        { hour: 10, count: 52, day: 'Monday' },
        { hour: 11, count: 38, day: 'Monday' },
        { hour: 13, count: 41, day: 'Monday' },
        { hour: 14, count: 55, day: 'Monday' },
        { hour: 15, count: 33, day: 'Monday' },
        { hour: 16, count: 29, day: 'Monday' }
      ];

      setUserActivities(mockUserActivities);
      setActivityMetrics(mockMetrics);
      setFeatureUsage(mockFeatureUsage);
      setLoginPatterns(mockLoginPatterns);

    } catch (error) {
      console.error('Failed to load analytics data:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Format time duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  // Format relative time
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes}m ago`;
    } else if (diffInMinutes < 1440) {
      return `${Math.floor(diffInMinutes / 60)}h ago`;
    } else {
      return `${Math.floor(diffInMinutes / 1440)}d ago`;
    }
  };

  // Get device icon
  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile': return <Smartphone className="h-4 w-4" />;
      case 'tablet': return <Smartphone className="h-4 w-4" />;
      default: return <Monitor className="h-4 w-4" />;
    }
  };

  // Get role color
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-700';
      case 'recruiter': return 'bg-blue-100 text-blue-700';
      case 'hiring_manager': return 'bg-green-100 text-green-700';
      case 'interviewer': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  // Calculate activity level
  const getActivityLevel = (actionsCount: number) => {
    if (actionsCount >= 20) return { level: 'High', color: 'text-green-600' };
    if (actionsCount >= 10) return { level: 'Medium', color: 'text-yellow-600' };
    return { level: 'Low', color: 'text-red-600' };
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Analytics data export will be available shortly.",
    });
  };

  const handleRefreshData = () => {
    loadAnalyticsData();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Activity Analytics</h1>
          <p className="text-gray-600 mt-1">
            Track user engagement, behavior patterns, and activity insights
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1d">Last 24h</SelectItem>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Activity Metrics Overview */}
      {activityMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activityMetrics.totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                +{activityMetrics.growthRate}% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activityMetrics.activeUsers}</div>
              <p className="text-xs text-muted-foreground">
                {((activityMetrics.activeUsers / activityMetrics.totalUsers) * 100).toFixed(1)}% of total users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Session</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(activityMetrics.avgSessionDuration)}</div>
              <p className="text-xs text-muted-foreground">
                {activityMetrics.totalSessions} total sessions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{activityMetrics.retentionRate}%</div>
              <p className="text-xs text-muted-foreground">
                {activityMetrics.bounceRate}% bounce rate
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="activity" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="activity">User Activity</TabsTrigger>
          <TabsTrigger value="features">Feature Usage</TabsTrigger>
          <TabsTrigger value="patterns">Login Patterns</TabsTrigger>
          <TabsTrigger value="devices">Device Analytics</TabsTrigger>
        </TabsList>

        {/* User Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent User Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userActivities.map((activity) => {
                  const activityLevel = getActivityLevel(activity.actionsCount);
                  return (
                    <div key={activity.userId} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          {getDeviceIcon(activity.deviceType)}
                          <div>
                            <div className="font-medium">{activity.userName}</div>
                            <div className="text-sm text-gray-600">{activity.email}</div>
                          </div>
                        </div>
                        <Badge className={getRoleColor(activity.role)}>
                          {activity.role.replace('_', ' ')}
                        </Badge>
                      </div>

                      <div className="flex items-center space-x-6 text-sm">
                        <div className="text-center">
                          <div className="font-medium">{formatDuration(activity.sessionDuration)}</div>
                          <div className="text-gray-500">Session</div>
                        </div>
                        <div className="text-center">
                          <div className={`font-medium ${activityLevel.color}`}>{activity.actionsCount}</div>
                          <div className="text-gray-500">Actions</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{activity.loginCount}</div>
                          <div className="text-gray-500">Logins</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium">{formatRelativeTime(activity.lastActive)}</div>
                          <div className="text-gray-500">Last Active</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feature Usage Tab */}
        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Feature Usage Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {featureUsage.map((feature, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {feature.icon}
                      <span className="font-medium">{feature.feature}</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <div className="font-medium">{feature.usage}%</div>
                        <div className="text-sm text-gray-500">Usage Rate</div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${feature.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {feature.growth > 0 ? '+' : ''}{feature.growth}%
                        </div>
                        <div className="text-sm text-gray-500">Growth</div>
                      </div>
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${feature.usage}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Login Patterns Tab */}
        <TabsContent value="patterns" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5" />
                Login Patterns
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-8 gap-2">
                  {loginPatterns.map((pattern, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-blue-100 rounded-lg p-3 mb-2">
                        <div className="h-16 flex items-end justify-center">
                          <div
                            className="bg-blue-600 rounded-t w-full"
                            style={{ height: `${(pattern.count / 70) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-sm font-medium">{pattern.hour}:00</div>
                      <div className="text-xs text-gray-500">{pattern.count}</div>
                    </div>
                  ))}
                </div>
                <div className="text-center text-sm text-gray-600 mt-4">
                  Peak login hours: 9:00 AM and 2:00 PM
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Device Analytics Tab */}
        <TabsContent value="devices" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Monitor className="h-5 w-5" />
                  Device Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Monitor className="h-4 w-4" />
                      <span>Desktop</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '65%' }}></div>
                      </div>
                      <span className="text-sm font-medium">65%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Smartphone className="h-4 w-4" />
                      <span>Mobile</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '28%' }}></div>
                      </div>
                      <span className="text-sm font-medium">28%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Smartphone className="h-4 w-4" />
                      <span>Tablet</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-purple-600 h-2 rounded-full" style={{ width: '7%' }}></div>
                      </div>
                      <span className="text-sm font-medium">7%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  Browser Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span>Chrome</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-blue-600 h-2 rounded-full" style={{ width: '72%' }}></div>
                      </div>
                      <span className="text-sm font-medium">72%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Safari</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-green-600 h-2 rounded-full" style={{ width: '18%' }}></div>
                      </div>
                      <span className="text-sm font-medium">18%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Firefox</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-600 h-2 rounded-full" style={{ width: '10%' }}></div>
                      </div>
                      <span className="text-sm font-medium">10%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
