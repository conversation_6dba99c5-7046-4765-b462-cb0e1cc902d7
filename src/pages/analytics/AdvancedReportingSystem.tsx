import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  FileText,
  Download,
  Calendar,
  Filter,
  Settings,
  BarChart3,
  PieChart,
  TrendingUp,
  Users,
  Building,
  Clock,
  Target,
  Award,
  RefreshCw,
  Play,
  Pause,
  Eye,
  Edit,
  Trash2,
  Plus,
  Save,
  Share,
  Mail,
  FileSpreadsheet,
  FileImage,
  Zap
} from 'lucide-react';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  category: 'user_analytics' | 'role_effectiveness' | 'onboarding' | 'organizational' | 'custom';
  metrics: string[];
  filters: { [key: string]: string | string[] | number | boolean };
  schedule: 'manual' | 'daily' | 'weekly' | 'monthly';
  format: 'pdf' | 'excel' | 'csv';
  recipients: string[];
  lastGenerated: string;
  isActive: boolean;
  createdBy: string;
}

interface ScheduledReport {
  id: string;
  templateId: string;
  templateName: string;
  nextRun: string;
  frequency: string;
  status: 'active' | 'paused' | 'failed';
  lastRun: string;
  recipients: number;
}

interface ReportMetric {
  id: string;
  name: string;
  category: string;
  description: string;
  dataType: 'number' | 'percentage' | 'duration' | 'date';
  isSelected: boolean;
}

interface GeneratedReport {
  id: string;
  name: string;
  generatedAt: string;
  format: string;
  size: string;
  downloadUrl: string;
  status: 'ready' | 'generating' | 'failed';
}

export default function AdvancedReportingSystem() {
  const { organization } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [reportTemplates, setReportTemplates] = useState<ReportTemplate[]>([]);
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([]);
  const [availableMetrics, setAvailableMetrics] = useState<ReportMetric[]>([]);
  const [generatedReports, setGeneratedReports] = useState<GeneratedReport[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [isCreatingReport, setIsCreatingReport] = useState(false);

  useEffect(() => {
    if (organization) {
      loadReportingData();
    }
  }, [organization]);

  const loadReportingData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock report templates
      const mockTemplates: ReportTemplate[] = [
        {
          id: 'user_activity_weekly',
          name: 'Weekly User Activity Report',
          description: 'Comprehensive weekly analysis of user engagement and activity patterns',
          category: 'user_analytics',
          metrics: ['active_users', 'session_duration', 'feature_usage', 'login_patterns'],
          filters: { dateRange: '7d', departments: ['all'] },
          schedule: 'weekly',
          format: 'pdf',
          recipients: ['<EMAIL>', '<EMAIL>'],
          lastGenerated: '2024-01-15T09:00:00Z',
          isActive: true,
          createdBy: 'admin'
        },
        {
          id: 'role_effectiveness_monthly',
          name: 'Monthly Role Effectiveness Analysis',
          description: 'Detailed monthly analysis of role performance and optimization opportunities',
          category: 'role_effectiveness',
          metrics: ['role_utilization', 'permission_usage', 'effectiveness_scores', 'transitions'],
          filters: { dateRange: '30d', roles: ['all'] },
          schedule: 'monthly',
          format: 'excel',
          recipients: ['<EMAIL>'],
          lastGenerated: '2024-01-01T08:00:00Z',
          isActive: true,
          createdBy: 'hr_manager'
        },
        {
          id: 'onboarding_insights',
          name: 'Onboarding Performance Dashboard',
          description: 'Real-time insights into onboarding completion rates and bottlenecks',
          category: 'onboarding',
          metrics: ['completion_rates', 'bottlenecks', 'user_progress', 'step_analysis'],
          filters: { dateRange: '30d', departments: ['all'] },
          schedule: 'manual',
          format: 'pdf',
          recipients: ['<EMAIL>'],
          lastGenerated: '2024-01-10T14:30:00Z',
          isActive: false,
          createdBy: 'onboarding_specialist'
        }
      ];

      // Mock scheduled reports
      const mockScheduledReports: ScheduledReport[] = [
        {
          id: 'sched_1',
          templateId: 'user_activity_weekly',
          templateName: 'Weekly User Activity Report',
          nextRun: '2024-01-22T09:00:00Z',
          frequency: 'Weekly (Mondays)',
          status: 'active',
          lastRun: '2024-01-15T09:00:00Z',
          recipients: 2
        },
        {
          id: 'sched_2',
          templateId: 'role_effectiveness_monthly',
          templateName: 'Monthly Role Effectiveness Analysis',
          nextRun: '2024-02-01T08:00:00Z',
          frequency: 'Monthly (1st)',
          status: 'active',
          lastRun: '2024-01-01T08:00:00Z',
          recipients: 1
        }
      ];

      // Mock available metrics
      const mockMetrics: ReportMetric[] = [
        { id: 'active_users', name: 'Active Users', category: 'User Analytics', description: 'Number of active users in the system', dataType: 'number', isSelected: false },
        { id: 'session_duration', name: 'Session Duration', category: 'User Analytics', description: 'Average session duration', dataType: 'duration', isSelected: false },
        { id: 'login_patterns', name: 'Login Patterns', category: 'User Analytics', description: 'User login frequency and timing', dataType: 'number', isSelected: false },
        { id: 'role_utilization', name: 'Role Utilization', category: 'Role Analytics', description: 'Role usage and effectiveness metrics', dataType: 'percentage', isSelected: false },
        { id: 'permission_usage', name: 'Permission Usage', category: 'Role Analytics', description: 'Permission utilization across roles', dataType: 'percentage', isSelected: false },
        { id: 'completion_rates', name: 'Completion Rates', category: 'Onboarding', description: 'Onboarding completion statistics', dataType: 'percentage', isSelected: false },
        { id: 'department_growth', name: 'Department Growth', category: 'Organizational', description: 'Growth rates by department', dataType: 'percentage', isSelected: false },
        { id: 'retention_rates', name: 'Retention Rates', category: 'Organizational', description: 'Employee retention metrics', dataType: 'percentage', isSelected: false }
      ];

      // Mock generated reports
      const mockGeneratedReports: GeneratedReport[] = [
        {
          id: 'gen_1',
          name: 'Weekly User Activity Report - Jan 15, 2024',
          generatedAt: '2024-01-15T09:15:00Z',
          format: 'PDF',
          size: '2.4 MB',
          downloadUrl: '/reports/weekly_activity_jan15.pdf',
          status: 'ready'
        },
        {
          id: 'gen_2',
          name: 'Monthly Role Effectiveness - Jan 2024',
          generatedAt: '2024-01-01T08:30:00Z',
          format: 'Excel',
          size: '1.8 MB',
          downloadUrl: '/reports/role_effectiveness_jan.xlsx',
          status: 'ready'
        },
        {
          id: 'gen_3',
          name: 'Onboarding Insights - Current',
          generatedAt: '2024-01-18T10:00:00Z',
          format: 'PDF',
          size: '1.2 MB',
          downloadUrl: '/reports/onboarding_current.pdf',
          status: 'generating'
        }
      ];

      setReportTemplates(mockTemplates);
      setScheduledReports(mockScheduledReports);
      setAvailableMetrics(mockMetrics);
      setGeneratedReports(mockGeneratedReports);

    } catch (error) {
      console.error('Failed to load reporting data:', error);
      toast({
        title: "Error",
        description: "Failed to load reporting data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'user_analytics': return 'bg-blue-100 text-blue-700';
      case 'role_effectiveness': return 'bg-green-100 text-green-700';
      case 'onboarding': return 'bg-purple-100 text-purple-700';
      case 'organizational': return 'bg-orange-100 text-orange-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700';
      case 'paused': return 'bg-yellow-100 text-yellow-700';
      case 'failed': return 'bg-red-100 text-red-700';
      case 'ready': return 'bg-green-100 text-green-700';
      case 'generating': return 'bg-blue-100 text-blue-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <Play className="h-4 w-4" />;
      case 'paused': return <Pause className="h-4 w-4" />;
      case 'failed': return <Trash2 className="h-4 w-4" />;
      case 'ready': return <Download className="h-4 w-4" />;
      case 'generating': return <RefreshCw className="h-4 w-4 animate-spin" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const handleGenerateReport = (templateId: string) => {
    toast({
      title: "Report Generation Started",
      description: "Your report is being generated and will be available shortly.",
    });
  };

  const handleDownloadReport = (reportId: string) => {
    toast({
      title: "Download Started",
      description: "Your report download has begun.",
    });
  };

  const handleCreateNewReport = () => {
    setIsCreatingReport(true);
  };

  const handleScheduleReport = (templateId: string) => {
    toast({
      title: "Report Scheduled",
      description: "Your report has been scheduled successfully.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Advanced Reporting & Export</h1>
          <p className="text-gray-600 mt-1">
            Create, schedule, and manage comprehensive analytics reports
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={handleCreateNewReport}>
            <Plus className="w-4 h-4 mr-2" />
            New Report
          </Button>
          <Button variant="outline">
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <Tabs defaultValue="templates" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="templates">Report Templates</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Reports</TabsTrigger>
          <TabsTrigger value="generated">Generated Reports</TabsTrigger>
          <TabsTrigger value="insights">Automated Insights</TabsTrigger>
        </TabsList>

        {/* Report Templates Tab */}
        <TabsContent value="templates" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Report Templates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {reportTemplates.map((template) => (
                  <div key={template.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-sm text-gray-600">{template.description}</div>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge className={getCategoryColor(template.category)}>
                            {template.category.replace('_', ' ')}
                          </Badge>
                          <Badge variant="outline">{template.format.toUpperCase()}</Badge>
                          <Badge variant="outline">{template.schedule}</Badge>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="text-right text-sm">
                        <div className="font-medium">{template.recipients.length} recipients</div>
                        <div className="text-gray-500">Last: {formatRelativeTime(template.lastGenerated)}</div>
                      </div>
                      <Button variant="outline" size="sm" onClick={() => handleGenerateReport(template.id)}>
                        <Play className="w-4 h-4 mr-1" />
                        Generate
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Reports Tab */}
        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Scheduled Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduledReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <div>
                          <div className="font-medium">{report.templateName}</div>
                          <div className="text-sm text-gray-600">{report.frequency}</div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{new Date(report.nextRun).toLocaleDateString()}</div>
                        <div className="text-gray-500">Next Run</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatRelativeTime(report.lastRun)}</div>
                        <div className="text-gray-500">Last Run</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{report.recipients}</div>
                        <div className="text-gray-500">Recipients</div>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Pause className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Generated Reports Tab */}
        <TabsContent value="generated" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Generated Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generatedReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <div>
                          <div className="font-medium">{report.name}</div>
                          <div className="text-sm text-gray-600">
                            Generated {formatRelativeTime(report.generatedAt)}
                          </div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{report.format}</div>
                        <div className="text-gray-500">Format</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{report.size}</div>
                        <div className="text-gray-500">Size</div>
                      </div>
                      <div className="flex space-x-1">
                        {report.status === 'ready' && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => handleDownloadReport(report.id)}>
                              <Download className="w-4 h-4 mr-1" />
                              Download
                            </Button>
                            <Button variant="outline" size="sm">
                              <Share className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                        {report.status === 'generating' && (
                          <Button variant="outline" size="sm" disabled>
                            <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                            Generating...
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Automated Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Automated Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-blue-900">User Engagement Trending Up</h3>
                      <p className="text-sm text-blue-700 mt-1">
                        User activity has increased by 15.2% this week, with Engineering department showing the highest growth.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">High Impact</Badge>
                        <span className="text-xs text-blue-600">2 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-yellow-50">
                  <div className="flex items-start space-x-3">
                    <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-900">Onboarding Bottleneck Detected</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Training completion step is taking 72 minutes on average, 40% longer than target.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">Medium Impact</Badge>
                        <span className="text-xs text-yellow-600">4 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-green-50">
                  <div className="flex items-start space-x-3">
                    <Award className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-green-900">Retention Rate Improvement</h3>
                      <p className="text-sm text-green-700 mt-1">
                        Overall retention rate has improved to 91.7%, exceeding the quarterly target of 90%.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">Positive</Badge>
                        <span className="text-xs text-green-600">6 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-purple-50">
                  <div className="flex items-start space-x-3">
                    <Users className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-purple-900">Role Optimization Opportunity</h3>
                      <p className="text-sm text-purple-700 mt-1">
                        Analytics permissions are underutilized across roles. Consider expanding access to improve data-driven decisions.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">Optimization</Badge>
                        <span className="text-xs text-purple-600">1 day ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6">
                <h3 className="font-medium mb-3">Insight Configuration</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Alert Thresholds</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>User Activity Drop</span>
                        <span className="font-medium">-10%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Retention Rate Alert</span>
                        <span className="font-medium">&lt;85%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Onboarding Delay</span>
                        <span className="font-medium">&gt;3 days</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Notification Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Email Alerts</span>
                        <Badge className="bg-green-100 text-green-700">Enabled</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Dashboard Notifications</span>
                        <Badge className="bg-green-100 text-green-700">Enabled</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Weekly Summary</span>
                        <Badge className="bg-green-100 text-green-700">Enabled</Badge>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Scheduled Reports Tab */}
        <TabsContent value="scheduled" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Scheduled Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {scheduledReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <div>
                          <div className="font-medium">{report.templateName}</div>
                          <div className="text-sm text-gray-600">{report.frequency}</div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{new Date(report.nextRun).toLocaleDateString()}</div>
                        <div className="text-gray-500">Next Run</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatRelativeTime(report.lastRun)}</div>
                        <div className="text-gray-500">Last Run</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{report.recipients}</div>
                        <div className="text-gray-500">Recipients</div>
                      </div>
                      <div className="flex space-x-1">
                        <Button variant="outline" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Pause className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Generated Reports Tab */}
        <TabsContent value="generated" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Generated Reports
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generatedReports.map((report) => (
                  <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(report.status)}
                        <div>
                          <div className="font-medium">{report.name}</div>
                          <div className="text-sm text-gray-600">
                            Generated {formatRelativeTime(report.generatedAt)}
                          </div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(report.status)}>
                        {report.status}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{report.format}</div>
                        <div className="text-gray-500">Format</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{report.size}</div>
                        <div className="text-gray-500">Size</div>
                      </div>
                      <div className="flex space-x-1">
                        {report.status === 'ready' && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => handleDownloadReport(report.id)}>
                              <Download className="w-4 h-4 mr-1" />
                              Download
                            </Button>
                            <Button variant="outline" size="sm">
                              <Share className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                        {report.status === 'generating' && (
                          <Button variant="outline" size="sm" disabled>
                            <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                            Generating...
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Automated Insights Tab */}
        <TabsContent value="insights" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Automated Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="p-4 border rounded-lg bg-blue-50">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-blue-900">User Engagement Trending Up</h3>
                      <p className="text-sm text-blue-700 mt-1">
                        User activity has increased by 15.2% this week, with Engineering department showing the highest growth.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">High Impact</Badge>
                        <span className="text-xs text-blue-600">2 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-yellow-50">
                  <div className="flex items-start space-x-3">
                    <Clock className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-yellow-900">Onboarding Bottleneck Detected</h3>
                      <p className="text-sm text-yellow-700 mt-1">
                        Training completion step is taking 72 minutes on average, 40% longer than target.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">Medium Impact</Badge>
                        <span className="text-xs text-yellow-600">4 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg bg-green-50">
                  <div className="flex items-start space-x-3">
                    <Award className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h3 className="font-medium text-green-900">Retention Rate Improvement</h3>
                      <p className="text-sm text-green-700 mt-1">
                        Overall retention rate has improved to 91.7%, exceeding the quarterly target of 90%.
                      </p>
                      <div className="flex items-center space-x-2 mt-2">
                        <Badge variant="outline" className="text-xs">Positive</Badge>
                        <span className="text-xs text-green-600">6 hours ago</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
