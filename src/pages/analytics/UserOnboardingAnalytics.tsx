import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  UserPlus,
  Users,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award,
  Calendar,
  FileText,
  MessageSquare,
  Settings,
  Download,
  RefreshCw,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  Eye,
  User,
  Building,
  Briefcase,
  GraduationCap,
  Shield
} from 'lucide-react';

interface OnboardingStepData {
  stepId: string;
  stepName: string;
  stepOrder: number;
  completionRate: number;
  avgCompletionTime: number;
  dropOffRate: number;
  commonIssues: string[];
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  icon: React.ReactNode;
}

interface UserOnboardingProgress {
  userId: string;
  userName: string;
  email: string;
  role: string;
  startDate: string;
  currentStep: string;
  completedSteps: number;
  totalSteps: number;
  progressPercentage: number;
  timeSpent: number;
  status: 'in_progress' | 'completed' | 'stalled' | 'dropped_off';
  lastActivity: string;
  department: string;
}

interface OnboardingMetrics {
  totalNewUsers: number;
  completedOnboarding: number;
  inProgress: number;
  dropOffRate: number;
  avgCompletionTime: number;
  completionRate: number;
  stalledUsers: number;
  avgTimeToFirstAction: number;
}

interface BottleneckAnalysis {
  stepName: string;
  bottleneckSeverity: 'high' | 'medium' | 'low';
  avgStuckTime: number;
  affectedUsers: number;
  commonReasons: string[];
  recommendations: string[];
  impact: string;
}

export default function UserOnboardingAnalytics() {
  const { organization } = useAuth();
  const [dateRange, setDateRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);
  const [onboardingSteps, setOnboardingSteps] = useState<OnboardingStepData[]>([]);
  const [userProgress, setUserProgress] = useState<UserOnboardingProgress[]>([]);
  const [onboardingMetrics, setOnboardingMetrics] = useState<OnboardingMetrics | null>(null);
  const [bottlenecks, setBottlenecks] = useState<BottleneckAnalysis[]>([]);

  useEffect(() => {
    if (organization) {
      loadOnboardingAnalytics();
    }
  }, [organization, dateRange]);

  const loadOnboardingAnalytics = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock onboarding steps data
      const mockOnboardingSteps: OnboardingStepData[] = [
        {
          stepId: 'profile_setup',
          stepName: 'Profile Setup',
          stepOrder: 1,
          completionRate: 95,
          avgCompletionTime: 8,
          dropOffRate: 5,
          commonIssues: ['Missing profile photo', 'Incomplete contact info'],
          trend: 'up',
          trendPercentage: 3.2,
          icon: <User className="h-4 w-4" />
        },
        {
          stepId: 'role_assignment',
          stepName: 'Role Assignment',
          stepOrder: 2,
          completionRate: 88,
          avgCompletionTime: 15,
          dropOffRate: 12,
          commonIssues: ['Role confusion', 'Permission clarification needed'],
          trend: 'stable',
          trendPercentage: 0.8,
          icon: <Shield className="h-4 w-4" />
        },
        {
          stepId: 'system_tour',
          stepName: 'System Tour',
          stepOrder: 3,
          completionRate: 76,
          avgCompletionTime: 25,
          dropOffRate: 24,
          commonIssues: ['Tour too long', 'Skip tutorial option used'],
          trend: 'down',
          trendPercentage: -5.1,
          icon: <Eye className="h-4 w-4" />
        },
        {
          stepId: 'department_intro',
          stepName: 'Department Introduction',
          stepOrder: 4,
          completionRate: 82,
          avgCompletionTime: 20,
          dropOffRate: 18,
          commonIssues: ['Department contact unavailable', 'Scheduling conflicts'],
          trend: 'up',
          trendPercentage: 7.3,
          icon: <Building className="h-4 w-4" />
        },
        {
          stepId: 'first_task',
          stepName: 'First Task Assignment',
          stepOrder: 5,
          completionRate: 69,
          avgCompletionTime: 45,
          dropOffRate: 31,
          commonIssues: ['Task complexity', 'Unclear instructions', 'Missing resources'],
          trend: 'down',
          trendPercentage: -8.7,
          icon: <Briefcase className="h-4 w-4" />
        },
        {
          stepId: 'training_completion',
          stepName: 'Training Completion',
          stepOrder: 6,
          completionRate: 64,
          avgCompletionTime: 120,
          dropOffRate: 36,
          commonIssues: ['Training material outdated', 'Time constraints', 'Technical issues'],
          trend: 'down',
          trendPercentage: -12.4,
          icon: <GraduationCap className="h-4 w-4" />
        }
      ];

      // Mock user progress data
      const mockUserProgress: UserOnboardingProgress[] = [
        {
          userId: '1',
          userName: 'Alice Johnson',
          email: '<EMAIL>',
          role: 'recruiter',
          startDate: '2024-01-15',
          currentStep: 'training_completion',
          completedSteps: 5,
          totalSteps: 6,
          progressPercentage: 83,
          timeSpent: 180,
          status: 'in_progress',
          lastActivity: '2024-01-18T14:30:00Z',
          department: 'HR'
        },
        {
          userId: '2',
          userName: 'Bob Chen',
          email: '<EMAIL>',
          role: 'hiring_manager',
          startDate: '2024-01-12',
          currentStep: 'completed',
          completedSteps: 6,
          totalSteps: 6,
          progressPercentage: 100,
          timeSpent: 145,
          status: 'completed',
          lastActivity: '2024-01-16T16:45:00Z',
          department: 'Engineering'
        },
        {
          userId: '3',
          userName: 'Carol Davis',
          email: '<EMAIL>',
          role: 'interviewer',
          startDate: '2024-01-10',
          currentStep: 'system_tour',
          completedSteps: 2,
          totalSteps: 6,
          progressPercentage: 33,
          timeSpent: 45,
          status: 'stalled',
          lastActivity: '2024-01-12T10:15:00Z',
          department: 'Marketing'
        },
        {
          userId: '4',
          userName: 'David Wilson',
          email: '<EMAIL>',
          role: 'employee',
          startDate: '2024-01-08',
          currentStep: 'first_task',
          completedSteps: 4,
          totalSteps: 6,
          progressPercentage: 67,
          timeSpent: 95,
          status: 'in_progress',
          lastActivity: '2024-01-17T11:20:00Z',
          department: 'Sales'
        },
        {
          userId: '5',
          userName: 'Eva Martinez',
          email: '<EMAIL>',
          role: 'recruiter',
          startDate: '2024-01-05',
          currentStep: 'role_assignment',
          completedSteps: 1,
          totalSteps: 6,
          progressPercentage: 17,
          timeSpent: 25,
          status: 'dropped_off',
          lastActivity: '2024-01-06T09:30:00Z',
          department: 'HR'
        }
      ];

      // Mock onboarding metrics
      const mockMetrics: OnboardingMetrics = {
        totalNewUsers: 45,
        completedOnboarding: 28,
        inProgress: 12,
        dropOffRate: 11.1,
        avgCompletionTime: 156,
        completionRate: 62.2,
        stalledUsers: 5,
        avgTimeToFirstAction: 18
      };

      // Mock bottleneck analysis
      const mockBottlenecks: BottleneckAnalysis[] = [
        {
          stepName: 'Training Completion',
          bottleneckSeverity: 'high',
          avgStuckTime: 72,
          affectedUsers: 16,
          commonReasons: ['Outdated training materials', 'Technical platform issues', 'Time allocation conflicts'],
          recommendations: ['Update training content', 'Improve platform stability', 'Flexible scheduling options'],
          impact: 'Delays final onboarding completion by average 3 days, affecting productivity'
        },
        {
          stepName: 'First Task Assignment',
          bottleneckSeverity: 'high',
          avgStuckTime: 48,
          affectedUsers: 14,
          commonReasons: ['Unclear task instructions', 'Missing required resources', 'Supervisor unavailability'],
          recommendations: ['Standardize task templates', 'Pre-provision resources', 'Assign backup supervisors'],
          impact: 'Reduces new hire confidence and delays productive contribution'
        },
        {
          stepName: 'System Tour',
          bottleneckSeverity: 'medium',
          avgStuckTime: 36,
          affectedUsers: 11,
          commonReasons: ['Tour length concerns', 'Information overload', 'Skip option overuse'],
          recommendations: ['Break tour into smaller segments', 'Add interactive elements', 'Gamify experience'],
          impact: 'Users miss important system features, leading to later support requests'
        },
        {
          stepName: 'Department Introduction',
          bottleneckSeverity: 'low',
          avgStuckTime: 24,
          affectedUsers: 8,
          commonReasons: ['Scheduling conflicts', 'Department contact unavailable', 'Remote work challenges'],
          recommendations: ['Automated scheduling system', 'Multiple contact options', 'Virtual introduction sessions'],
          impact: 'Delays team integration and relationship building'
        }
      ];

      setOnboardingSteps(mockOnboardingSteps);
      setUserProgress(mockUserProgress);
      setOnboardingMetrics(mockMetrics);
      setBottlenecks(mockBottlenecks);

    } catch (error) {
      console.error('Failed to load onboarding analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load onboarding analytics. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins}m`;
  };

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffInDays === 0) return 'Today';
    if (diffInDays === 1) return 'Yesterday';
    return `${diffInDays} days ago`;
  };

  const getTrendIcon = (trend: string) => {
    if (trend === 'up') return <ArrowUp className="h-4 w-4 text-green-600" />;
    if (trend === 'down') return <ArrowDown className="h-4 w-4 text-red-600" />;
    return <ArrowRight className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = (trend: string) => {
    if (trend === 'up') return 'text-green-600';
    if (trend === 'down') return 'text-red-600';
    return 'text-gray-600';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700';
      case 'in_progress': return 'bg-blue-100 text-blue-700';
      case 'stalled': return 'bg-yellow-100 text-yellow-700';
      case 'dropped_off': return 'bg-red-100 text-red-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'in_progress': return <Clock className="h-4 w-4" />;
      case 'stalled': return <AlertTriangle className="h-4 w-4" />;
      case 'dropped_off': return <XCircle className="h-4 w-4" />;
      default: return <User className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-700 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-700 border-yellow-200';
      default: return 'bg-blue-100 text-blue-700 border-blue-200';
    }
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Onboarding analytics export will be available shortly.",
    });
  };

  const handleRefreshData = () => {
    loadOnboardingAnalytics();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">User Onboarding Analytics</h1>
          <p className="text-gray-600 mt-1">
            Track onboarding progress, identify bottlenecks, and optimize user journey
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Onboarding Metrics Overview */}
      {onboardingMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Users</CardTitle>
              <UserPlus className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{onboardingMetrics.totalNewUsers}</div>
              <p className="text-xs text-muted-foreground">
                {onboardingMetrics.inProgress} in progress
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{onboardingMetrics.completionRate}%</div>
              <p className="text-xs text-muted-foreground">
                {onboardingMetrics.completedOnboarding} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Completion Time</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(onboardingMetrics.avgCompletionTime)}</div>
              <p className="text-xs text-muted-foreground">
                {formatDuration(onboardingMetrics.avgTimeToFirstAction)} to first action
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Drop-off Rate</CardTitle>
              <TrendingDown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{onboardingMetrics.dropOffRate}%</div>
              <p className="text-xs text-muted-foreground">
                {onboardingMetrics.stalledUsers} stalled users
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="progress" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="progress">User Progress</TabsTrigger>
          <TabsTrigger value="steps">Step Analysis</TabsTrigger>
          <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
          <TabsTrigger value="journey">User Journey</TabsTrigger>
        </TabsList>

        {/* User Progress Tab */}
        <TabsContent value="progress" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Individual User Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userProgress.map((user) => (
                  <div key={user.userId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(user.status)}
                        <div>
                          <div className="font-medium">{user.userName}</div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(user.status)}>
                        {user.status.replace('_', ' ')}
                      </Badge>
                      <Badge variant="outline">
                        {user.role.replace('_', ' ')}
                      </Badge>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{user.progressPercentage}%</div>
                        <div className="text-gray-500">Progress</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{user.completedSteps}/{user.totalSteps}</div>
                        <div className="text-gray-500">Steps</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatDuration(user.timeSpent)}</div>
                        <div className="text-gray-500">Time Spent</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatRelativeTime(user.lastActivity)}</div>
                        <div className="text-gray-500">Last Active</div>
                      </div>
                      <div className="w-32">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              user.status === 'completed' ? 'bg-green-600' :
                              user.status === 'in_progress' ? 'bg-blue-600' :
                              user.status === 'stalled' ? 'bg-yellow-600' : 'bg-red-600'
                            }`}
                            style={{ width: `${user.progressPercentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Step Analysis Tab */}
        <TabsContent value="steps" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Onboarding Step Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {onboardingSteps.map((step) => (
                  <div key={step.stepId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {step.icon}
                        <div>
                          <div className="font-medium">{step.stepName}</div>
                          <div className="text-sm text-gray-600">Step {step.stepOrder}</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className={`font-medium ${step.completionRate >= 80 ? 'text-green-600' : step.completionRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                          {step.completionRate}%
                        </div>
                        <div className="text-gray-500">Completion</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatDuration(step.avgCompletionTime)}</div>
                        <div className="text-gray-500">Avg Time</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-medium ${step.dropOffRate <= 10 ? 'text-green-600' : step.dropOffRate <= 20 ? 'text-yellow-600' : 'text-red-600'}`}>
                          {step.dropOffRate}%
                        </div>
                        <div className="text-gray-500">Drop-off</div>
                      </div>
                      <div className="text-center">
                        <div className={`flex items-center space-x-1 ${getTrendColor(step.trend)}`}>
                          {getTrendIcon(step.trend)}
                          <span className="font-medium">
                            {step.trend !== 'stable' && (step.trendPercentage > 0 ? '+' : '')}{step.trendPercentage}%
                          </span>
                        </div>
                        <div className="text-gray-500">Trend</div>
                      </div>
                      <div className="w-32">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              step.completionRate >= 80 ? 'bg-green-600' :
                              step.completionRate >= 60 ? 'bg-yellow-600' : 'bg-red-600'
                            }`}
                            style={{ width: `${step.completionRate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Bottlenecks Tab */}
        <TabsContent value="bottlenecks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Onboarding Bottleneck Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {bottlenecks.map((bottleneck, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${getSeverityColor(bottleneck.bottleneckSeverity)}`}>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-5 w-5" />
                        <h3 className="font-medium">{bottleneck.stepName}</h3>
                      </div>
                      <Badge variant="outline" className="capitalize">
                        {bottleneck.bottleneckSeverity} Severity
                      </Badge>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className="text-center">
                        <div className="font-medium text-lg">{formatDuration(bottleneck.avgStuckTime)}</div>
                        <div className="text-sm text-gray-600">Avg Stuck Time</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-lg">{bottleneck.affectedUsers}</div>
                        <div className="text-sm text-gray-600">Affected Users</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-lg capitalize">{bottleneck.bottleneckSeverity}</div>
                        <div className="text-sm text-gray-600">Severity Level</div>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <h4 className="text-sm font-medium mb-1">Common Reasons</h4>
                        <div className="flex flex-wrap gap-1">
                          {bottleneck.commonReasons.map((reason, reasonIndex) => (
                            <Badge key={reasonIndex} variant="secondary" className="text-xs">
                              {reason}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-1">Recommendations</h4>
                        <ul className="text-sm space-y-1">
                          {bottleneck.recommendations.map((rec, recIndex) => (
                            <li key={recIndex} className="flex items-start space-x-2">
                              <span className="text-blue-600">•</span>
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium mb-1">Impact</h4>
                        <p className="text-sm">{bottleneck.impact}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Journey Tab */}
        <TabsContent value="journey" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                User Journey Visualization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Journey Flow */}
                <div className="flex items-center justify-between">
                  {onboardingSteps.map((step, index) => (
                    <div key={step.stepId} className="flex items-center">
                      <div className="text-center">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                          step.completionRate >= 80 ? 'bg-green-100 text-green-600' :
                          step.completionRate >= 60 ? 'bg-yellow-100 text-yellow-600' : 'bg-red-100 text-red-600'
                        }`}>
                          {step.icon}
                        </div>
                        <div className="text-xs font-medium mt-2">{step.stepName}</div>
                        <div className="text-xs text-gray-500">{step.completionRate}%</div>
                      </div>
                      {index < onboardingSteps.length - 1 && (
                        <ArrowRight className="h-4 w-4 text-gray-400 mx-4" />
                      )}
                    </div>
                  ))}
                </div>

                {/* Journey Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Journey Completion</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Completed</span>
                          <span className="font-medium text-green-600">62.2%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>In Progress</span>
                          <span className="font-medium text-blue-600">26.7%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Dropped Off</span>
                          <span className="font-medium text-red-600">11.1%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Time Analysis</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Avg Completion</span>
                          <span className="font-medium">2h 36m</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Fastest</span>
                          <span className="font-medium text-green-600">1h 45m</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Slowest</span>
                          <span className="font-medium text-red-600">4h 20m</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Department Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Engineering</span>
                          <span className="font-medium">85%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>HR</span>
                          <span className="font-medium">72%</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span>Sales</span>
                          <span className="font-medium">58%</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
