import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { mockOrganizations } from '@/data/multiTenantData';
import { getSecureRandomFloat, getSecureRandomInt } from '@/lib/utils';
import UserActivityDashboard from './UserActivityDashboard';
import RoleEffectivenessReporting from './RoleEffectivenessReporting';
import UserOnboardingAnalytics from './UserOnboardingAnalytics';
import OrganizationalInsightsDashboard from './OrganizationalInsightsDashboard';
import AdvancedReportingSystem from './AdvancedReportingSystem';
import {
  Award,
  BarChart3,
  Briefcase,
  Building2,
  CheckCircle,
  Clock,
  RefreshCw,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
  Activity,
  PieChart
} from 'lucide-react';
import { useState } from 'react';

export default function Analytics() {
  const [dateRange, setDateRange] = useState('30d');
  const { isClientOrganization, isVendorOrganization, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900">Loading Analytics...</h2>
          <p className="text-gray-600">Please wait while we load your analytics data.</p>
        </div>
      </div>
    );
  }

  const isClient = isClientOrganization();
  const isVendor = isVendorOrganization();

  // Mock analytics data - in real app, this would come from API
  const generateMockData = () => {
    const baseData = {
      totalJobs: getSecureRandomInt(20, 70),
      totalCandidates: getSecureRandomInt(100, 300),
      successRate: getSecureRandomInt(65, 95),
      avgTimeToHire: getSecureRandomInt(15, 25),
      activeVendors: getSecureRandomInt(5, 13),
      pendingReviews: getSecureRandomInt(10, 35),
    };

    return baseData;
  };

  const analyticsData = generateMockData();


  const exportData = (format: 'csv' | 'pdf' | 'excel') => {
    // In real app, this would generate and download the file
    console.log(`Exporting analytics data as ${format.toUpperCase()}`);
    alert(`Analytics data exported as ${format.toUpperCase()}!`);
  };

  const ClientAnalytics = () => (
    <div className="space-y-6">
      {/* Client KPI Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Jobs Posted</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.totalJobs}</p>
              </div>
              <Briefcase className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+12% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Candidates Received</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.totalCandidates}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+18% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.successRate}%</p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+5% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Time to Hire</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.avgTimeToHire} days</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingDown className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">-3 days from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Client Charts and Tables */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Vendor Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Award className="mr-2 h-5 w-5" />
              Top Vendor Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockOrganizations.filter(org => org.type === 'vendor').slice(0, 5).map((vendor, index) => {
                const getBadgeColor = (position: number) => {
                  if (position === 0) return 'bg-yellow-500';
                  if (position === 1) return 'bg-gray-400';
                  if (position === 2) return 'bg-orange-500';
                  return 'bg-blue-500';
                };

                return (
                  <div key={vendor.id} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${getBadgeColor(index)}`}>
                        {index + 1}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{vendor.name}</p>
                        <p className="text-xs text-gray-500">{vendor.vendorProfile?.specializations?.[0] ?? 'General'}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{vendor.vendorProfile?.successRate ?? 85}%</p>
                      <p className="text-xs text-gray-500">{getSecureRandomInt(10, 30)} hires</p>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Hiring Funnel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              Hiring Funnel
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm">
                  <span>Applications Received</span>
                  <span>{analyticsData.totalCandidates}</span>
                </div>
                <Progress value={100} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Initial Screening</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.6)}</span>
                </div>
                <Progress value={60} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Interviews Scheduled</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.3)}</span>
                </div>
                <Progress value={30} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Final Round</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.15)}</span>
                </div>
                <Progress value={15} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Offers Extended</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.08)}</span>
                </div>
                <Progress value={8} className="mt-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const VendorAnalytics = () => (
    <div className="space-y-6">
      {/* Vendor KPI Cards */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Jobs Assigned</p>
                <p className="text-2xl font-bold text-gray-900">{Math.floor(analyticsData.totalJobs * 0.6)}</p>
              </div>
              <Briefcase className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+8% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Candidates Submitted</p>
                <p className="text-2xl font-bold text-gray-900">{Math.floor(analyticsData.totalCandidates * 0.4)}</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+15% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Approval Rate</p>
                <p className="text-2xl font-bold text-gray-900">{analyticsData.successRate}%</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">+7% from last month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                <p className="text-2xl font-bold text-gray-900">{Math.floor(analyticsData.avgTimeToHire * 0.3)} hrs</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-4 flex items-center">
              <TrendingDown className="h-4 w-4 text-green-600" />
              <span className="ml-1 text-sm text-green-600">-2 hrs from last month</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Vendor Performance Charts */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Client Satisfaction */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building2 className="mr-2 h-5 w-5" />
              Client Satisfaction
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockOrganizations.filter(org => org.type === 'client').slice(0, 3).map((client, index) => (
                <div key={client.id} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium">{client.name}</span>
                    <span className="text-sm text-gray-600">{getSecureRandomFloat(4.2, 4.8).toFixed(1)}/5.0</span>
                  </div>
                  <Progress value={getSecureRandomFloat(4.2, 4.8) * 20} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Monthly Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5" />
              Monthly Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm">
                  <span>Candidates Submitted</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.4)}</span>
                </div>
                <Progress value={75} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Interviews Scheduled</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.2)}</span>
                </div>
                <Progress value={60} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Successful Placements</span>
                  <span>{Math.floor(analyticsData.totalCandidates * 0.08)}</span>
                </div>
                <Progress value={45} className="mt-2" />
              </div>
              <div>
                <div className="flex justify-between text-sm">
                  <span>Revenue Generated</span>
                  <span>${(Math.floor(analyticsData.totalCandidates * 0.08) * 5000).toLocaleString()}</span>
                </div>
                <Progress value={65} className="mt-2" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
          <p className="text-gray-600">
            {isClient ? 'Track your hiring performance and vendor metrics' : 'Monitor your performance and client satisfaction'}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          
          <Select onValueChange={(format) => exportData(format as 'csv' | 'pdf' | 'excel')}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Export" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="csv">Export CSV</SelectItem>
              <SelectItem value="pdf">Export PDF</SelectItem>
              <SelectItem value="excel">Export Excel</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Analytics Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="user-activity">User Activity</TabsTrigger>
          <TabsTrigger value="role-effectiveness">Role Analytics</TabsTrigger>
          <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
          <TabsTrigger value="organizational">Organizational</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {(() => {
            if (isClient) return <ClientAnalytics />;
            if (isVendor) return <VendorAnalytics />;
            return <ClientAnalytics />;
          })()}
        </TabsContent>

        <TabsContent value="user-activity" className="space-y-6">
          <UserActivityDashboard />
        </TabsContent>

        <TabsContent value="role-effectiveness" className="space-y-6">
          <RoleEffectivenessReporting />
        </TabsContent>

        <TabsContent value="onboarding" className="space-y-6">
          <UserOnboardingAnalytics />
        </TabsContent>

        <TabsContent value="organizational" className="space-y-6">
          <OrganizationalInsightsDashboard />
        </TabsContent>

        <TabsContent value="reporting" className="space-y-6">
          <AdvancedReportingSystem />
        </TabsContent>
      </Tabs>
    </div>
  );
}
