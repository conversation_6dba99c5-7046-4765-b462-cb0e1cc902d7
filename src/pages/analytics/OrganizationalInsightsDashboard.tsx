import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, Ta<PERSON>Content, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  Building,
  Users,
  TrendingUp,
  TrendingDown,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award,
  Calendar,
  Clock,
  UserPlus,
  UserMinus,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  Download,
  RefreshCw,
  Eye,
  Briefcase,
  GraduationCap,
  Shield,
  MessageSquare,
  Settings,
  FileText
} from 'lucide-react';

interface DepartmentData {
  departmentId: string;
  departmentName: string;
  userCount: number;
  activeUsers: number;
  newHires: number;
  departures: number;
  avgTenure: number;
  retentionRate: number;
  growthRate: number;
  avgSessionDuration: number;
  productivityScore: number;
  trend: 'up' | 'down' | 'stable';
  roles: { [roleName: string]: number };
}

interface UserLifecycleData {
  stage: string;
  userCount: number;
  percentage: number;
  avgDuration: number;
  conversionRate: number;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  icon: React.ReactNode;
}

interface OrganizationalMetrics {
  totalEmployees: number;
  totalDepartments: number;
  avgRetentionRate: number;
  overallGrowthRate: number;
  avgTenure: number;
  activeUsersPercentage: number;
  newHiresThisMonth: number;
  departuresThisMonth: number;
}

interface GrowthTrendData {
  month: string;
  totalUsers: number;
  newHires: number;
  departures: number;
  netGrowth: number;
  retentionRate: number;
}

export default function OrganizationalInsightsDashboard() {
  const { organization } = useAuth();
  const [dateRange, setDateRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(false);
  const [departmentData, setDepartmentData] = useState<DepartmentData[]>([]);
  const [userLifecycleData, setUserLifecycleData] = useState<UserLifecycleData[]>([]);
  const [organizationalMetrics, setOrganizationalMetrics] = useState<OrganizationalMetrics | null>(null);
  const [growthTrends, setGrowthTrends] = useState<GrowthTrendData[]>([]);

  useEffect(() => {
    if (organization) {
      loadOrganizationalInsights();
    }
  }, [organization, dateRange]);

  const loadOrganizationalInsights = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock department data
      const mockDepartmentData: DepartmentData[] = [
        {
          departmentId: 'engineering',
          departmentName: 'Engineering',
          userCount: 45,
          activeUsers: 42,
          newHires: 8,
          departures: 2,
          avgTenure: 28,
          retentionRate: 95.6,
          growthRate: 15.4,
          avgSessionDuration: 145,
          productivityScore: 92,
          trend: 'up',
          roles: { 'admin': 3, 'hiring_manager': 8, 'interviewer': 15, 'employee': 19 }
        },
        {
          departmentId: 'hr',
          departmentName: 'Human Resources',
          userCount: 18,
          activeUsers: 17,
          newHires: 3,
          departures: 1,
          avgTenure: 32,
          retentionRate: 94.4,
          growthRate: 12.5,
          avgSessionDuration: 98,
          productivityScore: 88,
          trend: 'up',
          roles: { 'admin': 2, 'recruiter': 12, 'hiring_manager': 4 }
        },
        {
          departmentId: 'sales',
          departmentName: 'Sales',
          userCount: 32,
          activeUsers: 28,
          newHires: 5,
          departures: 4,
          avgTenure: 18,
          retentionRate: 87.5,
          growthRate: 3.2,
          avgSessionDuration: 76,
          productivityScore: 78,
          trend: 'stable',
          roles: { 'hiring_manager': 6, 'interviewer': 10, 'employee': 16 }
        },
        {
          departmentId: 'marketing',
          departmentName: 'Marketing',
          userCount: 24,
          activeUsers: 21,
          newHires: 2,
          departures: 3,
          avgTenure: 22,
          retentionRate: 87.5,
          growthRate: -4.2,
          avgSessionDuration: 67,
          productivityScore: 74,
          trend: 'down',
          roles: { 'hiring_manager': 4, 'interviewer': 8, 'employee': 12 }
        },
        {
          departmentId: 'finance',
          departmentName: 'Finance',
          userCount: 15,
          activeUsers: 14,
          newHires: 1,
          departures: 1,
          avgTenure: 35,
          retentionRate: 93.3,
          growthRate: 0,
          avgSessionDuration: 89,
          productivityScore: 85,
          trend: 'stable',
          roles: { 'admin': 1, 'hiring_manager': 3, 'employee': 11 }
        }
      ];

      // Mock user lifecycle data
      const mockUserLifecycleData: UserLifecycleData[] = [
        {
          stage: 'New Hire',
          userCount: 19,
          percentage: 14.1,
          avgDuration: 30,
          conversionRate: 89.5,
          trend: 'up',
          trendPercentage: 12.3,
          icon: <UserPlus className="h-4 w-4" />
        },
        {
          stage: 'Onboarding',
          userCount: 12,
          percentage: 8.9,
          avgDuration: 14,
          conversionRate: 91.7,
          trend: 'up',
          trendPercentage: 8.7,
          icon: <GraduationCap className="h-4 w-4" />
        },
        {
          stage: 'Active Employee',
          userCount: 98,
          percentage: 72.6,
          avgDuration: 540,
          conversionRate: 95.9,
          trend: 'stable',
          trendPercentage: 1.2,
          icon: <Users className="h-4 w-4" />
        },
        {
          stage: 'At Risk',
          userCount: 4,
          percentage: 3.0,
          avgDuration: 90,
          conversionRate: 25.0,
          trend: 'down',
          trendPercentage: -15.6,
          icon: <Clock className="h-4 w-4" />
        },
        {
          stage: 'Departing',
          userCount: 2,
          percentage: 1.5,
          avgDuration: 15,
          conversionRate: 0,
          trend: 'down',
          trendPercentage: -8.3,
          icon: <UserMinus className="h-4 w-4" />
        }
      ];

      // Mock organizational metrics
      const mockOrganizationalMetrics: OrganizationalMetrics = {
        totalEmployees: 134,
        totalDepartments: 5,
        avgRetentionRate: 91.7,
        overallGrowthRate: 8.2,
        avgTenure: 27,
        activeUsersPercentage: 91.0,
        newHiresThisMonth: 19,
        departuresThisMonth: 11
      };

      // Mock growth trend data
      const mockGrowthTrends: GrowthTrendData[] = [
        { month: 'Oct', totalUsers: 118, newHires: 12, departures: 8, netGrowth: 4, retentionRate: 93.2 },
        { month: 'Nov', totalUsers: 125, newHires: 15, departures: 8, netGrowth: 7, retentionRate: 93.6 },
        { month: 'Dec', totalUsers: 129, newHires: 8, departures: 4, netGrowth: 4, retentionRate: 96.9 },
        { month: 'Jan', totalUsers: 134, newHires: 19, departures: 14, netGrowth: 5, retentionRate: 89.1 },
        { month: 'Feb', totalUsers: 134, newHires: 11, departures: 11, netGrowth: 0, retentionRate: 91.8 }
      ];

      setDepartmentData(mockDepartmentData);
      setUserLifecycleData(mockUserLifecycleData);
      setOrganizationalMetrics(mockOrganizationalMetrics);
      setGrowthTrends(mockGrowthTrends);

    } catch (error) {
      console.error('Failed to load organizational insights:', error);
      toast({
        title: "Error",
        description: "Failed to load organizational insights. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getTrendIcon = (trend: string) => {
    if (trend === 'up') return <ArrowUp className="h-4 w-4 text-green-600" />;
    if (trend === 'down') return <ArrowDown className="h-4 w-4 text-red-600" />;
    return <ArrowRight className="h-4 w-4 text-gray-600" />;
  };

  const getTrendColor = (trend: string) => {
    if (trend === 'up') return 'text-green-600';
    if (trend === 'down') return 'text-red-600';
    return 'text-gray-600';
  };

  const getRetentionColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getProductivityColor = (score: number) => {
    if (score >= 85) return 'text-green-600';
    if (score >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatDuration = (days: number) => {
    if (days >= 365) {
      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      return `${years}y ${months}m`;
    } else if (days >= 30) {
      const months = Math.floor(days / 30);
      return `${months}m`;
    }
    return `${days}d`;
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Organizational insights export will be available shortly.",
    });
  };

  const handleRefreshData = () => {
    loadOrganizationalInsights();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Organizational Insights</h1>
          <p className="text-gray-600 mt-1">
            Comprehensive analytics on organizational structure, growth, and employee lifecycle
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleRefreshData} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline" onClick={handleExportData}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Organizational Metrics Overview */}
      {organizationalMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{organizationalMetrics.totalEmployees}</div>
              <p className="text-xs text-muted-foreground">
                {organizationalMetrics.activeUsersPercentage}% active users
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Retention Rate</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{organizationalMetrics.avgRetentionRate}%</div>
              <p className="text-xs text-muted-foreground">
                {organizationalMetrics.totalDepartments} departments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Growth Rate</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">+{organizationalMetrics.overallGrowthRate}%</div>
              <p className="text-xs text-muted-foreground">
                {organizationalMetrics.newHiresThisMonth} new hires this month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Avg Tenure</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatDuration(organizationalMetrics.avgTenure * 30)}</div>
              <p className="text-xs text-muted-foreground">
                {organizationalMetrics.departuresThisMonth} departures this month
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="departments" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="departments">Departments</TabsTrigger>
          <TabsTrigger value="lifecycle">User Lifecycle</TabsTrigger>
          <TabsTrigger value="growth">Growth Trends</TabsTrigger>
          <TabsTrigger value="comparative">Comparative Analysis</TabsTrigger>
        </TabsList>

        {/* Departments Tab */}
        <TabsContent value="departments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building className="h-5 w-5" />
                Department Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {departmentData.map((dept) => (
                  <div key={dept.departmentId} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Building className="h-5 w-5 text-blue-600" />
                        <div>
                          <div className="font-medium">{dept.departmentName}</div>
                          <div className="text-sm text-gray-600">
                            {dept.activeUsers}/{dept.userCount} active users
                          </div>
                        </div>
                      </div>
                      <div className={`flex items-center space-x-1 ${getTrendColor(dept.trend)}`}>
                        {getTrendIcon(dept.trend)}
                        <span className="text-sm font-medium">
                          {dept.trend !== 'stable' && (dept.growthRate > 0 ? '+' : '')}{dept.growthRate}%
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className={`font-medium ${getRetentionColor(dept.retentionRate)}`}>
                          {dept.retentionRate}%
                        </div>
                        <div className="text-gray-500">Retention</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{formatDuration(dept.avgTenure * 30)}</div>
                        <div className="text-gray-500">Avg Tenure</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-medium ${getProductivityColor(dept.productivityScore)}`}>
                          {dept.productivityScore}
                        </div>
                        <div className="text-gray-500">Productivity</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-green-600">+{dept.newHires}</div>
                        <div className="text-gray-500">New Hires</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-red-600">-{dept.departures}</div>
                        <div className="text-gray-500">Departures</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* User Lifecycle Tab */}
        <TabsContent value="lifecycle" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Employee Lifecycle Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {userLifecycleData.map((stage, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        {stage.icon}
                        <div>
                          <div className="font-medium">{stage.stage}</div>
                          <div className="text-sm text-gray-600">
                            {stage.userCount} users ({stage.percentage}%)
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-center">
                        <div className="font-medium">{formatDuration(stage.avgDuration)}</div>
                        <div className="text-gray-500">Avg Duration</div>
                      </div>
                      <div className="text-center">
                        <div className={`font-medium ${stage.conversionRate >= 80 ? 'text-green-600' : stage.conversionRate >= 60 ? 'text-yellow-600' : 'text-red-600'}`}>
                          {stage.conversionRate}%
                        </div>
                        <div className="text-gray-500">Success Rate</div>
                      </div>
                      <div className="text-center">
                        <div className={`flex items-center space-x-1 ${getTrendColor(stage.trend)}`}>
                          {getTrendIcon(stage.trend)}
                          <span className="font-medium">
                            {stage.trend !== 'stable' && (stage.trendPercentage > 0 ? '+' : '')}{stage.trendPercentage}%
                          </span>
                        </div>
                        <div className="text-gray-500">Trend</div>
                      </div>
                      <div className="w-32">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${stage.percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Growth Trends Tab */}
        <TabsContent value="growth" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Growth Trend Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Growth Chart */}
                <div className="grid grid-cols-5 gap-4">
                  {growthTrends.map((trend, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-blue-100 rounded-lg p-3 mb-2">
                        <div className="h-20 flex items-end justify-center">
                          <div
                            className="bg-blue-600 rounded-t w-full"
                            style={{ height: `${(trend.totalUsers / 140) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="text-sm font-medium">{trend.month}</div>
                      <div className="text-xs text-gray-500">{trend.totalUsers} users</div>
                    </div>
                  ))}
                </div>

                {/* Growth Statistics */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Monthly Growth</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {growthTrends.slice(-3).map((trend, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span>{trend.month}</span>
                            <span className={`font-medium ${trend.netGrowth > 0 ? 'text-green-600' : trend.netGrowth < 0 ? 'text-red-600' : 'text-gray-600'}`}>
                              {trend.netGrowth > 0 ? '+' : ''}{trend.netGrowth}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Hiring Activity</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {growthTrends.slice(-3).map((trend, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span>{trend.month}</span>
                            <div className="flex space-x-2">
                              <span className="text-green-600">+{trend.newHires}</span>
                              <span className="text-red-600">-{trend.departures}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm">Retention Trends</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {growthTrends.slice(-3).map((trend, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span>{trend.month}</span>
                            <span className={`font-medium ${getRetentionColor(trend.retentionRate)}`}>
                              {trend.retentionRate}%
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Comparative Analysis Tab */}
        <TabsContent value="comparative" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Department Size Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {departmentData.map((dept) => (
                    <div key={dept.departmentId} className="flex items-center justify-between">
                      <span className="text-sm">{dept.departmentName}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${(dept.userCount / 45) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium w-8">{dept.userCount}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Performance Comparison
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {departmentData.map((dept) => (
                    <div key={dept.departmentId} className="flex items-center justify-between">
                      <span className="text-sm">{dept.departmentName}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${getProductivityColor(dept.productivityScore) === 'text-green-600' ? 'bg-green-600' : getProductivityColor(dept.productivityScore) === 'text-yellow-600' ? 'bg-yellow-600' : 'bg-red-600'}`}
                            style={{ width: `${dept.productivityScore}%` }}
                          ></div>
                        </div>
                        <span className={`text-sm font-medium w-8 ${getProductivityColor(dept.productivityScore)}`}>
                          {dept.productivityScore}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Department Rankings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium mb-3 text-green-600">🏆 Top Performers</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Engineering</span>
                      <span className="font-medium">95.6% retention</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>HR</span>
                      <span className="font-medium">94.4% retention</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Finance</span>
                      <span className="font-medium">93.3% retention</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3 text-blue-600">📈 Fastest Growing</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Engineering</span>
                      <span className="font-medium">+15.4% growth</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>HR</span>
                      <span className="font-medium">+12.5% growth</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Sales</span>
                      <span className="font-medium">+3.2% growth</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3 text-purple-600">⭐ Most Productive</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Engineering</span>
                      <span className="font-medium">92 score</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>HR</span>
                      <span className="font-medium">88 score</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Finance</span>
                      <span className="font-medium">85 score</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
