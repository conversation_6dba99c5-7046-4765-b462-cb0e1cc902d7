import {
  activateApplicant,
  createApplicant,
  createApplicantWithFiles,
  deleteApplicant,
  getApplicant,
  getListValuesById,
  listApplicants,
  patchApplicantWithFiles
} from '@/api/applicantsApi';
import { ApplicantFormData } from '@/components/applicants/ApplicantFormWizard';
import { ApplicantCard, ApplicantCardData } from '@/components/applicants/ui';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { WizardFallback } from '@/components/ui/LazyLoadFallback';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { API_CONSTANTS } from '@/constants/apiConstants';
import { getFormListIds } from '@/constants/listIds';
import { useToast } from '@/hooks/use-toast';
import { generateUUID } from '@/lib/utils';
import { Applicant } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { ApplicantDetailResponse, ApplicantListParams } from '@/types/applicants';
import {
  convertApiApplicantToLegacy,
  convertApplicantToCardData,
  convertFormDataToApiRequest,
  createFilesArray
} from '@/utils/applicants';
import {
  Filter,
  Loader2,
  Plus,
  Search
} from 'lucide-react';
import { Suspense, lazy, useCallback, useEffect, useState } from 'react';

// Lazy load wizard components for better performance
const ApplicantFormWizard = lazy(() => import('@/components/applicants/ApplicantFormWizard'));
const ApplicantViewWizard = lazy(() => import('@/components/applicants/ApplicantViewWizard'));

interface ApplicantUpdateData {
  id: string;
  [key: string]: unknown;
}



export default function Applicants() {
  const { toast } = useToast();
  const { listValues } = useAuth();
  const [applicants, setApplicants] = useState<Applicant[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedApplicant, setSelectedApplicant] = useState<Applicant | null>(null);
  const [selectedApiApplicant, setSelectedApiApplicant] = useState<ApplicantDetailResponse | null>(null);
  const [wizardOpen, setWizardOpen] = useState(false);
  const [wizardMode, setWizardMode] = useState<'add' | 'edit'>('add');
  const [viewWizardOpen, setViewWizardOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'compact'>('grid');

  // Pre-load list values for dropdowns when applicants page loads
  useEffect(() => {
    const preloadListValues = async () => {
      // Get commonly used list IDs for forms
      const listIds = getFormListIds();
      
      // Pre-load all list values in parallel for better performance
      const promises = listIds.map(async (listId) => {
        try {
          const result = await getListValuesById(listId, (error) => {
            // Silently handle individual failures - don't break the flow
            if (process.env.NODE_ENV === 'development') {
              console.warn(`Failed to preload list values for ${listId}:`, error.message);
            }
          });
          
          return { listId, success: true, count: result?.length || 0 };
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            console.error(`Error preloading list values for ${listId}:`, error);
          }
          return { listId, success: false, error };
        }
      });

      // Wait for all preloading attempts to complete
      const results = await Promise.allSettled(promises);
      
      // Log summary
      const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
      const failed = results.length - successful;
      
      if (failed > 0) {
        toast({
          title: "Dropdown Data",
          description: `Loaded dropdown options for ${successful} out of ${results.length} lists. Some options may not be available.`,
          variant: "default",
        });
      }
    };

    preloadListValues();
  }, [toast]);


  // Load applicants on component mount
  const fetchApplicants = useCallback(async () => {
    setLoading(true);
    
    const params: ApplicantListParams = {
      page: 1,
      pageSize: 100, // Load more for demo
      searchKeyWord: searchTerm || undefined,
      statusFilter: statusFilter !== 'all' ? statusFilter : undefined,
    };

    const result = await listApplicants(
      { query: params },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to load applicants",
          variant: "destructive",
        });
      }
    );

    if (result) {
      // Convert API response to legacy format
      // Handle both possible response structures (items vs applicants)
      const responseItems = (result as { items?: unknown[]; applicants?: unknown[] }).items || 
                           (result as { items?: unknown[]; applicants?: unknown[] }).applicants || 
                           [];
      const legacyApplicants = (responseItems as ApplicantDetailResponse[]).map(apiApplicant => 
        convertApiApplicantToLegacy(apiApplicant, listValues)
      );
      setApplicants(legacyApplicants);
      
    }
    
    setLoading(false);
  }, [searchTerm, statusFilter, toast]);

  useEffect(() => {
    fetchApplicants();
  }, [fetchApplicants]);

  const filteredApplicants = applicants.filter(applicant => {
    const matchesSearch =
      applicant.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      applicant.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      applicant.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      applicant.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || applicant.status === statusFilter;

    return matchesSearch && matchesStatus;
  });


  const handleAddApplicant = () => {
    setSelectedApplicant(null);
    setSelectedApiApplicant(null);
    setWizardMode('add');
    setWizardOpen(true);
  };

  const handleWizardClose = (open: boolean) => {
    setWizardOpen(open);
    if (!open) {
      setSelectedApplicant(null);
      setSelectedApiApplicant(null);
    }
  };

  const handleViewApplicant = async (applicantData: ApplicantCardData) => {
    setLoading(true);
    
    const result = await getApplicant(
      { pathParams: { applicantId: applicantData.id } },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to load applicant details",
          variant: "destructive",
        });
      }
    );

    if (result) {
      const legacyApplicant = convertApiApplicantToLegacy(result, listValues);
      setSelectedApplicant(legacyApplicant);
      setViewWizardOpen(true);
    }
    
    setLoading(false);
  };

  const handleEditApplicant = async (applicantData: ApplicantCardData) => {
    setLoading(true);

    const result = await getApplicant(
      { pathParams: { applicantId: applicantData.id } },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to load applicant details",
          variant: "destructive",
        });
      }
    );

    if (result) {
      // Store both API response and legacy format for compatibility
      setSelectedApiApplicant(result);
      const legacyApplicant = convertApiApplicantToLegacy(result, listValues);
      setSelectedApplicant(legacyApplicant);
      setWizardMode('edit');
      setWizardOpen(true);
    }

    setLoading(false);
  };

  const handleDeleteApplicant = async (applicantId: string) => {
    setLoading(true);
    
    const result = await deleteApplicant(
      { pathParams: { applicantId } },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to deactivate applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant deactivated successfully",
      });
      // Refresh the list
      fetchApplicants();
    }
    
    setLoading(false);
  };

  const handleActivateApplicant = async (applicantId: string) => {
    setLoading(true);
    
    const result = await activateApplicant(
      { pathParams: { applicantId } },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to activate applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant activated successfully",
      });
      // Refresh the list
      fetchApplicants();
    }
    
    setLoading(false);
  };

  const handleSaveApiApplicant = async (apiData: ApplicantUpdateData, formData?: ApplicantFormData) => {
    setLoading(true);

    // Remove system-managed fields that shouldn't be sent in update requests
    const cleanApiData = { ...apiData };
    delete (cleanApiData as Record<string, unknown>).isActive;
    delete (cleanApiData as Record<string, unknown>).createdBy;
    delete (cleanApiData as Record<string, unknown>).updatedBy;
    delete (cleanApiData as Record<string, unknown>).createdOn;
    delete (cleanApiData as Record<string, unknown>).updatedOn;
    delete (cleanApiData as Record<string, unknown>).assignedTo;

    // Fix addresses: convert array to single object (backend expects object, not array)
    const dataWithAddresses = cleanApiData as Record<string, unknown>;
    const addressesArray = dataWithAddresses.addresses;
    if (addressesArray && Array.isArray(addressesArray) && addressesArray.length > 0) {
      // Take the first address (primary address) and convert array to single object
      dataWithAddresses.addresses = addressesArray[0];
    }

    // Extract files from formData if available, fallback to apiData
    let filesToUpload: File[] | undefined = undefined;
    
    if (formData?.documents) {
      // Use formData documents which contain the actual File objects
      const files = createFilesArray(formData.documents);
      
      if (files.length > 0) {
        // Extract just the File objects for the API
        filesToUpload = files.map(f => f.file);
        
        // Update documents to reference files by fileIndex
        const fileIndices = files.map(f => f.fileIndex);
        const documentsWithoutFiles = formData.documents.filter(doc => 
          typeof doc.fileIndex === 'number' && !fileIndices.includes(doc.fileIndex)
        ).map(doc => ({
          ...doc,
          id: doc.id || generateUUID(),
          fileIndex: doc.fileIndex!,
        }));
        
        // Build documents array with fileIndex references
        const documentsWithFiles = files.map(file => ({
          id: generateUUID(),
          fileIndex: file.fileIndex,
          title: file.title,
          comments: file.comments || '',
          category: API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT,
          subcategory: file.type,
          isActive: true,
        }));
        
        // Update documents in cleanApiData to include file references
        dataWithAddresses.documents = [
          ...documentsWithoutFiles,
          ...documentsWithFiles
        ];
      }
    }

    const result = await patchApplicantWithFiles(
      {
        pathParams: { applicantId: apiData.id },
        body: {
          patchData: JSON.stringify(cleanApiData),
          files: filesToUpload
        }
      },
      (error) => {
        toast({
          title: "Error",
          description: error.message || "Failed to update applicant",
          variant: "destructive",
        });
      }
    );

    if (result?.success) {
      toast({
        title: "Success",
        description: "Applicant updated successfully",
      });
      fetchApplicants(); // Refresh the list
      setWizardOpen(false);
      setSelectedApplicant(null);
      setSelectedApiApplicant(null);
    }

    setLoading(false);
  };

  const handleSaveApplicant = async (formData: ApplicantFormData) => {
    setLoading(true);
    
    if (wizardMode === 'add') {
      const apiRequest = convertFormDataToApiRequest(formData);
      
      // Check if there are files to upload
      const files = createFilesArray(formData.documents);
      const hasFiles = files.length > 0;
      
      let result;
      
      if (hasFiles) {
        // Separate documents with and without files
        const { documents, ...applicantDataWithoutDocuments } = apiRequest;
        
        // Find document metadata that corresponds to files (by matching fileIndex)
        const fileIndices = files.map(f => f.fileIndex);
        const documentsWithoutFiles = documents?.filter(doc => 
          typeof doc.fileIndex === 'number' && !fileIndices.includes(doc.fileIndex)
        ).map(doc => ({
          ...doc,
          id: doc.id || generateUUID(), // Ensure id is always present
          fileIndex: doc.fileIndex!, // We already checked it's a number above
        })) || [];
        
        // Use multipart form data API when files are present
        result = await createApplicantWithFiles(
          {
            applicantData: {
              ...applicantDataWithoutDocuments,
              documents: documentsWithoutFiles // Only include documents that don't have files
            },
            files: files
          },
          (error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to create applicant with files",
              variant: "destructive",
            });
          }
        );
      } else {
        // Use JSON-only API when no files
        result = await createApplicant(
          { body: apiRequest },
          (error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to create applicant",
              variant: "destructive",
            });
          }
        );
      }

      if (result?.success) {
        toast({
          title: "Success",
          description: "Applicant created successfully",
        });
        fetchApplicants(); // Refresh the list
      }
    } else if (selectedApplicant) {
      const apiRequest = convertFormDataToApiRequest(formData, selectedApplicant.id);
      
      // Check if there are files to upload (same logic as POST)
      const files = createFilesArray(formData.documents);
      const hasFiles = files.length > 0;
      
      let result;
      
      if (hasFiles) {
        // Separate documents with and without files (same logic as POST)
        const { documents, ...applicantDataWithoutDocuments } = apiRequest;
        
        // Find document metadata that corresponds to files (by matching fileIndex)
        const fileIndices = files.map(f => f.fileIndex);
        const documentsWithoutFiles = documents?.filter(doc => 
          typeof doc.fileIndex === 'number' && !fileIndices.includes(doc.fileIndex)
        ).map(doc => ({
          ...doc,
          id: doc.id || generateUUID(), // Ensure id is always present
          fileIndex: doc.fileIndex!, // We already checked it's a number above
        })) || [];
        
        // Build documents array with fileIndex references and id (same logic as POST)
        const documentsWithFiles = files.map(file => ({
          id: generateUUID(), // Random UUID for document identification
          fileIndex: file.fileIndex,
          title: file.title,
          comments: file.comments || '',
          category: API_CONSTANTS.DOCUMENT_CATEGORIES.DEFAULT, // Fixed category ID
          subcategory: file.type, // Document type UUID from dropdown
          isActive: true, // Mark document as active
        }));
        
        // Combine applicant data with documents (same structure as POST)
        const finalApplicantData = {
          ...applicantDataWithoutDocuments,
          id: selectedApplicant.id, // Ensure applicant has existing id
          documents: [
            ...documentsWithoutFiles, // Documents without files
            ...documentsWithFiles     // Documents with files
          ]
        };
        
        // Use multipart form data API when files are present (same as POST)
        result = await patchApplicantWithFiles(
          {
            pathParams: { applicantId: selectedApplicant.id },
            body: {
              patchData: JSON.stringify(finalApplicantData),
              files: files.map(f => f.file)
            }
          },
          (error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to update applicant with files",
              variant: "destructive",
            });
          }
        );
      } else {
        // Use FormData API without files
        result = await patchApplicantWithFiles(
          { 
            pathParams: { applicantId: selectedApplicant.id },
            body: {
              patchData: JSON.stringify({ ...apiRequest, id: selectedApplicant.id }),
              files: undefined
            }
          },
          (error) => {
            toast({
              title: "Error",
              description: error.message || "Failed to update applicant",
              variant: "destructive",
            });
          }
        );
      }

      if (result?.success) {
        toast({
          title: "Success",
          description: "Applicant updated successfully",
        });
        fetchApplicants(); // Refresh the list
      }
    }
    
    setWizardOpen(false);
    setSelectedApplicant(null);
    setSelectedApiApplicant(null);
    setLoading(false);
  };



  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-xl font-bold text-gray-900">Applicants</h1>
          <p className="text-sm text-gray-600">Manage your applicant pipeline</p>
        </div>
        <div className="flex items-center gap-3">
          <Button onClick={handleAddApplicant} size="sm" disabled={loading}>
            {loading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <Plus className="mr-2 h-4 w-4" />
            )}
            Add Applicant
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-3">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search candidates..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-9"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48 h-9">
                <Filter className="mr-2 h-4 w-4" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="applied">Applied</SelectItem>
                <SelectItem value="screening">Screening</SelectItem>
                <SelectItem value="interview">Interview</SelectItem>
                <SelectItem value="offer">Offer</SelectItem>
                <SelectItem value="hired">Hired</SelectItem>
                <SelectItem value="onboarding">Onboarding</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Applicants Grid */}
      {loading && applicants.length === 0 ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="ml-2 text-gray-600">Loading applicants...</span>
        </div>
      ) : (
        <div className={
          viewMode === 'compact'
            ? "grid grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6"
            : "grid grid-cols-1 gap-3 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
        }>
          {filteredApplicants?.map((applicant) => (
            <ApplicantCard
              key={applicant.id}
              applicant={convertApplicantToCardData(applicant)}
              onView={handleViewApplicant}
              onEdit={handleEditApplicant}
              onDelete={handleDeleteApplicant}
              onActivate={handleActivateApplicant}
            />
          ))}
          {filteredApplicants.length === 0 && !loading && (
            <div className="col-span-full flex items-center justify-center py-8 text-gray-500">
              No applicants found. {searchTerm || statusFilter !== 'all' ? 'Try adjusting your search criteria.' : 'Add your first applicant to get started.'}
            </div>
          )}
        </div>
      )}

      {/* Applicant Form Wizard */}
      {/* Note: Form components can now use ApiSelectInput with preloaded values
          Example: <ApiSelectInput listNameId={LIST_IDS.WORK_AUTHORIZATION} ... />
          The dropdown options will be instantly available due to preloading */}
      <Suspense fallback={<WizardFallback />}>
        <ApplicantFormWizard
          open={wizardOpen}
          onOpenChange={handleWizardClose}
          applicant={selectedApplicant}
          apiApplicant={selectedApiApplicant}
          onSave={handleSaveApplicant}
          onSaveApi={handleSaveApiApplicant}
          mode={wizardMode}
        />
      </Suspense>

      {/* Applicant View Wizard */}
      <Suspense fallback={<WizardFallback />}>
        <ApplicantViewWizard
          open={viewWizardOpen}
          onOpenChange={setViewWizardOpen}
          applicant={selectedApplicant}
        />
      </Suspense>
    </div>
  );
}