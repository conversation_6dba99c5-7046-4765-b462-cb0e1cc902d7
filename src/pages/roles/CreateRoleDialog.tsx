import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { RoleTemplate, Permission } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Shield,
  Users,
  Briefcase,
  Calendar,
  BarChart3,
  Settings,
  UserCheck,
  Building,
  FileText,
  MessageSquare,
  Loader2,
  Plus,
  X,
  Info,
  AlertTriangle
} from 'lucide-react';

interface CreateRoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRoleCreated: (role: RoleTemplate) => void;
  editingRole?: RoleTemplate | null;
}

interface RoleForm {
  name: string;
  description: string;
  organizationType: 'client' | 'vendor' | 'both';
  isActive: boolean;
  permissions: Permission[];
}

const AVAILABLE_MODULES = [
  { id: 'applicants', name: 'Applicants', icon: Users, description: 'Manage candidate applications and profiles' },
  { id: 'jobs', name: 'Jobs', icon: Briefcase, description: 'Create and manage job postings' },
  { id: 'interviews', name: 'Interviews', icon: Calendar, description: 'Schedule and conduct interviews' },
  { id: 'analytics', name: 'Analytics', icon: BarChart3, description: 'View reports and analytics' },
  { id: 'settings', name: 'Settings', icon: Settings, description: 'Configure system settings' },
  { id: 'users', name: 'Users', icon: UserCheck, description: 'Manage user accounts and permissions' },
  { id: 'vendors', name: 'Vendors', icon: Building, description: 'Manage vendor relationships' },
  { id: 'form_builder', name: 'Form Builder', icon: FileText, description: 'Create and manage forms' },
  { id: 'communications', name: 'Communications', icon: MessageSquare, description: 'Send messages and notifications' },
  { id: 'onboarding', name: 'Onboarding', icon: UserCheck, description: 'Manage employee onboarding' },
] as const;

const AVAILABLE_ACTIONS = [
  { id: 'create', name: 'Create', description: 'Create new records' },
  { id: 'read', name: 'View', description: 'View and read records' },
  { id: 'update', name: 'Edit', description: 'Modify existing records' },
  { id: 'delete', name: 'Delete', description: 'Remove records' },
  { id: 'approve', name: 'Approve', description: 'Approve or reject items' },
  { id: 'invite', name: 'Invite', description: 'Send invitations' },
  { id: 'manage_roles', name: 'Manage Roles', description: 'Assign and modify user roles' },
] as const;

export default function CreateRoleDialog({ 
  open, 
  onOpenChange, 
  onRoleCreated, 
  editingRole 
}: CreateRoleDialogProps) {
  const { organization } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<RoleForm>({
    name: '',
    description: '',
    organizationType: 'both',
    isActive: true,
    permissions: [],
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // Initialize form data when editing
  useEffect(() => {
    if (editingRole) {
      setFormData({
        name: editingRole.name,
        description: editingRole.description,
        organizationType: editingRole.organizationType,
        isActive: editingRole.isActive,
        permissions: [...editingRole.permissions],
      });
    } else {
      // Reset form for new role
      setFormData({
        name: '',
        description: '',
        organizationType: 'both',
        isActive: true,
        permissions: [],
      });
    }
    setErrors({});
  }, [editingRole, open]);

  const handleInputChange = (field: keyof RoleForm, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handlePermissionChange = (moduleId: string, actionId: string, checked: boolean) => {
    setFormData(prev => {
      const newPermissions = [...prev.permissions];
      const moduleIndex = newPermissions.findIndex(p => p.module === moduleId);

      if (moduleIndex >= 0) {
        // Module exists, update actions
        const currentActions = newPermissions[moduleIndex].actions;
        if (checked) {
          // Add action if not present
          if (!currentActions.includes(actionId as Permission['actions'][number])) {
            newPermissions[moduleIndex].actions = [...currentActions, actionId as Permission['actions'][number]];
          }
        } else {
          // Remove action
          newPermissions[moduleIndex].actions = currentActions.filter(a => a !== actionId);
          // Remove module if no actions left
          if (newPermissions[moduleIndex].actions.length === 0) {
            newPermissions.splice(moduleIndex, 1);
          }
        }
      } else if (checked) {
        // Module doesn't exist, create it with this action
        newPermissions.push({
          module: moduleId as Permission['module'],
          actions: [actionId as Permission['actions'][number]],
        });
      }

      return { ...prev, permissions: newPermissions };
    });
  };

  const isActionChecked = (moduleId: string, actionId: string): boolean => {
    const permission = formData.permissions.find(p => p.module === moduleId);
    return permission ? permission.actions.includes(actionId as Permission['actions'][number]) : false;
  };

  const getModulePermissionCount = (moduleId: string): number => {
    const permission = formData.permissions.find(p => p.module === moduleId);
    return permission ? permission.actions.length : 0;
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Role name is required';
    } else if (formData.name.length < 2) {
      newErrors.name = 'Role name must be at least 2 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Role description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (formData.permissions.length === 0) {
      newErrors.permissions = 'At least one permission must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !organization) return;

    try {
      setIsLoading(true);

      const roleData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        permissions: formData.permissions,
        organizationType: formData.organizationType,
        isCustom: true,
        isActive: formData.isActive,
        createdBy: 'current-user', // In real app, this would come from auth context
      };

      let result: RoleTemplate;
      if (editingRole) {
        result = await UserManagementAPI.updateRoleTemplate(editingRole.id, roleData);
        toast({
          title: "Role Updated",
          description: `Role "${formData.name}" has been updated successfully.`,
        });
      } else {
        result = await UserManagementAPI.createRoleTemplate(organization.id, roleData);
        toast({
          title: "Role Created",
          description: `Role "${formData.name}" has been created successfully.`,
        });
      }

      onRoleCreated(result);
      onOpenChange(false);
    } catch (error) {
      console.error('Failed to save role:', error);
      toast({
        title: "Error",
        description: editingRole ? "Failed to update role." : "Failed to create role.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectAllActions = (moduleId: string, checked: boolean) => {
    AVAILABLE_ACTIONS.forEach(action => {
      handlePermissionChange(moduleId, action.id, checked);
    });
  };

  const isModuleFullySelected = (moduleId: string): boolean => {
    const permission = formData.permissions.find(p => p.module === moduleId);
    return permission ? permission.actions.length === AVAILABLE_ACTIONS.length : false;
  };

  const isModulePartiallySelected = (moduleId: string): boolean => {
    const count = getModulePermissionCount(moduleId);
    return count > 0 && count < AVAILABLE_ACTIONS.length;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {editingRole ? 'Edit Role' : 'Create Custom Role'}
          </DialogTitle>
          <DialogDescription>
            {editingRole 
              ? 'Modify the role details and permissions below.'
              : 'Create a custom role with specific permissions for your organization.'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Role Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., HR Manager, Senior Recruiter"
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-600 flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {errors.name}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="organizationType">Organization Type</Label>
              <Select
                value={formData.organizationType}
                onValueChange={(value) => handleInputChange('organizationType', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="both">Both Client & Vendor</SelectItem>
                  <SelectItem value="client">Client Only</SelectItem>
                  <SelectItem value="vendor">Vendor Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the role's responsibilities and purpose..."
              rows={3}
              className={errors.description ? 'border-red-500' : ''}
            />
            {errors.description && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {errors.description}
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) => handleInputChange('isActive', checked)}
            />
            <Label htmlFor="isActive">Active Role</Label>
            <Info className="h-4 w-4 text-gray-400" />
          </div>

          <Separator />

          {/* Permissions Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium">Permissions</h3>
                <p className="text-sm text-gray-600">
                  Select the modules and actions this role can access
                </p>
              </div>
              <Badge variant="outline">
                {formData.permissions.length} modules selected
              </Badge>
            </div>

            {errors.permissions && (
              <p className="text-sm text-red-600 flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {errors.permissions}
              </p>
            )}

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {AVAILABLE_MODULES.map((module) => {
                const Icon = module.icon;
                const permissionCount = getModulePermissionCount(module.id);
                const isFullySelected = isModuleFullySelected(module.id);
                const isPartiallySelected = isModulePartiallySelected(module.id);

                return (
                  <Card key={module.id} className={`${permissionCount > 0 ? 'border-blue-200 bg-blue-50' : ''}`}>
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center justify-between text-sm">
                        <div className="flex items-center gap-2">
                          <Icon className="h-4 w-4" />
                          <span>{module.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          {permissionCount > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {permissionCount}/{AVAILABLE_ACTIONS.length}
                            </Badge>
                          )}
                          <Checkbox
                            checked={isFullySelected}
                            ref={(el) => {
                              if (el) el.indeterminate = isPartiallySelected;
                            }}
                            onCheckedChange={(checked) => 
                              handleSelectAllActions(module.id, checked as boolean)
                            }
                          />
                        </div>
                      </CardTitle>
                      <p className="text-xs text-gray-600">{module.description}</p>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="grid grid-cols-2 gap-2">
                        {AVAILABLE_ACTIONS.map((action) => (
                          <div key={action.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`${module.id}-${action.id}`}
                              checked={isActionChecked(module.id, action.id)}
                              onCheckedChange={(checked) => 
                                handlePermissionChange(module.id, action.id, checked as boolean)
                              }
                            />
                            <Label 
                              htmlFor={`${module.id}-${action.id}`}
                              className="text-xs font-normal cursor-pointer"
                              title={action.description}
                            >
                              {action.name}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editingRole ? 'Update Role' : 'Create Role'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
