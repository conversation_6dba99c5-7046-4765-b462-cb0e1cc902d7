import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { RoleTemplate } from '@/types/multiTenant';
import { 
  Shield,
  Users,
  Briefcase,
  Calendar,
  BarChart3,
  Settings,
  UserCheck,
  Building,
  FileText,
  MessageSquare,
  Edit,
  Copy,
  Trash2,
  Clock,
  User,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface RoleDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role: RoleTemplate | null;
  onEdit?: (role: RoleTemplate) => void;
  onDuplicate?: (role: RoleTemplate) => void;
  onDelete?: (role: RoleTemplate) => void;
}

const getModuleIcon = (module: string) => {
  switch (module) {
    case 'applicants': return <Users className="w-4 h-4" />;
    case 'jobs': return <Briefcase className="w-4 h-4" />;
    case 'interviews': return <Calendar className="w-4 h-4" />;
    case 'analytics': return <BarChart3 className="w-4 h-4" />;
    case 'settings': return <Settings className="w-4 h-4" />;
    case 'users': return <UserCheck className="w-4 h-4" />;
    case 'vendors': return <Building className="w-4 h-4" />;
    case 'form_builder': return <FileText className="w-4 h-4" />;
    case 'communications': return <MessageSquare className="w-4 h-4" />;
    case 'onboarding': return <UserCheck className="w-4 h-4" />;
    default: return <Shield className="w-4 h-4" />;
  }
};

const getModuleName = (module: string) => {
  switch (module) {
    case 'applicants': return 'Applicants';
    case 'jobs': return 'Jobs';
    case 'interviews': return 'Interviews';
    case 'analytics': return 'Analytics';
    case 'settings': return 'Settings';
    case 'users': return 'Users';
    case 'vendors': return 'Vendors';
    case 'form_builder': return 'Form Builder';
    case 'communications': return 'Communications';
    case 'onboarding': return 'Onboarding';
    default: return module;
  }
};

const getActionName = (action: string) => {
  switch (action) {
    case 'create': return 'Create';
    case 'read': return 'View';
    case 'update': return 'Edit';
    case 'delete': return 'Delete';
    case 'approve': return 'Approve';
    case 'invite': return 'Invite';
    case 'manage_roles': return 'Manage Roles';
    default: return action;
  }
};

const getActionColor = (action: string) => {
  switch (action) {
    case 'create': return 'bg-green-100 text-green-700';
    case 'read': return 'bg-blue-100 text-blue-700';
    case 'update': return 'bg-yellow-100 text-yellow-700';
    case 'delete': return 'bg-red-100 text-red-700';
    case 'approve': return 'bg-purple-100 text-purple-700';
    case 'invite': return 'bg-indigo-100 text-indigo-700';
    case 'manage_roles': return 'bg-orange-100 text-orange-700';
    default: return 'bg-gray-100 text-gray-700';
  }
};

export default function RoleDetailsDialog({ 
  open, 
  onOpenChange, 
  role, 
  onEdit, 
  onDuplicate, 
  onDelete 
}: RoleDetailsDialogProps) {
  if (!role) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {role.name}
            <Badge className={role.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-600'}>
              {role.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <Badge className={role.isCustom ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}>
              {role.isCustom ? 'Custom' : 'System'}
            </Badge>
          </DialogTitle>
          <DialogDescription>
            {role.description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Role Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <Building className="h-5 w-5 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Organization Type</p>
                    <p className="text-lg font-semibold capitalize">{role.organizationType}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-600">Permissions</p>
                    <p className="text-lg font-semibold">{role.permissions.length} modules</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center space-x-2">
                  {role.isActive ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-500" />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-600">Status</p>
                    <p className="text-lg font-semibold">{role.isActive ? 'Active' : 'Inactive'}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Separator />

          {/* Permissions Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Permissions & Access Control
            </h3>
            
            {role.permissions.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {role.permissions.map((permission, index) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardHeader className="pb-3">
                      <CardTitle className="flex items-center gap-2 text-base">
                        {getModuleIcon(permission.module)}
                        {getModuleName(permission.module)}
                        <Badge variant="outline" className="ml-auto">
                          {permission.actions.length} actions
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex flex-wrap gap-2">
                        {permission.actions.map((action, actionIndex) => (
                          <Badge 
                            key={actionIndex} 
                            className={`${getActionColor(action)} border-0`}
                          >
                            {getActionName(action)}
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="pt-6">
                  <div className="text-center py-8">
                    <Shield className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No Permissions</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      This role has no permissions assigned.
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <Separator />

          {/* Metadata */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Role Information
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">Created By</p>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{role.createdBy}</span>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">Created At</p>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{formatDate(role.createdAt)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">Last Updated</p>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{formatDate(role.updatedAt)}</span>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-600">Role Type</p>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{role.isCustom ? 'Custom Role' : 'System Role'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center gap-2">
              {onEdit && (
                <Button
                  variant="outline"
                  onClick={() => {
                    onEdit(role);
                    onOpenChange(false);
                  }}
                  className="flex items-center gap-2"
                >
                  <Edit className="h-4 w-4" />
                  Edit Role
                </Button>
              )}
              
              {onDuplicate && (
                <Button
                  variant="outline"
                  onClick={() => {
                    onDuplicate(role);
                    onOpenChange(false);
                  }}
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  Duplicate
                </Button>
              )}
            </div>

            <div className="flex items-center gap-2">
              {onDelete && role.isCustom && (
                <Button
                  variant="destructive"
                  onClick={() => {
                    onDelete(role);
                    onOpenChange(false);
                  }}
                  className="flex items-center gap-2"
                >
                  <Trash2 className="h-4 w-4" />
                  Delete Role
                </Button>
              )}
              
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
