import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { RoleTemplate } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Shield,
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  Copy,
  Download,
  Upload,
  Star,
  StarOff,
  History,
  Share,
  Building2,
  Users,
  Briefcase,
  GraduationCap,
  Stethoscope,
  Gavel,
  Truck,
  ShoppingCart,
  Loader2,
  CheckCircle,
  Clock,
  AlertTriangle
} from 'lucide-react';

interface RoleTemplatesManagerProps {
  roles: RoleTemplate[];
  onRolesUpdate: () => void;
  onCreateRole: () => void;
  onEditRole: (role: RoleTemplate) => void;
  onViewRole: (role: RoleTemplate) => void;
}

interface IndustryTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  category: string;
  templates: RoleTemplate[];
  isPopular: boolean;
}

const INDUSTRY_TEMPLATES: IndustryTemplate[] = [
  {
    id: 'technology',
    name: 'Technology',
    description: 'Software development, IT services, and tech startups',
    icon: Building2,
    category: 'Tech',
    templates: [],
    isPopular: true,
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    description: 'Hospitals, clinics, and medical services',
    icon: Stethoscope,
    category: 'Healthcare',
    templates: [],
    isPopular: true,
  },
  {
    id: 'finance',
    name: 'Finance',
    description: 'Banking, insurance, and financial services',
    icon: Building2,
    category: 'Finance',
    templates: [],
    isPopular: false,
  },
  {
    id: 'education',
    name: 'Education',
    description: 'Schools, universities, and training organizations',
    icon: GraduationCap,
    category: 'Education',
    templates: [],
    isPopular: false,
  },
  {
    id: 'legal',
    name: 'Legal',
    description: 'Law firms and legal services',
    icon: Gavel,
    category: 'Legal',
    templates: [],
    isPopular: false,
  },
  {
    id: 'logistics',
    name: 'Logistics',
    description: 'Transportation and supply chain management',
    icon: Truck,
    category: 'Logistics',
    templates: [],
    isPopular: false,
  },
  {
    id: 'retail',
    name: 'Retail',
    description: 'E-commerce and retail businesses',
    icon: ShoppingCart,
    category: 'Retail',
    templates: [],
    isPopular: true,
  },
];

export default function RoleTemplatesManager({ 
  roles, 
  onRolesUpdate, 
  onCreateRole, 
  onEditRole, 
  onViewRole 
}: RoleTemplatesManagerProps) {
  const { organization } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedIndustry, setSelectedIndustry] = useState<string>('all');
  const [templateTypeFilter, setTemplateTypeFilter] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [favoriteTemplates, setFavoriteTemplates] = useState<string[]>([]);

  // Load favorite templates from localStorage
  useEffect(() => {
    const saved = localStorage.getItem(`favorite-templates-${organization?.id}`);
    if (saved) {
      setFavoriteTemplates(JSON.parse(saved));
    }
  }, [organization?.id]);

  // Save favorite templates to localStorage
  const saveFavorites = (favorites: string[]) => {
    setFavoriteTemplates(favorites);
    localStorage.setItem(`favorite-templates-${organization?.id}`, JSON.stringify(favorites));
  };

  // Filter roles based on search and filters
  const filteredRoles = roles.filter(role => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = templateTypeFilter === 'all' || 
                       (templateTypeFilter === 'custom' && role.isCustom) ||
                       (templateTypeFilter === 'system' && !role.isCustom) ||
                       (templateTypeFilter === 'favorites' && favoriteTemplates.includes(role.id));
    return matchesSearch && matchesType;
  });

  // Toggle favorite status
  const toggleFavorite = (roleId: string) => {
    const newFavorites = favoriteTemplates.includes(roleId)
      ? favoriteTemplates.filter(id => id !== roleId)
      : [...favoriteTemplates, roleId];
    saveFavorites(newFavorites);
    toast({
      title: favoriteTemplates.includes(roleId) ? "Removed from Favorites" : "Added to Favorites",
      description: "Template favorite status updated.",
    });
  };

  // Duplicate role template
  const handleDuplicateRole = async (role: RoleTemplate) => {
    if (!organization) return;

    try {
      setIsLoading(true);
      const duplicatedRole = await UserManagementAPI.createRoleTemplate(organization.id, {
        name: `${role.name} (Copy)`,
        description: `Copy of ${role.description}`,
        permissions: [...role.permissions],
        organizationType: role.organizationType,
        isCustom: true,
        isActive: true,
        createdBy: 'current-user',
      });

      toast({
        title: "Template Duplicated",
        description: `"${duplicatedRole.name}" has been created successfully.`,
      });

      onRolesUpdate();
    } catch (error) {
      console.error('Failed to duplicate role:', error);
      toast({
        title: "Error",
        description: "Failed to duplicate template. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Export role template
  const handleExportTemplate = (role: RoleTemplate) => {
    const exportData = {
      name: role.name,
      description: role.description,
      permissions: role.permissions,
      organizationType: role.organizationType,
      exportedAt: new Date().toISOString(),
      exportedBy: 'current-user',
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${role.name.replace(/\s+/g, '_')}_template.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Template Exported",
      description: `"${role.name}" template has been downloaded.`,
    });
  };

  // Share role template
  const handleShareTemplate = (role: RoleTemplate) => {
    const shareData = {
      title: `Role Template: ${role.name}`,
      text: `Check out this role template: ${role.description}`,
      url: window.location.href,
    };

    if (navigator.share) {
      navigator.share(shareData);
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
      toast({
        title: "Link Copied",
        description: "Template sharing link copied to clipboard.",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Role Templates Manager
          </h2>
          <p className="text-gray-600">
            Manage, organize, and share role templates with advanced features
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm">
            <Upload className="h-4 w-4 mr-1" />
            Import
          </Button>
          <Button onClick={onCreateRole} size="sm">
            <Plus className="h-4 w-4 mr-1" />
            Create Template
          </Button>
        </div>
      </div>

      <Tabs defaultValue="templates" className="space-y-4">
        <TabsList>
          <TabsTrigger value="templates">My Templates</TabsTrigger>
          <TabsTrigger value="industry">Industry Templates</TabsTrigger>
          <TabsTrigger value="shared">Shared Templates</TabsTrigger>
        </TabsList>

        {/* My Templates Tab */}
        <TabsContent value="templates" className="space-y-4">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Filter className="h-5 w-5" />
                Filters & Search
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">Search Templates</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="Search by name or description..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="industry-filter">Industry</Label>
                  <Select value={selectedIndustry} onValueChange={setSelectedIndustry}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Industries</SelectItem>
                      {INDUSTRY_TEMPLATES.map((industry) => (
                        <SelectItem key={industry.id} value={industry.id}>
                          {industry.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type-filter">Template Type</Label>
                  <Select value={templateTypeFilter} onValueChange={setTemplateTypeFilter}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Templates</SelectItem>
                      <SelectItem value="custom">Custom Templates</SelectItem>
                      <SelectItem value="system">System Templates</SelectItem>
                      <SelectItem value="favorites">Favorites</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Templates Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Role Templates ({filteredRoles.length})</span>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Star className="h-4 w-4 text-yellow-500" />
                  <span>{favoriteTemplates.length} favorites</span>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredRoles.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead></TableHead>
                      <TableHead>Template Name</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Permissions</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRoles.map((role) => (
                      <TableRow key={role.id}>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleFavorite(role.id)}
                            className="p-1"
                          >
                            {favoriteTemplates.includes(role.id) ? (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            ) : (
                              <StarOff className="h-4 w-4 text-gray-400" />
                            )}
                          </Button>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">{role.name}</div>
                            <div className="text-sm text-gray-600 truncate max-w-xs">
                              {role.description}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <Badge className={role.isCustom ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}>
                              {role.isCustom ? 'Custom' : 'System'}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {role.organizationType}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {role.permissions.length} modules
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {role.isActive ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Clock className="h-4 w-4 text-gray-400" />
                            )}
                            <span className="text-sm">
                              {role.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell className="text-sm text-gray-600">
                          {formatDate(role.createdAt)}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => onViewRole(role)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              {role.isCustom && (
                                <DropdownMenuItem onClick={() => onEditRole(role)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit Template
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem onClick={() => handleDuplicateRole(role)} disabled={isLoading}>
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleExportTemplate(role)}>
                                <Download className="mr-2 h-4 w-4" />
                                Export
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleShareTemplate(role)}>
                                <Share className="mr-2 h-4 w-4" />
                                Share
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <History className="mr-2 h-4 w-4" />
                                Version History
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="text-center py-8">
                  <Shield className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No Templates Found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Try adjusting your search criteria or create a new template.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Industry Templates Tab */}
        <TabsContent value="industry" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {INDUSTRY_TEMPLATES.map((industry) => {
              const Icon = industry.icon;
              return (
                <Card key={industry.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      {industry.name}
                      {industry.isPopular && (
                        <Badge className="bg-yellow-100 text-yellow-700">Popular</Badge>
                      )}
                    </CardTitle>
                    <p className="text-sm text-gray-600">{industry.description}</p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Available Templates</span>
                        <Badge variant="outline">Coming Soon</Badge>
                      </div>
                      <Button variant="outline" className="w-full" disabled>
                        <Plus className="h-4 w-4 mr-1" />
                        Browse Templates
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Shared Templates Tab */}
        <TabsContent value="shared" className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <Share className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">Shared Templates</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Template sharing functionality will be available in the next update.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
