import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '@/contexts/AuthContext';
import { RoleTemplate, User } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { mockUsers } from '@/data/multiTenantData';
import { toast } from '@/hooks/use-toast';
import { 
  Users,
  Shield,
  Search,
  Filter,
  UserCheck,
  UserX,
  AlertTriangle,
  CheckCircle,
  Clock,
  Loader2,
  ArrowRight,
  X,
  Plus,
  Minus
} from 'lucide-react';

interface BulkRoleAssignmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  roles: RoleTemplate[];
  onAssignmentComplete: () => void;
}

interface UserSelection {
  user: User;
  currentRole: string;
  newRole: string;
  isSelected: boolean;
  hasConflict: boolean;
  conflictReason?: string;
}

interface AssignmentSummary {
  totalUsers: number;
  usersToUpdate: number;
  usersWithConflicts: number;
  roleChanges: { [roleId: string]: number };
}

export default function BulkRoleAssignmentDialog({ 
  open, 
  onOpenChange, 
  roles, 
  onAssignmentComplete 
}: BulkRoleAssignmentDialogProps) {
  const { organization } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [userSelections, setUserSelections] = useState<UserSelection[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [step, setStep] = useState<'select' | 'assign' | 'confirm'>('select');

  // Load users when dialog opens
  useEffect(() => {
    if (open && organization) {
      loadUsers();
    }
  }, [open, organization]);

  // Initialize user selections when users are loaded
  useEffect(() => {
    if (users.length > 0) {
      const selections: UserSelection[] = users.map(user => ({
        user,
        currentRole: user.role,
        newRole: user.role,
        isSelected: false,
        hasConflict: false,
      }));
      setUserSelections(selections);
    }
  }, [users]);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      // In a real app, this would fetch from API
      const orgUsers = mockUsers.filter(user => user.organizationId === organization?.id);
      setUsers(orgUsers);
    } catch (error) {
      console.error('Failed to load users:', error);
      toast({
        title: "Error",
        description: "Failed to load users. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Filter users based on search and role filter
  const filteredSelections = userSelections.filter(selection => {
    const matchesSearch = 
      selection.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      selection.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      selection.user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesRole = roleFilter === 'all' || selection.user.role === roleFilter;
    
    return matchesSearch && matchesRole;
  });

  // Toggle user selection
  const toggleUserSelection = (userId: string) => {
    setUserSelections(prev => prev.map(selection => 
      selection.user.id === userId 
        ? { ...selection, isSelected: !selection.isSelected }
        : selection
    ));
  };

  // Select all filtered users
  const selectAllUsers = () => {
    const filteredUserIds = filteredSelections.map(s => s.user.id);
    setUserSelections(prev => prev.map(selection => 
      filteredUserIds.includes(selection.user.id)
        ? { ...selection, isSelected: true }
        : selection
    ));
  };

  // Deselect all users
  const deselectAllUsers = () => {
    setUserSelections(prev => prev.map(selection => ({ 
      ...selection, 
      isSelected: false 
    })));
  };

  // Assign role to selected users
  const assignRoleToSelected = () => {
    if (!selectedRole) {
      toast({
        title: "No Role Selected",
        description: "Please select a role to assign to the selected users.",
        variant: "destructive",
      });
      return;
    }

    setUserSelections(prev => prev.map(selection => {
      if (selection.isSelected) {
        const hasConflict = checkRoleConflict(selection.user, selectedRole);
        return {
          ...selection,
          newRole: selectedRole,
          hasConflict: hasConflict.hasConflict,
          conflictReason: hasConflict.reason,
        };
      }
      return selection;
    }));

    setStep('confirm');
  };

  // Check for role assignment conflicts
  const checkRoleConflict = (user: User, newRole: string): { hasConflict: boolean; reason?: string } => {
    // Example conflict checks
    if (user.role === newRole) {
      return { hasConflict: true, reason: 'User already has this role' };
    }

    if (newRole === 'admin' && user.role === 'super_admin') {
      return { hasConflict: true, reason: 'Cannot downgrade from super admin to admin' };
    }

    if (newRole === 'employee' && ['admin', 'super_admin'].includes(user.role)) {
      return { hasConflict: true, reason: 'Cannot assign employee role to admin users' };
    }

    return { hasConflict: false };
  };

  // Calculate assignment summary
  const getAssignmentSummary = (): AssignmentSummary => {
    const selectedUsers = userSelections.filter(s => s.isSelected);
    const usersToUpdate = selectedUsers.filter(s => s.currentRole !== s.newRole && !s.hasConflict);
    const usersWithConflicts = selectedUsers.filter(s => s.hasConflict);
    
    const roleChanges: { [roleId: string]: number } = {};
    usersToUpdate.forEach(selection => {
      roleChanges[selection.newRole] = (roleChanges[selection.newRole] || 0) + 1;
    });

    return {
      totalUsers: selectedUsers.length,
      usersToUpdate: usersToUpdate.length,
      usersWithConflicts: usersWithConflicts.length,
      roleChanges,
    };
  };

  // Execute bulk role assignment
  const executeBulkAssignment = async () => {
    const usersToUpdate = userSelections.filter(s => 
      s.isSelected && s.currentRole !== s.newRole && !s.hasConflict
    );

    if (usersToUpdate.length === 0) {
      toast({
        title: "No Changes",
        description: "No valid role changes to apply.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // In a real app, this would be a batch API call
      const updatePromises = usersToUpdate.map(selection => 
        UserManagementAPI.assignRoleToUsers([selection.user.id], selection.newRole)
      );

      await Promise.all(updatePromises);

      toast({
        title: "Roles Updated",
        description: `Successfully updated roles for ${usersToUpdate.length} users.`,
      });

      onAssignmentComplete();
      onOpenChange(false);
      resetDialog();
    } catch (error) {
      console.error('Failed to update roles:', error);
      toast({
        title: "Error",
        description: "Failed to update user roles. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Reset dialog state
  const resetDialog = () => {
    setStep('select');
    setSearchTerm('');
    setRoleFilter('all');
    setSelectedRole('');
    setUserSelections([]);
  };

  // Get role name
  const getRoleName = (roleId: string) => {
    const role = roles.find(r => r.name.toLowerCase().replace(/\s+/g, '_') === roleId);
    return role?.name || roleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const selectedCount = userSelections.filter(s => s.isSelected).length;
  const summary = getAssignmentSummary();

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Bulk Role Assignment
            {step === 'confirm' && (
              <Badge variant="outline" className="ml-2">
                Step 2: Confirm Changes
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {step === 'select' 
              ? 'Select users and assign roles in bulk to streamline user management.'
              : 'Review and confirm the role assignments before applying changes.'
            }
          </DialogDescription>
        </DialogHeader>

        {step === 'select' && (
          <div className="space-y-6">
            {/* Filters and Search */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  User Selection
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="search">Search Users</Label>
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="search"
                        placeholder="Search by name or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role-filter">Filter by Current Role</Label>
                    <Select value={roleFilter} onValueChange={setRoleFilter}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Roles</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                        <SelectItem value="recruiter">Recruiter</SelectItem>
                        <SelectItem value="hiring_manager">Hiring Manager</SelectItem>
                        <SelectItem value="interviewer">Interviewer</SelectItem>
                        <SelectItem value="employee">Employee</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="new-role">Assign Role</Label>
                    <Select value={selectedRole} onValueChange={setSelectedRole}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role to assign" />
                      </SelectTrigger>
                      <SelectContent>
                        {roles.filter(role => role.isActive).map((role) => (
                          <SelectItem key={role.id} value={role.name.toLowerCase().replace(/\s+/g, '_')}>
                            {role.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" onClick={selectAllUsers}>
                      <Plus className="h-4 w-4 mr-1" />
                      Select All ({filteredSelections.length})
                    </Button>
                    <Button variant="outline" size="sm" onClick={deselectAllUsers}>
                      <Minus className="h-4 w-4 mr-1" />
                      Deselect All
                    </Button>
                  </div>
                  <Badge variant="outline">
                    {selectedCount} users selected
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Users Table */}
            <Card>
              <CardHeader>
                <CardTitle>Users ({filteredSelections.length})</CardTitle>
              </CardHeader>
              <CardContent>
                {filteredSelections.length > 0 ? (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12"></TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Current Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Last Active</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredSelections.map((selection) => (
                        <TableRow key={selection.user.id}>
                          <TableCell>
                            <Checkbox
                              checked={selection.isSelected}
                              onCheckedChange={() => toggleUserSelection(selection.user.id)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className="space-y-1">
                              <div className="font-medium">
                                {selection.user.firstName} {selection.user.lastName}
                              </div>
                              <div className="text-sm text-gray-600">
                                {selection.user.email}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {getRoleName(selection.user.role)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-1">
                              {selection.user.isActive ? (
                                <CheckCircle className="h-4 w-4 text-green-500" />
                              ) : (
                                <Clock className="h-4 w-4 text-gray-400" />
                              )}
                              <span className="text-sm">
                                {selection.user.isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell className="text-sm text-gray-600">
                            {new Date(selection.user.lastLoginAt || selection.user.createdAt).toLocaleDateString()}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                ) : (
                  <div className="text-center py-8">
                    <Users className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No Users Found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Try adjusting your search criteria or filters.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {step === 'confirm' && (
          <div className="space-y-6">
            {/* Assignment Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Assignment Summary
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{summary.totalUsers}</div>
                    <div className="text-sm text-gray-600">Total Selected</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{summary.usersToUpdate}</div>
                    <div className="text-sm text-gray-600">Will Update</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{summary.usersWithConflicts}</div>
                    <div className="text-sm text-gray-600">Conflicts</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">{Object.keys(summary.roleChanges).length}</div>
                    <div className="text-sm text-gray-600">Roles Affected</div>
                  </div>
                </div>

                {Object.keys(summary.roleChanges).length > 0 && (
                  <div className="mt-4">
                    <h4 className="font-medium mb-2">Role Changes:</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(summary.roleChanges).map(([roleId, count]) => (
                        <Badge key={roleId} variant="outline">
                          {getRoleName(roleId)}: +{count}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Changes Preview */}
            <Card>
              <CardHeader>
                <CardTitle>Changes Preview</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Current Role</TableHead>
                      <TableHead></TableHead>
                      <TableHead>New Role</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {userSelections.filter(s => s.isSelected).map((selection) => (
                      <TableRow key={selection.user.id}>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="font-medium">
                              {selection.user.firstName} {selection.user.lastName}
                            </div>
                            <div className="text-sm text-gray-600">
                              {selection.user.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {getRoleName(selection.currentRole)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <ArrowRight className="h-4 w-4 text-gray-400" />
                        </TableCell>
                        <TableCell>
                          <Badge className={selection.hasConflict ? 'bg-red-100 text-red-700' : 'bg-green-100 text-green-700'}>
                            {getRoleName(selection.newRole)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {selection.hasConflict ? (
                            <div className="flex items-center gap-1 text-red-600">
                              <AlertTriangle className="h-4 w-4" />
                              <span className="text-sm">{selection.conflictReason}</span>
                            </div>
                          ) : selection.currentRole === selection.newRole ? (
                            <div className="flex items-center gap-1 text-gray-600">
                              <Clock className="h-4 w-4" />
                              <span className="text-sm">No change</span>
                            </div>
                          ) : (
                            <div className="flex items-center gap-1 text-green-600">
                              <CheckCircle className="h-4 w-4" />
                              <span className="text-sm">Ready</span>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              {step === 'confirm' && (
                <Button variant="outline" onClick={() => setStep('select')}>
                  Back to Selection
                </Button>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              {step === 'select' ? (
                <Button 
                  onClick={assignRoleToSelected} 
                  disabled={selectedCount === 0 || !selectedRole}
                >
                  Review Assignment ({selectedCount})
                </Button>
              ) : (
                <Button 
                  onClick={executeBulkAssignment} 
                  disabled={isLoading || summary.usersToUpdate === 0}
                >
                  {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Apply Changes ({summary.usersToUpdate})
                </Button>
              )}
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
