import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useAuth } from '@/contexts/AuthContext';
import { RoleTemplate, Permission } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { toast } from '@/hooks/use-toast';
import { 
  Shield,
  Users,
  Briefcase,
  Calendar,
  BarChart3,
  Settings,
  UserCheck,
  Building,
  FileText,
  MessageSquare,
  Search,
  Filter,
  Save,
  RotateCcw,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react';

interface PermissionMatrixProps {
  roles: RoleTemplate[];
  onRolesUpdate: () => void;
}

const AVAILABLE_MODULES = [
  { id: 'applicants', name: 'Applicants', icon: Users },
  { id: 'jobs', name: 'Jobs', icon: Briefcase },
  { id: 'interviews', name: 'Interviews', icon: Calendar },
  { id: 'analytics', name: 'Analytics', icon: BarChart3 },
  { id: 'settings', name: 'Settings', icon: Settings },
  { id: 'users', name: 'Users', icon: UserCheck },
  { id: 'vendors', name: 'Vendors', icon: Building },
  { id: 'form_builder', name: 'Form Builder', icon: FileText },
  { id: 'communications', name: 'Communications', icon: MessageSquare },
  { id: 'onboarding', name: 'Onboarding', icon: UserCheck },
] as const;

const AVAILABLE_ACTIONS = [
  { id: 'create', name: 'Create', color: 'bg-green-100 text-green-700' },
  { id: 'read', name: 'View', color: 'bg-blue-100 text-blue-700' },
  { id: 'update', name: 'Edit', color: 'bg-yellow-100 text-yellow-700' },
  { id: 'delete', name: 'Delete', color: 'bg-red-100 text-red-700' },
  { id: 'approve', name: 'Approve', color: 'bg-purple-100 text-purple-700' },
  { id: 'invite', name: 'Invite', color: 'bg-indigo-100 text-indigo-700' },
  { id: 'manage_roles', name: 'Manage Roles', color: 'bg-orange-100 text-orange-700' },
] as const;

export default function PermissionMatrix({ roles, onRolesUpdate }: PermissionMatrixProps) {
  const { organization } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedModule, setSelectedModule] = useState<string>('all');
  const [roleTypeFilter, setRoleTypeFilter] = useState<string>('all');
  const [isLoading, setSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [permissionChanges, setPermissionChanges] = useState<{
    [roleId: string]: Permission[];
  }>({});

  // Filter roles based on search and filters
  const filteredRoles = roles.filter(role => {
    const matchesSearch = role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = roleTypeFilter === 'all' || 
                       (roleTypeFilter === 'custom' && role.isCustom) ||
                       (roleTypeFilter === 'system' && !role.isCustom);
    return matchesSearch && matchesType;
  });

  // Get filtered modules based on selection
  const filteredModules = selectedModule === 'all' 
    ? AVAILABLE_MODULES 
    : AVAILABLE_MODULES.filter(module => module.id === selectedModule);

  // Check if role has permission for specific module and action
  const hasPermission = (roleId: string, moduleId: string, actionId: string): boolean => {
    const role = roles.find(r => r.id === roleId);
    if (!role) return false;

    // Check if there are pending changes for this role
    const currentPermissions = permissionChanges[roleId] || role.permissions;
    const modulePermission = currentPermissions.find(p => p.module === moduleId);
    return modulePermission ? modulePermission.actions.includes(actionId as Permission['actions'][number]) : false;
  };

  // Toggle permission for a role
  const togglePermission = (roleId: string, moduleId: string, actionId: string) => {
    const role = roles.find(r => r.id === roleId);
    if (!role || !role.isCustom) return; // Only allow editing custom roles

    const currentPermissions = permissionChanges[roleId] || [...role.permissions];
    const moduleIndex = currentPermissions.findIndex(p => p.module === moduleId);

    const newPermissions = [...currentPermissions];

    if (moduleIndex >= 0) {
      // Module exists, toggle action
      const currentActions = newPermissions[moduleIndex].actions;
      if (currentActions.includes(actionId as Permission['actions'][number])) {
        // Remove action
        newPermissions[moduleIndex].actions = currentActions.filter(a => a !== actionId);
        // Remove module if no actions left
        if (newPermissions[moduleIndex].actions.length === 0) {
          newPermissions.splice(moduleIndex, 1);
        }
      } else {
        // Add action
        newPermissions[moduleIndex].actions = [...currentActions, actionId as Permission['actions'][number]];
      }
    } else {
      // Module doesn't exist, create it with this action
      newPermissions.push({
        module: moduleId as Permission['module'],
        actions: [actionId as Permission['actions'][number]],
      });
    }

    setPermissionChanges(prev => ({
      ...prev,
      [roleId]: newPermissions,
    }));
    setHasChanges(true);
  };

  // Toggle all permissions for a module across all roles
  const toggleModuleForAllRoles = (moduleId: string, actionId: string, enable: boolean) => {
    const customRoles = filteredRoles.filter(role => role.isCustom);
    
    customRoles.forEach(role => {
      const currentPermissions = permissionChanges[role.id] || [...role.permissions];
      const moduleIndex = currentPermissions.findIndex(p => p.module === moduleId);

      const newPermissions = [...currentPermissions];

      if (enable) {
        if (moduleIndex >= 0) {
          // Add action if not present
          if (!newPermissions[moduleIndex].actions.includes(actionId as Permission['actions'][number])) {
            newPermissions[moduleIndex].actions = [...newPermissions[moduleIndex].actions, actionId as Permission['actions'][number]];
          }
        } else {
          // Create module with action
          newPermissions.push({
            module: moduleId as Permission['module'],
            actions: [actionId as Permission['actions'][number]],
          });
        }
      } else {
        if (moduleIndex >= 0) {
          // Remove action
          newPermissions[moduleIndex].actions = newPermissions[moduleIndex].actions.filter(a => a !== actionId);
          // Remove module if no actions left
          if (newPermissions[moduleIndex].actions.length === 0) {
            newPermissions.splice(moduleIndex, 1);
          }
        }
      }

      setPermissionChanges(prev => ({
        ...prev,
        [role.id]: newPermissions,
      }));
    });

    setHasChanges(true);
  };

  // Check if all custom roles have a specific permission
  const allRolesHavePermission = (moduleId: string, actionId: string): boolean => {
    const customRoles = filteredRoles.filter(role => role.isCustom);
    return customRoles.every(role => hasPermission(role.id, moduleId, actionId));
  };

  // Save all permission changes
  const saveChanges = async () => {
    if (!organization || !hasChanges) return;

    try {
      setSaving(true);

      // Save changes for each modified role
      const savePromises = Object.entries(permissionChanges).map(async ([roleId, permissions]) => {
        const role = roles.find(r => r.id === roleId);
        if (role && role.isCustom) {
          await UserManagementAPI.updateRoleTemplate(roleId, {
            ...role,
            permissions,
          });
        }
      });

      await Promise.all(savePromises);

      toast({
        title: "Permissions Updated",
        description: `Successfully updated permissions for ${Object.keys(permissionChanges).length} roles.`,
      });

      // Clear changes and refresh
      setPermissionChanges({});
      setHasChanges(false);
      onRolesUpdate();
    } catch (error) {
      console.error('Failed to save permission changes:', error);
      toast({
        title: "Error",
        description: "Failed to save permission changes. Please try again.",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Reset all changes
  const resetChanges = () => {
    setPermissionChanges({});
    setHasChanges(false);
    toast({
      title: "Changes Reset",
      description: "All unsaved changes have been discarded.",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Shield className="h-6 w-6" />
            Permission Matrix
          </h2>
          <p className="text-gray-600">
            Manage permissions across all roles with bulk operations and visual comparison
          </p>
        </div>
        
        {hasChanges && (
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
              <AlertTriangle className="h-3 w-3 mr-1" />
              Unsaved Changes
            </Badge>
            <Button variant="outline" onClick={resetChanges} size="sm">
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button onClick={saveChanges} disabled={isLoading} size="sm">
              {isLoading && <Loader2 className="h-4 w-4 mr-1 animate-spin" />}
              <Save className="h-4 w-4 mr-1" />
              Save Changes
            </Button>
          </div>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="search">Search Roles</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search by role name or description..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="module-filter">Module Filter</Label>
              <Select value={selectedModule} onValueChange={setSelectedModule}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modules</SelectItem>
                  {AVAILABLE_MODULES.map((module) => (
                    <SelectItem key={module.id} value={module.id}>
                      {module.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type-filter">Role Type</Label>
              <Select value={roleTypeFilter} onValueChange={setRoleTypeFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="custom">Custom Roles Only</SelectItem>
                  <SelectItem value="system">System Roles Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permission Matrix */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Permission Matrix</CardTitle>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <div className="flex items-center gap-1">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Has Permission</span>
            </div>
            <div className="flex items-center gap-1">
              <XCircle className="h-4 w-4 text-gray-400" />
              <span>No Permission</span>
            </div>
            <div className="flex items-center gap-1">
              <Shield className="h-4 w-4 text-blue-500" />
              <span>System Role (Read-only)</span>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-48">Role</TableHead>
                  {filteredModules.map((module) => (
                    <TableHead key={module.id} className="text-center min-w-32">
                      <div className="flex flex-col items-center gap-1">
                        <module.icon className="h-4 w-4" />
                        <span className="text-xs">{module.name}</span>
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
                <TableRow>
                  <TableHead></TableHead>
                  {filteredModules.map((module) => (
                    <TableHead key={`${module.id}-actions`} className="p-0">
                      <div className="grid grid-cols-2 gap-1 p-2">
                        {AVAILABLE_ACTIONS.map((action) => (
                          <div key={action.id} className="text-center">
                            <div className="flex flex-col items-center gap-1">
                              <Checkbox
                                checked={allRolesHavePermission(module.id, action.id)}
                                onCheckedChange={(checked) => 
                                  toggleModuleForAllRoles(module.id, action.id, checked as boolean)
                                }
                                className="h-3 w-3"
                              />
                              <span className="text-xs">{action.name}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRoles.map((role) => (
                  <TableRow key={role.id}>
                    <TableCell>
                      <div className="flex flex-col gap-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{role.name}</span>
                          <Badge className={role.isCustom ? 'bg-purple-100 text-purple-700' : 'bg-blue-100 text-blue-700'}>
                            {role.isCustom ? 'Custom' : 'System'}
                          </Badge>
                          {!role.isActive && (
                            <Badge variant="outline" className="bg-gray-100 text-gray-600">
                              Inactive
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 truncate max-w-40">
                          {role.description}
                        </p>
                      </div>
                    </TableCell>
                    {filteredModules.map((module) => (
                      <TableCell key={`${role.id}-${module.id}`} className="p-0">
                        <div className="grid grid-cols-2 gap-1 p-2">
                          {AVAILABLE_ACTIONS.map((action) => {
                            const hasPermissionValue = hasPermission(role.id, module.id, action.id);
                            return (
                              <div key={action.id} className="flex justify-center">
                                <Checkbox
                                  checked={hasPermissionValue}
                                  onCheckedChange={() => togglePermission(role.id, module.id, action.id)}
                                  disabled={!role.isCustom}
                                  className="h-3 w-3"
                                />
                              </div>
                            );
                          })}
                        </div>
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {filteredRoles.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No Roles Found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or filters.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
