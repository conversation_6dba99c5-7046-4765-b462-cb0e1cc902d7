import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { RoleTemplate, Permission, User } from '@/types/multiTenant';
import { UserManagementAPI } from '@/lib/userManagement';
import { mockUsers } from '@/data/multiTenantData';
import { toast } from '@/hooks/use-toast';
import CreateRoleDialog from './CreateRoleDialog';
import RoleDetailsDialog from './RoleDetailsDialog';
import PermissionMatrix from './PermissionMatrix';
import RoleTemplatesManager from './RoleTemplatesManager';
import BulkRoleAssignmentDialog from './BulkRoleAssignmentDialog';
import { 
  Shield,
  Users,
  Settings,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Copy,
  Trash2,
  Eye,
  UserCheck,
  Crown,
  Building,
  Briefcase,
  Calendar,
  BarChart3,
  FileText,
  MessageSquare,
  Loader2
} from 'lucide-react';

interface RoleStats {
  totalRoles: number;
  activeRoles: number;
  customRoles: number;
  usersWithRoles: number;
  roleDistribution: { [key: string]: number };
}

export default function RolesManagement() {
  const { organization, user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [roleTemplates, setRoleTemplates] = useState<RoleTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState<RoleTemplate | null>(null);
  const [createRoleDialogOpen, setCreateRoleDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<RoleTemplate | null>(null);
  const [roleDetailsDialogOpen, setRoleDetailsDialogOpen] = useState(false);
  const [bulkAssignmentDialogOpen, setBulkAssignmentDialogOpen] = useState(false);

  // Load role templates and calculate statistics
  useEffect(() => {
    if (organization) {
      loadRoleTemplates();
    }
  }, [organization]);

  const loadRoleTemplates = async () => {
    try {
      setIsLoading(true);
      const templates = await UserManagementAPI.getRoleTemplates(organization?.type);
      setRoleTemplates(templates);
    } catch (error) {
      console.error('Failed to load role templates:', error);
      toast({
        title: "Error",
        description: "Failed to load role templates.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate role statistics
  const roleStats = useMemo((): RoleStats => {
    const organizationUsers = mockUsers.filter(u => u.organizationId === organization?.id);
    const roleDistribution: { [key: string]: number } = {};
    
    organizationUsers.forEach(user => {
      roleDistribution[user.role] = (roleDistribution[user.role] || 0) + 1;
    });

    return {
      totalRoles: roleTemplates.length,
      activeRoles: roleTemplates.filter(r => r.isActive).length,
      customRoles: roleTemplates.filter(r => r.isCustom).length,
      usersWithRoles: organizationUsers.length,
      roleDistribution,
    };
  }, [roleTemplates, organization]);

  // Filter role templates based on search
  const filteredRoles = useMemo(() => {
    return roleTemplates.filter(role =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [roleTemplates, searchTerm]);

  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'applicants': return <Users className="w-4 h-4" />;
      case 'jobs': return <Briefcase className="w-4 h-4" />;
      case 'interviews': return <Calendar className="w-4 h-4" />;
      case 'analytics': return <BarChart3 className="w-4 h-4" />;
      case 'settings': return <Settings className="w-4 h-4" />;
      case 'users': return <UserCheck className="w-4 h-4" />;
      case 'vendors': return <Building className="w-4 h-4" />;
      case 'form_builder': return <FileText className="w-4 h-4" />;
      case 'communications': return <MessageSquare className="w-4 h-4" />;
      default: return <Shield className="w-4 h-4" />;
    }
  };

  const getRoleTypeColor = (role: RoleTemplate) => {
    if (!role.isActive) return 'bg-gray-100 text-gray-600';
    if (role.isCustom) return 'bg-purple-100 text-purple-700';
    return 'bg-blue-100 text-blue-700';
  };

  const formatRoleName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const handleCreateRole = () => {
    setEditingRole(null);
    setCreateRoleDialogOpen(true);
  };

  const handleEditRole = (role: RoleTemplate) => {
    setEditingRole(role);
    setCreateRoleDialogOpen(true);
  };

  const handleRoleCreated = (role: RoleTemplate) => {
    // Refresh the role templates list
    loadRoleTemplates();
    toast({
      title: editingRole ? "Role Updated" : "Role Created",
      description: `Role "${role.name}" has been ${editingRole ? 'updated' : 'created'} successfully.`,
    });
  };

  const handleViewRole = (role: RoleTemplate) => {
    setSelectedRole(role);
    setRoleDetailsDialogOpen(true);
  };

  const handleDuplicateRole = (role: RoleTemplate) => {
    // TODO: Implement role duplication
    toast({
      title: "Coming Soon",
      description: "Role duplication will be available in the next update.",
    });
  };

  const handleDeleteRole = (role: RoleTemplate) => {
    if (!role.isCustom) {
      toast({
        title: "Cannot Delete",
        description: "System roles cannot be deleted.",
        variant: "destructive",
      });
      return;
    }
    // TODO: Implement role deletion with confirmation
    toast({
      title: "Coming Soon",
      description: "Role deletion will be available in the next update.",
    });
  };

  const handleBulkAssignment = () => {
    setBulkAssignmentDialogOpen(true);
  };

  const handleBulkAssignmentComplete = () => {
    loadRoleTemplates();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Role Management</h1>
          <p className="text-gray-600 mt-1">
            Manage roles, permissions, and access control for your organization
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleBulkAssignment} className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Bulk Assignment
          </Button>
          <Button onClick={handleCreateRole} className="flex items-center gap-2">
            <Plus className="w-4 h-4" />
            Create Custom Role
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Roles</p>
                <p className="text-2xl font-bold">{roleStats.totalRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <UserCheck className="h-8 w-8 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Active Roles</p>
                <p className="text-2xl font-bold">{roleStats.activeRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Crown className="h-8 w-8 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Custom Roles</p>
                <p className="text-2xl font-bold">{roleStats.customRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Users className="h-8 w-8 text-orange-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Users Assigned</p>
                <p className="text-2xl font-bold">{roleStats.usersWithRoles}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-fit grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="roles">Role Templates</TabsTrigger>
          <TabsTrigger value="permissions">Permission Matrix</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Role Distribution Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Role Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(roleStats.roleDistribution).map(([role, count]) => (
                    <div key={role} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium">{formatRoleName(role)}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600">{count} users</span>
                        <div className="w-20 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full" 
                            style={{ width: `${(count / roleStats.usersWithRoles) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Role Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Recent Role Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Administrator role updated</p>
                      <p className="text-xs text-gray-500">2 hours ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">New user assigned Recruiter role</p>
                      <p className="text-xs text-gray-500">1 day ago</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-purple-50 rounded-lg">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">Custom role "HR Manager" created</p>
                      <p className="text-xs text-gray-500">3 days ago</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Role Templates Tab */}
        <TabsContent value="roles" className="space-y-6">
          <RoleTemplatesManager
            roles={roleTemplates}
            onRolesUpdate={loadRoleTemplates}
            onCreateRole={handleCreateRole}
            onEditRole={handleEditRole}
            onViewRole={handleViewRole}
          />

        </TabsContent>

        {/* Permission Matrix Tab */}
        <TabsContent value="permissions">
          <PermissionMatrix
            roles={roleTemplates}
            onRolesUpdate={loadRoleTemplates}
          />
        </TabsContent>
      </Tabs>

      {/* Create/Edit Role Dialog */}
      <CreateRoleDialog
        open={createRoleDialogOpen}
        onOpenChange={setCreateRoleDialogOpen}
        onRoleCreated={handleRoleCreated}
        editingRole={editingRole}
      />

      {/* Role Details Dialog */}
      <RoleDetailsDialog
        open={roleDetailsDialogOpen}
        onOpenChange={setRoleDetailsDialogOpen}
        role={selectedRole}
        onEdit={handleEditRole}
        onDuplicate={handleDuplicateRole}
        onDelete={handleDeleteRole}
      />

      {/* Bulk Role Assignment Dialog */}
      <BulkRoleAssignmentDialog
        open={bulkAssignmentDialogOpen}
        onOpenChange={setBulkAssignmentDialogOpen}
        roles={roleTemplates}
        onAssignmentComplete={handleBulkAssignmentComplete}
      />
    </div>
  );
}
