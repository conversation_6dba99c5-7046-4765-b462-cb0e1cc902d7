/* Theme-aware CSS variables and responsive design utilities */

:root {
  /* Light theme colors */
  --color-background: hsl(0 0% 100%);
  --color-foreground: hsl(222.2 84% 4.9%);
  --color-muted: hsl(210 40% 96%);
  --color-muted-foreground: hsl(215.4 16.3% 46.9%);
  --color-primary: hsl(222.2 47.4% 11.2%);
  --color-primary-foreground: hsl(210 40% 98%);
  --color-destructive: hsl(0 84.2% 60.2%);
  --color-destructive-foreground: hsl(210 40% 98%);
  
  /* Loading and accessibility colors */
  --color-loading-bg: hsl(0 0% 100% / 0.8);
  --color-loading-backdrop: hsl(0 0% 0% / 0.1);
  --color-focus-ring: hsl(222.2 47.4% 11.2%);
  
  /* Spacing scale for responsive design */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Border radius scale */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  
  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

/* Dark theme */
@media (prefers-color-scheme: dark) {
  :root {
    --color-background: hsl(222.2 84% 4.9%);
    --color-foreground: hsl(210 40% 98%);
    --color-muted: hsl(217.2 32.6% 17.5%);
    --color-muted-foreground: hsl(215 20.2% 65.1%);
    --color-primary: hsl(210 40% 98%);
    --color-primary-foreground: hsl(222.2 47.4% 11.2%);
    --color-destructive: hsl(0 62.8% 30.6%);
    --color-destructive-foreground: hsl(210 40% 98%);
    
    --color-loading-bg: hsl(222.2 84% 4.9% / 0.8);
    --color-loading-backdrop: hsl(0 0% 100% / 0.1);
    --color-focus-ring: hsl(210 40% 98%);
  }
}

/* High contrast theme */
@media (prefers-contrast: high) {
  :root {
    --color-background: hsl(0 0% 100%);
    --color-foreground: hsl(0 0% 0%);
    --color-muted: hsl(0 0% 90%);
    --color-muted-foreground: hsl(0 0% 20%);
    --color-primary: hsl(0 0% 0%);
    --color-primary-foreground: hsl(0 0% 100%);
    --color-focus-ring: hsl(220 100% 50%);
  }
}

@media (prefers-contrast: high) and (prefers-color-scheme: dark) {
  :root {
    --color-background: hsl(0 0% 0%);
    --color-foreground: hsl(0 0% 100%);
    --color-muted: hsl(0 0% 10%);
    --color-muted-foreground: hsl(0 0% 80%);
    --color-primary: hsl(0 0% 100%);
    --color-primary-foreground: hsl(0 0% 0%);
    --color-focus-ring: hsl(60 100% 50%);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Loading component theme-aware styles */
.loading-spinner {
  color: var(--color-primary);
}

.loading-overlay {
  background-color: var(--color-loading-bg);
  backdrop-filter: blur(4px);
}

.loading-message {
  color: var(--color-muted-foreground);
}

/* Focus styles for accessibility */
.focus-visible {
  outline: 2px solid var(--color-focus-ring);
  outline-offset: 2px;
}

/* Error styles */
.error-text {
  color: var(--color-destructive);
}

.error-container {
  border-color: var(--color-destructive);
}

/* Screen reader only utility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Responsive breakpoints */
@media (max-width: 640px) {
  :root {
    --spacing-xs: 0.125rem;
    --spacing-sm: 0.25rem;
    --spacing-md: 0.75rem;
    --spacing-lg: 1rem;
    --spacing-xl: 1.5rem;
    --spacing-2xl: 2rem;
  }
}

@media (min-width: 1024px) {
  :root {
    --spacing-xs: 0.375rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1.25rem;
    --spacing-lg: 2rem;
    --spacing-xl: 2.5rem;
    --spacing-2xl: 4rem;
  }
}

/* Loading states with theme support */
.loading-state {
  padding: var(--spacing-md);
}

@media (min-width: 640px) {
  .loading-state {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .loading-state {
    padding: var(--spacing-xl);
  }
}

/* Error state responsive design */
.error-state {
  padding: var(--spacing-md);
  max-width: 28rem;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .error-state {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 768px) {
  .error-state {
    padding: var(--spacing-xl);
  }
}

/* Button focus and hover states */
.button-primary {
  background-color: var(--color-primary);
  color: var(--color-primary-foreground);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) ease-in-out;
}

.button-primary:hover {
  opacity: 0.9;
}

.button-primary:focus-visible {
  outline: 2px solid var(--color-focus-ring);
  outline-offset: 2px;
} 