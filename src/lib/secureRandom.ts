
/**
 * Secure random number utilities using Web Crypto API
 */
export class SecureRandom {
  private static counter = 0;
  private static readonly MAX_COUNTER = 9999999;

  /**
   * Generate a cryptographically secure random number between min and max
   */
  static getRandomInt(min: number, max: number): number {
    const range = max - min;
    const bytesNeeded = Math.ceil(Math.log2(range) / 8);
    const maxValid = Math.floor(256 ** bytesNeeded / range) * range - 1;
    const array = new Uint8Array(bytesNeeded);

    let value: number;
    do {
      crypto.getRandomValues(array);
      value = array.reduce((acc, byte) => (acc << 8) + byte, 0);
    } while (value > maxValid);

    return min + (value % range);
  }

  /**
   * Generate a UUID v4 using crypto API
   */
  static generateUUID(): string {
    try {
      // Try using crypto.randomUUID() first
      if (typeof crypto.randomUUID === 'function') {
        return crypto.randomUUID();
      }
      
      // Fallback implementation for environments where crypto.randomUUID is not available
      const array = new Uint8Array(16);
      crypto.getRandomValues(array);
      
      // Set version (4) and variant (RFC4122) bits
      const bytes = Array.from(array);
      if (bytes.length < 16) {
        throw new Error('Failed to generate UUID: insufficient random bytes');
      }
      // Set version 4
      const byte6 = bytes[6];
      const byte8 = bytes[8];
      if (byte6 === undefined || byte8 === undefined) {
        throw new Error('Failed to generate UUID: invalid byte access');
      }
      bytes[6] = (byte6 & 0x0f) | 0x40;
      // Set variant (RFC4122)
      bytes[8] = (byte8 & 0x3f) | 0x80;
      
      // Convert to hex string
      const hex = bytes
        .map(b => b.toString(16).padStart(2, '0'))
        .join('');
        
      return [
        hex.slice(0, 8),
        hex.slice(8, 12),
        hex.slice(12, 16),
        hex.slice(16, 20),
        hex.slice(20, 32)
      ].join('-');
    } catch (error) {
      // Log the error for debugging
      console.error('UUID generation failed:', error);
      throw new Error('Failed to generate UUID: ' + (error instanceof Error ? error.message : 'unknown error'));
    }
  }

  /**
   * Generate a unique operation ID with timestamp and secure random component
   */
  static generateOperationId(prefix: string): string {
    try {
      const timestamp = Date.now();
      const array = new Uint32Array(1);
      crypto.getRandomValues(array);
      const random = array[0];
      if (random === undefined) {
        throw new Error('Failed to generate random value');
      }
      return `${prefix}-${timestamp}-${random}`;
    } catch (error) {
      // Log the error for debugging
      console.error('Operation ID generation failed:', error);
      throw new Error('Failed to generate operation ID: ' + (error instanceof Error ? error.message : 'unknown error'));
    }
  }

  /**
   * Generate a unique ARIA ID (doesn't need to be cryptographically secure,
   * just needs to be unique within the page)
   */
  static generateAriaId(prefix: string = 'aria'): string {
    this.counter = (this.counter + 1) % this.MAX_COUNTER;
    return `${prefix}-${Date.now()}-${this.counter}`;
  }

  /**
   * Get a random item from an array using secure random
   */
  static getRandomArrayItem<T>(array: T[]): T {
    if (array.length === 0) {
      throw new Error('Cannot get random item from empty array');
    }
    const index = this.getRandomInt(0, array.length);
    const item = array[index];
    if (item === undefined) {
      throw new Error('Failed to get random array item: invalid index access');
    }
    return item;
  }
}
