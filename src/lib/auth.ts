// JWT-based authentication system
import { User, Organization } from '@/types/multiTenant';

// JWT token structure
export interface JWTPayload {
  sub: string; // user ID
  email: string;
  organizationId: string;
  role: string;
  permissions: Array<{
    module: string;
    actions: string[];
  }>;
  iat: number; // issued at
  exp: number; // expires at
}

// Auth response from server
export interface AuthResponse {
  user: User;
  organization: Organization;
  accessToken: string;
  refreshToken: string;
}

// Login credentials
export interface LoginCredentials {
  email: string;
  password: string;
}

// Registration data
export interface RegisterData {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  organizationName?: string;
  organizationType?: 'client' | 'vendor';
}

// JWT Token Manager with secure storage options
export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = 'ats_access_token';
  private static readonly REFRESH_TOKEN_KEY = 'ats_refresh_token';
  private static readonly TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer
  private static readonly COOKIE_OPTIONS = {
    secure: !import.meta.env.DEV, // HTTPS only in production
    sameSite: 'strict' as const,
    httpOnly: false, // Client-side access needed for token management
    maxAge: 60 * 60 * 24 * 7, // 7 days
  };

  // Store tokens securely based on environment
  static setTokens(accessToken: string, refreshToken: string): void {
    if (this.supportsSecureCookies()) {
      // Production: Use secure cookies when possible
      this.setCookie(this.ACCESS_TOKEN_KEY, accessToken, {
        ...this.COOKIE_OPTIONS,
        maxAge: 60 * 15 // 15 minutes for access token
      });
      this.setCookie(this.REFRESH_TOKEN_KEY, refreshToken, this.COOKIE_OPTIONS);
    } else {
      // Development/fallback: Use sessionStorage (better than localStorage)
      sessionStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
      sessionStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }

  // Check if secure cookies are supported
  private static supportsSecureCookies(): boolean {
    return !import.meta.env.DEV && window.location.protocol === 'https:';
  }

  // Set secure cookie
  private static setCookie(name: string, value: string, options: typeof this.COOKIE_OPTIONS): void {
    const cookieString = [
      `${name}=${value}`,
      `Max-Age=${options.maxAge}`,
      `SameSite=${options.sameSite}`,
      options.secure ? 'Secure' : '',
      'Path=/',
    ].filter(Boolean).join('; ');

    document.cookie = cookieString;
  }

  // Get cookie value
  private static getCookie(name: string): string | null {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() ?? null;
    }
    return null;
  }

  // Delete cookie
  private static deleteCookie(name: string): void {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  // Get access token from secure storage
  static getAccessToken(): string | null {
    if (this.supportsSecureCookies()) {
      return this.getCookie(this.ACCESS_TOKEN_KEY);
    }
    return sessionStorage.getItem(this.ACCESS_TOKEN_KEY);
  }

  // Get refresh token from secure storage
  static getRefreshToken(): string | null {
    if (this.supportsSecureCookies()) {
      return this.getCookie(this.REFRESH_TOKEN_KEY);
    }
    return sessionStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  // Clear all tokens from all storage mechanisms
  static clearTokens(): void {
    // Clear from sessionStorage
    sessionStorage.removeItem(this.ACCESS_TOKEN_KEY);
    sessionStorage.removeItem(this.REFRESH_TOKEN_KEY);

    // Clear from cookies
    this.deleteCookie(this.ACCESS_TOKEN_KEY);
    this.deleteCookie(this.REFRESH_TOKEN_KEY);
  }

  // Decode JWT payload (client-side only for UI purposes)
  static decodeToken(token: string): JWTPayload | null {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to decode JWT token:', error);
      return null;
    }
  }

  // Check if token is expired
  static isTokenExpired(token: string): boolean {
    const payload = this.decodeToken(token);
    if (!payload) return true;

    const now = Date.now();
    const expiry = payload.exp * 1000; // Convert to milliseconds
    
    // Add buffer time to refresh before actual expiry
    return now >= (expiry - this.TOKEN_EXPIRY_BUFFER);
  }

  // Check if access token is valid and not expired
  static isAccessTokenValid(): boolean {
    const token = this.getAccessToken();
    if (!token) return false;
    return !this.isTokenExpired(token);
  }

  // Get current user from token
  static getCurrentUserFromToken(): Partial<User> | null {
    const token = this.getAccessToken();
    if (!token || this.isTokenExpired(token)) return null;

    const payload = this.decodeToken(token);
    if (!payload) return null;

    return {
      id: payload.sub,
      email: payload.email,
      organizationId: payload.organizationId,
      role: payload.role as 'admin' | 'manager' | 'employee',
      permissions: payload.permissions as Array<{
        module: string;
        actions: string[];
      }>,
    };
  }
}

// API client for authentication
export class AuthAPI {
  private static readonly BASE_URL = '/api' ;
  // private static readonly BASE_URL = import.meta.env.VITE_API_URL ?? '/api';

  // Login user
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const response = await fetch(`${this.BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      credentials: 'include', // Include cookies for httpOnly tokens
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Login failed');
    }

    return response.json();
  }

  // Register user
  static async register(data: RegisterData): Promise<AuthResponse> {
    const response = await fetch(`${this.BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Registration failed');
    }

    return response.json();
  }

  // Refresh access token
  static async refreshToken(): Promise<{ accessToken: string }> {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await fetch(`${this.BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
      credentials: 'include',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Token refresh failed');
    }

    return response.json();
  }

  // Logout user
  static async logout(): Promise<void> {
    const refreshToken = TokenManager.getRefreshToken();
    
    try {
      await fetch(`${this.BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
        },
        body: JSON.stringify({ refreshToken }),
        credentials: 'include',
      });
    } catch (error) {
      console.error('Logout API call failed:', error);
      // Continue with local cleanup even if API call fails
    }

    // Always clear local tokens
    TokenManager.clearTokens();
  }

  // Get current user profile
  static async getCurrentUser(): Promise<User> {
    const response = await fetch(`${this.BASE_URL}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to get user profile');
    }

    return response.json();
  }

  // Get current organization
  static async getCurrentOrganization(): Promise<Organization> {
    const response = await fetch(`${this.BASE_URL}/auth/organization`, {
      headers: {
        'Authorization': `Bearer ${TokenManager.getAccessToken()}`,
      },
      credentials: 'include',
    });

    if (!response.ok) {
      throw new Error('Failed to get organization');
    }

    return response.json();
  }
}

// HTTP interceptor for automatic token refresh
export class AuthInterceptor {
  static async fetch(url: string, options: RequestInit = {}): Promise<Response> {
    // Add authorization header if token exists
    const token = TokenManager.getAccessToken();
    if (token && !TokenManager.isTokenExpired(token)) {
      options.headers = {
        ...options.headers,
        'Authorization': `Bearer ${token}`,
      };
    }

    let response = await fetch(url, options);

    // If unauthorized and we have a refresh token, try to refresh
    if (response.status === 401 && TokenManager.getRefreshToken()) {
      try {
        const { accessToken } = await AuthAPI.refreshToken();
        TokenManager.setTokens(accessToken, TokenManager.getRefreshToken()!);

        // Retry the original request with new token
        options.headers = {
          ...options.headers,
          'Authorization': `Bearer ${accessToken}`,
        };
        response = await fetch(url, options);
      } catch (error) {
        // Refresh failed, redirect to login
        TokenManager.clearTokens();
        window.location.href = '/login';
        throw error;
      }
    }

    return response;
  }
}
