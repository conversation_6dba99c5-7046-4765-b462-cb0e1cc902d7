import { AxiosError } from 'axios';
import { config, getApiConfig } from '@/config/appConfig';
import https from 'https';
// Get configuration from centralized config management
const appApiConfig = getApiConfig();

// API Base configuration - now using centralized config
const API_BASE_URL = appApiConfig.baseUrl;

// Default axios configuration for all requests with security enhancements
export const defaultAxiosConfig = {
  timeout: appApiConfig.timeout,
  // Security headers
  withCredentials: false, // Don't send cookies unless explicitly needed
  maxRedirects: 5,
  validateStatus: appApiConfig.validateStatus,
  // Ensure HTTPS in production
  httpsAgent: config.environment === 'production' ? new https.Agent({
    rejectUnauthorized: true, // Enforce SSL certificate validation in production
    minVersion: 'TLSv1.2' // Enforce minimum TLS version
  }) : undefined,
};

// API Endpoints configuration based on OpenAPI spec
export const apiConfig = {
  applicants: {
    list: '/applicant',
    create: '/applicant/create',
    detail: '/applicant/{id}',
    update: '/applicant/{id}',
    patch: '/applicant/{id}',
    deactivate: '/applicant/{id}/deactivate',
    activate: '/applicant/{id}/activate',
    listValues: '/list-value/list-name',
  },
  listValue: '/list-value',
} as const;

// Utility function to build URLs with path parameters
export function buildUrl(endpoint: string, params?: Record<string, string | number>): string {
  let url = endpoint;
  
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      url = url.replace(`{${key}}`, encodeURIComponent(String(value)));
    });
  }
  
  // Prepend the base URL
  return `${API_BASE_URL}${url}`;
}

// Utility function to attach query parameters
export function withQuery(url: string, query?: Record<string, string | number | boolean>): string {
  if (!query || Object.keys(query).length === 0) {
    return url;
  }
  
  const searchParams = new URLSearchParams();
  Object.entries(query).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `${url}?${queryString}` : url;
}

// Utility function to get headers (authorization removed)
export function getAuthorizationHeaders(): Record<string, string> {
  // const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    'Accept': 'application/vnd-chidhagni-ats.applicant.create.res-v1+json',
  };

  // Authorization removed for all endpoints
  // if (token) {
  //   headers['Authorization'] = `Bearer ${token}`;
  // }

  return headers;
}

// Utility function to get multipart form headers (authorization removed)
export function getMultipartAuthorizationHeaders(
  acceptHeader?: string,
  additionalHeaders?: Record<string, string>
): Record<string, string> {
  // const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');

  const headers: Record<string, string> = {
    'Accept': acceptHeader || 'application/vnd-chidhagni-ats.applicant.create.res-v1+json',
    ...additionalHeaders,
  };

  // Authorization removed for all endpoints
  // if (token) {
  //   headers['Authorization'] = `Bearer ${token}`;
  // }

  return headers;
}

// 401 error handling utility (can be called manually from API functions)
export function handle401Error(): void {
  // Clear tokens
  localStorage.removeItem('authToken');
  sessionStorage.removeItem('authToken');
  
  // Redirect to login page
  window.location.href = '/login';
}

// Type definitions for API errors
export interface ApiError extends Error {
  status?: number;
  data?: unknown;
  timestamp?: string;
  correlationId?: string;
  errorCode?: string;
  validationErrors?: Record<string, string>;
  retryable?: boolean;
  retryAfterMs?: number;
}

// Utility function to create API error from axios error
export function createApiError(axiosError: AxiosError): ApiError {
  const error = new Error(axiosError.message) as ApiError;
  error.name = 'ApiError';
  error.status = axiosError.response?.status;
  error.data = axiosError.response?.data;
  
  // Handle 401 errors automatically - DISABLED (no auth required)
  // if (axiosError.response?.status === 401) {
  //   handle401Error();
  // }
  
  // Extract additional error information if available
  if (axiosError.response?.data && typeof axiosError.response.data === 'object') {
    const errorData = axiosError.response.data as Record<string, unknown>;
    error.timestamp = typeof errorData.timestamp === 'string' ? errorData.timestamp : undefined;
    error.correlationId = typeof errorData.correlationId === 'string' ? errorData.correlationId : undefined;
    error.errorCode = typeof errorData.errorCode === 'string' ? errorData.errorCode : undefined;
    error.validationErrors = errorData.validationErrors && typeof errorData.validationErrors === 'object' 
      ? errorData.validationErrors as Record<string, string> : undefined;
    error.retryable = typeof errorData.retryable === 'boolean' ? errorData.retryable : undefined;
    error.retryAfterMs = typeof errorData.retryAfterMs === 'number' ? errorData.retryAfterMs : undefined;
    error.message = typeof errorData.message === 'string' ? errorData.message : axiosError.message;
  }
  
  return error;
} 