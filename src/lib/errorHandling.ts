import { ApiError } from './apiConfig';
import { API_CONSTANTS, ErrorMessageKey } from '@/constants/apiConstants';

/**
 * Convert API error to user-friendly message
 */
export function getErrorMessage(error: ApiError): string {
  // Check for specific error codes first
  if (error.status) {
    switch (error.status) {
      case 400:
        return API_CONSTANTS.ERROR_MESSAGES.VALIDATION_ERROR;
      case 401:
        return API_CONSTANTS.ERROR_MESSAGES.UNAUTHORIZED;
      case 403:
        return API_CONSTANTS.ERROR_MESSAGES.FORBIDDEN;
      case 404:
        return API_CONSTANTS.ERROR_MESSAGES.NOT_FOUND;
      case 408:
        return API_CONSTANTS.ERROR_MESSAGES.TIMEOUT;
      case 413:
        return API_CONSTANTS.ERROR_MESSAGES.FILE_UPLOAD_ERROR;
      case 500:
      case 502:
      case 503:
      case 504:
        return API_CONSTANTS.ERROR_MESSAGES.SERVER_ERROR;
      default:
        break;
    }
  }

  // Check for network errors
  if (error.message?.toLowerCase().includes('network') || 
      error.message?.toLowerCase().includes('connection')) {
    return API_CONSTANTS.ERROR_MESSAGES.NETWORK_ERROR;
  }

  // Check for timeout errors
  if (error.message?.toLowerCase().includes('timeout')) {
    return API_CONSTANTS.ERROR_MESSAGES.TIMEOUT;
  }

  // Return generic error message
  return API_CONSTANTS.ERROR_MESSAGES.GENERIC_ERROR;
}

/**
 * Enhanced error callback that provides user-friendly messages
 */
export function createUserFriendlyErrorCallback(
  onError: (message: string, originalError: ApiError) => void
) {
  return (error: ApiError) => {
    const userMessage = getErrorMessage(error);
    onError(userMessage, error);
  };
}

/**
 * Log error for debugging without exposing sensitive information
 */
export function logError(context: string, error: ApiError, additionalInfo?: Record<string, unknown>): void {
  // Only log in development or when explicitly enabled
  if (process.env.NODE_ENV === 'development' || process.env.VITE_ENABLE_ERROR_LOGGING === 'true') {
    const sanitizedError = {
      context,
      status: error.status,
      message: error.message,
      timestamp: new Date().toISOString(),
      correlationId: error.correlationId,
      errorCode: error.errorCode,
      ...additionalInfo
    };
    
    console.error(`[API Error] ${context}:`, sanitizedError);
  }
} 