import { z } from 'zod';

// Common validation patterns
const emailSchema = z.string().email('Invalid email address');
const phoneSchema = z.string().regex(/^\+?[1-9]\d{1,14}$|^\+?[\d\s\-()]{10,20}$/, 'Invalid phone number').max(20, 'Phone number too long');
const urlSchema = z.string().url('Invalid URL');
const nonEmptyString = z.string().min(1, 'This field is required');

// User validation schemas
export const userSchema = z.object({
  firstName: nonEmptyString.max(50, 'First name must be less than 50 characters'),
  lastName: nonEmptyString.max(50, 'Last name must be less than 50 characters'),
  email: emailSchema,
  phone: phoneSchema.optional(),
  title: nonEmptyString.max(100, 'Title must be less than 100 characters'),
  department: z.string().optional(),
});

// Candidate validation schemas
export const candidateSchema = z.object({
  firstName: nonEmptyString.max(50, 'First name must be less than 50 characters'),
  lastName: nonEmptyString.max(50, 'Last name must be less than 50 characters'),
  email: emailSchema,
  phone: phoneSchema,
  position: nonEmptyString.max(100, 'Position must be less than 100 characters'),
  department: nonEmptyString.max(100, 'Department must be less than 100 characters'),
  experience: z.number().min(0, 'Experience cannot be negative').max(50, 'Experience seems too high'),
  skills: z.array(z.string().max(50, 'Skill name too long')).min(1, 'At least one skill is required'),
  resume: z.string().url('Invalid resume URL').optional(),
  coverLetter: z.string().max(2000, 'Cover letter too long').optional(),
});

// Job validation schemas
export const jobSchema = z.object({
  title: nonEmptyString.max(100, 'Job title must be less than 100 characters'),
  department: nonEmptyString.max(100, 'Department must be less than 100 characters'),
  location: nonEmptyString.max(100, 'Location must be less than 100 characters'),
  type: z.enum(['full-time', 'part-time', 'contract', 'internship'], {
    errorMap: () => ({ message: 'Invalid job type' }),
  }),
  description: nonEmptyString.max(5000, 'Description too long'),
  requirements: z.array(z.string().max(200, 'Requirement too long')).min(1, 'At least one requirement needed'),
});

// Interview validation schemas
export const interviewSchema = z.object({
  candidateId: nonEmptyString,
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  time: z.string().regex(/^\d{2}:\d{2}$/, 'Invalid time format (HH:MM)'),
  interviewer: nonEmptyString.max(100, 'Interviewer name too long'),
  type: z.enum(['phone', 'video', 'in-person'], {
    errorMap: () => ({ message: 'Invalid interview type' }),
  }),
  duration: z.number().min(15, 'Interview too short').max(480, 'Interview too long').optional(),
  location: z.string().max(200, 'Location too long').optional(),
  notes: z.string().max(1000, 'Notes too long').optional(),
  agenda: z.string().max(1000, 'Agenda too long').optional(),
});

// Message validation schemas
export const messageSchema = z.object({
  subject: nonEmptyString.max(200, 'Subject too long'),
  content: nonEmptyString.max(5000, 'Message too long'),
  toOrganizationId: nonEmptyString,
  messageType: z.enum(['general', 'job-related', 'candidate-related', 'system'], {
    errorMap: () => ({ message: 'Invalid message type' }),
  }),
  relatedJobId: z.string().optional(),
  relatedCandidateId: z.string().optional(),
});

// Organization validation schemas
export const organizationSchema = z.object({
  name: nonEmptyString.max(100, 'Organization name too long'),
  type: z.enum(['client', 'vendor'], {
    errorMap: () => ({ message: 'Invalid organization type' }),
  }),
  industry: nonEmptyString.max(100, 'Industry name too long'),
  size: nonEmptyString.max(50, 'Size description too long'),
  website: urlSchema.optional(),
  address: nonEmptyString.max(200, 'Address too long'),
  phone: phoneSchema,
  email: emailSchema,
});

// Feedback validation schemas
export const feedbackSchema = z.object({
  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating cannot exceed 5'),
  notes: z.string().max(2000, 'Feedback notes too long').optional(),
  recommendation: z.enum(['hire', 'reject', 'maybe', 'next-round'], {
    errorMap: () => ({ message: 'Invalid recommendation' }),
  }).optional(),
  strengths: z.string().max(1000, 'Strengths description too long').optional(),
  concerns: z.string().max(1000, 'Concerns description too long').optional(),
  nextSteps: z.string().max(500, 'Next steps description too long').optional(),
});

// Search and filter validation schemas
export const searchSchema = z.object({
  query: z.string().max(100, 'Search query too long').optional(),
  filters: z.object({
    status: z.array(z.string()).optional(),
    department: z.array(z.string()).optional(),
    location: z.array(z.string()).optional(),
    dateRange: z.object({
      from: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid from date').optional(),
      to: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid to date').optional(),
    }).optional(),
  }).optional(),
  sort: z.object({
    field: z.string().max(50, 'Sort field name too long'),
    direction: z.enum(['asc', 'desc'], {
      errorMap: () => ({ message: 'Invalid sort direction' }),
    }),
  }).optional(),
  pagination: z.object({
    page: z.number().min(1, 'Page must be at least 1'),
    limit: z.number().min(1, 'Limit must be at least 1').max(100, 'Limit too high'),
  }).optional(),
});

// Export types for TypeScript
export type UserInput = z.infer<typeof userSchema>;
export type CandidateInput = z.infer<typeof candidateSchema>;
export type JobInput = z.infer<typeof jobSchema>;
export type InterviewInput = z.infer<typeof interviewSchema>;
export type MessageInput = z.infer<typeof messageSchema>;
export type OrganizationInput = z.infer<typeof organizationSchema>;
export type FeedbackInput = z.infer<typeof feedbackSchema>;
export type SearchInput = z.infer<typeof searchSchema>;

// Utility function for safe validation
export const validateInput = <T>(schema: z.ZodSchema<T>, data: unknown): { success: true; data: T } | { success: false; errors: string[] } => {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        errors: error.errors.map(err => `${err.path.join('.')}: ${err.message}`),
      };
    }
    return {
      success: false,
      errors: ['Validation failed'],
    };
  }
};

// Sanitization utilities
export const sanitizeHtml = (input: string): string => {
  // Basic HTML sanitization - in production, use a library like DOMPurify
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
};

export const sanitizeInput = (input: string): string => {
  // Remove potentially dangerous characters
  return input
    .trim()
    .replace(/[<>"'&]/g, '')
    .substring(0, 1000); // Limit length
};
