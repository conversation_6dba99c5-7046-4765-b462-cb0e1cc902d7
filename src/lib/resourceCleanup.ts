import { logError } from './errorHandling';
import { SecureRandom } from './secureRandom';

/**
 * Interface for resources that need cleanup
 */
export interface CleanupableResource {
  cleanup(): void | Promise<void>;
  getId(): string;
  getType(): string;
}

/**
 * FormData resource wrapper for proper cleanup
 */
export class FormDataResource implements CleanupableResource {
  private static counter = 0;
  private id: string;
  private formData: FormData;
  private isCleanedUp = false;

  constructor(formData: FormData) {
    this.id = `formdata-${++FormDataResource.counter}-${Date.now()}`;
    this.formData = formData;
  }

  getFormData(): FormData {
    if (this.isCleanedUp) {
      throw new Error('FormData resource has been cleaned up and is no longer available');
    }
    return this.formData;
  }

  cleanup(): void {
    if (this.isCleanedUp) return;

    try {
      // Clear all entries from FormData
      if (this.formData) {
        // Get all keys and delete them
        const keys: string[] = [];
        for (const key of this.formData.keys()) {
          keys.push(key);
        }
        
        keys.forEach(key => {
          this.formData.delete(key);
        });
      }
      
      this.isCleanedUp = true;
    } catch (error) {
      logError('ResourceCleanup', error as Error, { 
        resourceId: this.id, 
        resourceType: 'FormData' 
      });
    }
  }

  getId(): string {
    return this.id;
  }

  getType(): string {
    return 'FormData';
  }

  isDisposed(): boolean {
    return this.isCleanedUp;
  }
}

/**
 * AbortController resource wrapper
 */
export class AbortControllerResource implements CleanupableResource {
  private static counter = 0;
  private id: string;
  private controller: AbortController;
  private isCleanedUp = false;

  constructor(controller: AbortController) {
    this.id = `abortcontroller-${++AbortControllerResource.counter}-${Date.now()}`;
    this.controller = controller;
  }

  getController(): AbortController {
    if (this.isCleanedUp) {
      throw new Error('AbortController resource has been cleaned up and is no longer available');
    }
    return this.controller;
  }

  getSignal(): AbortSignal {
    return this.getController().signal;
  }

  abort(reason?: string): void {
    if (!this.isCleanedUp) {
      this.controller.abort(reason);
    }
  }

  cleanup(): void {
    if (this.isCleanedUp) return;

    try {
      // Abort any pending requests
      if (!this.controller.signal.aborted) {
        this.controller.abort('Resource cleanup');
      }
      
      this.isCleanedUp = true;
    } catch (error) {
      logError('ResourceCleanup', error as Error, { 
        resourceId: this.id, 
        resourceType: 'AbortController' 
      });
    }
  }

  getId(): string {
    return this.id;
  }

  getType(): string {
    return 'AbortController';
  }

  isDisposed(): boolean {
    return this.isCleanedUp;
  }
}

/**
 * Timer resource wrapper for cleanup
 */
export class TimerResource implements CleanupableResource {
  private static counter = 0;
  private id: string;
  private timerId: NodeJS.Timeout | number | null;
  private isCleanedUp = false;

  constructor(timerId: NodeJS.Timeout | number) {
    this.id = `timer-${++TimerResource.counter}-${Date.now()}`;
    this.timerId = timerId;
  }

  cleanup(): void {
    if (this.isCleanedUp || this.timerId === null) return;

    try {
      clearTimeout(this.timerId as NodeJS.Timeout);
      this.timerId = null;
      this.isCleanedUp = true;
    } catch (error) {
      logError('ResourceCleanup', error as Error, { 
        resourceId: this.id, 
        resourceType: 'Timer' 
      });
    }
  }

  getId(): string {
    return this.id;
  }

  getType(): string {
    return 'Timer';
  }

  isDisposed(): boolean {
    return this.isCleanedUp;
  }
}

/**
 * Resource cleanup service for managing and cleaning up resources
 */
export class ResourceCleanupService {
  private static instance: ResourceCleanupService;
  private resources = new Map<string, CleanupableResource>();
  private resourcesByOperation = new Map<string, Set<string>>();

  private constructor() {}

  /**
   * Get singleton instance
   */
  static getInstance(): ResourceCleanupService {
    if (!ResourceCleanupService.instance) {
      ResourceCleanupService.instance = new ResourceCleanupService();
    }
    return ResourceCleanupService.instance;
  }

  /**
   * Register a resource for cleanup
   */
  registerResource(resource: CleanupableResource, operationId?: string): string {
    const resourceId = resource.getId();
    this.resources.set(resourceId, resource);

    if (operationId) {
      if (!this.resourcesByOperation.has(operationId)) {
        this.resourcesByOperation.set(operationId, new Set());
      }
      this.resourcesByOperation.get(operationId)!.add(resourceId);
    }

    return resourceId;
  }

  /**
   * Create and register a FormData resource
   */
  createFormDataResource(operationId?: string): FormDataResource {
    const formData = new FormData();
    const resource = new FormDataResource(formData);
    this.registerResource(resource, operationId);
    return resource;
  }

  /**
   * Create and register an AbortController resource
   */
  createAbortControllerResource(operationId?: string): AbortControllerResource {
    const controller = new AbortController();
    const resource = new AbortControllerResource(controller);
    this.registerResource(resource, operationId);
    return resource;
  }

  /**
   * Create and register a Timer resource
   */
  createTimerResource(callback: () => void, delay: number, operationId?: string): TimerResource {
    const timerId = setTimeout(callback, delay);
    const resource = new TimerResource(timerId);
    this.registerResource(resource, operationId);
    return resource;
  }

  /**
   * Clean up a specific resource
   */
  cleanupResource(resourceId: string): boolean {
    const resource = this.resources.get(resourceId);
    if (!resource) return false;

    try {
      resource.cleanup();
      this.resources.delete(resourceId);

      // Remove from operation tracking
      for (const [operationId, resourceIds] of this.resourcesByOperation.entries()) {
        if (resourceIds.has(resourceId)) {
          resourceIds.delete(resourceId);
          if (resourceIds.size === 0) {
            this.resourcesByOperation.delete(operationId);
          }
          break;
        }
      }

      return true;
    } catch (error) {
      logError('ResourceCleanup', error as Error, { resourceId });
      return false;
    }
  }

  /**
   * Clean up all resources for a specific operation
   */
  cleanupOperation(operationId: string): number {
    const resourceIds = this.resourcesByOperation.get(operationId);
    if (!resourceIds) return 0;

    let cleanedCount = 0;
    for (const resourceId of Array.from(resourceIds)) {
      if (this.cleanupResource(resourceId)) {
        cleanedCount++;
      }
    }

    this.resourcesByOperation.delete(operationId);
    return cleanedCount;
  }

  /**
   * Clean up all resources
   */
  cleanupAll(): number {
    let cleanedCount = 0;
    
    for (const [resourceId] of Array.from(this.resources.entries())) {
      if (this.cleanupResource(resourceId)) {
        cleanedCount++;
      }
    }

    this.resources.clear();
    this.resourcesByOperation.clear();
    return cleanedCount;
  }

  /**
   * Get resource statistics
   */
  getStats(): {
    totalResources: number;
    operationsWithResources: number;
    resourcesByType: Record<string, number>;
  } {
    const resourcesByType: Record<string, number> = {};
    
    for (const resource of this.resources.values()) {
      const type = resource.getType();
      resourcesByType[type] = (resourcesByType[type] || 0) + 1;
    }

    return {
      totalResources: this.resources.size,
      operationsWithResources: this.resourcesByOperation.size,
      resourcesByType,
    };
  }

  /**
   * Execute operation with automatic resource cleanup
   */
  async withResourceCleanup<T>(
    operation: (cleanup: ResourceCleanupService) => Promise<T>,
    operationId?: string
  ): Promise<T> {
    const cleanupOperationId = operationId ?? SecureRandom.generateOperationId('cleanup');
    
    try {
      const result = await operation(this);
      return result;
    } finally {
      // Always cleanup resources associated with this operation
      const cleanedResources = this.cleanupOperation(cleanupOperationId);
      if (cleanedResources > 0) {
        logError('ResourceCleanup', new Error('Operation completed, cleaned up resources'), {
          operationId: cleanupOperationId,
          cleanedResources,
        });
      }
    }
  }
}

/**
 * Export singleton instance for convenience
 */
export const resourceCleanup = ResourceCleanupService.getInstance();

/**
 * Higher-order function for automatic resource cleanup
 */
export async function withCleanup<T>(
  operation: (cleanup: ResourceCleanupService) => Promise<T>,
  operationId?: string
): Promise<T> {
  return resourceCleanup.withResourceCleanup(operation, operationId);
} 