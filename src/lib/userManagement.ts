import { User, UserInvitation, RoleTemplate, UserSession, SocialConnection } from '@/types/multiTenant';

/**
 * Enhanced User Management API
 * Extends the existing authentication system with advanced user management features
 */

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  permissions: Array<{ module: string; actions: string[] }>;
  department?: string;
  title: string;
  sendInvitation: boolean;
  invitationMessage?: string;
}

export interface UpdateUserRequest {
  id: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  role?: string;
  permissions?: Array<{ module: string; actions: string[] }>;
  department?: string;
  title?: string;
  isActive?: boolean;
}

export interface InviteUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  permissions: Array<{ module: string; actions: string[] }>;
  department?: string;
  title: string;
  message?: string;
  expiresInDays?: number;
}

export interface BulkUserOperation {
  userIds: string[];
  operation: 'activate' | 'deactivate' | 'delete' | 'update_role';
  data?: {
    role?: string;
    permissions?: Array<{ module: string; actions: string[] }>;
  };
}

export interface UserSearchFilters {
  search?: string;
  role?: string;
  department?: string;
  status?: 'active' | 'inactive' | 'pending';
  invitationStatus?: 'pending' | 'accepted' | 'expired';
  lastLoginBefore?: string;
  lastLoginAfter?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

export class UserManagementAPI {
  private static readonly BASE_URL = import.meta.env.VITE_API_URL ?? '/api';
  private static readonly IS_DEVELOPMENT = import.meta.env.DEV;

  // Mock data for development
  private static mockDelay = () => new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

  // User CRUD operations
  static async getUsers(
    organizationId: string,
    filters?: UserSearchFilters,
    page = 1,
    pageSize = 20
  ): Promise<PaginatedResponse<User>> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock response for development
      const { mockUsers } = await import('@/data/multiTenantData');
      const filteredUsers = mockUsers.filter(user => user.organizationId === organizationId);

      return {
        data: filteredUsers.slice((page - 1) * pageSize, page * pageSize),
        total: filteredUsers.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredUsers.length / pageSize),
      };
    }

    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
      ...filters,
    });

    const response = await fetch(`${this.BASE_URL}/users?${params}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch users');
    }

    return response.json();
  }

  static async createUser(organizationId: string, userData: CreateUserRequest): Promise<User> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock user for development
      const newUser: User = {
        id: `user-${Date.now()}`,
        organizationId,
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: userData.role as 'admin' | 'recruiter' | 'hiring_manager' | 'interviewer' | 'employee' | 'super_admin',
        permissions: userData.permissions as Permission[],
        phone: userData.phone,
        department: userData.department,
        title: userData.title,
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        onboardingCompleted: false,
        invitationStatus: 'none',
        failedLoginAttempts: 0,
        accountLocked: false,
        twoFactorEnabled: false,
        emailVerified: false,
        socialConnections: [],
        activeSessions: [],
        preferences: {
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
          emailNotifications: {
            newApplications: true,
            interviewReminders: true,
            systemUpdates: true,
            weeklyReports: true,
          },
          dashboardLayout: ['overview', 'recent-activity'],
          defaultPageSize: 20,
        },
      };
      return newUser;
    }

    const response = await fetch(`${this.BASE_URL}/users`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...userData, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to create user');
    }

    return response.json();
  }

  static async updateUser(userData: UpdateUserRequest): Promise<User> {
    const response = await fetch(`${this.BASE_URL}/users/${userData.id}`, {
      method: 'PUT',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to update user');
    }

    return response.json();
  }

  static async deleteUser(userId: string): Promise<void> {
    const response = await fetch(`${this.BASE_URL}/users/${userId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to delete user');
    }
  }

  // User invitation system
  static async inviteUser(organizationId: string, invitationData: InviteUserRequest): Promise<UserInvitation> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock invitation for development
      const invitation: UserInvitation = {
        id: `inv-${Date.now()}`,
        organizationId,
        email: invitationData.email,
        firstName: invitationData.firstName,
        lastName: invitationData.lastName,
        role: invitationData.role as 'admin' | 'recruiter' | 'hiring_manager' | 'interviewer' | 'employee' | 'super_admin',
        permissions: invitationData.permissions as Permission[],
        department: invitationData.department,
        title: invitationData.title,
        invitedBy: 'current-user', // In real app, this would come from auth context
        invitedAt: new Date().toISOString(),
        expiresAt: new Date(Date.now() + (invitationData.expiresInDays || 7) * 24 * 60 * 60 * 1000).toISOString(),
        status: 'pending',
        token: `token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        message: invitationData.message,
      };
      return invitation;
    }

    const response = await fetch(`${this.BASE_URL}/users/invite`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...invitationData, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to send invitation');
    }

    return response.json();
  }

  static async getInvitations(organizationId: string): Promise<UserInvitation[]> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock invitations for development
      const { mockUserInvitations } = await import('@/data/multiTenantData');
      return mockUserInvitations.filter(inv => inv.organizationId === organizationId);
    }

    const response = await fetch(`${this.BASE_URL}/users/invitations?organizationId=${organizationId}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch invitations');
    }

    return response.json();
  }

  static async resendInvitation(invitationId: string): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock resend invitation - in real app this would send a new email
      console.log(`Mock: Resent invitation ${invitationId}`);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/users/invitations/${invitationId}/resend`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to resend invitation');
    }
  }

  static async cancelInvitation(invitationId: string): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock cancel invitation - in real app this would invalidate the invitation
      console.log(`Mock: Cancelled invitation ${invitationId}`);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/users/invitations/${invitationId}/cancel`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to cancel invitation');
    }
  }

  // Bulk operations
  static async bulkUserOperation(operation: BulkUserOperation): Promise<{ success: number; failed: number }> {
    const response = await fetch(`${this.BASE_URL}/users/bulk`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(operation),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Bulk operation failed');
    }

    return response.json();
  }

  // Session management
  static async getUserSessions(userId: string): Promise<UserSession[]> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock sessions for development
      const { mockUserSessions } = await import('@/data/multiTenantData');
      return mockUserSessions.filter(session => session.userId === userId);
    }

    const response = await fetch(`${this.BASE_URL}/users/${userId}/sessions`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch user sessions');
    }

    return response.json();
  }

  static async terminateSession(sessionId: string): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock session termination - in real app this would terminate the session
      console.log(`Mock: Terminated session ${sessionId}`);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/sessions/${sessionId}/terminate`, {
      method: 'POST',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to terminate session');
    }
  }

  static async terminateAllSessions(userId: string, exceptCurrent = true): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock session termination - in real app this would terminate all sessions
      console.log(`Mock: Terminated all sessions for user ${userId}, except current: ${exceptCurrent}`);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/users/${userId}/sessions/terminate-all`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ exceptCurrent }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to terminate sessions');
    }
  }

  // Role templates
  static async getRoleTemplates(organizationType?: 'client' | 'vendor'): Promise<RoleTemplate[]> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock role templates for development
      const mockTemplates: RoleTemplate[] = [
        {
          id: 'template-1',
          name: 'Administrator',
          description: 'Full system access with all permissions',
          permissions: [
            { module: 'applicants', actions: ['create', 'read', 'update', 'delete', 'approve'] },
            { module: 'jobs', actions: ['create', 'read', 'update', 'delete', 'approve'] },
            { module: 'interviews', actions: ['create', 'read', 'update', 'delete'] },
            { module: 'users', actions: ['create', 'read', 'update', 'delete', 'invite', 'manage_roles'] },
            { module: 'settings', actions: ['read', 'update'] },
          ],
          organizationType: 'both',
          isCustom: false,
          isActive: true,
          createdBy: 'system',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'template-2',
          name: 'Hiring Manager',
          description: 'Manage jobs and candidates for hiring',
          permissions: [
            { module: 'applicants', actions: ['read', 'update'] },
            { module: 'jobs', actions: ['create', 'read', 'update'] },
            { module: 'interviews', actions: ['create', 'read', 'update'] },
          ],
          organizationType: 'both',
          isCustom: false,
          isActive: true,
          createdBy: 'system',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'template-3',
          name: 'Recruiter',
          description: 'Source and manage candidates',
          permissions: [
            { module: 'applicants', actions: ['create', 'read', 'update'] },
            { module: 'jobs', actions: ['read'] },
            { module: 'interviews', actions: ['create', 'read', 'update'] },
          ],
          organizationType: 'both',
          isCustom: false,
          isActive: true,
          createdBy: 'system',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      return organizationType
        ? mockTemplates.filter(t => t.organizationType === organizationType || t.organizationType === 'both')
        : mockTemplates;
    }

    const params = organizationType ? `?organizationType=${organizationType}` : '';
    const response = await fetch(`${this.BASE_URL}/role-templates${params}`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch role templates');
    }

    return response.json();
  }

  // Role management methods
  static async createRoleTemplate(organizationId: string, roleData: Omit<RoleTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<RoleTemplate> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock role template for development
      const newRole: RoleTemplate = {
        id: `role-${Date.now()}`,
        ...roleData,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      return newRole;
    }

    const response = await fetch(`${this.BASE_URL}/role-templates`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ...roleData, organizationId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to create role template');
    }

    return response.json();
  }

  static async updateRoleTemplate(roleId: string, updates: Partial<RoleTemplate>): Promise<RoleTemplate> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock updated role template for development
      const updatedRole: RoleTemplate = {
        id: roleId,
        name: updates.name || 'Updated Role',
        description: updates.description || 'Updated description',
        permissions: updates.permissions || [],
        organizationType: updates.organizationType || 'both',
        isCustom: updates.isCustom ?? true,
        isActive: updates.isActive ?? true,
        createdBy: updates.createdBy || 'current-user',
        createdAt: updates.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      return updatedRole;
    }

    const response = await fetch(`${this.BASE_URL}/role-templates/${roleId}`, {
      method: 'PUT',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updates),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to update role template');
    }

    return response.json();
  }

  static async deleteRoleTemplate(roleId: string): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock delete role template for development
      console.log(`Mock: Deleted role template ${roleId}`);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/role-templates/${roleId}`, {
      method: 'DELETE',
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to delete role template');
    }
  }

  static async assignRoleToUsers(userIds: string[], roleId: string): Promise<void> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Mock bulk role assignment for development
      console.log(`Mock: Assigned role ${roleId} to users:`, userIds);
      return;
    }

    const response = await fetch(`${this.BASE_URL}/users/bulk-assign-role`, {
      method: 'POST',
      headers: {
        ...this.getAuthHeaders(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ userIds, roleId }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message ?? 'Failed to assign role to users');
    }
  }

  static async getRoleUsageStats(organizationId: string): Promise<{ [roleId: string]: number }> {
    if (this.IS_DEVELOPMENT) {
      await this.mockDelay();
      // Return mock role usage statistics for development
      const { mockUsers } = await import('@/data/multiTenantData');
      const orgUsers = mockUsers.filter(user => user.organizationId === organizationId);
      const roleStats: { [role: string]: number } = {};

      orgUsers.forEach(user => {
        roleStats[user.role] = (roleStats[user.role] || 0) + 1;
      });

      return roleStats;
    }

    const response = await fetch(`${this.BASE_URL}/organizations/${organizationId}/role-stats`, {
      headers: this.getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to fetch role usage statistics');
    }

    return response.json();
  }

  // Helper method to get auth headers
  private static getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('accessToken');
    return {
      'Authorization': `Bearer ${token}`,
    };
  }
}

// User management utilities
export class UserManagementUtils {
  static generateStrongPassword(length = 12): string {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
      password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static formatUserName(user: User): string {
    return `${user.firstName} ${user.lastName}`;
  }

  static getUserInitials(user: User): string {
    return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase();
  }

  static isUserActive(user: User): boolean {
    return user.isActive && !user.accountLocked;
  }

  static getAccountStatus(user: User): 'active' | 'inactive' | 'locked' | 'pending' {
    if (user.invitationStatus === 'pending') return 'pending';
    if (user.accountLocked) return 'locked';
    if (!user.isActive) return 'inactive';
    return 'active';
  }

  static formatLastLogin(lastLogin?: string): string {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${Math.floor(diffInHours)} hours ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)} days ago`;
    return date.toLocaleDateString();
  }
}
