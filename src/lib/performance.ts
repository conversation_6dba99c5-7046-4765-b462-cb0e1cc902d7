// Performance monitoring and optimization utilities

interface LayoutShiftEntry extends PerformanceEntry {
  value: number;
  hadRecentInput: boolean;
}

interface PerformanceEventTiming extends PerformanceEntry {
  processingStart: number;
}

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  type: 'navigation' | 'resource' | 'measure' | 'custom';
}

export class PerformanceMonitor {
  private static metrics: PerformanceMetric[] = [];
  private static observers: PerformanceObserver[] = [];

  // Initialize performance monitoring
  static init(): void {
    if (typeof window === 'undefined') return;

    // Monitor navigation timing
    this.observeNavigationTiming();
    
    // Monitor resource loading
    this.observeResourceTiming();
    
    // Monitor layout shifts
    this.observeLayoutShifts();
    
    // Monitor largest contentful paint
    this.observeLCP();
    
    // Monitor first input delay
    this.observeFID();
  }

  // Observe navigation timing
  private static observeNavigationTiming(): void {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const navigationEntries = performance.getEntriesByType('navigation') as PerformanceNavigationTiming[];
      
      navigationEntries.forEach(entry => {
        this.addMetric('DOM Content Loaded', entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart, 'navigation');
        this.addMetric('Load Complete', entry.loadEventEnd - entry.loadEventStart, 'navigation');
        this.addMetric('DNS Lookup', entry.domainLookupEnd - entry.domainLookupStart, 'navigation');
        this.addMetric('TCP Connection', entry.connectEnd - entry.connectStart, 'navigation');
        this.addMetric('Request', entry.responseStart - entry.requestStart, 'navigation');
        this.addMetric('Response', entry.responseEnd - entry.responseStart, 'navigation');
        this.addMetric('DOM Processing', entry.domComplete - entry.domLoading, 'navigation');
      });
    }
  }

  // Observe resource timing
  private static observeResourceTiming(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'resource') {
            const resourceEntry = entry as PerformanceResourceTiming;
            this.addMetric(`Resource: ${resourceEntry.name}`, resourceEntry.duration, 'resource');
          }
        });
      });

      observer.observe({ entryTypes: ['resource'] });
      this.observers.push(observer);
    }
  }

  // Observe Cumulative Layout Shift (CLS)
  private static observeLayoutShifts(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        let clsValue = 0;
        
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'layout-shift' && !(entry as LayoutShiftEntry).hadRecentInput) {
            clsValue += (entry as LayoutShiftEntry).value;
          }
        });

        if (clsValue > 0) {
          this.addMetric('Cumulative Layout Shift', clsValue, 'measure');
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    }
  }

  // Observe Largest Contentful Paint (LCP)
  private static observeLCP(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        if (lastEntry) {
          this.addMetric('Largest Contentful Paint', lastEntry.startTime, 'measure');
        }
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    }
  }

  // Observe First Input Delay (FID)
  private static observeFID(): void {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.entryType === 'first-input') {
            const fidValue = (entry as PerformanceEventTiming).processingStart - entry.startTime;
            this.addMetric('First Input Delay', fidValue, 'measure');
          }
        });
      });

      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    }
  }

  // Add custom metric
  static addMetric(name: string, value: number, type: PerformanceMetric['type'] = 'custom'): void {
    this.metrics.push({
      name,
      value,
      timestamp: Date.now(),
      type,
    });

    // Keep only last 100 metrics
    if (this.metrics.length > 100) {
      this.metrics.shift();
    }
  }

  // Get all metrics
  static getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }

  // Get metrics by type
  static getMetricsByType(type: PerformanceMetric['type']): PerformanceMetric[] {
    return this.metrics.filter(metric => metric.type === type);
  }

  // Get performance summary
  static getSummary(): Record<string, unknown> {
    const summary: Record<string, unknown> = {};

    // Group metrics by name
    const groupedMetrics = this.metrics.reduce((acc, metric) => {
      if (!acc[metric.name]) {
        acc[metric.name] = [];
      }
      acc[metric.name].push(metric.value);
      return acc;
    }, {} as Record<string, number[]>);

    // Calculate statistics for each metric
    Object.entries(groupedMetrics).forEach(([name, values]) => {
      summary[name] = {
        count: values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        latest: values[values.length - 1],
      };
    });

    return summary;
  }

  // Measure function execution time
  static measureFunction<T extends (...args: unknown[]) => unknown>(
    fn: T,
    name?: string
  ): T {
    return ((...args: Parameters<T>) => {
      const startTime = performance.now();
      const result = fn(...args);
      const endTime = performance.now();
      
      this.addMetric(name ?? fn.name ?? 'Anonymous Function', endTime - startTime, 'custom');
      
      return result;
    }) as T;
  }

  // Measure async function execution time
  static measureAsyncFunction<T extends (...args: unknown[]) => Promise<unknown>>(
    fn: T,
    name?: string
  ): T {
    return (async (...args: Parameters<T>) => {
      const startTime = performance.now();
      const result = await fn(...args);
      const endTime = performance.now();
      
      this.addMetric(name ?? fn.name ?? 'Anonymous Async Function', endTime - startTime, 'custom');
      
      return result;
    }) as T;
  }

  // Start a custom timer
  static startTimer(name: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      this.addMetric(name, endTime - startTime, 'custom');
    };
  }

  // Get Web Vitals scores
  static getWebVitals(): Record<string, { value: number; rating: 'good' | 'needs-improvement' | 'poor' }> {
    const vitals: Record<string, { value: number; rating: 'good' | 'needs-improvement' | 'poor' }> = {};

    // LCP thresholds: good (≤2.5s), needs improvement (≤4s), poor (>4s)
    const lcpMetrics = this.metrics.filter(m => m.name === 'Largest Contentful Paint');
    if (lcpMetrics.length > 0) {
      const lcp = lcpMetrics[lcpMetrics.length - 1].value;
      const getLCPRating = (value: number): 'good' | 'needs-improvement' | 'poor' => {
        if (value <= 2500) return 'good';
        if (value <= 4000) return 'needs-improvement';
        return 'poor';
      };
      vitals.LCP = {
        value: lcp,
        rating: getLCPRating(lcp)
      };
    }

    // FID thresholds: good (≤100ms), needs improvement (≤300ms), poor (>300ms)
    const fidMetrics = this.metrics.filter(m => m.name === 'First Input Delay');
    if (fidMetrics.length > 0) {
      const fid = fidMetrics[fidMetrics.length - 1].value;
      const getFIDRating = (value: number): 'good' | 'needs-improvement' | 'poor' => {
        if (value <= 100) return 'good';
        if (value <= 300) return 'needs-improvement';
        return 'poor';
      };
      vitals.FID = {
        value: fid,
        rating: getFIDRating(fid)
      };
    }

    // CLS thresholds: good (≤0.1), needs improvement (≤0.25), poor (>0.25)
    const clsMetrics = this.metrics.filter(m => m.name === 'Cumulative Layout Shift');
    if (clsMetrics.length > 0) {
      const cls = clsMetrics[clsMetrics.length - 1].value;
      const getCLSRating = (value: number): 'good' | 'needs-improvement' | 'poor' => {
        if (value <= 0.1) return 'good';
        if (value <= 0.25) return 'needs-improvement';
        return 'poor';
      };
      vitals.CLS = {
        value: cls,
        rating: getCLSRating(cls)
      };
    }

    return vitals;
  }

  // Export metrics for analysis
  static exportMetrics(): string {
    return JSON.stringify({
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      metrics: this.metrics,
      summary: this.getSummary(),
      webVitals: this.getWebVitals(),
    }, null, 2);
  }

  // Clean up observers
  static cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics = [];
  }
}

// React hook for performance monitoring
export const usePerformanceMonitor = () => {
  const measureRender = (componentName: string) => {
    const stopTimer = PerformanceMonitor.startTimer(`Render: ${componentName}`);
    
    return () => {
      stopTimer();
    };
  };

  const measureEffect = (effectName: string) => {
    const stopTimer = PerformanceMonitor.startTimer(`Effect: ${effectName}`);
    
    return () => {
      stopTimer();
    };
  };

  return {
    measureRender,
    measureEffect,
    addMetric: PerformanceMonitor.addMetric,
    getMetrics: PerformanceMonitor.getMetrics,
    getSummary: PerformanceMonitor.getSummary,
    getWebVitals: PerformanceMonitor.getWebVitals,
  };
};
