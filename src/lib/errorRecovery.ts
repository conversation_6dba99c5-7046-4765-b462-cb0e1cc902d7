import { ApiError } from './apiConfig';
import { logError } from './errorHandling';

/**
 * Authentication service interface for token refresh
 */
interface AuthService {
  refreshToken(): Promise<void>;
  redirectToLogin?(): void;
}

/**
 * Configuration for error recovery strategies
 */
export interface ErrorRecoveryConfig {
  enableAutoRetry: boolean;
  enableTokenRefresh: boolean;
  maxRecoveryAttempts: number;
  retryDelayMs: number;
  enableCircuitBreaker: boolean;
  circuitBreakerThreshold: number;
  circuitBreakerTimeoutMs: number;
}

/**
 * Default error recovery configuration
 */
const defaultConfig: ErrorRecoveryConfig = {
  enableAutoRetry: true,
  enableTokenRefresh: true,
  maxRecoveryAttempts: 3,
  retryDelayMs: 1000,
  enableCircuitBreaker: true,
  circuitBreakerThreshold: 5,
  circuitBreakerTimeoutMs: 30000,
};

/**
 * Circuit breaker state for preventing cascade failures
 */
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private threshold: number,
    private timeoutMs: number
  ) {}

  canExecute(): boolean {
    const now = Date.now();
    
    if (this.state === 'OPEN') {
      if (now - this.lastFailureTime >= this.timeoutMs) {
        this.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }
    
    return true;
  }

  onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.threshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }
}

/**
 * Global error recovery service for handling API errors and implementing recovery strategies
 */
export class ErrorRecoveryService {
  private static instance: ErrorRecoveryService;
  private config: ErrorRecoveryConfig;
  private circuitBreaker: CircuitBreaker;
  private authService: AuthService | null = null; // Will be injected
  private recoveryAttempts = new Map<string, number>();

  private constructor(config: Partial<ErrorRecoveryConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    this.circuitBreaker = new CircuitBreaker(
      this.config.circuitBreakerThreshold,
      this.config.circuitBreakerTimeoutMs
    );
  }

  /**
   * Get singleton instance of ErrorRecoveryService
   */
  static getInstance(config?: Partial<ErrorRecoveryConfig>): ErrorRecoveryService {
    if (!ErrorRecoveryService.instance) {
      ErrorRecoveryService.instance = new ErrorRecoveryService(config);
    }
    return ErrorRecoveryService.instance;
  }

  /**
   * Inject authentication service for token refresh
   */
  setAuthService(authService: AuthService): void {
    this.authService = authService;
  }

  /**
   * Main error recovery handler
   */
  async handleApiError<T>(
    error: ApiError, 
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    // Check circuit breaker
    if (this.config.enableCircuitBreaker && !this.circuitBreaker.canExecute()) {
      const circuitError = new Error('Circuit breaker is open - service temporarily unavailable');
      logError('ErrorRecovery', circuitError, { operationId, circuitState: this.circuitBreaker.getState() });
      throw circuitError;
    }

    try {
      const recoveryResult = await this.attemptRecovery(error, operation, operationId, signal);
      
      // Recovery successful
      this.circuitBreaker.onSuccess();
      this.recoveryAttempts.delete(operationId);
      
      return recoveryResult;
    } catch (recoveryError) {
      // Recovery failed
      this.circuitBreaker.onFailure();
      logError('ErrorRecovery', recoveryError as Error, { 
        operationId, 
        originalError: error,
        circuitState: this.circuitBreaker.getState()
      });
      throw recoveryError;
    }
  }

  /**
   * Attempt to recover from the error using various strategies
   */
  private async attemptRecovery<T>(
    error: ApiError,
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    const attempts = this.recoveryAttempts.get(operationId) || 0;
    
    if (attempts >= this.config.maxRecoveryAttempts) {
      throw new Error(`Max recovery attempts (${this.config.maxRecoveryAttempts}) exceeded for operation: ${operationId}`);
    }

    this.recoveryAttempts.set(operationId, attempts + 1);

    // Strategy 1: Handle authentication errors
    if (error.status === 401 && this.config.enableTokenRefresh) {
      return await this.handleAuthenticationError(operation, operationId, signal);
    }

    // Strategy 2: Handle rate limiting
    if (error.status === 429) {
      return await this.handleRateLimitError(error, operation, operationId, signal);
    }

    // Strategy 3: Handle server errors with retry
    if (error.status >= 500 && this.config.enableAutoRetry) {
      return await this.handleServerError(operation, operationId, signal);
    }

    // Strategy 4: Handle network errors
    if (!error.status && this.config.enableAutoRetry) {
      return await this.handleNetworkError(operation, operationId, signal);
    }

    // No recovery strategy available
    throw error;
  }

  /**
   * Handle authentication errors by refreshing token and retrying
   */
  private async handleAuthenticationError<T>(
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    if (!this.authService) {
      throw new Error('Auth service not configured for token refresh');
    }

    try {
      logError('ErrorRecovery', new Error('Attempting token refresh'), { operationId });
      
      // Attempt token refresh
      await this.authService.refreshToken();
      
      // Wait a bit before retry
      await this.delay(500);
      
      // Check if request was aborted during refresh
      if (signal?.aborted) {
        throw new Error('Request aborted during token refresh');
      }
      
      // Retry original operation
      return await operation();
    } catch (refreshError) {
      // If token refresh fails, redirect to login or handle appropriately
      if (this.authService.redirectToLogin) {
        this.authService.redirectToLogin();
      }
      throw new Error('Authentication recovery failed');
    }
  }

  /**
   * Handle rate limiting errors by waiting and retrying
   */
  private async handleRateLimitError<T>(
    error: ApiError,
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    // Extract retry-after header or use default delay
    const retryAfterMs = this.extractRetryAfter(error) || this.config.retryDelayMs;
    
    logError('ErrorRecovery', new Error('Rate limited, waiting before retry'), { 
      operationId, 
      retryAfterMs 
    });
    
    await this.delay(retryAfterMs);
    
    // Check if request was aborted during wait
    if (signal?.aborted) {
      throw new Error('Request aborted during rate limit wait');
    }
    
    return await operation();
  }

  /**
   * Handle server errors with exponential backoff retry
   */
  private async handleServerError<T>(
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    const attempts = this.recoveryAttempts.get(operationId) || 0;
    const delayMs = this.config.retryDelayMs * Math.pow(2, attempts);
    
    logError('ErrorRecovery', new Error('Server error, retrying with backoff'), { 
      operationId, 
      attempt: attempts + 1,
      delayMs 
    });
    
    await this.delay(delayMs);
    
    // Check if request was aborted during wait
    if (signal?.aborted) {
      throw new Error('Request aborted during server error retry');
    }
    
    return await operation();
  }

  /**
   * Handle network errors with retry
   */
  private async handleNetworkError<T>(
    operation: () => Promise<T>,
    operationId: string,
    signal?: AbortSignal
  ): Promise<T> {
    const attempts = this.recoveryAttempts.get(operationId) || 0;
    const delayMs = this.config.retryDelayMs * Math.pow(1.5, attempts); // Gentler backoff for network issues
    
    logError('ErrorRecovery', new Error('Network error, retrying'), { 
      operationId, 
      attempt: attempts + 1,
      delayMs 
    });
    
    await this.delay(delayMs);
    
    // Check if request was aborted during wait
    if (signal?.aborted) {
      throw new Error('Request aborted during network error retry');
    }
    
    return await operation();
  }

  /**
   * Extract retry-after value from error response
   */
  private extractRetryAfter(error: ApiError): number | null {
    if (error.response?.headers) {
      const retryAfter = error.response.headers['retry-after'] || error.response.headers['Retry-After'];
      if (retryAfter) {
        const seconds = parseInt(retryAfter as string, 10);
        return isNaN(seconds) ? null : seconds * 1000;
      }
    }
    return null;
  }

  /**
   * Utility method for delays
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Get current circuit breaker state
   */
  getCircuitBreakerState(): string {
    return this.circuitBreaker.getState();
  }

  /**
   * Reset circuit breaker (for manual recovery)
   */
  resetCircuitBreaker(): void {
    this.circuitBreaker.onSuccess();
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorRecoveryConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<ErrorRecoveryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update circuit breaker if needed
    if (newConfig.circuitBreakerThreshold || newConfig.circuitBreakerTimeoutMs) {
      this.circuitBreaker = new CircuitBreaker(
        this.config.circuitBreakerThreshold,
        this.config.circuitBreakerTimeoutMs
      );
    }
  }

  /**
   * Clear all recovery attempts (useful for testing or manual reset)
   */
  clearRecoveryAttempts(): void {
    this.recoveryAttempts.clear();
  }
}

/**
 * Export singleton instance for convenience
 */
export const errorRecoveryService = ErrorRecoveryService.getInstance(); 