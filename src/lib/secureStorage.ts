/**
 * Secure storage utility for sensitive data
 * Provides encryption and secure storage for tokens and other sensitive information
 */

// Simple encryption/decryption using Web Crypto API (for demo purposes)
// In production, consider using a more robust solution
class SecureStorage {
  private readonly storageKey = 'secure_app_data';
  private readonly encryptionKey: string;

  constructor() {
    // Generate or retrieve encryption key
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  private getOrCreateEncryptionKey(): string {
    const keyName = 'app_encryption_key';
    let key = sessionStorage.getItem(keyName);
    
    if (!key) {
      // Generate a new key for this session
      key = this.generateRandomKey();
      sessionStorage.setItem(keyName, key);
    }
    
    return key;
  }

  private generateRandomKey(): string {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  private async encrypt(data: string): Promise<string> {
    try {
      // Simple XOR encryption for demo (use proper encryption in production)
      const encrypted = data
        .split('')
        .map((char, index) => {
          const keyChar = this.encryptionKey[index % this.encryptionKey.length];
          return String.fromCharCode(char.charCodeAt(0) ^ keyChar.charCodeAt(0));
        })
        .join('');
      
      return btoa(encrypted);
    } catch (error) {
      console.error('Encryption failed:', error);
      return data; // Fallback to unencrypted
    }
  }

  private async decrypt(encryptedData: string): Promise<string> {
    try {
      const data = atob(encryptedData);
      const decrypted = data
        .split('')
        .map((char, index) => {
          const keyChar = this.encryptionKey[index % this.encryptionKey.length];
          return String.fromCharCode(char.charCodeAt(0) ^ keyChar.charCodeAt(0));
        })
        .join('');
      
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedData; // Fallback to encrypted data
    }
  }

  async setItem(key: string, value: string): Promise<void> {
    try {
      const encrypted = await this.encrypt(value);
      const data = this.getAllData();
      data[key] = encrypted;
      sessionStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to store secure data:', error);
      // Fallback to regular storage (not recommended for production)
      sessionStorage.setItem(key, value);
    }
  }

  async getItem(key: string): Promise<string | null> {
    try {
      const data = this.getAllData();
      const encryptedValue = data[key];
      
      if (!encryptedValue) {
        // Check fallback storage
        return sessionStorage.getItem(key);
      }
      
      return await this.decrypt(encryptedValue);
    } catch (error) {
      console.error('Failed to retrieve secure data:', error);
      return sessionStorage.getItem(key);
    }
  }

  removeItem(key: string): void {
    try {
      const data = this.getAllData();
      delete data[key];
      sessionStorage.setItem(this.storageKey, JSON.stringify(data));
      
      // Also remove from fallback storage
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove secure data:', error);
      sessionStorage.removeItem(key);
    }
  }

  clear(): void {
    sessionStorage.removeItem(this.storageKey);
    // Clear encryption key as well
    sessionStorage.removeItem('app_encryption_key');
  }

  private getAllData(): Record<string, string> {
    try {
      const data = sessionStorage.getItem(this.storageKey);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Failed to parse secure storage data:', error);
      return {};
    }
  }
}

// Create singleton instance
const secureStorage = new SecureStorage();

// Token management utilities
export const TokenStorage = {
  async setToken(token: string): Promise<void> {
    await secureStorage.setItem('auth_token', token);
  },

  async getToken(): Promise<string | null> {
    return await secureStorage.getItem('auth_token');
  },

  removeToken(): void {
    secureStorage.removeItem('auth_token');
  },

  async setRefreshToken(token: string): Promise<void> {
    await secureStorage.setItem('refresh_token', token);
  },

  async getRefreshToken(): Promise<string | null> {
    return await secureStorage.getItem('refresh_token');
  },

  removeRefreshToken(): void {
    secureStorage.removeItem('refresh_token');
  },

  clearAllTokens(): void {
    secureStorage.removeItem('auth_token');
    secureStorage.removeItem('refresh_token');
  }
};

// General secure storage interface
export const SecureStorageAPI = {
  async setItem(key: string, value: string): Promise<void> {
    await secureStorage.setItem(key, value);
  },

  async getItem(key: string): Promise<string | null> {
    return await secureStorage.getItem(key);
  },

  removeItem(key: string): void {
    secureStorage.removeItem(key);
  },

  clear(): void {
    secureStorage.clear();
  }
};

export default secureStorage; 