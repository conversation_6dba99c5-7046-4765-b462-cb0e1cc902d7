import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

import { SecureRandom } from './secureRandom';

export const getSecureRandomInt = SecureRandom.getRandomInt.bind(SecureRandom);
export const getSecureRandomFloat = (min: number, max: number): number => {
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);
  return min + (array[0] / (0xffffffff + 1)) * (max - min);
};
export const getSecureRandomArrayItem = SecureRandom.getRandomArrayItem.bind(SecureRandom);
export const generateUUID = SecureRandom.generateUUID.bind(SecureRandom);

/**
 * Maps a UUID to its corresponding name from the list values
 * @param uuid - The UUID to map
 * @param listValues - Array of GlobalListValueDTO from authContext
 * @returns The name if found, otherwise returns the original UUID
 */
export function mapUuidToName(uuid: string | undefined, listValues: { id: string; name: string }[]): string {
  if (!uuid) return '';
  
  // If it doesn't look like a UUID, return as-is (might already be a name)
  if (!isValidUuid(uuid)) return uuid;
  
  // Try exact match first
  let listValue = listValues.find(item => item.id === uuid);
  
  // If no exact match, try case-insensitive match
  if (!listValue) {
    listValue = listValues.find(item => item.id.toLowerCase() === uuid.toLowerCase());
  }
  
  // Debug logging in development
  if (process.env.NODE_ENV === 'development' && !listValue && listValues.length > 0) {
    console.warn(`No mapping found for UUID: ${uuid}`, {
      uuid,
      listValuesCount: listValues.length,
      sampleListValues: listValues.slice(0, 3)
    });
  }
  
  return listValue?.name || `[ID: ${uuid.slice(0, 8)}...]`;
}

/**
 * Maps an array of UUIDs to their corresponding names
 * @param uuids - Array of UUIDs to map
 * @param listValues - Array of GlobalListValueDTO from authContext
 * @returns Array of names
 */
export function mapUuidsToNames(uuids: string[] | undefined, listValues: { id: string; name: string }[]): string[] {
  if (!uuids || !Array.isArray(uuids)) return [];
  if (!listValues || !Array.isArray(listValues)) return [];
  
  try {
    return uuids.map(uuid => mapUuidToName(uuid, listValues));
  } catch (error) {
    console.error('Error in mapUuidsToNames:', error, { uuids, listValuesCount: listValues?.length });
    return [];
  }
}

/**
 * Checks if a string is a valid UUID format
 */
function isValidUuid(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}
