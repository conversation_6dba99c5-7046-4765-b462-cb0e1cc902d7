import { ApiError } from './apiConfig';

// Simple in-memory cache for API responses
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  expiry: number;
}

class ApiCache {
  private cache = new Map<string, CacheEntry<unknown>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      expiry: Date.now() + ttl
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  // Clear expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const apiCache = new ApiCache();

// Cleanup expired entries every 10 minutes
setInterval(() => {
  apiCache.cleanup();
}, 10 * 60 * 1000);

/**
 * Create a cache key from function parameters
 */
function createCacheKey(functionName: string, params: unknown): string {
  const paramString = JSON.stringify(params, (key, value) => {
    // Sort object keys for consistent cache keys
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return Object.keys(value)
        .sort((a, b) => a.localeCompare(b))
        .reduce((sorted, key) => {
          sorted[key] = value[key];
          return sorted;
        }, {} as Record<string, unknown>);
    }
    return value;
  });
  
  return `${functionName}:${paramString}`;
}

/**
 * Memoize an API function with configurable TTL
 */
export function memoizeApiCall<TParams, TResult>(
  apiFunction: (params: TParams, errorCallback?: (error: ApiError) => void) => Promise<TResult | undefined>,
  options: {
    ttl?: number;
    keyGenerator?: (params: TParams) => string;
    shouldCache?: (params: TParams, result: TResult) => boolean;
  } = {}
) {
  const { ttl = 5 * 60 * 1000, keyGenerator, shouldCache = () => true } = options;

  return async (
    params: TParams,
    errorCallback?: (error: ApiError) => void
  ): Promise<TResult | undefined> => {
    const cacheKey = keyGenerator 
      ? keyGenerator(params)
      : createCacheKey(apiFunction.name, params);

    // Try to get from cache first
    const cachedResult = apiCache.get<TResult>(cacheKey);
    if (cachedResult !== null) {
      return cachedResult;
    }

    // Call the actual API function
    const result = await apiFunction(params, errorCallback);
    
    // Cache the result if it's successful and should be cached
    if (result !== undefined && shouldCache(params, result)) {
      apiCache.set(cacheKey, result, ttl);
    }
    
    return result;
  };
}

/**
 * Invalidate cache entries by pattern
 */
export function invalidateCache(pattern?: string): void {
  if (!pattern) {
    apiCache.clear();
    return;
  }

  // Simple pattern matching - invalidate keys that start with pattern
  for (const key of Array.from(apiCache['cache'].keys())) {
    if (key.startsWith(pattern)) {
      apiCache.delete(key);
    }
  }
}

/**
 * Preload and cache API data
 */
export async function preloadApiData<TParams, TResult>(
  apiFunction: (params: TParams, errorCallback?: (error: ApiError) => void) => Promise<TResult | undefined>,
  paramsList: TParams[],
  options: { ttl?: number } = {}
): Promise<void> {
  const { ttl = 5 * 60 * 1000 } = options;
  
  const promises = paramsList.map(async (params) => {
    try {
      const result = await apiFunction(params);
      if (result !== undefined) {
        const cacheKey = createCacheKey(apiFunction.name, params);
        apiCache.set(cacheKey, result, ttl);
      }
    } catch (error) {
      // Silently ignore preload errors
      if (process.env.NODE_ENV === 'development') {
        console.warn('Preload failed for:', params, error);
      }
    }
  });

  await Promise.allSettled(promises);
}

// Export cache instance for manual operations
export { apiCache }; 