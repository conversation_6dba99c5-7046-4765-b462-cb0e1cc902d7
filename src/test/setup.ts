import '@testing-library/jest-dom'
import { expect, afterEach, vi } from 'vitest'
import { cleanup } from '@testing-library/react'
import './mocks/server'

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup()
})

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock crypto.getRandomValues for security utilities
Object.defineProperty(global, 'crypto', {
  value: {
    getRandomValues: vi.fn().mockImplementation((arr: Uint8Array | Uint32Array) => {
      for (let i = 0; i < arr.length; i++) {
        if (arr instanceof Uint8Array) {
          arr[i] = Math.floor(Math.random() * 256)
        } else if (arr instanceof Uint32Array) {
          arr[i] = Math.floor(Math.random() * 0x100000000)
        }
      }
      return arr
    }),
  },
})

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
})

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
})

// Extend expect with custom matchers
expect.extend({
  toBeValidEmail(received: string) {
    // Use the same secure email regex as in SecurityValidator
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
    const pass = emailRegex.test(received) && received.length <= 254
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid email`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected ${received} to be a valid email`,
        pass: false,
      }
    }
  },
  
  toBeValidPhone(received: string) {
    // Use the same secure phone regex as in SecurityValidator
    const phoneRegex = /^\+?[1-9]\d{1,14}$|^\+?[\d\s\-()]{10,20}$/
    const pass = phoneRegex.test(received) && received.length <= 20
    
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid phone number`,
        pass: true,
      }
    } else {
      return {
        message: () => `expected ${received} to be a valid phone number`,
        pass: false,
      }
    }
  },
})

// Declare custom matchers for TypeScript
declare module 'vitest' {
  interface Assertion<T = unknown> {
    toBeValidEmail(): T
    toBeValidPhone(): T
  }
  interface AsymmetricMatchersContaining {
    toBeValidEmail(): unknown
    toBeValidPhone(): unknown
  }
}
