package com.chidhagni;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Minimal test application class for @WebMvcTest scenarios in ATS.
 *
 * This test application completely bypasses the main application configuration
 * and its @Import dependencies (particularly SecurityConfig.class) to avoid
 * circular dependency issues in test contexts.
 *
 * Key Benefits:
 * - Eliminates @Import(SecurityConfig.class) dependency issues
 * - Avoids circular dependencies with security infrastructure
 * - Provides minimal Spring context for fast controller testing
 * - Enables industry-standard @WebMvcTest with minimal mocking
 * - No component scanning to avoid loading repositories or services
 *
 * Usage:
 * Use this as the test application class in @WebMvcTest annotations:
 * @WebMvcTest(controllers = YourController.class)
 * @ContextConfiguration(classes = TestApplication.class)
 *
 * <AUTHOR> Development Team
 */
@SpringBootApplication(scanBasePackages = {})
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
