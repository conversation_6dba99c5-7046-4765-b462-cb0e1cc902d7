package com.chidhagni.config;

import com.chidhagni.AtsApplication;
import lombok.extern.slf4j.Slf4j;
import org.jooq.DSLContext;
import org.jooq.Table;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.containers.wait.strategy.Wait;

import java.time.Duration;
import java.util.Arrays;
import java.util.Map;

/**
 * Base class for integration tests that provides:
 * - Singleton Testcontainers PostgreSQL setup shared across all test classes
 * - Database schema initialization via Liquibase
 * - Automatic table cleanup between tests
 * - Mock configurations for external services
 * - Optimized connection pool settings for container environment
 */
@SpringBootTest(
    classes = AtsApplication.class,
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT
)
@ActiveProfiles("integration-test")
@Tag("integration")
@Slf4j
public abstract class BaseIntegrationTest {

    /**
     * Singleton PostgreSQL container shared across all test classes.
     * This container is started once and reused for all tests to improve performance
     * and avoid container lifecycle issues.
     */
    private static final PostgreSQLContainer<?> postgres;

    static {
        postgres = new PostgreSQLContainer<>("postgres:15.3")
                .withDatabaseName("testdb")
                .withUsername("test")
                .withPassword("test")
                .withStartupTimeout(Duration.ofMinutes(5))
                .withReuse(false)  // Disable reuse for CI stability
                .withCommand("postgres",
                    "-c", "max_connections=200",           // Increased for multiple test classes
                    "-c", "shared_buffers=256MB",          // Increased for better performance
                    "-c", "log_statement=none",            // Reduce logging for performance
                    "-c", "log_duration=off",              // Reduce logging for performance
                    "-c", "fsync=off",                     // Disable fsync for test performance
                    "-c", "synchronous_commit=off",        // Disable synchronous commit for tests
                    "-c", "max_wal_size=2GB",              // Increased for better performance
                    "-c", "checkpoint_completion_target=0.9",
                    "-c", "wal_buffers=16MB",              // Increased for better performance
                    "-c", "effective_cache_size=512MB")    // Increased for better performance
                .withEnv("POSTGRES_INITDB_ARGS", "--auth-host=trust")
                .withTmpFs(Map.of("/var/lib/postgresql/data", "rw,noexec,nosuid,size=1024m")) // Increased tmpfs size
                .waitingFor(Wait.forListeningPort().withStartupTimeout(Duration.ofMinutes(5)));

        // Start the container once for all test classes
        postgres.start();

        // Add shutdown hook to ensure container is stopped when JVM exits
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (postgres.isRunning()) {
                postgres.stop();
            }
        }));

        log.info("=== PostgreSQL Singleton Container Started ===");
        log.info("Container ID: {}", postgres.getContainerId());
        log.info("JDBC URL: {}", postgres.getJdbcUrl());
        log.info("Mapped Port: {}", postgres.getMappedPort(5432));
        log.info("==============================================");
    }

    @Autowired
    protected DSLContext dslContext;

    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");

        // Enable Liquibase for integration tests
        registry.add("spring.liquibase.enabled", () -> "true");
        registry.add("spring.liquibase.change-log", () -> "classpath:db/changelog/db.changelog-master.yaml");

        // Disable JPA auto-configuration
        registry.add("spring.jpa.hibernate.ddl-auto", () -> "none");

        // Test-specific configurations
        registry.add("app.secure.file.permissions.enabled", () -> "false");
        registry.add("logging.level.org.testcontainers", () -> "INFO");
        registry.add("logging.level.com.github.dockerjava", () -> "WARN");
        registry.add("logging.level.testcontainers", () -> "INFO");

        // Optimized HikariCP connection pool settings for Testcontainers environment
        registry.add("spring.datasource.hikari.maximum-pool-size", () -> "15");           // Increased for multiple test classes
        registry.add("spring.datasource.hikari.minimum-idle", () -> "5");                // Increased minimum idle connections
        registry.add("spring.datasource.hikari.connection-timeout", () -> "60000");      // Increased timeout for container startup
        registry.add("spring.datasource.hikari.idle-timeout", () -> "600000");           // 10 minutes idle timeout
        registry.add("spring.datasource.hikari.max-lifetime", () -> "1800000");          // 30 minutes max lifetime
        registry.add("spring.datasource.hikari.leak-detection-threshold", () -> "60000"); // 1 minute leak detection
        registry.add("spring.datasource.hikari.validation-timeout", () -> "10000");      // 10 seconds validation timeout
        registry.add("spring.datasource.hikari.connection-test-query", () -> "SELECT 1"); // Connection validation query
        registry.add("spring.datasource.hikari.test-while-idle", () -> "true");          // Test connections while idle
        registry.add("spring.datasource.hikari.test-on-borrow", () -> "true");           // Test connections on borrow
        registry.add("spring.datasource.hikari.test-on-return", () -> "false");          // Don't test on return for performance
        registry.add("spring.datasource.hikari.pool-name", () -> "IntegrationTestPool"); // Named pool for debugging
        registry.add("spring.datasource.hikari.register-mbeans", () -> "true");          // Enable JMX monitoring

        // Additional PostgreSQL driver settings for container environment
        registry.add("spring.datasource.hikari.data-source-properties.socketTimeout", () -> "60");
        registry.add("spring.datasource.hikari.data-source-properties.loginTimeout", () -> "60");
        registry.add("spring.datasource.hikari.data-source-properties.connectTimeout", () -> "60");
        registry.add("spring.datasource.hikari.data-source-properties.cancelSignalTimeout", () -> "30");
    }

    /**
     * Get the singleton PostgreSQL container instance.
     * This method provides access to the container for debugging purposes.
     *
     * @return the PostgreSQL container instance
     */
    protected static PostgreSQLContainer<?> getPostgresContainer() {
        return postgres;
    }

    /**
     * Check if the PostgreSQL container is running and healthy.
     * This method performs a lightweight check without attempting to restart the container.
     *
     * @return true if the container is running and accessible, false otherwise
     */
    protected static boolean isContainerHealthy() {
        return postgres.isRunning() && postgres.isCreated();
    }

    /**
     * Clean up database tables after each test with enhanced error handling.
     * This method uses optimized cleanup strategies and proper error handling
     * to ensure test isolation without affecting container stability.
     *
     * Subclasses should override getTable() to specify which tables to clean.
     */
    @AfterEach
    void cleanupDatabase() {
        Table[] tables = getTable();
        if (tables == null || tables.length == 0) {
            return;
        }

        try {
            // Perform cleanup in a single transaction for better performance
            dslContext.transaction(configuration -> {
                DSLContext transactionalContext = configuration.dsl();

                // Clean tables in the order provided (child tables first)
                // The test classes should provide tables in dependency order
                Arrays.stream(tables)
                        .forEach(table -> {
                            try {
                                // Use DELETE for better foreign key constraint handling
                                int deletedRows = transactionalContext.deleteFrom(table).execute();
                                if (deletedRows > 0) {
                                    log.debug("Cleaned {} rows from table: {}", deletedRows, table.getName());
                                }
                            } catch (Exception e) {
                                log.warn("Failed to clean table {}: {}", table.getName(), e.getMessage());
                                // Continue with other tables - don't fail the entire cleanup
                            }
                        });
            });
        } catch (Exception e) {
            log.warn("Database cleanup transaction failed: {}", e.getMessage());
            // Attempt individual table cleanup as fallback
            fallbackCleanup(tables);
        }
    }

    /**
     * Fallback cleanup method that attempts to clean tables individually
     * if the transactional cleanup fails.
     */
    private void fallbackCleanup(Table[] tables) {
        log.warn("Attempting fallback cleanup for {} tables", tables.length);
        Arrays.stream(tables)
                .forEach(table -> {
                    try {
                        dslContext.deleteFrom(table).execute();
                    } catch (Exception e) {
                        log.error("Fallback cleanup failed for table {}: {}", table.getName(), e.getMessage());
                        // Continue with other tables
                    }
                });
    }

    /**
     * Subclasses should override this method to specify which tables need cleanup.
     * Tables will be cleaned in the order specified (child tables first to avoid FK violations).
     *
     * @return Array of JOOQ Table objects to be cleaned up after each test
     */
    protected abstract Table[] getTable();

    /**
     * Utility method to check if the database is accessible.
     * This method performs a lightweight check without affecting container lifecycle.
     *
     * @return true if database is accessible, false otherwise
     */
    protected boolean isDatabaseAccessible() {
        try {
            // Use a simple, fast query to check connectivity
            dslContext.selectOne().fetchOne();
            return true;
        } catch (Exception e) {
            // Log the error but don't print stack trace to reduce noise
            log.debug("Database accessibility check failed: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            return false;
        }
    }

    /**
     * Utility method to get the current database URL for debugging purposes.
     *
     * @return the JDBC URL of the PostgreSQL container
     */
    protected String getDatabaseUrl() {
        return postgres.getJdbcUrl();
    }

    /**
     * Utility method to get container information for debugging purposes.
     *
     * @return formatted string with container details
     */
    protected String getContainerInfo() {
        return String.format("Container[id=%s, running=%s, port=%d, url=%s]",
                postgres.getContainerId(),
                postgres.isRunning(),
                postgres.getMappedPort(5432),
                postgres.getJdbcUrl());
    }
}
