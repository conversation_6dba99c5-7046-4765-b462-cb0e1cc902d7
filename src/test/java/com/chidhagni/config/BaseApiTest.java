package com.chidhagni.config;

import com.chidhagni.TestApplication;
import com.chidhagni.exception.GlobalExceptionHandler;
import com.chidhagni.filter.CorrelationIdFilter;
import org.junit.jupiter.api.Tag;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

import java.lang.annotation.*;

/**
 * Custom meta-annotation that encapsulates all common @WebMvcTest configuration
 * for ATS API test classes. This eliminates code duplication across controller test classes.
 * 
 * Usage:
 * <pre>
 * {@code
 * @BaseApiTest(controllers = YourController.class)
 * @DisplayName("YourController API Tests")
 * class YourControllerApiTest {
 *     // Test methods...
 * }
 * }
 * </pre>
 * 
 * This annotation automatically provides:
 * - Standard @WebMvcTest configuration with common exclusions
 * - TestSecurityConfig integration for basic authentication support
 * - Test property source configuration
 * - API test tagging
 * - Exclusion of common infrastructure components
 * 
 * Benefits:
 * - Reduces ~20 lines of boilerplate to 1 line per test class
 * - Centralizes test configuration management
 * - Ensures consistency across all API test classes
 * - Simplifies maintenance and updates
 * - Provides fast, focused controller testing
 * 
 * <AUTHOR> Development Team
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Inherited
@WebMvcTest(
    excludeAutoConfiguration = {
        org.springframework.boot.autoconfigure.security.oauth2.resource.servlet.OAuth2ResourceServerAutoConfiguration.class,
        org.springframework.boot.autoconfigure.jooq.JooqAutoConfiguration.class,
        org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration.class,
        org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration.class,
        org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration.class,
        org.springframework.boot.actuate.autoconfigure.metrics.MetricsAutoConfiguration.class,
        org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration.class,
        org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration.class,
        org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration.class
    },
    excludeFilters = {
        @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {
            GlobalExceptionHandler.class,
            CorrelationIdFilter.class,
            SecurityConfig.class,
            ThymeleafTemplateConfig.class
        })
    }
)
@ContextConfiguration(classes = {
    TestApplication.class
})
@TestPropertySource(locations = "classpath:application-test.properties")
@Tag("api")
public @interface BaseApiTest {
    
    /**
     * The controller classes to test. This is the only parameter that needs
     * to be specified per test class.
     * 
     * @return array of controller classes to include in the test context
     */
    Class<?>[] controllers() default {};
    
    /**
     * Additional auto-configuration classes to exclude beyond the defaults.
     * Use this if specific test classes need to exclude additional configurations.
     * 
     * @return array of additional auto-configuration classes to exclude
     */
    Class<?>[] additionalExcludeAutoConfiguration() default {};
    
    /**
     * Additional component filters to exclude beyond the defaults.
     * Use this if specific test classes need additional exclusions.
     * 
     * @return array of additional component scan filters
     */
    ComponentScan.Filter[] additionalExcludeFilters() default {};
    
    /**
     * Whether to include security testing support. When true, provides
     * basic authentication and authorization testing capabilities.
     * 
     * @return true to enable security testing support
     */
    boolean includeSecurityTest() default true;
    
    /**
     * Whether to mock external services by default. When true, common
     * external service dependencies are automatically mocked.
     * 
     * @return true to mock external services
     */
    boolean mockExternalServices() default true;
}
