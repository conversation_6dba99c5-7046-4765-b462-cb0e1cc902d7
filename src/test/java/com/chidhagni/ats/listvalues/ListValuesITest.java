package com.chidhagni.ats.listvalues;

import com.chidhagni.ats.db.jooq.tables.daos.ListNamesDao;
import com.chidhagni.ats.db.jooq.tables.daos.ListValuesDao;
import com.chidhagni.ats.db.jooq.tables.pojos.ListNames;
import com.chidhagni.ats.db.jooq.tables.pojos.ListValues;
import com.chidhagni.ats.listvalues.dto.response.GetAllListValuesDTO;
import com.chidhagni.config.BaseIntegrationTest;
import org.jooq.Table;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.UUID;

import static com.chidhagni.ats.db.jooq.Tables.LIST_NAMES;
import static com.chidhagni.ats.db.jooq.Tables.LIST_VALUES;
import static org.assertj.core.api.Assertions.assertThat;

@DisplayName("List Values Service Integration Tests")
@Tag("integration")
@Timeout(value = 2, unit = java.util.concurrent.TimeUnit.MINUTES)
class ListValuesITest extends BaseIntegrationTest {

    @Autowired
    private ListValuesService listValuesService;

    @Autowired
    private ListValuesDao listValuesDao;

    @Autowired
    private ListNamesDao listNamesDao;

    private UUID testListNameId;

    @Override
    protected Table[] getTable() {
        // Order matters if there are foreign key relationships
        return new Table[]{LIST_VALUES, LIST_NAMES};
    }

    @BeforeEach
    void setUp() {
        testListNameId = UUID.randomUUID();

        // 1. Insert into LIST_NAMES first
        ListNames listName = new ListNames();
        listName.setId(testListNameId);
        listName.setName("Test List Name");
        listName.setCreatedOn(LocalDateTime.now());
        listName.setUpdatedOn(LocalDateTime.now());
        listNamesDao.insert(listName);

        // 2. Insert list values referencing the listNamesId
        ListValues lv1 = new ListValues();
        lv1.setId(UUID.randomUUID());
        lv1.setListNamesId(testListNameId);
        lv1.setName("Value 1");
        lv1.setCreatedOn(LocalDateTime.now());
        lv1.setUpdatedOn(LocalDateTime.now());
        listValuesDao.insert(lv1);

        ListValues lv2 = new ListValues();
        lv2.setId(UUID.randomUUID());
        lv2.setListNamesId(testListNameId);
        lv2.setName("Value 2");
        lv2.setCreatedOn(LocalDateTime.now());
        lv2.setUpdatedOn(LocalDateTime.now());
        listValuesDao.insert(lv2);
    }

    @Test
    @DisplayName("Should fetch all list values by listNameId")
    void getAllListValuesById_Success() {
        GetAllListValuesDTO result = listValuesService.getAllListValuesById(testListNameId);

        assertThat(result).isNotNull();
        assertThat(result.getListValues())
                .hasSize(2)
                .extracting("listValue") // matches DTO field
                .containsExactlyInAnyOrder("Value 1", "Value 2");
    }


    @Test
    @DisplayName("Should throw exception for invalid listNameId")
    void getAllListValuesById_InvalidListNameId() {
        UUID invalidId = UUID.randomUUID();

        try {
            listValuesService.getAllListValuesById(invalidId);
        } catch (IllegalArgumentException ex) {
            assertThat(ex).hasMessageContaining("Invalid listNameId");
        }
    }
}
