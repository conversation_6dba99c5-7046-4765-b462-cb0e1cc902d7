import { http, HttpResponse } from 'msw'
import { mockUsers, mockOrganizations } from '@/data/multiTenantData'
import { mockApplicants, mockJobs, mockInterviews } from '@/data/mockData'

// Helper functions for creating mock data
const createMockUser = (overrides = {}) => ({
  id: 'user-1',
  organizationId: 'org-1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  role: 'admin' as const,
  permissions: [],
  title: 'Test User',
  isActive: true,
  lastLogin: '2024-01-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  onboardingCompleted: true,
  ...overrides,
});

const createMockApplicant = (overrides = {}) => ({
  id: 'applicant-1',
  firstName: 'Jane',
  lastName: '<PERSON>',
  email: '<EMAIL>',
  phone: '+1234567890',
  position: 'Software Engineer',
  department: 'Engineering',
  applicationDate: '2024-01-01',
  status: 'applied' as const,
  experience: 5,
  skills: ['React', 'TypeScript', 'Node.js'],
  ...overrides,
});

const createMockJob = (overrides = {}) => ({
  id: 'job-1',
  title: 'Software Engineer',
  department: 'Engineering',
  location: 'Remote',
  type: 'full-time' as const,
  description: 'We are looking for a talented software engineer...',
  requirements: ['5+ years experience', 'React expertise', 'TypeScript knowledge'],
  postedDate: '2024-01-01',
  isActive: true,
  ...overrides,
});

const createMockInterview = (overrides = {}) => ({
  id: 'interview-1',
  candidateId: 'candidate-1',
  date: '2024-01-15',
  time: '10:00',
  interviewer: 'John Doe',
  type: 'video' as const,
  status: 'scheduled' as const,
  ...overrides,
});

// Mock JWT tokens for testing
const generateMockJWT = (user: typeof mockUsers[0], organization: typeof mockOrganizations[0]) => {
  const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
  const payload = btoa(JSON.stringify({
    sub: user.id,
    email: user.email,
    organizationId: organization.id,
    role: user.role,
    permissions: user.permissions,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour
  }));
  const signature = btoa('mock-signature');
  return `${header}.${payload}.${signature}`;
};

export const handlers = [
  // Authentication endpoints
  http.post('/api/auth/login', async ({ request }) => {
    const { email, password } = await request.json() as { email: string; password: string }

    // Find user by email
    const user = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
    const organization = user ? mockOrganizations.find(org => org.id === user.organizationId) : null;

    // Mock password validation (in real app, this would be hashed)
    if (user && organization && password === 'password123') {
      const accessToken = generateMockJWT(user, organization);
      const refreshToken = btoa(`refresh-${user.id}-${Date.now()}`);

      return HttpResponse.json({
        user,
        organization,
        accessToken,
        refreshToken,
      })
    }

    return HttpResponse.json(
      { message: 'Invalid email or password' },
      { status: 401 }
    )
  }),

  http.post('/api/auth/refresh', async ({ request }) => {
    const { refreshToken } = await request.json() as { refreshToken: string }

    if (refreshToken && refreshToken.startsWith('cmVmcmVzaC0=')) { // base64 'refresh-'
      // In real app, validate refresh token
      const user = mockUsers[0];
      const organization = mockOrganizations[0];
      const accessToken = generateMockJWT(user, organization);

      return HttpResponse.json({ accessToken })
    }

    return HttpResponse.json(
      { message: 'Invalid refresh token' },
      { status: 401 }
    )
  }),

  http.post('/api/auth/logout', () => {
    return HttpResponse.json({ success: true })
  }),

  http.get('/api/auth/me', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Mock token validation - in real app, verify JWT
    return HttpResponse.json(mockUsers[0])
  }),

  http.get('/api/auth/organization', ({ request }) => {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Mock token validation - in real app, verify JWT
    return HttpResponse.json(mockOrganizations[0])
  }),

  // User endpoints
  http.get('/api/users', () => {
    return HttpResponse.json(mockUsers)
  }),

  http.get('/api/users/:id', ({ params }) => {
    const user = mockUsers.find(u => u.id === params.id)
    if (!user) {
      return HttpResponse.json({ error: 'User not found' }, { status: 404 })
    }
    return HttpResponse.json(user)
  }),

  http.post('/api/users', async ({ request }) => {
    const userData = await request.json()
    const newUser = createMockUser({ ...userData, id: `user-${Date.now()}` })
    mockUsers.push(newUser)
    return HttpResponse.json(newUser, { status: 201 })
  }),

  // Organization endpoints
  http.get('/api/organizations', () => {
    return HttpResponse.json(mockOrganizations)
  }),

  http.get('/api/organizations/:id', ({ params }) => {
    const org = mockOrganizations.find(o => o.id === params.id)
    if (!org) {
      return HttpResponse.json({ error: 'Organization not found' }, { status: 404 })
    }
    return HttpResponse.json(org)
  }),

  // Candidate endpoints
  http.get('/api/candidates', ({ request }) => {
    const url = new URL(request.url)
    const search = url.searchParams.get('search')
    const status = url.searchParams.get('status')
    
    let filteredCandidates = [...mockApplicants]
    
    if (search) {
      filteredCandidates = filteredCandidates.filter(candidate =>
        candidate.firstName.toLowerCase().includes(search.toLowerCase()) ||
        candidate.lastName.toLowerCase().includes(search.toLowerCase()) ||
        candidate.email.toLowerCase().includes(search.toLowerCase())
      )
    }
    
    if (status) {
      filteredCandidates = filteredCandidates.filter(candidate => candidate.status === status)
    }
    
    return HttpResponse.json(filteredCandidates)
  }),

  http.get('/api/candidates/:id', ({ params }) => {
    const candidate = mockApplicants.find(c => c.id === params.id)
    if (!candidate) {
      return HttpResponse.json({ error: 'Candidate not found' }, { status: 404 })
    }
    return HttpResponse.json(candidate)
  }),

  http.post('/api/candidates', async ({ request }) => {
    const candidateData = await request.json()
    const newCandidate = createMockCandidate({ ...candidateData, id: `candidate-${Date.now()}` })
    mockApplicants.push(newCandidate)
    return HttpResponse.json(newCandidate, { status: 201 })
  }),

  http.put('/api/candidates/:id', async ({ params, request }) => {
    const candidateIndex = mockApplicants.findIndex(c => c.id === params.id)
    if (candidateIndex === -1) {
      return HttpResponse.json({ error: 'Candidate not found' }, { status: 404 })
    }

    const updates = await request.json()
    mockApplicants[candidateIndex] = { ...mockApplicants[candidateIndex], ...updates }
    return HttpResponse.json(mockApplicants[candidateIndex])
  }),

  // Job endpoints
  http.get('/api/jobs', ({ request }) => {
    const url = new URL(request.url)
    const department = url.searchParams.get('department')
    const isActive = url.searchParams.get('isActive')
    
    let filteredJobs = [...mockJobs]
    
    if (department) {
      filteredJobs = filteredJobs.filter(job => job.department === department)
    }
    
    if (isActive !== null) {
      filteredJobs = filteredJobs.filter(job => job.isActive === (isActive === 'true'))
    }
    
    return HttpResponse.json(filteredJobs)
  }),

  http.get('/api/jobs/:id', ({ params }) => {
    const job = mockJobs.find(j => j.id === params.id)
    if (!job) {
      return HttpResponse.json({ error: 'Job not found' }, { status: 404 })
    }
    return HttpResponse.json(job)
  }),

  http.post('/api/jobs', async ({ request }) => {
    const jobData = await request.json()
    const newJob = createMockJob({ ...jobData, id: `job-${Date.now()}` })
    mockJobs.push(newJob)
    return HttpResponse.json(newJob, { status: 201 })
  }),

  // Interview endpoints
  http.get('/api/interviews', ({ request }) => {
    const url = new URL(request.url)
    const candidateId = url.searchParams.get('candidateId')
    const status = url.searchParams.get('status')
    
    let filteredInterviews = [...mockInterviews]
    
    if (candidateId) {
      filteredInterviews = filteredInterviews.filter(interview => interview.candidateId === candidateId)
    }
    
    if (status) {
      filteredInterviews = filteredInterviews.filter(interview => interview.status === status)
    }
    
    return HttpResponse.json(filteredInterviews)
  }),

  http.post('/api/interviews', async ({ request }) => {
    const interviewData = await request.json()
    const newInterview = createMockInterview({ ...interviewData, id: `interview-${Date.now()}` })
    mockInterviews.push(newInterview)
    return HttpResponse.json(newInterview, { status: 201 })
  }),

  http.put('/api/interviews/:id', async ({ params, request }) => {
    const interviewIndex = mockInterviews.findIndex(i => i.id === params.id)
    if (interviewIndex === -1) {
      return HttpResponse.json({ error: 'Interview not found' }, { status: 404 })
    }
    
    const updates = await request.json()
    mockInterviews[interviewIndex] = { ...mockInterviews[interviewIndex], ...updates }
    return HttpResponse.json(mockInterviews[interviewIndex])
  }),

  // Analytics endpoints
  http.get('/api/analytics/dashboard', () => {
    return HttpResponse.json({
      totalCandidates: mockApplicants.length,
      totalJobs: mockJobs.length,
      totalInterviews: mockInterviews.length,
      successRate: 85,
      avgTimeToHire: 21,
    })
  }),

  // File upload endpoint
  http.post('/api/upload', async ({ request }) => {
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return HttpResponse.json({ error: 'No file provided' }, { status: 400 })
    }
    
    // Simulate file upload
    return HttpResponse.json({
      url: `https://example.com/uploads/${file.name}`,
      filename: file.name,
      size: file.size,
      type: file.type,
    })
  }),

  // Error simulation endpoints for testing error handling
  http.get('/api/error/500', () => {
    return HttpResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }),

  http.get('/api/error/timeout', () => {
    return new Promise(() => {
      // Never resolves to simulate timeout
    })
  }),
]
