// Mock data for Form Builder functionality
// Following the existing pattern from mockData.ts and multiTenantData.ts

export interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'select' | 'multiselect' | 'date' | 'textarea' | 'file' | 'number' | 'url';
  label: string;
  placeholder?: string;
  required: boolean;
  description?: string;
  options?: { value: string; label: string }[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
    min?: number;
    max?: number;
  };
  order: number;
}

export interface FormSchema {
  id: string;
  organizationId: string;
  title: string;
  description?: string;
  fields: FormField[];
  settings: {
    allowMultipleSubmissions: boolean;
    requireAuthentication: boolean;
    showProgressBar: boolean;
    customTheme?: {
      primaryColor: string;
      backgroundColor: string;
      fontFamily: string;
    };
    notifications: {
      sendToEmail: string[];
      sendOnSubmission: boolean;
    };
  };
  status: 'draft' | 'published' | 'archived';
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  submissionCount: number;
  shareUrl?: string;
}

export interface FormResponse {
  id: string;
  formId: string;
  responses: Record<string, string | string[] | number | boolean>;
  submittedAt: string;
  submitterEmail?: string;
  submitterName?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface FormTemplate {
  id: string;
  name: string;
  description: string;
  category: 'candidate-screening' | 'job-application' | 'feedback' | 'survey' | 'custom';
  thumbnail: string;
  fields: FormField[];
  usageCount: number;
  tags: string[];
}

// Mock Forms
export const mockForms: FormSchema[] = [
  {
    id: 'form-1',
    organizationId: 'org-1',
    title: 'Senior Developer Application Form',
    description: 'Comprehensive application form for senior developer positions',
    fields: [
      {
        id: 'field-1',
        type: 'text',
        label: 'Full Name',
        placeholder: 'Enter your full name',
        required: true,
        order: 1,
      },
      {
        id: 'field-2',
        type: 'email',
        label: 'Email Address',
        placeholder: '<EMAIL>',
        required: true,
        order: 2,
      },
      {
        id: 'field-3',
        type: 'phone',
        label: 'Phone Number',
        placeholder: '+****************',
        required: true,
        order: 3,
      },
      {
        id: 'field-4',
        type: 'select',
        label: 'Years of Experience',
        required: true,
        options: [
          { value: '0-2', label: '0-2 years' },
          { value: '3-5', label: '3-5 years' },
          { value: '6-10', label: '6-10 years' },
          { value: '10+', label: '10+ years' },
        ],
        order: 4,
      },
      {
        id: 'field-5',
        type: 'multiselect',
        label: 'Technical Skills',
        required: true,
        options: [
          { value: 'react', label: 'React' },
          { value: 'typescript', label: 'TypeScript' },
          { value: 'nodejs', label: 'Node.js' },
          { value: 'python', label: 'Python' },
          { value: 'aws', label: 'AWS' },
          { value: 'docker', label: 'Docker' },
        ],
        order: 5,
      },
      {
        id: 'field-6',
        type: 'textarea',
        label: 'Why are you interested in this position?',
        placeholder: 'Tell us about your motivation...',
        required: true,
        validation: {
          minLength: 50,
          maxLength: 500,
        },
        order: 6,
      },
      {
        id: 'field-7',
        type: 'file',
        label: 'Resume/CV',
        description: 'Please upload your resume in PDF format',
        required: true,
        order: 7,
      },
      {
        id: 'field-8',
        type: 'date',
        label: 'Available Start Date',
        required: false,
        order: 8,
      },
    ],
    settings: {
      allowMultipleSubmissions: false,
      requireAuthentication: false,
      showProgressBar: true,
      notifications: {
        sendToEmail: ['<EMAIL>'],
        sendOnSubmission: true,
      },
    },
    status: 'published',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
    createdBy: 'user-1',
    submissionCount: 23,
    shareUrl: 'https://forms.talentflow.com/f/senior-dev-app',
  },
  {
    id: 'form-2',
    organizationId: 'org-1',
    title: 'Quick Candidate Screening',
    description: 'Short form for initial candidate screening',
    fields: [
      {
        id: 'field-9',
        type: 'text',
        label: 'Full Name',
        required: true,
        order: 1,
      },
      {
        id: 'field-10',
        type: 'email',
        label: 'Email',
        required: true,
        order: 2,
      },
      {
        id: 'field-11',
        type: 'select',
        label: 'Current Location',
        required: true,
        options: [
          { value: 'us', label: 'United States' },
          { value: 'ca', label: 'Canada' },
          { value: 'uk', label: 'United Kingdom' },
          { value: 'other', label: 'Other' },
        ],
        order: 3,
      },
      {
        id: 'field-12',
        type: 'number',
        label: 'Expected Salary (USD)',
        placeholder: '75000',
        required: false,
        validation: {
          min: 30000,
          max: 300000,
        },
        order: 4,
      },
    ],
    settings: {
      allowMultipleSubmissions: true,
      requireAuthentication: false,
      showProgressBar: false,
      notifications: {
        sendToEmail: ['<EMAIL>'],
        sendOnSubmission: true,
      },
    },
    status: 'published',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T11:15:00Z',
    createdBy: 'user-2',
    submissionCount: 45,
    shareUrl: 'https://forms.talentflow.com/f/quick-screening',
  },
  {
    id: 'form-3',
    organizationId: 'org-2',
    title: 'Interview Feedback Form',
    description: 'Post-interview feedback collection',
    fields: [
      {
        id: 'field-13',
        type: 'text',
        label: 'Candidate Name',
        required: true,
        order: 1,
      },
      {
        id: 'field-14',
        type: 'select',
        label: 'Overall Rating',
        required: true,
        options: [
          { value: '5', label: 'Excellent' },
          { value: '4', label: 'Good' },
          { value: '3', label: 'Average' },
          { value: '2', label: 'Below Average' },
          { value: '1', label: 'Poor' },
        ],
        order: 2,
      },
      {
        id: 'field-15',
        type: 'textarea',
        label: 'Detailed Feedback',
        required: true,
        validation: {
          minLength: 20,
        },
        order: 3,
      },
    ],
    settings: {
      allowMultipleSubmissions: false,
      requireAuthentication: true,
      showProgressBar: false,
      notifications: {
        sendToEmail: ['<EMAIL>'],
        sendOnSubmission: true,
      },
    },
    status: 'draft',
    createdAt: '2024-01-22T16:00:00Z',
    updatedAt: '2024-01-22T16:00:00Z',
    createdBy: 'user-3',
    submissionCount: 0,
  },
];

// Mock Form Templates
export const mockFormTemplates: FormTemplate[] = [
  {
    id: 'template-1',
    name: 'Job Application Form',
    description: 'Standard job application with personal info, experience, and file upload',
    category: 'job-application',
    thumbnail: '/images/templates/job-application.png',
    usageCount: 156,
    tags: ['hiring', 'application', 'standard'],
    fields: [
      {
        id: 'temp-field-1',
        type: 'text',
        label: 'Full Name',
        required: true,
        order: 1,
      },
      {
        id: 'temp-field-2',
        type: 'email',
        label: 'Email Address',
        required: true,
        order: 2,
      },
      {
        id: 'temp-field-3',
        type: 'phone',
        label: 'Phone Number',
        required: true,
        order: 3,
      },
      {
        id: 'temp-field-4',
        type: 'file',
        label: 'Resume',
        required: true,
        order: 4,
      },
    ],
  },
  {
    id: 'template-2',
    name: 'Technical Skills Assessment',
    description: 'Evaluate technical competencies and experience levels',
    category: 'candidate-screening',
    thumbnail: '/images/templates/tech-assessment.png',
    usageCount: 89,
    tags: ['technical', 'skills', 'assessment'],
    fields: [
      {
        id: 'temp-field-5',
        type: 'multiselect',
        label: 'Programming Languages',
        required: true,
        options: [
          { value: 'javascript', label: 'JavaScript' },
          { value: 'python', label: 'Python' },
          { value: 'java', label: 'Java' },
          { value: 'csharp', label: 'C#' },
        ],
        order: 1,
      },
      {
        id: 'temp-field-6',
        type: 'select',
        label: 'Experience Level',
        required: true,
        options: [
          { value: 'junior', label: 'Junior (0-2 years)' },
          { value: 'mid', label: 'Mid-level (3-5 years)' },
          { value: 'senior', label: 'Senior (6+ years)' },
        ],
        order: 2,
      },
    ],
  },
];

// Mock Form Responses
export const mockFormResponses: FormResponse[] = [
  {
    id: 'response-1',
    formId: 'form-1',
    responses: {
      'field-1': 'John Smith',
      'field-2': '<EMAIL>',
      'field-3': '+****************',
      'field-4': '6-10',
      'field-5': ['react', 'typescript', 'nodejs'],
      'field-6': 'I am passionate about building scalable web applications and have extensive experience with React and TypeScript. This position aligns perfectly with my career goals.',
      'field-7': 'john-smith-resume.pdf',
      'field-8': '2024-03-01',
    },
    submittedAt: '2024-01-25T14:30:00Z',
    submitterEmail: '<EMAIL>',
    submitterName: 'John Smith',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  {
    id: 'response-2',
    formId: 'form-1',
    responses: {
      'field-1': 'Sarah Johnson',
      'field-2': '<EMAIL>',
      'field-3': '+****************',
      'field-4': '3-5',
      'field-5': ['react', 'python', 'aws'],
      'field-6': 'I have been working as a full-stack developer for 4 years and am excited about the opportunity to work with cutting-edge technologies.',
      'field-7': 'sarah-johnson-cv.pdf',
      'field-8': '2024-02-15',
    },
    submittedAt: '2024-01-26T09:15:00Z',
    submitterEmail: '<EMAIL>',
    submitterName: 'Sarah Johnson',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
  },
];

// Field type definitions for the form builder
export const availableFieldTypes = [
  {
    type: 'text',
    label: 'Text Input',
    icon: 'Type',
    description: 'Single line text input',
  },
  {
    type: 'textarea',
    label: 'Text Area',
    icon: 'AlignLeft',
    description: 'Multi-line text input',
  },
  {
    type: 'email',
    label: 'Email',
    icon: 'Mail',
    description: 'Email address input with validation',
  },
  {
    type: 'phone',
    label: 'Phone',
    icon: 'Phone',
    description: 'Phone number input with formatting',
  },
  {
    type: 'number',
    label: 'Number',
    icon: 'Hash',
    description: 'Numeric input with validation',
  },
  {
    type: 'date',
    label: 'Date',
    icon: 'Calendar',
    description: 'Date picker input',
  },
  {
    type: 'select',
    label: 'Dropdown',
    icon: 'ChevronDown',
    description: 'Single selection dropdown',
  },
  {
    type: 'multiselect',
    label: 'Multi-Select',
    icon: 'CheckSquare',
    description: 'Multiple selection dropdown',
  },
  {
    type: 'file',
    label: 'File Upload',
    icon: 'Upload',
    description: 'File upload with validation',
  },
  {
    type: 'url',
    label: 'URL',
    icon: 'Link',
    description: 'URL input with validation',
  },
] as const;
