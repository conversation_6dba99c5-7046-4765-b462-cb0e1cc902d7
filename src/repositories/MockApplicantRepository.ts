/**
 * Mock implementation of ApplicantRepository
 * 
 * This implementation provides fake data for testing and development
 * when the actual API is not available or when feature flags enable mocking.
 */

import {
  ApplicantListParams,
  GetAllApplicantsResponse,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  PatchApplicantFormData,
  PatchApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  ApiSelectOption,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
} from '@/types/applicants';
import { ApiError } from '@/lib/apiConfig';
import { BaseApplicantRepository, RepositoryConfig } from './ApplicantRepository';
import { generateUUID } from '@/lib/utils';

/**
 * Mock Repository implementation providing fake data
 * Useful for development, testing, and offline scenarios
 */
export class MockApplicantRepository extends BaseApplicantRepository {
  private mockData: Map<string, unknown> = new Map();
  private mockDelay: number = 500; // Simulate network delay

  constructor(repositoryConfig?: RepositoryConfig) {
    super({
      enableCaching: false, // Mocks don't need caching
      enableRetry: false, // Mocks don't need retries
      ...repositoryConfig,
    });

    this.initializeMockData();
    this.log('info', 'Mock Repository initialized');
  }

  private async simulateDelay(): Promise<void> {
    await new Promise(resolve => setTimeout(resolve, this.mockDelay));
  }

  private initializeMockData(): void {
    // Initialize mock applicants
    const mockApplicants: ApplicantDetailResponse[] = [
      {
        id: '1',
        firstName: 'Sarah',
        lastName: 'Johnson',
        email: '<EMAIL>',
        dateOfBirth: '1990-05-15',
        contactInfo: {
          id: generateUUID(),
          homePhone: '+****************',
          mobilePhone: '+****************',
        },
        socialProfiles: {
          id: generateUUID(),
          linkedin: 'https://linkedin.com/in/sarah-johnson',
        },
        addresses: {
          id: generateUUID(),
          country: 'United States',
          state: 'California',
          city: 'San Francisco',
          zipcode: '94105',
          isPrimary: true,
        },
        additionalInfo: {
          id: generateUUID(),
          experience: {
            id: generateUUID(),
            years: '5',
            months: '0',
          },
          jobTitle: 'Senior Frontend Developer',
          skills: ['React', 'TypeScript', 'Node.js', 'AWS'],
          primarySkills: ['React', 'TypeScript'],
        },
        workExperience: [
          {
            id: generateUUID(),
            companyName: 'Tech Corp',
            jobTitle: 'Frontend Developer',
            startDate: '2020-01-01',
            endDate: '2024-01-01',
            location: 'San Francisco, CA',
            employeeType: 'Full-time',
            responsibilities: 'Developed React applications',
          },
        ],
        education: [
          {
            id: generateUUID(),
            schoolName: 'Stanford University',
            degree: 'Computer Science',
            yearCompleted: '2019',
            country: 'United States',
            state: 'California',
            city: 'Stanford',
            isHigherEducation: true,
          },
        ],
        certifications: [
          {
            id: generateUUID(),
            certification: 'AWS Solutions Architect',
            yearCompleted: '2023',
            comments: 'Professional level certification',
          },
        ],
        documents: [
          {
            id: generateUUID(),
            title: 'Resume',
            category: 'resume',
            subcategory: 'professional-resume',
            filePath: '/documents/sarah-johnson-resume.pdf',
            isActive: true,
          },
        ],
        employerDetails: {
          id: generateUUID(),
          selectionType: 'NEW',
          firstName: 'John',
          lastName: 'Manager',
          employerName: 'Tech Corp',
          emailId: '<EMAIL>',
          status: true,
        },
        status: 'active',
        applicationDate: '2024-01-15',
        isActive: true,
      },
    ];

    mockApplicants.forEach(applicant => {
      this.mockData.set(`applicant:${applicant.id}`, applicant);
    });

    // Initialize mock list values
    const mockListValues: ApiSelectOption[] = [
      { value: '1', key: 'React' },
      { value: '2', key: 'Angular' },
      { value: '3', key: 'Vue.js' },
      { value: '4', key: 'TypeScript' },
      { value: '5', key: 'JavaScript' },
    ];
    this.mockData.set('listValues:skills', mockListValues);

    // Initialize mock document tree
    const mockDocumentTree: DocumentTreeResponse = {
      id: 'doc-tree-1',
      name: 'Documents',
      children: [
        {
          id: 'resume',
          name: 'Resume',
          children: [
            { id: 'professional-resume', name: 'Professional Resume' },
            { id: 'academic-resume', name: 'Academic Resume' },
          ],
        },
        {
          id: 'certificates',
          name: 'Certificates',
          children: [
            { id: 'education-cert', name: 'Education Certificate' },
            { id: 'professional-cert', name: 'Professional Certificate' },
          ],
        },
      ],
    };
    this.mockData.set('documentTree:default', mockDocumentTree);
  }

  async listApplicants(
    params?: { query?: ApplicantListParams },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllApplicantsResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const allApplicants = Array.from(this.mockData.entries())
        .filter(([key]) => key.startsWith('applicant:'))
        .map(([, value]) => value as ApplicantDetailResponse);

      // Apply basic filtering
      let filteredApplicants = allApplicants;
      if (params?.query?.search) {
        const searchTerm = params.query.search.toLowerCase();
        filteredApplicants = allApplicants.filter(
          applicant =>
            applicant.personalInfo?.firstName?.toLowerCase().includes(searchTerm) ||
            applicant.personalInfo?.lastName?.toLowerCase().includes(searchTerm) ||
            applicant.personalInfo?.email?.toLowerCase().includes(searchTerm)
        );
      }

      // Apply pagination
      const page = params?.query?.page || 0;
      const pageSize = params?.query?.pageSize || 10;
      const startIndex = page * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedApplicants = filteredApplicants.slice(startIndex, endIndex);

      const response: GetAllApplicantsResponse = {
        items: paginatedApplicants,
        totalElements: filteredApplicants.length,
        totalPages: Math.ceil(filteredApplicants.length / pageSize),
        currentPage: page,
        pageSize: pageSize,
      } as GetAllApplicantsResponse;

      this.log('info', `Mock: Listed ${paginatedApplicants.length} applicants`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to list applicants', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async getApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApplicantDetailResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const applicant = this.mockData.get(`applicant:${params.pathParams.applicantId}`) as ApplicantDetailResponse;
      
      if (!applicant) {
        throw new Error(`Applicant with ID ${params.pathParams.applicantId} not found`);
      }

      this.log('info', `Mock: Retrieved applicant ${params.pathParams.applicantId}`);
      return applicant;
    } catch (error) {
      this.log('error', 'Mock: Failed to get applicant', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async createApplicant(
    params: { body: CreateApplicantRequest },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const newId = generateUUID();
      const newApplicant: ApplicantDetailResponse = {
        ...params.body,
        id: newId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      this.mockData.set(`applicant:${newId}`, newApplicant);

      const response: CreateApplicantResponse = {
        id: newId,
        message: 'Applicant created successfully',
        success: true,
      };

      this.log('info', `Mock: Created applicant ${newId}`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to create applicant', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async createApplicantWithFiles(
    params: CreateApplicantWithFilesRequest,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const newId = generateUUID();
      const newApplicant: ApplicantDetailResponse = {
        ...params.applicantData,
        id: newId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Simulate file processing
      if (params.files && params.files.length > 0) {
        newApplicant.documents = params.files.map((file, index) => ({
          id: generateUUID(),
          fileIndex: index,
          title: file.title,
          category: 'default-category',
          subcategory: file.type,
          comments: file.comments,
          filePath: `/mock/files/${file.file.name}`,
          isActive: true,
        }));
      }

      this.mockData.set(`applicant:${newId}`, newApplicant);

      const response: CreateApplicantResponse = {
        id: newId,
        message: 'Applicant with files created successfully',
        success: true,
      };

      this.log('info', `Mock: Created applicant with files ${newId}`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to create applicant with files', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async updateApplicant(
    params: { pathParams: { applicantId: string }; body: PatchApplicantFormData },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<PatchApplicantResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const existingApplicant = this.mockData.get(`applicant:${params.pathParams.applicantId}`) as ApplicantDetailResponse;
      
      if (!existingApplicant) {
        throw new Error(`Applicant with ID ${params.pathParams.applicantId} not found`);
      }

      // Merge the patch data
      const patchData = typeof params.body.patchData === 'string' 
        ? JSON.parse(params.body.patchData) 
        : params.body.patchData;

      const updatedApplicant: ApplicantDetailResponse = {
        ...existingApplicant,
        ...patchData,
        id: params.pathParams.applicantId, // Preserve the original ID
        updatedAt: new Date().toISOString(),
      };

      this.mockData.set(`applicant:${params.pathParams.applicantId}`, updatedApplicant);

      const response: PatchApplicantResponse = {
        id: params.pathParams.applicantId,
        message: 'Applicant updated successfully',
        success: true,
      };

      this.log('info', `Mock: Updated applicant ${params.pathParams.applicantId}`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to update applicant', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async deleteApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DeleteApplicantResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const existingApplicant = this.mockData.get(`applicant:${params.pathParams.applicantId}`) as ApplicantDetailResponse;
      
      if (!existingApplicant) {
        throw new Error(`Applicant with ID ${params.pathParams.applicantId} not found`);
      }

      // Soft delete by marking as inactive
      const deletedApplicant: ApplicantDetailResponse = {
        ...existingApplicant,
        isActive: false,
        updatedAt: new Date().toISOString(),
      };

      this.mockData.set(`applicant:${params.pathParams.applicantId}`, deletedApplicant);

      const response: DeleteApplicantResponse = {
        id: params.pathParams.applicantId,
        message: 'Applicant deactivated successfully',
        success: true,
      };

      this.log('info', `Mock: Deleted applicant ${params.pathParams.applicantId}`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to delete applicant', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async activateApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ActivateApplicantResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const existingApplicant = this.mockData.get(`applicant:${params.pathParams.applicantId}`) as ApplicantDetailResponse;
      
      if (!existingApplicant) {
        throw new Error(`Applicant with ID ${params.pathParams.applicantId} not found`);
      }

      // Activate by marking as active
      const activatedApplicant: ApplicantDetailResponse = {
        ...existingApplicant,
        isActive: true,
        updatedAt: new Date().toISOString(),
      };

      this.mockData.set(`applicant:${params.pathParams.applicantId}`, activatedApplicant);

      const response: ActivateApplicantResponse = {
        id: params.pathParams.applicantId,
        message: 'Applicant activated successfully',
        success: true,
      };

      this.log('info', `Mock: Activated applicant ${params.pathParams.applicantId}`);
      return response;
    } catch (error) {
      this.log('error', 'Mock: Failed to activate applicant', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async getListValuesById(
    listNameId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApiSelectOption[] | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const listValues = this.mockData.get(`listValues:${listNameId}`) as ApiSelectOption[];
      
      if (!listValues) {
        // Return empty array for unknown list IDs
        this.log('info', `Mock: No list values found for ${listNameId}`);
        return [];
      }

      this.log('info', `Mock: Retrieved ${listValues.length} list values for ${listNameId}`);
      return listValues;
    } catch (error) {
      this.log('error', 'Mock: Failed to get list values', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async getDocumentTree(
    resourceId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DocumentTreeResponse | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const documentTree = this.mockData.get(`documentTree:${resourceId}`) as DocumentTreeResponse;
      
      if (!documentTree) {
        // Return default document tree for unknown resource IDs
        const defaultTree: DocumentTreeResponse = {
          id: resourceId,
          name: 'Default Documents',
          children: [
            {
              id: 'resume',
              name: 'Resume',
              children: []
            },
            {
              id: 'cover-letter',
              name: 'Cover Letter',
              children: []
            }
          ]
        };
        
        this.log('info', `Mock: Returned default document tree for ${resourceId}`);
        return defaultTree;
      }

      this.log('info', `Mock: Retrieved document tree for ${resourceId}`);
      return documentTree;
    } catch (error) {
      this.log('error', 'Mock: Failed to get document tree', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  async getAllListValues(
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllGlobalListValuesDTO | undefined> {
    await this.simulateDelay();

    // Check if request was aborted
    if (signal?.aborted) {
      throw new Error('Request aborted');
    }

    try {
      const allListValues = this.mockData.get('allListValues') as GetAllGlobalListValuesDTO;
      
      if (!allListValues) {
        // Return default list values
        const defaultListValues: GetAllGlobalListValuesDTO = [
          { id: 'experience-level', name: 'Experience Level' },
          { id: 'employment-type', name: 'Employment Type' },
          { id: 'education-level', name: 'Education Level' },
          { id: 'skills', name: 'Skills' },
        ];
        
        this.log('info', 'Mock: Returned default all list values');
        return defaultListValues;
      }

      this.log('info', `Mock: Retrieved ${allListValues.length} global list values`);
      return allListValues;
    } catch (error) {
      this.log('error', 'Mock: Failed to get all list values', error);
      if (errorCallback) {
        errorCallback(error as ApiError);
      }
      return undefined;
    }
  }

  // Mock-specific utilities
  public setMockDelay(delay: number): void {
    this.mockDelay = delay;
  }

  public addMockApplicant(applicant: ApplicantDetailResponse): void {
    this.mockData.set(`applicant:${applicant.id}`, applicant);
  }

  public clearMockData(): void {
    this.mockData.clear();
    this.initializeMockData();
  }
} 