/**
 * Repository Module Exports
 * 
 * This module provides a central export point for all repository-related
 * classes, interfaces, and utilities.
 */

// Core repository interfaces and base classes
export {
  type ApplicantRepository,
  type RepositoryConfig,
  type RepositoryProvider,
  BaseApplicantRepository,
  RepositoryFactory,
  createRepositoryProvider,
} from './ApplicantRepository';

// Concrete repository implementations
export { ApiApplicantRepository } from './ApiApplicantRepository';
export { MockApplicantRepository } from './MockApplicantRepository';

// Repository-based hooks
export {
  useApplicantRepository,
  useListApplicants,
  useGetApplicant,
  useCreateApplicant,
  useCreateApplicantWithFiles,
  useUpdateApplicant,
  useDeleteApplicant,
  useActivateApplicant,
  useGetListValues,
  useGetDocumentTree,
  useGetAllListValues,
  useApplicantOperations,
  useRepositoryManager,
} from '../hooks/useApplicantsRepository';

// Re-export configuration for convenience
export { config, isFeatureEnabled, getApiConfig } from '../config/appConfig'; 