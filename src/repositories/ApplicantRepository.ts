/**
 * Repository Pattern Implementation for Applicant Management
 * 
 * This module provides a clean abstraction layer between components and data sources,
 * supporting both API and mock implementations with identical interfaces.
 */

import {
  ApplicantListParams,
  GetAllApplicantsResponse,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  PatchApplicantFormData,
  PatchApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  GetAllListValuesDTO,
  ApiSelectOption,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
} from '@/types/applicants';
import { ApiError } from '@/lib/apiConfig';

/**
 * Repository interface defining the contract for applicant data operations
 * This interface ensures both API and mock implementations provide the same methods
 */
export interface ApplicantRepository {
  // Core CRUD operations
  listApplicants(
    params?: { query?: ApplicantListParams },
    errorCallback?: (error: ApiError) => void
  ): Promise<GetAllApplicantsResponse | undefined>;

  getApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void
  ): Promise<ApplicantDetailResponse | undefined>;

  createApplicant(
    params: { body: CreateApplicantRequest },
    errorCallback?: (error: ApiError) => void
  ): Promise<CreateApplicantResponse | undefined>;

  createApplicantWithFiles(
    params: CreateApplicantWithFilesRequest,
    errorCallback?: (error: ApiError) => void
  ): Promise<CreateApplicantResponse | undefined>;

  updateApplicant(
    params: { pathParams: { applicantId: string }; body: PatchApplicantFormData },
    errorCallback?: (error: ApiError) => void
  ): Promise<PatchApplicantResponse | undefined>;

  deleteApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void
  ): Promise<DeleteApplicantResponse | undefined>;

  activateApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void
  ): Promise<ActivateApplicantResponse | undefined>;

  // Utility operations
  getListValuesById(
    listNameId: string,
    errorCallback?: (error: ApiError) => void
  ): Promise<ApiSelectOption[] | undefined>;

  getDocumentTree(
    resourceId: string,
    errorCallback?: (error: ApiError) => void
  ): Promise<DocumentTreeResponse | undefined>;

  getAllListValues(
    errorCallback?: (error: ApiError) => void
  ): Promise<GetAllGlobalListValuesDTO | undefined>;
}

/**
 * Configuration interface for repository implementations
 */
export interface RepositoryConfig {
  enableCaching?: boolean;
  cacheTimeout?: number;
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

/**
 * Base repository class with common functionality
 */
export abstract class BaseApplicantRepository implements ApplicantRepository {
  protected config: RepositoryConfig;

  constructor(config: RepositoryConfig = {}) {
    this.config = {
      enableCaching: false,
      cacheTimeout: 300000, // 5 minutes
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      ...config,
    };
  }

  // Abstract methods to be implemented by concrete classes
  abstract listApplicants(
    params?: { query?: ApplicantListParams },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllApplicantsResponse | undefined>;

  abstract getApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApplicantDetailResponse | undefined>;

  abstract createApplicant(
    params: { body: CreateApplicantRequest },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined>;

  abstract createApplicantWithFiles(
    params: CreateApplicantWithFilesRequest,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined>;

  abstract updateApplicant(
    params: { pathParams: { applicantId: string }; body: PatchApplicantFormData },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<PatchApplicantResponse | undefined>;

  abstract deleteApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DeleteApplicantResponse | undefined>;

  abstract activateApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ActivateApplicantResponse | undefined>;

  abstract getListValuesById(
    listNameId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApiSelectOption[] | undefined>;

  abstract getDocumentTree(
    resourceId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DocumentTreeResponse | undefined>;

  abstract getAllListValues(
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllGlobalListValuesDTO | undefined>;

  // Common utility methods
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.maxRetries || 3
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay || 1000));
      }
    }
    
    throw lastError!;
  }

  protected log(level: 'info' | 'warn' | 'error', message: string, data?: unknown): void {
    if (process.env.NODE_ENV === 'development') {
      console[level](`[ApplicantRepository] ${message}`, data);
    }
  }
}

/**
 * Repository factory for creating appropriate repository instances
 */
export class RepositoryFactory {
  private static instance: ApplicantRepository | null = null;

  static createRepository(type: 'api' | 'mock' = 'api', config?: RepositoryConfig): ApplicantRepository {
    switch (type) {
      case 'api':
        // Lazy import to avoid circular dependencies
        return import('./ApiApplicantRepository').then(module => 
          new module.ApiApplicantRepository(config)
        ) as Promise<ApplicantRepository>;
      case 'mock':
        return import('./MockApplicantRepository').then(module => 
          new module.MockApplicantRepository(config)
        ) as Promise<ApplicantRepository>;
      default:
        throw new Error(`Unknown repository type: ${type}`);
    }
  }

  static getInstance(type?: 'api' | 'mock', config?: RepositoryConfig): ApplicantRepository {
    if (!this.instance) {
      this.instance = this.createRepository(type, config);
    }
    return this.instance;
  }

  static resetInstance(): void {
    this.instance = null;
  }
}

/**
 * Repository provider hook for dependency injection
 * This can be used in React context or dependency injection frameworks
 */
export interface RepositoryProvider {
  applicantRepository: ApplicantRepository;
}

export function createRepositoryProvider(
  type: 'api' | 'mock' = 'api',
  config?: RepositoryConfig
): RepositoryProvider {
  return {
    applicantRepository: RepositoryFactory.createRepository(type, config),
  };
} 