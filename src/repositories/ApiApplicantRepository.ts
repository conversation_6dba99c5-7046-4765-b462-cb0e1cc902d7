/**
 * API-based implementation of ApplicantRepository
 * 
 * This implementation wraps the existing API functions to provide
 * a clean repository interface while maintaining all existing functionality.
 */

import {
  ApplicantListParams,
  GetAllApplicantsResponse,
  ApplicantDetailResponse,
  CreateApplicantRequest,
  CreateApplicantWithFilesRequest,
  CreateApplicantResponse,
  PatchApplicantFormData,
  PatchApplicantResponse,
  DeleteApplicantResponse,
  ActivateApplicantResponse,
  ApiSelectOption,
  DocumentTreeResponse,
  GetAllGlobalListValuesDTO,
} from '@/types/applicants';
import { ApiError } from '@/lib/apiConfig';
import { config } from '@/config/appConfig';
import { BaseApplicantRepository, RepositoryConfig } from './ApplicantRepository';

// Import existing API functions
import * as applicantsApi from '@/api/applicantsApi';

/**
 * API Repository implementation using the refactored API layer
 * Provides caching, retry logic, and performance monitoring capabilities
 */
export class ApiApplicantRepository extends BaseApplicantRepository {
  private cache: Map<string, { data: unknown; timestamp: number }> = new Map();

  constructor(repositoryConfig?: RepositoryConfig) {
    super({
      enableCaching: config.performance.enableCaching,
      cacheTimeout: config.performance.cacheTimeout,
      enableRetry: true,
      maxRetries: config.api.maxRetries,
      retryDelay: config.api.retryDelay,
      ...repositoryConfig,
    });

    this.log('info', 'API Repository initialized', {
      caching: this.config.enableCaching,
      retries: this.config.maxRetries,
    });
  }

  async listApplicants(
    params?: { query?: ApplicantListParams },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllApplicantsResponse | undefined> {
    const cacheKey = `listApplicants:${JSON.stringify(params)}`;
    
    // Check cache first (skip if request is aborted)
    if (this.config.enableCaching && !signal?.aborted) {
      const cached = this.getFromCache<GetAllApplicantsResponse>(cacheKey);
      if (cached) {
        this.log('info', 'Cache hit for listApplicants');
        return cached;
      }
    }

    const operation = () => applicantsApi.listApplicants(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Cache successful results (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      this.log('error', 'Failed to list applicants', error);
      throw error;
    }
  }

  async getApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApplicantDetailResponse | undefined> {
    const cacheKey = `getApplicant:${params.pathParams.applicantId}`;
    
    // Check cache first (skip if request is aborted)
    if (this.config.enableCaching && !signal?.aborted) {
      const cached = this.getFromCache<ApplicantDetailResponse>(cacheKey);
      if (cached) {
        this.log('info', 'Cache hit for getApplicant');
        return cached;
      }
    }

    const operation = () => applicantsApi.getApplicant(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Cache successful results (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      this.log('error', 'Failed to get applicant', error);
      throw error;
    }
  }

  async createApplicant(
    params: { body: CreateApplicantRequest },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined> {
    const operation = () => applicantsApi.createApplicant(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Invalidate list cache after successful creation
      if (result && this.config.enableCaching) {
        this.invalidateListCache();
      }

      this.log('info', 'Applicant created successfully');
      return result;
    } catch (error) {
      this.log('error', 'Failed to create applicant', error);
      throw error;
    }
  }

  async createApplicantWithFiles(
    params: CreateApplicantWithFilesRequest,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<CreateApplicantResponse | undefined> {
    const operation = () => applicantsApi.createApplicantWithFiles(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Invalidate list cache after successful creation (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.invalidateListCache();
      }

      this.log('info', 'Applicant with files created successfully');
      return result;
    } catch (error) {
      this.log('error', 'Failed to create applicant with files', error);
      throw error;
    }
  }

  async updateApplicant(
    params: { pathParams: { applicantId: string }; body: PatchApplicantFormData },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<PatchApplicantResponse | undefined> {
    const operation = () => applicantsApi.patchApplicantWithFiles(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Invalidate caches after successful update (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.invalidateApplicantCache(params.pathParams.applicantId);
        this.invalidateListCache();
      }

      this.log('info', 'Applicant updated successfully');
      return result;
    } catch (error) {
      this.log('error', 'Failed to update applicant', error);
      throw error;
    }
  }

  async deleteApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DeleteApplicantResponse | undefined> {
    const operation = () => applicantsApi.deleteApplicant(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Invalidate caches after successful deletion (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.invalidateApplicantCache(params.pathParams.applicantId);
        this.invalidateListCache();
      }

      this.log('info', 'Applicant deleted successfully');
      return result;
    } catch (error) {
      this.log('error', 'Failed to delete applicant', error);
      throw error;
    }
  }

  async activateApplicant(
    params: { pathParams: { applicantId: string } },
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ActivateApplicantResponse | undefined> {
    const operation = () => applicantsApi.activateApplicant(params, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Invalidate caches after successful activation (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.invalidateApplicantCache(params.pathParams.applicantId);
        this.invalidateListCache();
      }

      this.log('info', 'Applicant activated successfully');
      return result;
    } catch (error) {
      this.log('error', 'Failed to activate applicant', error);
      throw error;
    }
  }

  async getListValuesById(
    listNameId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<ApiSelectOption[] | undefined> {
    const cacheKey = `listValues:${listNameId}`;
    
    // Check cache first (skip if request is aborted)
    if (this.config.enableCaching && !signal?.aborted) {
      const cached = this.getFromCache<ApiSelectOption[]>(cacheKey);
      if (cached) {
        this.log('info', 'Cache hit for getListValuesById');
        return cached;
      }
    }

    const operation = () => applicantsApi.getListValuesById(listNameId, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Cache successful results (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      this.log('error', 'Failed to get list values', error);
      throw error;
    }
  }

  async getDocumentTree(
    resourceId: string,
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<DocumentTreeResponse | undefined> {
    const cacheKey = `documentTree:${resourceId}`;
    
    // Check cache first (skip if request is aborted)
    if (this.config.enableCaching && !signal?.aborted) {
      const cached = this.getFromCache<DocumentTreeResponse>(cacheKey);
      if (cached) {
        this.log('info', 'Cache hit for getDocumentTree');
        return cached;
      }
    }

    const operation = () => applicantsApi.getDocumentTree(resourceId, errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Cache successful results (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      this.log('error', 'Failed to get document tree', error);
      throw error;
    }
  }

  async getAllListValues(
    errorCallback?: (error: ApiError) => void,
    signal?: AbortSignal
  ): Promise<GetAllGlobalListValuesDTO | undefined> {
    const cacheKey = 'allListValues';
    
    // Check cache first (skip if request is aborted)
    if (this.config.enableCaching && !signal?.aborted) {
      const cached = this.getFromCache<GetAllGlobalListValuesDTO>(cacheKey);
      if (cached) {
        this.log('info', 'Cache hit for getAllListValues');
        return cached;
      }
    }

    const operation = () => applicantsApi.getAllListValues(errorCallback, signal);
    
    try {
      const result = this.config.enableRetry 
        ? await this.withRetry(operation)
        : await operation();

      // Cache successful results (skip if request was aborted)
      if (result && this.config.enableCaching && !signal?.aborted) {
        this.setCache(cacheKey, result);
      }

      return result;
    } catch (error) {
      this.log('error', 'Failed to get all list values', error);
      throw error;
    }
  }

  // Cache management methods
  private getFromCache<T>(key: string): T | null {
    if (!this.config.enableCaching) return null;
    
    const cached = this.cache.get(key);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > (this.config.cacheTimeout || 300000);
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data as T;
  }

  private setCache(key: string, data: unknown, customTimeout?: number): void {
    if (!this.config.enableCaching) return;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
    
    // Set custom expiration if provided
    if (customTimeout) {
      setTimeout(() => {
        this.cache.delete(key);
      }, customTimeout);
    }
  }

  private invalidateApplicantCache(applicantId: string): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.includes(`getApplicant:${applicantId}`)
    );
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  private invalidateListCache(): void {
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.startsWith('listApplicants:')
    );
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  // Public cache management
  public clearCache(): void {
    this.cache.clear();
    this.log('info', 'Cache cleared');
  }

  public getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
} 