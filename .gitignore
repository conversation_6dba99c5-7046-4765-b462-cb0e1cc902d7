node_modules/
dist/
build/
out/
bin/
obj/
*.o
*.exe
*.log
logs/

.DS_Store
.idea/
*.iml
.vscode/
.vscode/extensions.json
.temp/
*.tmp
coverage/
CMakeFiles/
CMakeCache.txt
yarn-error.log
npm-debug.log
*~
*.bak
*.swp
Thumbs.db
ehthumbs.db
Desktop.ini

*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.sass-cache/
.vite_opt_cache/
test-results/
*.coverage
*.lcov
*.user
*.code-workspace
# pnpm-lock.yaml - Keep this file for dependency consistency
*.pyc
__pycache__

# MGX/MetaGPTX development tools
.MGXTools/

# Environment filess
.env
.env.local
.env.*
.env.example
.env.dev-local
.env.template

