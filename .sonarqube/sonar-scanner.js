const scanner = require('sonarqube-scanner');
const path = require('path');

/**
 * SonarQube Scanner configuration for TalentFlow ATS Platform
 * This script provides programmatic access to SonarQube analysis
 */

const projectRoot = path.resolve(__dirname, '..');

const scannerOptions = {
  serverUrl: process.env.SONAR_HOST_URL ?? 'http://localhost:9002',
  token: process.env.SONAR_TOKEN ?? process.env.SONAR_LOGIN,
  options: {
    'sonar.projectKey': 'talentflow-ats-frontend',
    'sonar.projectName': 'TalentFlow ATS Frontend',
    'sonar.projectVersion': process.env.npm_package_version ?? '1.0.0',
    'sonar.projectDescription': 'Multi-Tenant Applicant Tracking System with client-vendor collaboration capabilities',
    'sonar.sources': 'src',
    'sonar.sourceEncoding': 'UTF-8',
    'sonar.javascript.lcov.reportPaths': 'coverage/lcov.info',
    'sonar.typescript.lcov.reportPaths': 'coverage/lcov.info',
    'sonar.coverage.exclusions': [
      '**/*.test.ts',
      '**/*.test.tsx',
      '**/*.spec.ts',
      '**/*.spec.tsx',
      'src/test/**/*',
      '**/__tests__/**/*',
      '**/*.stories.tsx',
      '**/*.config.*'
    ].join(','),
    'sonar.tests': 'src',
    'sonar.test.inclusions': [
      '**/*.test.ts',
      '**/*.test.tsx',
      '**/*.spec.ts',
      '**/*.spec.tsx',
      'src/__tests__/**/*',
      'src/test/**/*'
    ].join(','),
    'sonar.exclusions': [
      'node_modules/**/*',
      'dist/**/*',
      'build/**/*',
      'coverage/**/*',
      'public/**/*',
      '**/*.min.js',
      '**/*.bundle.js',
      '**/*.config.js',
      '**/*.config.ts',
      'vite.config.ts',
      'tailwind.config.ts',
      'postcss.config.js',
      'eslint.config.js',
      '**/*.d.ts',
      'src/vite-env.d.ts',
      '**/*.stories.tsx',
      '**/*.stories.ts',
      'pnpm-lock.yaml',
      'package-lock.json',
      'yarn.lock'
    ].join(','),
    'sonar.cpd.exclusions': [
      '**/*.test.ts',
      '**/*.test.tsx',
      '**/*.spec.ts',
      '**/*.spec.tsx',
      'src/components/ui/**/*',
      '**/*.stories.tsx'
    ].join(','),
    'sonar.analysis.mode': 'publish',
    'sonar.scm.provider': 'git',
    'sonar.scm.forceReloadAll': 'true',
    'sonar.qualitygate.wait': 'true',
    'sonar.javascript.file.suffixes': '.js,.jsx',
    'sonar.typescript.file.suffixes': '.ts,.tsx',
    'sonar.css.file.suffixes': '.css,.scss,.sass,.less',
    'sonar.javascript.environments': 'browser,node,jest,vitest',
    'sonar.typescript.tsconfigPath': 'tsconfig.json',
    'sonar.log.level': process.env.SONAR_LOG_LEVEL ?? 'INFO'
  }
};

// Add branch analysis if running in CI/CD
if (process.env.CI) {
  if (process.env.GITHUB_HEAD_REF) {
    // Pull Request
    scannerOptions.options['sonar.pullrequest.key'] = process.env.GITHUB_HEAD_REF;
    scannerOptions.options['sonar.pullrequest.branch'] = process.env.GITHUB_HEAD_REF;
    scannerOptions.options['sonar.pullrequest.base'] = process.env.GITHUB_BASE_REF ?? 'main';
  } else if (process.env.GITHUB_REF_NAME) {
    // Branch analysis
    scannerOptions.options['sonar.branch.name'] = process.env.GITHUB_REF_NAME;
  }
}

console.log('🔍 Starting SonarQube analysis for TalentFlow ATS Platform...');
console.log(`📊 Server URL: ${scannerOptions.serverUrl}`);
console.log(`🎯 Project Key: ${scannerOptions.options['sonar.projectKey']}`);
console.log(`📁 Source Directory: ${scannerOptions.options['sonar.sources']}`);

scanner(
  scannerOptions,
  (result) => {
    if (result.status === 'SUCCESS') {
      console.log('✅ SonarQube analysis completed successfully!');
      console.log(`📈 View results at: ${scannerOptions.serverUrl}/dashboard?id=${scannerOptions.options['sonar.projectKey']}`);
    } else {
      console.error('❌ SonarQube analysis failed:', result);
      process.exit(1);
    }
  }
);
