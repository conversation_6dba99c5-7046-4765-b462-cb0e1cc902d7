plugins {
	id 'java'
	id 'org.springframework.boot' version '3.5.5'
	id 'io.spring.dependency-management' version '1.1.7'
	id 'nu.studer.jooq' version '8.2.1'
	id 'org.sonarqube' version '5.1.0.4882'
	id 'jacoco'
}


ext {
	jooqVersion = '3.18.7'  // Use a stable version that's compatible with the plugin
}

group = 'com.chidhagni'
version = '0.0.1-SNAPSHOT'
description = 'Application Tracking System (ATS)'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	// ========================================
	// SPRING BOOT STARTERS
	// ========================================
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-jooq'
	implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-thymeleaf'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'org.thymeleaf.extras:thymeleaf-extras-springsecurity6'
	// Thymeleaf Layout Dialect for advanced templating
	implementation 'nz.net.ultraq.thymeleaf:thymeleaf-layout-dialect'

	// ========================================
	// DATABASE DEPENDENCIES
	// ========================================
	// PostgreSQL driver
	runtimeOnly 'org.postgresql:postgresql'
	// Database migration with Liquibase (using compatible version similar to Project Houzer)
	implementation 'org.liquibase:liquibase-core:4.20.0'
	// Connection pooling
	implementation 'com.zaxxer:HikariCP'
	// JOOQ for type-safe SQL queries (using consistent versions)
	implementation "org.jooq:jooq:${jooqVersion}"
	implementation "org.jooq:jooq-meta:${jooqVersion}"
	implementation "org.jooq:jooq-codegen:${jooqVersion}"
	// JOOQ code generation dependency
	jooqGenerator 'org.postgresql:postgresql'

	// ========================================
	// UTILITY LIBRARIES
	// ========================================
	// Lombok for reducing boilerplate code
	compileOnly 'org.projectlombok:lombok'
	annotationProcessor 'org.projectlombok:lombok'
	testCompileOnly 'org.projectlombok:lombok'
	testAnnotationProcessor 'org.projectlombok:lombok'

	// Apache Commons utilities
	implementation 'org.apache.commons:commons-lang3'

	// JSON processing
	implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'

	// ========================================
	// OBSERVABILITY DEPENDENCIES
	// ========================================
	// Spring Boot Actuator for metrics and health endpoints
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	// Micrometer for metrics
	implementation 'io.micrometer:micrometer-registry-prometheus'
	// OpenTelemetry for tracing
	implementation platform('io.opentelemetry:opentelemetry-bom:1.35.0')
	implementation 'io.opentelemetry:opentelemetry-api'
	implementation 'io.opentelemetry:opentelemetry-sdk'
	implementation 'io.opentelemetry:opentelemetry-exporter-otlp'
	implementation 'io.opentelemetry:opentelemetry-sdk-extension-autoconfigure'
	// Micrometer OpenTelemetry bridge
	implementation 'io.micrometer:micrometer-tracing-bridge-otel'

	// ========================================
	// FILE STORAGE DEPENDENCIES (MinIO & DigitalOcean Spaces)
	// ========================================
	// MinIO client for object storage
	implementation 'io.minio:minio:8.2.0'

	// AWS SDK for DigitalOcean Spaces (S3-compatible)
	implementation 'software.amazon.awssdk:s3:2.21.29'
	implementation 'software.amazon.awssdk:auth:2.21.29'

	// ========================================
	// API DOCUMENTATION DEPENDENCIES
	// ========================================
	// SpringDoc OpenAPI for Swagger documentation (compatible with Spring Boot 3.5.5)
	implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:2.7.0"

	// ========================================
	// TEST DEPENDENCIES
	// ========================================
	// Spring Boot Test starter (includes JUnit 5, Mockito, AssertJ, etc.)
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	// Spring Security Test
	testImplementation 'org.springframework.security:spring-security-test'
	// Testcontainers for integration testing
	testImplementation 'org.springframework.boot:spring-boot-testcontainers'
	testImplementation 'org.testcontainers:junit-jupiter'
	testImplementation 'org.testcontainers:postgresql'
	// JUnit Platform Launcher
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	// H2 for unit tests (optional)
	testRuntimeOnly 'com.h2database:h2'

    // MapStruct library dependency
    implementation 'org.mapstruct:mapstruct:1.5.5.Final'

    // MapStruct annotation processor for code generation
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.5.Final'


}

// ========================================
// JOOQ CONFIGURATION
// ========================================
jooq {
	configurations {
		main {
			generateSchemaSourceOnCompilation = false
			generationTool {
				onError = 'LOG'
				jdbc {
					driver = 'org.postgresql.Driver'
					url = '***************************************'
					user = 'ats_user'
					password = 'ats_password'
					properties {
						property {
							key = 'useSSL'
							value = 'false'
						}
					}
				}
				generator {
					name = 'org.jooq.codegen.DefaultGenerator'
					database {

						forcedTypes {
							forcedType {
								userType = "com.chidhagni.ats.applicant.dto.request.ContactInfoDTO"
								converter = "com.chidhagni.ats.applicant.jooq.ContactInfoJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.contact_info)"
							}
							forcedType {
								userType = "com.chidhagni.ats.applicant.dto.request.SocialProfilesDTO"
								converter = "com.chidhagni.ats.applicant.jooq.SocialProfilesJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.social_profiles)"
							}
							forcedType {
								userType = "com.chidhagni.ats.applicant.dto.request.AddressesDTO"
								converter = "com.chidhagni.ats.applicant.jooq.AddressesJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.addresses)"
							}
							forcedType {
								userType = "com.chidhagni.ats.applicant.dto.request.EmployerDetailsDTO"
								converter = "com.chidhagni.ats.applicant.jooq.EmployerDetailsJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.employer_details)"
							}
							forcedType {
								userType = "java.util.List<com.chidhagni.ats.applicant.dto.request.WorkExperienceDTO>"
								converter = "com.chidhagni.ats.applicant.jooq.WorkExperienceJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.work_experience)"
							}
							forcedType {
								userType = "java.util.List<com.chidhagni.ats.applicant.dto.request.EducationDTO>"
								converter = "com.chidhagni.ats.applicant.jooq.EducationJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.education)"
							}
							forcedType {
								userType = "java.util.List<com.chidhagni.ats.applicant.dto.request.CertificationsDTO>"
								converter = "com.chidhagni.ats.applicant.jooq.CertificationsJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.certifications)"
							}
							forcedType {
								userType = "com.chidhagni.ats.applicant.dto.request.AdditionalInfoDTO"
								converter = "com.chidhagni.ats.applicant.jooq.AdditionalInfoJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.additional_info)"
							}
							forcedType {
								userType = "java.util.List<com.chidhagni.ats.applicant.dto.request.DocumentDTO>"
								converter = "com.chidhagni.ats.applicant.jooq.DocumentJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(applicant\\.documents)"
							}
							forcedType {
								userType = "java.util.List<com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO>"
								converter = "com.chidhagni.ats.documentrepo.jooq.DocumentRepoRecipientJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(document_repo\\.recipients)"
							}
							forcedType {
								userType = "com.chidhagni.ats.documentrepo.dto.request.ParticipantDetailsDTO"
								converter = "com.chidhagni.ats.documentrepo.jooq.DocumentRepoSenderJsonConverter"
								includeTypes = "JSONB"
								includeExpression = "(?i)([A-Z_-]+\\.)*(document_repo\\.sender)"
							}
						}
						name = 'org.jooq.meta.postgres.PostgresDatabase'
						inputSchema = 'public'
						outputSchemaToDefault = true
						// Exclude Liquibase and system tables from code generation
						excludes = 'DATABASECHANGELOG|DATABASECHANGELOGLOCK|SHEDLOCK'
					}
					generate {
						relations = false
						deprecated = false
						records = true
						pojos = true
						daos = true
						springAnnotations = true
						javaTimeTypes = true
						fluentSetters = true
						pojosEqualsAndHashCode = true
					}
					target {
						packageName = 'com.chidhagni.ats.db.jooq'
						directory = 'src/generated-db-entities/java/'
					}
				}
			}
		}
	}
}


// ========================================
// TEST CONFIGURATION
// ========================================
tasks.named('test') {
	useJUnitPlatform()
	// Enable JaCoCo test coverage
	finalizedBy jacocoTestReport
}

// ========================================
// JACOCO CONFIGURATION
// ========================================
jacoco {
	toolVersion = "0.8.11"
}

jacocoTestReport {
	dependsOn test
	reports {
		xml.required = true
		html.required = true
		csv.required = false
	}
	afterEvaluate {
		classDirectories.setFrom(files(classDirectories.files.collect {
			fileTree(dir: it, exclude: [
				'com/chidhagni/ats/db/jooq/**',  // Exclude JOOQ generated code
				'**/*Config.class',              // Exclude configuration classes
				'**/*Configuration.class',       // Exclude configuration classes
				'**/*Application.class',         // Exclude main application class
				'**/*DTO.class',                 // Exclude DTO classes
				'**/*Dto.class',                 // Exclude Dto classes
				'**/dto/**/*.class'              // Exclude all classes in dto packages
			])
		}))
	}
}

// ========================================
// SONARQUBE CONFIGURATION
// ========================================
sonarqube {
	properties {
		property "sonar.projectKey", "ats-spring-backend"
		property "sonar.projectName", "Application Tracking System"
		property "sonar.projectVersion", version
		property "sonar.host.url", System.getenv("SONAR_HOST_URL") ?: "http://localhost:9000"
		property "sonar.sources", "src/main/java"
		property "sonar.tests", "src/test/java"
		property "sonar.java.binaries", "build/classes/java/main"
		property "sonar.java.source", "21"
		property "sonar.java.target", "21"
		property "sonar.sourceEncoding", "UTF-8"
		property "sonar.coverage.jacoco.xmlReportPaths", "build/reports/jacoco/test/jacocoTestReport.xml"

		// Exclusions
		property "sonar.exclusions", "src/generated/**,**/*DTO.java,**/*Dto.java,**/dto/**"
		property "sonar.coverage.exclusions", "src/generated/**,**/*DTO.java,**/*Dto.java,**/dto/**,**/*Config.java,**/*Configuration.java,**/*Application.class"
		property "sonar.cpd.exclusions", "src/generated/**,**/*DTO.java,**/*Dto.java,**/dto/**"
	}
}

// ========================================
// SOURCE SETS CONFIGURATION
// ========================================
sourceSets {
	main {
		java {
			srcDirs 'src/generated-db-entities/java'
		}
	}
}

// ========================================
// TEST CONFIGURATION
// ========================================

// Test logging configuration
tasks.withType(Test) {
	testLogging.showStandardStreams = true
	afterTest { desc, result ->
		println "Executing test ${desc.name} [${desc.className}] with result: ${result.resultType}"
	}
	testLogging {
		events "passed", "skipped", "failed"
	}
}

// ========================================
// OPTIMIZED TEST TASKS CONFIGURATION
// ========================================

task unitTest(type: Test) {
	description = 'Runs unit tests only (tagged with @Tag("unit"))'
	group = 'verification'

	useJUnitPlatform {
		includeTags 'unit'
	}
	maxParallelForks = Runtime.runtime.availableProcessors()
	include '**/*UTest.class'
	failFast = true
	filter {
		setFailOnNoMatchingTests(false)
	}

	// Generate JaCoCo execution data (report generated separately)
	jacoco {
		destinationFile = layout.buildDirectory.file("jacoco/unitTest.exec").get().asFile
	}

	doLast {
		println "✅ Unit tests completed. Run './gradlew jacocoTestReport' to generate coverage report."
	}
}

task integrationTest(type: Test) {
	description = 'Runs integration tests only (tagged with @Tag("integration"))'
	group = 'verification'

	useJUnitPlatform {
		includeTags 'integration'
	}
	include '**/*ITest.class'
	failFast = true
	filter {
		setFailOnNoMatchingTests(false)
	}

	// Generate JaCoCo execution data (report generated separately)
	jacoco {
		destinationFile = layout.buildDirectory.file("jacoco/integrationTest.exec").get().asFile
	}

	doLast {
		println "✅ Integration tests completed. Run './gradlew jacocoTestReport' to generate coverage report."
	}
}

task apiTest(type: Test) {
	description = 'Runs API tests only (tagged with @Tag("api"))'
	group = 'verification'

	useJUnitPlatform {
		includeTags 'api'
	}
	include '**/*ApiTest.class'
	failFast = true
	filter {
		setFailOnNoMatchingTests(false)
	}

	// Generate JaCoCo execution data (report generated separately)
	jacoco {
		destinationFile = layout.buildDirectory.file("jacoco/apiTest.exec").get().asFile
	}

	doLast {
		println "✅ API tests completed. Run './gradlew jacocoTestReport' to generate coverage report."
	}
}

// ========================================
// COMBINED TEST TASKS WITH COVERAGE
// ========================================

task unitTestsWithCoverage {
	description = 'Runs unit tests and generates coverage report'
	group = 'verification'

	dependsOn unitTest
	finalizedBy jacocoTestReport

	doLast {
		println "✅ Unit tests with coverage completed. Report: build/reports/jacoco/test/index.html"
	}
}

task integrationTestsWithCoverage {
	description = 'Runs integration tests and generates coverage report'
	group = 'verification'

	dependsOn integrationTest
	finalizedBy jacocoTestReport

	doLast {
		println "✅ Integration tests with coverage completed. Report: build/reports/jacoco/integrationTest/index.html"
	}
}

task apiTestsWithCoverage {
	description = 'Runs API tests and generates coverage report'
	group = 'verification'

	dependsOn apiTest
	finalizedBy jacocoTestReport

	doLast {
		println "✅ API tests with coverage completed. Report: build/reports/jacoco/test/index.html"
	}
}

task allTestsWithCoverage {
	description = 'Runs all tests (unit, integration, API) and generates combined coverage report'
	group = 'verification'

	dependsOn unitTest, integrationTest, apiTest
	finalizedBy jacocoTestReport

	doLast {
		println "✅ All tests with coverage completed. Report: build/reports/jacoco/test/index.html"
	}
}

// ========================================
// GRADLE TASK CUSTOMIZATIONS
// ========================================

// Custom task to generate JOOQ code (explicit generation only)
tasks.register('jooqGen') {
	dependsOn 'generateJooq'
	description = 'Generates JOOQ classes from database schema'
	group = 'build'
}

bootJar {
    archiveFileName = 'app.jar'
}