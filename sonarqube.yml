version: '3.8'

# SonarQube Docker Compose Configuration for ATS Application
# This file sets up SonarQube for code quality analysis with PostgreSQL backend
services:
  sonarqube:
    image: sonarqube:25.8.0.112029-community
    container_name: ats-sonarqube
    depends_on:
      - sonarqubedb
    environment:
      SONAR_JDBC_URL: ****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_logs:/opt/sonarqube/logs
    ports:
      - "9002:9000"  # Access SonarQube on http://localhost:9002
    ulimits:
      memlock:
        soft: -1
        hard: -1

  sonarqubedb:
    image: postgres:16-alpine
    container_name: ats-sonarqube-db
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - postgresql_sonarqube:/var/lib/postgresql
      - postgresql_sonarqube_data:/var/lib/postgresql/data

volumes:
  sonarqube_data:
  sonarqube_extensions:
  sonarqube_logs:
  postgresql_sonarqube:
  postgresql_sonarqube_data:


# Usage Instructions:
# 1. Start SonarQube: docker-compose -f sonarqube.yml up -d
# 2. Wait for startup (check logs): docker-compose -f sonarqube.yml logs -f sonarqube
# 3. Access SonarQube: http://localhost:9000
# 4. Default credentials: admin/admin (change on first login)
# 5. Create a project and generate a token for your ATS application
# 6. Run analysis: ./gradlew sonarqube -Dsonar.token=YOUR_TOKEN
# 7. Stop SonarQube: docker-compose -f sonarqube.yml down
