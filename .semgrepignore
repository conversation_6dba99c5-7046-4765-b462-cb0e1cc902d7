# Semgrep ignore file
# This file contains patterns for files and rules to ignore during Semgrep analysis

# Build and dependency directories
build/
target/
node_modules/
.gradle/
gradle/
gradlew
gradlew.bat

# Docker files (often contain intentional patterns that trigger false positives)
Dockerfile
docker-compose.yml
docker/

# Configuration files
*.properties
*.yml
*.yaml

# Generated files
src/generated-db-entities/

# Test files (uncomment if you want to skip security analysis on tests)
# src/test/

# Documentation
docs/
*.md
README*

# Logs and temporary files
*.log
*.tmp
*.temp

# IDE files
.idea/
.vscode/
*.iml

# OS files
.DS_Store
Thumbs.db
