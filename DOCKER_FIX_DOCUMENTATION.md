# Docker Build Fix Documentation

## Issue Description

The CI/CD pipeline was failing during the Docker image build process with the following error:

```
Step 13/20 : COPY --from=builder /app/build/libs/*SNAPSHOT.jar /app/app.jar
COPY failed: no source files were specified
Error: Process completed with exit code 1.
```

## Root Cause Analysis

The issue was caused by a mismatch between the expected JAR file name pattern in the Dockerfile and the actual JAR file name generated by the Gradle build process.

### Expected vs Actual JAR File Names

- **Dockerfile Expected**: `*SNAPSHOT.jar` (wildcard pattern)
- **Actual JAR File**: `app.jar` (configured in `build.gradle`)

### Key Configuration in build.gradle

The `build.gradle` file contains a custom `bootJar` configuration that explicitly sets the JAR file name:

```gradle
bootJar {
    archiveFileName = 'app.jar'
}
```

This configuration overrides the default Spring Boot JAR naming convention, which would typically create a file named `ats-spring-backend-0.0.1-SNAPSHOT.jar`.

## Solution Implemented

### 1. Dockerfile Modification

**Before (Line 47):**
```dockerfile
COPY --from=builder /app/build/libs/*SNAPSHOT.jar /app/app.jar
```

**After (Line 46):**
```dockerfile
COPY --from=builder /app/build/libs/app.jar /app/app.jar
```

### 2. Updated Comments

The comment was also updated to reflect the actual configuration:

**Before:**
```dockerfile
# ✅ Copy JAR and rename to app.jar (safe version)
# This assumes only one JAR is produced in build/libs
```

**After:**
```dockerfile
# ✅ Copy JAR file (build.gradle configures bootJar to create app.jar)
```

## Testing and Verification

### Local Docker Build Test

The fix was tested locally using the following commands:

```bash
# Build the Docker image
docker build -t ats-spring-backend:test .

# Verify image creation
docker images ats-spring-backend:test

# Test container startup
docker run --rm -d --name ats-test-new -p 8081:8080 ats-spring-backend:test

# Check application logs
docker logs ats-test-new
```

### Test Results

✅ **Docker Build**: Successful (completed in 18.1s)
✅ **Image Creation**: Successful (785MB image created)
✅ **Container Startup**: Successful (Spring Boot application started)
✅ **JAR File Copy**: Successful (confirmed in build logs)

### Build Output Confirmation

The successful COPY operation was confirmed in the Docker build output:
```
=> [stage-1 6/7] COPY --from=builder /app/build/libs/app.jar /app/app.jar     7.8s
```

### Application Startup Verification

The container started successfully and showed proper Spring Boot initialization:
```
:: Spring Boot ::                (v3.5.5)

2025-09-03 08:32:09.539 [main] INFO  [] [] com.chidhagni.AtsApplication - Starting AtsApplication v0.0.1-SNAPSHOT using Java 21.0.8 with PID 7 (/app/app.jar started by ats in /app)
2025-09-03 08:32:09.543 [main] INFO  [] [] com.chidhagni.AtsApplication - The following 1 profile is active: "docker"
```

## Files Modified

1. **Dockerfile** - Updated COPY command and comments (lines 45-46)

## Impact

- ✅ CI/CD pipeline Docker build will now succeed
- ✅ No impact on application functionality
- ✅ Maintains existing Docker image structure and security configurations
- ✅ Preserves multi-stage build optimization

## Prevention

To prevent similar issues in the future:

1. **Consistency Check**: Ensure Dockerfile COPY commands match the actual build output file names
2. **Build Configuration Review**: When modifying `bootJar` configuration in `build.gradle`, update corresponding Dockerfile references
3. **Local Testing**: Always test Docker builds locally before pushing to CI/CD pipeline

## Related Configuration

### Gradle Configuration (build.gradle)
- Project version: `0.0.1-SNAPSHOT`
- Custom JAR name: `app.jar` (via `bootJar.archiveFileName`)
- Build directory: `build/libs/`

### Docker Configuration
- Base images: `gradle:8.5-jdk21-alpine` (builder), `eclipse-temurin:21-jre` (runtime)
- Application port: `8080`
- Spring profile: `docker`
- User: `ats` (non-root for security)

## Conclusion

The Docker build issue has been successfully resolved by aligning the Dockerfile COPY command with the actual JAR file name generated by the Gradle build process. The fix has been tested locally and confirmed to work correctly.
