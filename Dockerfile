# ========================================
# DOCKERFILE FOR ATS APPLICATION
# ========================================
# Multi-stage build for optimized production image

# ========================================
# BUILD STAGE
# ========================================
FROM gradle:8.5-jdk21-alpine AS builder

WORKDIR /app

# Copy Gradle files for dependency caching
COPY build.gradle settings.gradle ./
COPY gradle/ gradle/

RUN gradle dependencies --no-daemon

# Copy source code
COPY src/ src/

# Build application (skip tests for faster builds)
RUN gradle clean build -x test --no-daemon

# ========================================
# RUNTIME STAGE
# ========================================
FROM eclipse-temurin:21-jre

# Install necessary packages
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create application user for security
RUN groupadd -r ats && useradd -r -g ats ats

# Set working directory
WORKDIR /app

# Create directories
RUN mkdir -p /app/logs /app/uploads && \
    chown -R ats:ats /app

# ✅ Copy JAR file (build.gradle configures bootJar to create app.jar)
COPY --from=builder /app/build/libs/app.jar /app/app.jar

RUN chown -R ats:ats /app

# Switch to application user
USER ats

# Expose application port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/ats/actuator/health || exit 1

# Environment variables
ENV SPRING_PROFILES_ACTIVE=docker
ENV JAVA_OPTS="-Xmx512m -Xms256m"

ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar /app/app.jar"]
