# Stage 1: Builder
FROM node:18-alpine AS builder

# Install pnpm - use exact version from package.json
RUN corepack enable && corepack prepare pnpm@8.10.0 --activate

# Set working directory
WORKDIR /app

# Set up pnpm store
RUN pnpm config set store-dir /app/.pnpm-store

# Copy package files
COPY package.json pnpm-lock.yaml ./

ARG BUILD_ENV=development
ENV NODE_ENV=${BUILD_ENV}

# Copy source files
COPY . .

# Clean install dependencies
RUN pnpm install --force

# Build the application
RUN pnpm build --mode ${BUILD_ENV}

# Stage 2: Production
FROM nginx:alpine

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Copy built files from builder stage
COPY --from=builder /app/dist /usr/share/nginx/html

# Expose port 5173
EXPOSE 5173

# Add healthcheck
HEALTHCHECK --interval=30s --timeout=3s \
    CMD wget -q --spider http://localhost:5173/health || exit 1