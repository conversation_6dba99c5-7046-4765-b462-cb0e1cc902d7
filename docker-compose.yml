# Docker Compose configuration for ATS (Application Tracking System)
# Complete development setup with PostgreSQL database and MinIO object storage

volumes:
  ats_db_data:
  ats_minio_data:

networks:
  ats-network:
    driver: bridge

services:
  # PostgreSQL Database Service
  # This is the main database for the ATS application
  ats-db:
    image: postgres:15-alpine
    container_name: ats-postgres
    restart: unless-stopped
    environment:
      # Database configuration matching your requirements
      POSTGRES_DB: ats_db
      POSTGRES_USER: ats_user
      POSTGRES_PASSWORD: ats_password
      # Additional PostgreSQL configurations for better performance
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      # Expose on port 5433 to avoid conflicts with local PostgreSQL installations
      - "5433:5432"
    volumes:
      # No persistent volume - fresh database on each startup (like Project Houzer)
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    networks:
      - ats-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ats_user -d ats_db"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100

  minio:
    image: "minio/minio"
    command: ["server", "/data", "--console-address", ":9001"]
    restart: always
    environment:
      MINIO_ROOT_USER: minio
      MINIO_ROOT_PASSWORD: minio123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - "ats_minio_data:/data"
    stdin_open: true
    tty: true
    networks:
      - ats-network

