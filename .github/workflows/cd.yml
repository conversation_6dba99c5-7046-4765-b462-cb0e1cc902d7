
name: Complete CD Pipeline

on:
  push:
    branches: 
      main

jobs:
  sonar:
    uses: ./.github/workflows/sonarqube-analysis.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  semgrep:
    needs: sonar
    uses: ./.github/workflows/semgrep-scan.yml
    secrets:
      SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}
  docker:
    needs: semgrep
    uses: ./.github/workflows/docker-push.yml 
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  gitops-deploy:
    needs: docker
    uses: ./.github/workflows/deploy-gitops.yml
    secrets:
      GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}