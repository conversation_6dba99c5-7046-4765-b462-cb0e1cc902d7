name: CD Pipeline - ATS-Backend

on:
  push:
    branches:
      - main

jobs:
  build-test:
    uses: ./.github/workflows/build-test.yml
    with:
      timeout_minutes: 20

  sonarqube:
    needs: build-test
    uses: ./.github/workflows/sonarqube-analysis.yml
    with:
      sonar_host_url: "http://**************:9100/"
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  semgrep-analysis:
    needs: build-test
    uses: ./.github/workflows/semgrep-analysis.yml
    secrets:
      SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}

  docker-build-and-push:
    needs: [sonarqube, semgrep-analysis]
    uses: ./.github/workflows/docker-build-push.yml
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

  cd-gitops-deploy:
    needs: docker-build-and-push
    uses: ./.github/workflows/gitops-deploy.yml
    secrets:
      DB_USER: ${{ secrets.DB_USER }}
      DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      DB_HOST: ${{ secrets.DB_HOST }}
      DB_PORT: ${{ secrets.DB_PORT }}
      DB_NAME: ${{ secrets.DB_NAME }}
      SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}
      DO_BUCKET_URL: ${{ secrets.DO_BUCKET_URL }}
      DO_BUCKET_NAME: ${{ secrets.DO_BUCKET_NAME }}
      DO_ACCESS_KEY_NAME: ${{ secrets.DO_ACCESS_KEY_NAME }}
      DO_ACCESS_KEY_ID: ${{ secrets.DO_ACCESS_KEY_ID }}
      DO_SECRET_KEY: ${{ secrets.DO_SECRET_KEY }}
      DO_REGION: ${{ secrets.DO_REGION }}
      DO_ENDPOINT: ${{ secrets.DO_ENDPOINT }}
      FILE_STORE_PROVIDER: ${{ secrets.FILE_STORE_PROVIDER }}
