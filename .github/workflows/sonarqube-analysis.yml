name: Reusable SonarQube Analysis Workflow

on:
  workflow_call:
    inputs:
      sonar_host_url:
        description: 'SonarQube server URL'
        required: true
        type: string
    secrets:
      SONAR_TOKEN:
        description: 'SonarQube authentication token'
        required: true

jobs:
  sonarqube:
    name: SonarQube Analysis
    runs-on: [self-hosted, linux]
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Make gradlew executable
        run: chmod +x ./gradlew
      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      - name: Download Jacoco Report
        uses: actions/download-artifact@v4
        with:
          name: jacoco-report
          path: build/reports/jacoco/test/
        continue-on-error: true
      - name: Analyze with SonarQube
        env:
          SONAR_HOST_URL: ${{ inputs.sonar_host_url }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          # Ensure the JaCoCo report directory exists
          mkdir -p build/reports/jacoco/test

          echo "=== SonarQube Analysis Configuration ==="
          echo "Note: Running with temporary configuration that disables coverage requirements"
          echo "TODO: Re-enable coverage requirements once comprehensive tests are implemented"
          echo "SonarQube Host: $SONAR_HOST_URL"
          echo "Project Key: ats-spring-backend"

          # Verify SONAR_TOKEN is set (without exposing it)
          if [ -z "$SONAR_TOKEN" ]; then
            echo "ERROR: SONAR_TOKEN is not set!"
            exit 1
          else
            echo "SONAR_TOKEN is configured (length: ${#SONAR_TOKEN} characters)"
          fi

          # Check if JaCoCo report was downloaded successfully
          if [ ! -f "build/reports/jacoco/test/jacocoTestReport.xml" ]; then
            echo "JaCoCo report not found, generating empty coverage report for SonarQube..."
            # Create minimal JaCoCo report structure for SonarQube (even if no tests exist)
            ./gradlew compileJava
            # Generate empty JaCoCo report
            ./gradlew jacocoTestReport || echo "JaCoCo report generation completed (may be empty due to no tests)"
          else
            echo "JaCoCo report found, proceeding with SonarQube analysis..."
            ./gradlew compileJava
          fi

          echo "=== Starting SonarQube Analysis ==="
          echo "If this fails with authorization error, check:"
          echo "1. Project 'ats-spring-backend' exists on SonarQube server"
          echo "2. SONAR_TOKEN has correct permissions"
          echo "3. Token is not expired"

          # Run SonarQube analysis with detailed error handling
          if ./gradlew sonar -Dsonar.verbose=true --info; then
            echo "=== SonarQube Analysis Completed Successfully ==="
          else
            echo "=== SonarQube Analysis Failed ==="
            echo "Common solutions:"
            echo "1. Create project 'ats-spring-backend' on SonarQube server manually"
            echo "2. Verify SONAR_TOKEN has 'Execute Analysis' permission"
            echo "3. Check if token belongs to user with project creation rights"
            echo "4. Verify SonarQube server is accessible at: $SONAR_HOST_URL"
            exit 1
          fi
