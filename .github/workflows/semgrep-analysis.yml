name: Reusable Semgrep Static Analysis Workflow

on:
  workflow_call:
    secrets:
      SERVER_PASSWORD:
        description: 'VM server SSH password'
        required: true

jobs:
  semgrep:
    name: Run Semgrep on Dedicated Server
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install sshpass
        run: |
          sudo apt-get update
          sudo apt-get install -y sshpass

      - name: Setup SSH known_hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H ************** >> ~/.ssh/known_hosts

      - name: Prepare source code for transfer
        run: |
          mkdir -p /tmp/semgrep-source
          rsync -av --exclude='.git' --exclude='node_modules' --exclude='target' --exclude='build' --exclude='.gradle' --exclude='*.log' . /tmp/semgrep-source/
          if [ ! -f /tmp/semgrep-source/.semgrepignore ]; then
            echo "Dockerfile" > /tmp/semgrep-source/.semgrepignore
            echo "docker-compose.yml" >> /tmp/semgrep-source/.semgrepignore
          else
            echo "Dockerfile" >> /tmp/semgrep-source/.semgrepignore
            echo "docker-compose.yml" >> /tmp/semgrep-source/.semgrepignore
          fi
          cd /tmp
          tar -czf semgrep-source.tar.gz semgrep-source/

      - name: Transfer source code to Semgrep server
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" scp -o StrictHostKeyChecking=no /tmp/semgrep-source.tar.gz root@**************:/tmp/
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" ssh -o StrictHostKeyChecking=no root@************** "
            cd /root/devsecops-tools/semgrep
            rm -rf source/*
            cd /tmp
            tar -xzf semgrep-source.tar.gz
            mv semgrep-source/* /root/devsecops-tools/semgrep/source/
            rm -rf semgrep-source
            rm -f semgrep-source.tar.gz
          "

      - name: Run Semgrep scan using Docker Compose
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" ssh -o StrictHostKeyChecking=no root@************** "
            cd /root/devsecops-tools/semgrep
            rm -f results/results.json
            if command -v docker-compose &> /dev/null; then
              docker-compose up --remove-orphans
            elif command -v docker &> /dev/null && docker compose version &> /dev/null; then
              docker compose up --remove-orphans
            else
              echo 'Error: Neither docker-compose nor docker compose found'
              exit 1
            fi
            if [ -f results/results.json ]; then
              TIMESTAMP=\$(date +%Y%m%d_%H%M%S)
              cp results/results.json results/results_\${TIMESTAMP}.json
            else
              echo 'Error: Semgrep scan did not produce results.json'
              exit 1
            fi
          "

      - name: Download Semgrep results
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" scp -o StrictHostKeyChecking=no root@**************:/root/devsecops-tools/semgrep/results/results.json ./results.json

      - name: Upload Semgrep Results as Artifact
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-results
          path: results.json
          if-no-files-found: warn
          compression-level: 6
          overwrite: false
          include-hidden-files: false

      - name: Install jq
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Fail if Semgrep found blocking issues
        run: |
          if jq '.results[] | select(.severity == "WARNING" or .severity == "ERROR")' results.json | grep -q .; then
            echo "Semgrep found blocking issues! Failing the workflow."
            echo "--- Semgrep Findings (first 100 lines) ---"
            jq '.results[] | select(.severity == "WARNING" or .severity == "ERROR")' results.json | head -100
            echo "------------------------"
            exit 1
          else
            echo "No blocking Semgrep issues found."
          fi

      - name: Cleanup temporary files
        if: always()
        run: |
          rm -rf /tmp/semgrep-source*