name: GitOps Deployment Trigger

on:
  workflow_call:
    secrets:
      GITOPS_TOKEN:
        required: true

jobs:
  gitops-deploy:
    runs-on: [self-hosted, Linux]
    steps:
      - name: Prepare Secrets
        id: secrets
        run: |
          SECRETS_JSON=$(cat << EOF | base64 -w 0
          {
            "CONTAINER_PORT": 5173,
            "HEALTH_CHECK_PATH": "/",
            "API_URL": "https://devapi.ats.hrhighwaves.com"
          }
          EOF
          )
          echo "secrets_encoded=$SECRETS_JSON" >> $GITHUB_OUTPUT
      - name: 🚀 Trigger GitOps Deployment
        uses: actions/github-script@v7
        env:
          SECRETS_ENCODED: ${{ steps.secrets.outputs.secrets_encoded }}
        with:
          github-token: ${{ secrets.GITOPS_TOKEN }}
          script: |
            console.log('🚀 testing Triggering GitOps deployment for TalentFlow ATS React Frontend application...');
            console.log('Branch:', '${{ github.ref_name }}');
            console.log('Event:', '${{ github.event_name }}');
            const secretsEncoded = process.env.SECRETS_ENCODED || '';
            // Determine environment based on branch
            const dockerTag = 'latest';
            console.log('Using universal Docker image tag:', dockerTag);
            let environment = 'dev';
            // Docker tag is always 'latest' - no validation needed
            const payload = {
              project_id: 'talentflow-ats-frontend',
              application_type: 'react-frontend',
              environment: environment,
              docker_image: 'registry.digitalocean.com/chidhagni-container-registry/talentflow-ats-frontend',
              host_name: 'dev.ats.hrhighwaves.com',
              docker_tag: dockerTag,
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              secrets_encoded: secretsEncoded || ''
            };
            console.log('📦 Dispatch payload for TalentFlow ATS react-frontend:', JSON.stringify(payload, null, 2));
            // Validate payload before sending
            const requiredFields = ['project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }
            // Validate project_id format (must be lowercase alphanumeric with hyphens)
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }
            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });
              console.log('✅ GitOps deployment triggered successfully!');
              console.log('📱 App: TalentFlow ATS Frontend (talentflow-ats-frontend)');
              console.log(`🌍 Environment: ${environment}`);
              console.log(`🐳 Universal Docker image: registry.digitalocean.com/chidhagni-container-registry/talentflow-ats-frontend:${dockerTag}`);
              console.log('⚙️ Runtime config: Backend URL will be configured via ConfigMap');
              console.log('🌿 Source branch: ${{ github.ref_name }}');
              console.log(`📝 Commit SHA: ${context.sha}`);
              console.log('🔗 Monitor deployment: https://github.com/ChidhagniConsulting/gitops-argocd-apps/actions');
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              console.error('Error details:', error.message);
              if (error.response) {
                console.error('Response status:', error.response.status);
                console.error('Response data:', error.response.data);
              }
              throw error;
            }