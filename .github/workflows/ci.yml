name: CI Pipeline - ATS-Backend

on:
  pull_request:
    branches:
      - main

jobs:
   build-test:
     uses: ./.github/workflows/build-test.yml
     with:
       timeout_minutes: 20

   sonarqube:
     needs: build-test
     uses: ./.github/workflows/sonarqube-analysis.yml
     with:
       sonar_host_url: "http://**************:9100/"
     secrets:
       SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

   semgrep-analysis:
     needs: build-test
     uses: ./.github/workflows/semgrep-analysis.yml
     secrets:
       SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}