name: CI Pipeline

on:
  pull_request:
    branches: [main]

jobs:
  test_and_build:
    uses: ./.github/workflows/pnpm-build-and-test.yml

  sonarqube:
    needs: test_and_build
    uses: ./.github/workflows/sonarqube-analysis.yml
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  semgrep:
    needs: sonarqube
    uses: ./.github/workflows/semgrep-scan.yml
    secrets:
      SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}
      SEMGREP_SERVER_HOST: ${{ secrets.SEMGREP_SERVER_HOST }}

  