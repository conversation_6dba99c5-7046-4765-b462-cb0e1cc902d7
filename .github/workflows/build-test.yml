name: Reusable Build and Test Workflow

on:
  workflow_call:
    inputs:
      timeout_minutes:
        description: 'Timeout in minutes for the test execution step'
        required: false
        type: number
        default: 20

jobs:
  build-test:
    name: Build and Test
    runs-on: [self-hosted, linux]
    services:
      docker:
        image: docker:24.0-dind
        options: --privileged --volume /var/run/docker.sock:/var/run/docker.sock
    env:
      TESTCONTAINERS_DOCKER_CLIENT_STRATEGY: org.testcontainers.dockerclient.UnixSocketClientProviderStrategy
      TESTCONTAINERS_RYUK_DISABLED: true
      TESTCONTAINERS_REUSE_ENABLE: true
      DOCKER_HOST: unix:///var/run/docker.sock
      TESTCONTAINERS_HOST_OVERRIDE: localhost
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Make gradlew executable
        run: chmod +x ./gradlew
      - name: Check Docker Status
        run: |
          echo "=== Docker Status ==="
          docker info
          echo "=== Docker Version ==="
          docker version
          echo "=== Docker Socket Permissions ==="
          ls -la /var/run/docker.sock
          echo "=== Docker Images ==="
          docker images
          echo "=== Docker Containers ==="
          docker ps -a
          echo "=== Environment Variables ==="
          env | grep -E "(DOCKER|TESTCONTAINERS)" || echo "No Docker/Testcontainers env vars found"
      - name: Clean up existing containers and test Docker
        run: |
          echo "=== Cleaning up existing containers ==="
          docker container prune -f || echo "No containers to clean"
          docker volume prune -f || echo "No volumes to clean"
          echo "=== Testing Docker Connectivity ==="
          docker run --rm hello-world
          echo "=== Testing PostgreSQL Container ==="
          docker run --rm postgres:15.3 postgres --version
      - name: Cache Gradle dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.gradle/caches
            ~/.gradle/wrapper
          key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
          restore-keys: |
            ${{ runner.os }}-gradle-
      - name: Run ALL tests (unit + integration)
        run: |
          echo "=== Starting Test Execution ==="
          echo "=== Pre-test Docker Status ==="
          docker ps -a
          echo "=== Running Tests ==="
          # Run tests with optimized settings for CI
          ./gradlew test --no-daemon --console=plain --info --stacktrace --parallel 2>&1 | tee test-output.log
          TEST_EXIT_CODE=${PIPESTATUS[0]}
          echo "=== Test Execution Complete with exit code: $TEST_EXIT_CODE ==="
          echo "=== Post-test Docker Status ==="
          docker ps -a
          exit $TEST_EXIT_CODE
        timeout-minutes: ${{ inputs.timeout_minutes }}
      - name: Show Test Results Summary
        if: always()
        run: |
          echo "=== Test Results Summary ==="
          if [ -f "build/reports/tests/test/index.html" ]; then
            echo "Test report generated successfully"
          else
            echo "No test report found"
          fi
          echo "=== Gradle Test Exit Code ==="
          ./gradlew test --no-daemon --console=plain --dry-run > /dev/null 2>&1 && echo "Tests completed" || echo "Tests failed"
      - name: Show Specific Test Failures
        if: failure()
        run: |
          echo "=== SPECIFIC TEST FAILURES ==="
          echo "Looking for failed tests in logs..."
          # Show test results from XML files if they exist
          if [ -d "build/test-results/test" ]; then
            echo "=== Test Results from XML ==="
            find build/test-results/test -name "*.xml" -exec echo "=== {} ===" \; -exec cat {} \;
          fi
          # Show summary from test report
          if [ -f "build/reports/tests/test/index.html" ]; then
            echo "=== Test Report Summary ==="
            grep -A 10 -B 5 "FAILED\|ERROR\|Exception" build/reports/tests/test/index.html || echo "No failures found in HTML report"
          fi
          # Show filtered test output with more context
          echo "=== Filtered Test Output ==="
          if [ -f "test-output.log" ]; then
            echo "=== Database Related Errors ==="
            grep -A 5 -B 5 "relation.*does not exist\|PSQLException\|BadSqlGrammarException" test-output.log || echo "No database errors found"
            echo "=== Testcontainers Errors ==="
            grep -A 5 -B 5 "testcontainers\|DockerClientFactory\|Container startup failed" test-output.log || echo "No Testcontainers errors found"
            echo "=== General Test Failures ==="
            grep -E "(FAILED|ERROR|Exception|BUILD FAILED|Tests run:|Failures:|Skipped:|BUILD SUCCESSFUL)" test-output.log || echo "No specific failures found in log"
          fi
          echo "=== End of Test Failures ==="
      - name: Generate JaCoCo XML report
        if: always()
        run: ./gradlew jacocoTestReport
      - name: Upload Jacoco Coverage Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: jacoco-report
          path: build/reports/jacoco/test/jacocoTestReport.xml
          if-no-files-found: warn
      - name: Upload Test Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: junit-test-report
          path: build/reports/tests/test/
      - name: Upload Test Log
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-output-log
          path: test-output.log
      - name: Debug Docker State (if tests fail)
        if: failure()
        run: |
          echo "=== Debug Docker State ==="
          echo "=== All Containers ==="
          docker ps -a
          echo "=== Container Logs ==="
          for container in $(docker ps -aq); do
            echo "=== Logs for container $container ==="
            docker logs $container 2>&1 || echo "Failed to get logs for $container"
          done
          echo "=== Docker System Info ==="
          docker system df
          docker system events --since 30m --until now || echo "No recent Docker events"
          echo "=== Docker Network Info ==="
          docker network ls
          echo "=== Docker Volume Info ==="
          docker volume ls
