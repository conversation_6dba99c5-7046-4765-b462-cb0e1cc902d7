name: Docker Build and Push Workflow

on:
  workflow_call:
    secrets:
      DIGITALOCEAN_ACCESS_TOKEN:
        description: 'DigitalOcean access token'
        required: true

jobs:
  docker-build-and-push:
    name: Build and Push Docker Image to DOCR
    runs-on: [self-hosted, linux]
    services:
      docker:
        image: docker:24.0-dind
        options: --privileged
    steps:
      - name: Checkout Code
        uses: actions/checkout@v3
      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
      - name: Authenticate Docker with DigitalOcean Container Registry (DOCR)
        run: doctl registry login
      - name: Set up JDK 21
        uses: actions/setup-java@v3
        with:
          java-version: '21'
          distribution: 'temurin'
      - name: Make gradlew executable
        run: chmod +x ./gradlew
      - name: Build JAR
        run: ./gradlew bootJar
      - name: Build Docker Image
        run: docker build -t ats-spring-backend:latest .
      - name: Tag Docker Image as Latest for DOCR
        run: |
          IMAGE=registry.digitalocean.com/chidhagni-container-registry/ats-spring-backend:latest
          docker tag ats-spring-backend:latest $IMAGE
          echo "IMAGE=$IMAGE" >> $GITHUB_ENV
      - name: Push Image to DOCR
        run: |
          docker push $IMAGE