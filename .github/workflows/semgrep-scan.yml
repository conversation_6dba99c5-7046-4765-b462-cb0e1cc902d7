name: SemGrep Static Analysis

on:
  workflow_call:
    secrets:
      SERVER_PASSWORD:
        description: "Password for accessing the Semgrep server"
        required: true
      SEMGREP_SERVER_HOST:
        description: "Semgrep server host IP address"
        required: false

jobs:
  semgrep:
    name: Run Semgrep on Dedicated Server
    runs-on: [self-hosted, Linux]
    env:
      SEMGREP_HOST: ${{ secrets.SEMGREP_SERVER_HOST || '**************' }}

    steps:
      - name: Checkout Code
        uses: actions/checkout@v3

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y sshpass jq rsync

      - name: Setup SSH connection
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -H $SEMGREP_HOST >> ~/.ssh/known_hosts

      - name: Package source code
        run: |
          SRC_DIR="/tmp/semgrep-source"
          mkdir -p "$SRC_DIR"

          rsync -av \
            --exclude='.git' \
            --exclude='node_modules' \
            --exclude='dist' \
            --exclude='coverage' \
            --exclude='.scannerwork' \
            . "$SRC_DIR/"

          # Ensure .semgrepignore exists
          if [ ! -f "$SRC_DIR/.semgrepignore" ]; then
            echo "node_modules/" > "$SRC_DIR/.semgrepignore"
            echo "dist/" >> "$SRC_DIR/.semgrepignore"
            echo "coverage/" >> "$SRC_DIR/.semgrepignore"
            echo "build/" >> "$SRC_DIR/.semgrepignore"
            echo "⚠️ Created fallback .semgrepignore"
          fi

          tar -czf /tmp/semgrep-source.tar.gz -C /tmp semgrep-source

      - name: Transfer source code to server
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" scp -o StrictHostKeyChecking=no \
            /tmp/semgrep-source.tar.gz root@$SEMGREP_HOST:/tmp/

          sshpass -p "${{ secrets.SERVER_PASSWORD }}" ssh -o StrictHostKeyChecking=no root@$SEMGREP_HOST "
            rm -rf /root/devsecops-tools/semgrep/source/*
            mkdir -p /root/devsecops-tools/semgrep/source
            tar -xzf /tmp/semgrep-source.tar.gz -C /tmp
            rsync -av /tmp/semgrep-source/ /root/devsecops-tools/semgrep/source/
            rm -rf /tmp/semgrep-source /tmp/semgrep-source.tar.gz
          "

      - name: Run Semgrep scan
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" ssh -o StrictHostKeyChecking=no root@$SEMGREP_HOST "
            cd /root/devsecops-tools/semgrep
            rm -f results/results.json
            echo '🐳 Running Semgrep scan....'

            if command -v docker-compose &>/dev/null; then
              docker-compose up --remove-orphans
            elif docker compose version &>/dev/null; then
              docker compose up --remove-orphans
            else
              echo '❌ Docker Compose not found' && exit 1
            fi

            if [ ! -f results/results.json ]; then
              echo '❌ Semgrep did not produce results.json' && exit 1
            fi

            cp results/results.json results/results_$(date +%Y%m%d_%H%M%S).json
          "

      - name: Download Semgrep results
        run: |
          sshpass -p "${{ secrets.SERVER_PASSWORD }}" scp -o StrictHostKeyChecking=no \
            root@$SEMGREP_HOST:/root/devsecops-tools/semgrep/results/results.json ./results.json

      - name: Upload Semgrep Results
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-results
          path: results.json

      - name: Analyze findings
        run: |
          echo "=== SEMGREP SCAN RESULTS ==="
          TOTAL=$(jq '.results | length' results.json)
          FILES=$(jq '.paths.scanned | length' results.json 2>/dev/null || echo "unknown")

          echo "📊 Findings: $TOTAL"
          echo "📁 Files scanned: $FILES"

          jq -r '.results[] | "\(.check_id) | \(.path):\(.start.line) | Severity: \(.severity // "INFO") | \(.extra.message)"' results.json | head -20 | tee results_summary.txt

      - name: Upload Findings Summary
        uses: actions/upload-artifact@v4
        with:
          name: semgrep-findings-summary
          path: results_summary.txt

      - name: Fail on blocking issues
        run: |
          # Count critical and high severity issues
          CRITICAL=$(jq '[.results[] | select(.severity=="ERROR")] | length' results.json)
          HIGH=$(jq '[.results[] | select(.severity=="WARNING")] | length' results.json)
          TOTAL_BLOCKING=$((CRITICAL + HIGH))

          echo "📊 Security Analysis Results:"
          echo "   🔴 Critical (ERROR): $CRITICAL"
          echo "   🟡 High (WARNING): $HIGH"
          echo "   📋 Total Blocking: $TOTAL_BLOCKING"

          # Fail on ERROR severity issues (critical security vulnerabilities)
          if [ "$CRITICAL" -gt 0 ]; then
            echo "❌ Found $CRITICAL critical security issues - Build FAILED"
            echo "🚨 Critical issues must be fixed before deployment"
            exit 1
          elif [ "$HIGH" -gt 5 ]; then
            echo "⚠️  Found $HIGH high-severity issues (threshold: 5) - Build FAILED"
            echo "🔧 Please address high-severity issues before deployment"
            exit 1
          else
            echo "✅ No blocking security issues found"
          fi

      - name: Cleanup
        if: always()
        run: rm -rf /tmp/semgrep-source*
