name: Reusable GitOps Deployment Workflow

on:
  workflow_call:
    secrets:
      DB_USER:
        description: 'Database user'
        required: true
      DB_PASSWORD:
        description: 'Database password'
        required: true
      DB_HOST:
        description: 'Database host'
        required: true
      DB_PORT:
        description: 'Database port'
        required: true
      DB_NAME:
        description: 'Database name'
        required: true
      SECURE_GITHUB_TOKEN:
        description: 'Secure GitHub token'
        required: true
      DO_BUCKET_URL:
        description: 'DigitalOcean bucket URL'
        required: true
      DO_BUCKET_NAME:
        description: 'DigitalOcean bucket name'
        required: true
      DO_ACCESS_KEY_NAME:
        description: 'DigitalOcean access key name'
        required: true
      DO_ACCESS_KEY_ID:
        description: 'DigitalOcean access key ID'
        required: true
      DO_SECRET_KEY:
        description: 'DigitalOcean secret key'
        required: true
      DO_REGION:
        description: 'DigitalOcean region'
        required: true
      DO_ENDPOINT:
        description: 'DigitalOcean endpoint'
        required: true
      FILE_STORE_PROVIDER:
        description: 'File store provider (minio or digitalocean)'
        required: true

jobs:
  cd-gitops-deploy:
    name: <PERSON><PERSON> GitOps Deployment (ATS-Backend)
    runs-on: [self-hosted, linux]
    steps:
      - name: Prepare Secrets
        id: secrets
        run: |
          SECRETS_JSON=$(cat << EOF | base64 -w 0
          {
            "ENABLE_DATABASE": true,
            "DB_USER": "${{ secrets.DB_USER }}",
            "DB_SSL_MODE": "require",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD }}",
            "DB_HOST": "${{ secrets.DB_HOST }}",
            "CONTAINER_PORT": 8080,
            "HEALTH_CHECK_PATH": "/ats/actuator/health",
            "DB_PORT": "${{ secrets.DB_PORT }}",
            "DB_NAME": "${{ secrets.DB_NAME }}",
            "DO_BUCKET_URL":"${{ secrets.DO_BUCKET_URL }}",
            "DO_BUCKET_NAME": "${{ secrets.DO_BUCKET_NAME }}",
            "DO_ACCESS_KEY_NAME": "${{ secrets.DO_ACCESS_KEY_NAME }}",
            "DO_ACCESS_KEY_ID": "${{ secrets.DO_ACCESS_KEY_ID }}",
            "DO_SECRET_KEY": "${{ secrets.DO_SECRET_KEY }}",
            "DO_REGION": "${{ secrets.DO_REGION }}",
            "DO_ENDPOINT": "${{ secrets.DO_ENDPOINT }}",
            "JWT_SECRET": "dummy-jwt-secret",
            "SMTP_USER": "<EMAIL>",
            "SMTP_PASS": "dummy-smtp-password",
            "EMAIL_HOST": "smtp.gmail.com",
            "EMAIL_PORT": 587,
            "GOOGLE_CLIENT_ID": "dummy-google-client-id",
            "GOOGLE_CLIENT_SECRET": "dummy-google-client-secret",
            "FILE_STORE_PROVIDER": "${{ secrets.FILE_STORE_PROVIDER }}"
          }
          EOF
          )
          echo "secrets_encoded=$SECRETS_JSON" >> $GITHUB_OUTPUT
      - name: 🚀 Trigger GitOps deployment for ATS-Backend
        uses: actions/github-script@v7
        env:
          SECRETS_ENCODED: ${{ steps.secrets.outputs.secrets_encoded }}
        with:
          github-token: ${{ secrets.SECURE_GITHUB_TOKEN }}
          script: |
            const secretsEncoded = process.env.SECRETS_ENCODED || '';
            const dockerTag = 'latest';
            let environment = 'dev';
            const payload = {
              project_id: 'ats-spring-backend',
              application_type: 'springboot-backend',
              environment: environment,
              docker_image: 'registry.digitalocean.com/chidhagni-container-registry/ats-spring-backend',
              docker_tag: dockerTag,
              host_name: 'devapi.ats.hrhighwaves.com',
              source_repo: `${context.repo.owner}/${context.repo.repo}`,
              source_branch: '${{ github.ref_name }}',
              commit_sha: context.sha,
              secrets_encoded: secretsEncoded || ''
            };
            const requiredFields = ['project_id', 'environment', 'docker_image', 'docker_tag'];
            for (const field of requiredFields) {
              if (!payload[field] || payload[field] === '') {
                throw new Error(`Required field '${field}' is missing or empty`);
              }
            }
            if (!/^[a-z0-9-]+$/.test(payload.project_id)) {
              throw new Error(`Invalid project_id format: ${payload.project_id}. Must be lowercase alphanumeric with hyphens only.`);
            }
            if (!['dev', 'staging', 'prod', 'production'].includes(payload.environment)) {
              throw new Error(`Invalid environment: ${payload.environment}. Must be dev, staging, prod, or production.`);
            }
            try {
              await github.rest.repos.createDispatchEvent({
                owner: 'ChidhagniConsulting',
                repo: 'gitops-argocd-apps',
                event_type: 'deploy-to-argocd',
                client_payload: payload
              });
            } catch (error) {
              console.error('❌ Failed to trigger GitOps deployment:', error);
              throw error;
            }