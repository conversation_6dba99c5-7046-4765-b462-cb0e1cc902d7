## Summary
Provide a clear summary of the frontend changes (e.g., features added, bugs fixed, UI updates).

## Description
Briefly describe the purpose of this change and any relevant context.

## Technical Details
List key technical details, such as:
- Components, hooks, or modules modified
- Any new API integrations or state management changes
- External libraries added or updated

## Checklist
- [ ] Application builds successfully using pnpm
- [ ] All unit and integration tests pass (pnpm test)
- [ ] Linting and formatting checks pass
- [ ] No console logs or commented-out code left in the branch
- [ ] Components are responsive and tested on multiple screen sizes
- [ ] Accessibility best practices followed (where applicable)
- [ ] No hardcoded values or sensitive information in the codebase
- [ ] SonarQube analysis completed and quality gate passed
- [ ] Static analysis (Semgrep) completed with no high/critical issues
- [ ] Cross-browser tested (Chrome, Firefox, Edge, Safari)
- [ ] Dark mode verified (if applicable)
- [ ] New/updated unit tests added for modified functionality

## Related Issue / Ticket
Add a link or reference to the relevant issue, task, or ticket.

## Additional Notes
- Include setup instructions, testing notes, or reviewer guidance
- Attach screenshots or recordings for visual/UI changes
