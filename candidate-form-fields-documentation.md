# Candidate Form - Field Documentation

## Form Sections

### 1. Personal Info
- **First Name** → Text Input (Required)
  - Notes: Marked with asterisk (*)
- **Middle Name** → Text Input
- **Last Name** → Text Input (Required)
  - Notes: Marked with asterisk (*)
- **Nick Name** → Text Input
- **Email Address** → Text Input (Required)
  - Notes: Marked with asterisk (*), email validation
- **Alternate Email Address** → Text Input
- **Home Phone Number** → Text Input with Country Code
  - Notes: Country code dropdown (+91, +1)
- **Mobile Number** → Text Input with Country Code
  - Notes: Country code dropdown (+91, +1)
- **Work Phone Number** → Text Input with Country Code
  - Notes: Country code dropdown (+91, +1)
- **Other Phone** → Text Input with Country Code
  - Notes: Country code dropdown (+91, +1)
- **Date Of Birth** → Date Picker
- **SSN** → Text Input
- **Skype ID** → Text Input
- **LinkedIn Profile URL** → Text Input
- **Facebook Profile URL** → Text Input
- **Twitter Profile URL** → Text Input
- **Video Reference** → Text Input
- **Work Authorization** → Select Dropdown
  - Options: US Citizen, Green Card, H1B, OPT, Other
- **Work Authorization Expiry** → Date Picker
- **Clearance** → Radio Button Group
  - Options: Yes, No
- **Address** → Textarea
- **City** → Text Input
- **Country** → Select Dropdown
  - Options: United States, India, Canada, United Kingdom
- **State** → Select Dropdown
  - Options: California, New York, Texas, Florida
- **Zip Code** → Text Input

### 2. Professional Info
- **Source** → Select Dropdown
  - Notes: Shows "Career Portal" selected
- **Experience** → Multi-part Input
  - Years: Select Dropdown (shows "8" selected)
  - Months: Select Dropdown (shows "5" selected)
- **Referred By** → Text Input
- **Applicant Status** → Select Dropdown
  - Notes: Shows "New Lead" selected
- **Applicant Group** → Multi-select Input
- **Ownership** → Select Dropdown
  - Notes: Shows "Prudhvi Kanmuri" selected
- **Job Title** → Text Input
- **Expected Pay** → Multi-part Input
  - Currency: Select Dropdown (shows "INR" selected)
  - Amount: Select Dropdown
- **Additional Comments** → Textarea
- **Relocation** → Radio Button Group
  - Options: Yes, No
- **Skills** → Multi-select Tags
  - Notes: Shows tags like "Team Management", "Time Management", "Python", "HTML", "Java", "CSS", "Data Structures", "MySQL"
- **Primary Skills** → Multi-select Input
- **Technology** → Multi-select Input
- **Tax Terms** → Select Dropdown (Required)
  - Notes: Marked with asterisk (*), shows "Full Time" selected
- **Notice Period** → Select Dropdown
  - Notes: Shows "Immediate Joiner" selected
- **Industry** → Multi-select Input
- **Current CTC** → Multi-part Input
  - Currency: Select Dropdown (shows "INR" selected)
  - Amount: Select Dropdown
- **Current Company** → Text Input
- **Function** → Multi-select Input
- **Work Authorization Expiry** → Text Input
- **Pan Card Number** → Text Input
- **GPA** → Text Input
- **Aadhar Number** → Text Input

### 3. Documents
- **Title** → Text Input
  - Notes: Shows example "1132 SnehithGaddala"
- **Type** → Select Dropdown
  - Notes: Shows "Resume" selected
- **Attachment** → File Upload Button
- **Resume Visibility** → Radio Button Group
  - Options: Add and Make Default
- **Comments** → Textarea

### 4. Education Details
- **School Name** → Text Input (Required)
  - Notes: Marked with asterisk (*), shows "Rajiv Gandhi University of Knowledge"
- **Degree** → Select Dropdown (Required)
  - Notes: Marked with asterisk (*)
- **Year Completed** → Text Input (Required)
  - Notes: Marked with asterisk (*), shows "May 2023"
- **Major Study** → Text Input
  - Notes: Shows "Computer Science Engineering"
- **Minor Study** → Text Input
- **GPA** → Text Input
- **Country** → Select Dropdown
  - Notes: Shows "India" selected
- **State** → Select Dropdown
  - Notes: Shows "Telangana" selected
- **City** → Text Input
  - Notes: Shows "Nirmal"
- **Higher Education** → Toggle Switch
  - Notes: Shows "OFF" state

### 5. Certifications
- **Certification** → Text Input (Required)
  - Notes: Marked with asterisk (*), shows "Required" placeholder
- **Year Completed** → Text Input (Required)
  - Notes: Marked with asterisk (*), shows "Required" placeholder
- **Comments** → Textarea
  - Notes: Multi-line text input for additional comments

### 6. Employer Details
- **New** → Radio Button (Selected)
- **Add from existing vendor contact records** → Radio Button
- **Vendor Contact** → Select Dropdown
  - Notes: Shows "Required" text
- **First Name** → Text Input
- **Last Name** → Text Input
- **Employer Name** → Text Input
- **Office Number** → Text Input
- **Email ID** → Text Input
- **Extension** → Text Input
- **Mobile Number** → Text Input
- **Status** → Select Dropdown
  - Notes: Shows "Select Status" placeholder

## Navigation Tabs
- Personal Details
- Documents
- Education Details
- Certifications
- Employer Details
- Work Experience

## Dropdown Options

### Country
- India

### State
- Telangana

### Source
- Career Portal

### Experience (Years)
- 8

### Experience (Months)
- 5

### Applicant Status
- New Lead

### Ownership
- Prudhvi Kanmuri

### Expected Pay (Currency)
- INR

### Tax Terms
- Full Time

### Notice Period
- Immediate Joiner

### Current CTC (Currency)
- INR

### Gender
- Male

### Race/Ethnicity
- Asian / Pacific Islander

### Veteran Status
- Yes

### Document Type
- Resume

### Education Country
- India

### Education State
- Telangana

## Skills Tags (Visible)
- Team Management
- Time Management
- Python
- HTML
- Java
- CSS
- Data Structures
- MySQL

## Notes
- Required fields are marked with asterisk (*)
- Form uses tab-based navigation
- Multi-select fields support tag-style input
- Some dropdowns show placeholder text when no option is selected
- File upload functionality available for document attachments
- Radio button groups used for binary choices (Yes/No, New/Existing)
