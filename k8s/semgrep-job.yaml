# Semgrep Job
apiVersion: batch/v1
kind: Job
metadata:
  name: semgrep-job
  namespace: semgrep
spec:
  template:
    spec:
      initContainers:
      - name: copy-source
        image: alpine/git
        env:
        - name: GIT_BRANCH
          value: "main" # Default, will be overridden by workflow
        command: ["sh", "-c", "timeout 120 git clone -b \"$GIT_BRANCH\" https://x-access-token:$(cat /etc/github/token)@github.com/ChidhagniConsulting/ats-spring-backend /src"]
        volumeMounts:
        - name: source-code
          mountPath: /src
        - name: secure-github-token
          mountPath: /etc/github
          readOnly: true
      containers:
      - name: semgrep
        image: returntocorp/semgrep:1.57.0
        command: ["sh", "-c", "cd /src && [ -f .semgrepignore ] && echo 'Found .semgrepignore:' && cat .semgrepignore || echo 'No .semgrepignore found'; semgrep --exclude-rule=dockerfile.security.missing-user-entrypoint.missing-user-entrypoint --exclude-rule=yaml.docker-compose.security.no-new-privileges.no-new-privileges --exclude-rule=yaml.docker-compose.security.writable-filesystem-service.writable-filesystem-service --exclude-rule=generic.secrets.security.detected-sonarqube-docs-api-key.detected-sonarqube-docs-api-key --config=auto --json --output=/results/results.json ."]
        volumeMounts:
        - name: results
          mountPath: /results
        - name: source-code
          mountPath: /src
        securityContext:
         # runAsNonRoot: true
          allowPrivilegeEscalation: false
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1"
      restartPolicy: Never
      volumes:
      - name: results
        persistentVolumeClaim:
          claimName: semgrep-results-pvc
      - name: source-code
        emptyDir: {}
      - name: secure-github-token
        secret:
          secretName: secure-github-token
