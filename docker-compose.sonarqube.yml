version: '3.8'

services:
  sonarqube-db:
    image: postgres:15-alpine
    container_name: talentflow-sonarqube-db
    environment:
      POSTGRES_USER: sonar
      POSTGRES_PASSWORD: sonar
      POSTGRES_DB: sonar
    volumes:
      - sonarqube_db_data:/var/lib/postgresql/data
    networks:
      - sonarqube-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U sonar"]
      interval: 30s
      timeout: 10s
      retries: 3

  sonarqube:
    image: sonarqube:latest
    container_name: talentflow-sonarqube
    depends_on:
      sonarqube-db:
        condition: service_healthy
    environment:
      SONAR_JDBC_URL: *****************************************
      SONAR_JDBC_USERNAME: sonar
      SONAR_JDBC_PASSWORD: sonar
      SONAR_WEB_PORT: 9002
      SONAR_WEB_CONTEXT: /
      # Increase memory limits for better performance
      SONAR_CE_JAVAOPTS: -Xmx2g -Xms512m
      SONAR_WEB_JAVAOPTS: -Xmx2g -Xms512m
      # Disable Elasticsearch bootstrap checks for Windows compatibility
      SONAR_ES_BOOTSTRAP_CHECKS_DISABLE: true
    ports:
      - "9002:9002"
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
      - sonarqube_conf:/opt/sonarqube/conf
    networks:
      - sonarqube-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9002/api/system/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    ulimits:
      nofile:
        soft: 65536
        hard: 65536

networks:
  sonarqube-network:
    driver: bridge

volumes:
  sonarqube_data:
    name: talentflow_sonarqube_data
  sonarqube_logs:
    name: talentflow_sonarqube_logs
  sonarqube_extensions:
    name: talentflow_sonarqube_extensions
  sonarqube_conf:
    name: talentflow_sonarqube_conf
  sonarqube_db_data:
    name: talentflow_sonarqube_db_data
