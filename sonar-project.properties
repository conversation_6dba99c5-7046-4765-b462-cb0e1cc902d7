# SonarQube Project Configuration for TalentFlow ATS Platform
# ============================================================

# Project identification
sonar.projectKey=talentflow-ats-frontend
sonar.projectName=TalentFlow ATS Frontend
sonar.projectVersion=1.0.0
sonar.projectDescription=Multi-Tenant Applicant Tracking System with client-vendor collaboration capabilities

# Source code configuration
sonar.sources=src
sonar.sourceEncoding=UTF-8

# Language-specific settings
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,src/test/**/*,**/__tests__/**/*,**/*.stories.tsx,**/*.config.*

# Test configuration
sonar.tests=src
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/*.spec.ts,**/*.spec.tsx,src/__tests__/**/*,src/test/**/*
sonar.test.exclusions=node_modules/**/*,dist/**/*,build/**/*,coverage/**/*

# Exclusions - Files and directories to exclude from analysis
sonar.exclusions=\
  node_modules/**/*,\
  dist/**/*,\
  build/**/*,\
  coverage/**/*,\
  public/**/*,\
  **/*.min.js,\
  **/*.bundle.js,\
  **/*.config.js,\
  **/*.config.ts,\
  vite.config.ts,\
  tailwind.config.ts,\
  postcss.config.js,\
  eslint.config.js,\
  **/*.d.ts,\
  src/vite-env.d.ts,\
  **/*.stories.tsx,\
  **/*.stories.ts,\
  pnpm-lock.yaml,\
  package-lock.json,\
  yarn.lock

# Duplication exclusions
sonar.cpd.exclusions=\
  **/*.test.ts,\
  **/*.test.tsx,\
  **/*.spec.ts,\
  **/*.spec.tsx,\
  src/components/ui/**/*,\
  **/*.stories.tsx

# Analysis parameters
sonar.analysis.mode=publish
sonar.scm.provider=git
sonar.scm.forceReloadAll=true

# Quality gate settings
sonar.qualitygate.wait=false

# TypeScript/JavaScript specific settings
sonar.javascript.file.suffixes=.js,.jsx
sonar.typescript.file.suffixes=.ts,.tsx

# CSS analysis
sonar.css.file.suffixes=.css,.scss,.sass,.less

# Additional settings for React projects
sonar.javascript.environments=browser,node,jest,vitest
sonar.typescript.tsconfigPath=tsconfig.json

# Logging level (INFO, DEBUG, WARN, ERROR)
sonar.log.level=INFO

# Server configuration (when running locally)
sonar.host.url=http://localhost:9002

# Authentication (set via environment variables or command line)
# sonar.login=your-token-here
# sonar.password=

# Branch analysis (for CI/CD)
# sonar.branch.name=main
# sonar.pullrequest.key=
# sonar.pullrequest.branch=
# sonar.pullrequest.base=main
