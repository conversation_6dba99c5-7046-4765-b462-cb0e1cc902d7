# ========================================
# DOCKER IGNORE FILE FOR ATS APPLICATION
# ========================================

# Build artifacts
build/
target/
*.jar
*.war

# IDE files
.idea/
.vscode/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Gradle
.gradle/
gradle/wrapper/gradle-wrapper.jar

# Logs
logs/
*.log

# Test reports
test-results/
coverage/

# Docker files
Dockerfile
.dockerignore
docker-compose*.yml
sonarqube.yml

# Documentation
*.md
docs/

# Environment files
.env
.env.local

# Temporary files
tmp/
temp/
*.tmp

# Node modules (if any)
node_modules/

# Generated files
src/generated/
