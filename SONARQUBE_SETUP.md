# 🔍 SonarQube Setup Guide for TalentFlow ATS Platform

This guide provides comprehensive instructions for setting up SonarQube code quality analysis for the TalentFlow ATS React/TypeScript application.

## 📋 Prerequisites

- **Docker & Docker Compose** (for SonarQube server)
- **Node.js** v22.7.0+ 
- **npm** or **pnpm** package manager
- **Git** (for SCM integration)

## 🚀 Quick Start

### 1. Start SonarQube Server

```bash
# Start SonarQube server on port 9002
npm run sonar:server:start

# Check server logs
npm run sonar:server:logs

# Wait for server to be ready (usually 2-3 minutes)
```

### 2. Access SonarQube Dashboard

- **URL**: http://localhost:9002
- **Default Login**: `admin`
- **Default Password**: `admin` (you'll be prompted to change this)

### 3. Run Code Analysis

```bash
# Run tests with coverage and SonarQube analysis
npm run sonar:analysis

# Or run SonarQube analysis only
npm run sonar
```

## 🐳 Docker Setup Details

### SonarQube Server Configuration

The `docker-compose.sonarqube.yml` file configures:

- **SonarQube Community Edition** (latest)
- **PostgreSQL 15** database
- **Port 9002** (instead of default 9000)
- **Persistent volumes** for data, logs, extensions
- **Health checks** for both services
- **Memory optimization** (2GB heap size)

### Docker Commands

```bash
# Start services
docker-compose -f docker-compose.sonarqube.yml up -d

# Stop services
docker-compose -f docker-compose.sonarqube.yml down

# View logs
docker-compose -f docker-compose.sonarqube.yml logs -f sonarqube

# Restart services
docker-compose -f docker-compose.sonarqube.yml restart
```

## ⚙️ Configuration Files

### 1. sonar-project.properties

Main configuration file with:
- Project metadata (name, key, version)
- Source and test directories
- Coverage report paths
- File exclusions
- Quality gate settings

### 2. .sonarqube/sonar-scanner.js

Programmatic scanner with:
- Environment variable support
- CI/CD integration (GitHub Actions)
- Branch and PR analysis
- Detailed logging

## 📊 Analysis Configuration

### Included Files
- **Source**: `src/**/*.{ts,tsx,js,jsx,css,scss}`
- **Tests**: `src/**/*.{test,spec}.{ts,tsx}`

### Excluded Files
- `node_modules/`, `dist/`, `build/`, `coverage/`
- Configuration files (`*.config.*`)
- Type definitions (`*.d.ts`)
- Storybook files (`*.stories.*`)
- Lock files (`pnpm-lock.yaml`, etc.)

### Coverage Integration
- Uses **Vitest coverage** reports
- LCOV format: `coverage/lcov.info`
- Excludes test files from coverage

## 🎯 Quality Gates

This project uses SonarQube's default "Sonar way" quality gate, which provides standard code quality thresholds for:
- Code coverage on new code
- Maintainability, reliability, and security ratings
- Duplication detection
- Bug and vulnerability detection
- Code smell identification

You can customize quality gates through the SonarQube web interface if needed.

## 🔧 NPM Scripts

```json
{
  "sonar": "sonar-scanner",
  "sonar:analysis": "npm run test:coverage && npm run sonar",
  "sonar:server:start": "docker-compose -f docker-compose.sonarqube.yml up -d",
  "sonar:server:stop": "docker-compose -f docker-compose.sonarqube.yml down",
  "sonar:server:logs": "docker-compose -f docker-compose.sonarqube.yml logs -f sonarqube",
  "sonar:server:restart": "npm run sonar:server:stop && npm run sonar:server:start"
}
```

## 🔐 Authentication

### Local Development
No authentication required for localhost analysis.

### CI/CD Integration
Set environment variables:
```bash
export SONAR_HOST_URL=http://localhost:9002
export SONAR_TOKEN=your-sonarqube-token
```

### Generate Token
1. Login to SonarQube dashboard
2. Go to **My Account** → **Security**
3. Generate new token
4. Use token in CI/CD or set as `SONAR_TOKEN`

## 🚨 Troubleshooting

### Common Issues

#### 1. Server Won't Start
```bash
# Check Docker status
docker ps

# Check logs
npm run sonar:server:logs

# Restart services
npm run sonar:server:restart
```

#### 2. Analysis Fails
```bash
# Check coverage reports exist
ls -la coverage/

# Run tests first
npm run test:coverage

# Check SonarQube server is running
curl http://localhost:9002/api/system/status
```

#### 3. Memory Issues
```bash
# Increase Docker memory limits
# Edit docker-compose.sonarqube.yml:
# SONAR_CE_JAVAOPTS: -Xmx4g -Xms1g
# SONAR_WEB_JAVAOPTS: -Xmx4g -Xms1g
```

#### 4. Port Conflicts
```bash
# Check if port 9002 is in use
netstat -an | grep 9002

# Change port in docker-compose.sonarqube.yml if needed
```

### System Requirements
- **Minimum RAM**: 4GB
- **Recommended RAM**: 8GB+
- **Disk Space**: 2GB+ for SonarQube data
- **Docker Memory**: 4GB+ allocated

## 📈 Best Practices

### 1. Regular Analysis
- Run analysis on every commit
- Set up pre-commit hooks
- Integrate with CI/CD pipeline

### 2. Quality Gate Management
- Review and adjust quality gates regularly
- Set realistic but strict thresholds
- Monitor trends over time

### 3. Coverage Goals
- Aim for 80%+ test coverage
- Focus on critical business logic
- Exclude UI components from strict coverage

### 4. Code Smells
- Address code smells promptly
- Use SonarQube suggestions
- Maintain consistent code style

## 🔗 Useful Links

- **SonarQube Dashboard**: http://localhost:9002
- **Project Dashboard**: http://localhost:9002/dashboard?id=talentflow-ats-platform
- **SonarQube Documentation**: https://docs.sonarqube.org/
- **TypeScript Analysis**: https://docs.sonarqube.org/latest/analysis/languages/typescript/

## 📞 Support

For issues or questions:
1. Check this documentation
2. Review SonarQube logs
3. Consult official SonarQube documentation
4. Check project GitHub issues
