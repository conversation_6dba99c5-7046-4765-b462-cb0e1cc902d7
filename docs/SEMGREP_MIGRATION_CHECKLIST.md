# Semgrep VM Migration - Validation Checklist

## Pre-Migration Verification

### ✅ Current State Analysis (Completed)
- [x] Analyzed existing `semgrep-analysis.yml` workflow structure
- [x] Reviewed Kubernetes configuration files (`k8s/semgrep-*.yaml`)
- [x] Identified integration points with `ci.yml`
- [x] Documented current secrets and dependencies

### ✅ Requirements Understanding (Completed)
- [x] VM server details: `162.241.152.50`
- [x] Authentication method: SSH with password
- [x] Execution approach: Docker Compose
- [x] Interface compatibility requirements
- [x] Cleanup and error handling requirements

## Migration Implementation

### ✅ Workflow Updates (Completed)
- [x] Updated `semgrep-analysis.yml` with VM server approach
- [x] Replaced Kubernetes secrets with `SERVER_PASSWORD`
- [x] Added `runner_type` input parameter for flexibility
- [x] Implemented SSH connection and file transfer logic
- [x] Created Docker Compose configuration generation
- [x] Added comprehensive error handling and cleanup

### ✅ CI Integration Updates (Completed)
- [x] Updated `ci.yml` to use new secrets structure
- [x] Enabled `runner_type` parameter
- [x] Maintained backward compatibility

### ✅ Documentation (Completed)
- [x] Created migration documentation (`SEMGREP_VM_MIGRATION.md`)
- [x] Created Docker Compose reference template
- [x] Documented troubleshooting procedures
- [x] Created validation checklist

## Post-Migration Validation

### 🔄 GitHub Secrets Configuration (Required)
- [ ] Add `SERVER_PASSWORD` secret to GitHub repository
- [ ] Verify `SECURE_GITHUB_TOKEN` secret exists
- [ ] Remove old Kubernetes-related secrets (optional cleanup):
  - [ ] `DIGITALOCEAN_ACCESS_TOKEN`
  - [ ] `KUBERNETES_CLUSTER_ID`
  - [ ] `KUBERNETES_CONTEXT`

### 🔄 VM Server Preparation (Required)
- [ ] Verify VM server `162.241.152.50` is accessible
- [ ] Confirm SSH access with root user and password
- [ ] Verify Docker is installed and running
- [ ] Verify Docker Compose is installed
- [ ] Test disk space availability for temporary files
- [ ] Ensure network connectivity for Docker image pulls

### 🔄 Workflow Testing (Required)
- [ ] Create a test pull request to trigger the workflow
- [ ] Verify SSH connection step succeeds
- [ ] Confirm source code transfer completes
- [ ] Check Docker Compose execution runs successfully
- [ ] Validate results.json is generated and downloaded
- [ ] Verify artifact upload works correctly
- [ ] Test error handling with intentional failures
- [ ] Confirm cleanup steps execute properly

## Key Migration Changes Summary

### Interface Changes
```yaml
# OLD (Kubernetes)
secrets:
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
  KUBERNETES_CLUSTER_ID: ${{ secrets.KUBERNETES_CLUSTER_ID }}
  KUBERNETES_CONTEXT: ${{ secrets.KUBERNETES_CONTEXT }}
  SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}

# NEW (VM Server)
with:
  runner_type: "[self-hosted, linux]"
secrets:
  SERVER_PASSWORD: ${{ secrets.SERVER_PASSWORD }}
  SECURE_GITHUB_TOKEN: ${{ secrets.SECURE_GITHUB_TOKEN }}
```

### Execution Flow Changes
```
OLD: GitHub Actions → kubectl → Kubernetes → PVC → Results
NEW: GitHub Actions → SSH → VM Server → Docker Compose → Results
```

### File Structure Changes
```
OLD: k8s/semgrep-*.yaml files (still present for reference)
NEW: docker/semgrep-docker-compose.yml (reference template)
```

## Rollback Plan (If Needed)

If issues arise, the rollback process is:
1. Revert `semgrep-analysis.yml` to previous Kubernetes version
2. Revert `ci.yml` to use Kubernetes secrets
3. Ensure Kubernetes cluster and secrets are still available
4. Test with a pull request to confirm functionality

## Success Criteria

The migration is successful when:
- [ ] Pull requests trigger Semgrep analysis without errors
- [ ] Results.json artifact is generated and uploaded
- [ ] ERROR-level issues cause workflow failure (same as before)
- [ ] No changes required in calling workflows
- [ ] VM server cleanup completes successfully
- [ ] Performance is equal or better than Kubernetes approach

## Next Steps After Validation

1. **Monitor First Few Runs**: Watch initial executions closely
2. **Performance Comparison**: Compare execution times with previous approach
3. **Resource Usage**: Monitor VM server resource utilization
4. **Cleanup Verification**: Ensure no temporary files accumulate
5. **Documentation Updates**: Update any additional documentation as needed

## Support Information

- **Migration Documentation**: `docs/SEMGREP_VM_MIGRATION.md`
- **Docker Compose Reference**: `docker/semgrep-docker-compose.yml`
- **Workflow File**: `.github/workflows/semgrep-analysis.yml`
- **CI Integration**: `.github/workflows/ci.yml`
