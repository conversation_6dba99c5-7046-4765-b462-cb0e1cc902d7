# Domain Mapping Document: ATS Applications

## Overview

This document maps the domain entities, business processes, and data models between the source application (`ats-app-frontend`) and target application (`talentflow-ats-frontend`) to identify enhancement opportunities and implementation strategies.

---

## 1. Domain Architecture Comparison

### TalentFlow ATS (Target) - Domain Structure
```
├── Authentication Domain
├── Organization Management Domain (Multi-tenant)
├── User & Role Management Domain
├── Applicant Management Domain
├── Job Management Domain
├── Vendor Management Domain
├── Interview Management Domain
├── Communication Domain
├── Form Builder Domain
├── Analytics Domain
├── Onboarding Domain
└── Settings Domain
```

### ATS App (Source) - Domain Structure
```
├── Authentication Domain
├── User Management Domain
├── Applicant Management Domain
│   ├── Applicant Configuration
│   ├── Application Processing
│   ├── Career Sites Management
│   └── Resume Management
├── Job Management Domain
│   ├── Job Requests
│   ├── Job Requisitions
│   └── Job Templates
├── Placement Management Domain
│   ├── Cost Management
│   ├── Approval Workflows
│   └── Margin Calculations
├── Vendor Management Domain
├── Client Management Domain
├── Leads Management Domain
├── Talent Bench Domain
├── Administrative Configuration Domain
│   ├── System Settings
│   ├── Security Management
│   ├── Integration Management
│   └── Reminder Systems
└── Reporting Domain
```

---

## 2. Entity Mapping Analysis

### 2.1 User & Authentication Domain

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **User** | ✅ Comprehensive | ✅ Standard | **Direct mapping** - Enhance with additional fields |
| **Role** | ✅ RBAC system | ✅ Advanced RBAC | **Enhancement** - Add permission matrix |
| **Permission** | ✅ Module-based | ✅ Granular | **Enhancement** - Add field-level permissions |
| **Organization** | ✅ Multi-tenant | 🟡 Basic | **Target advantage** - Keep existing structure |
| **Session** | ✅ Advanced | ✅ Standard | **Direct mapping** - Maintain current approach |

### 2.2 Applicant Management Domain

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **Applicant** | ✅ Core entity | ✅ Enhanced entity | **Enhancement** - Add configuration fields |
| **ApplicantStatus** | 🟡 Basic | ✅ Pipeline-based | **New implementation** - Add pipeline statuses |
| **ApplicantSource** | ❌ Missing | ✅ Categorized | **New entity** - Implement source tracking |
| **ApplicantFunction** | ❌ Missing | ✅ Lookup table | **New entity** - Add function categorization |
| **ApplicantTechnology** | ❌ Missing | ✅ Skill tracking | **New entity** - Implement skill management |
| **DocumentType** | 🟡 Basic | ✅ Configurable | **Enhancement** - Add document type configuration |
| **NoticePeriod** | ❌ Missing | ✅ Configurable | **New entity** - Add notice period management |
| **Degree** | ❌ Missing | ✅ Education lookup | **New entity** - Add education tracking |
| **LanguageProficiency** | ❌ Missing | ✅ Language skills | **New entity** - Add language capabilities |

### 2.3 Job Management Domain

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **Job** | ✅ Core entity | ✅ Enhanced entity | **Enhancement** - Add workflow fields |
| **JobRequest** | ❌ Missing | ✅ Separate entity | **New entity** - Implement request workflow |
| **JobRequisition** | ❌ Missing | ✅ Approval flow | **New entity** - Add formal requisition process |
| **JobTemplate** | ❌ Missing | ✅ Reusable templates | **New entity** - Implement template system |
| **JobStatus** | 🟡 Basic | ✅ Configurable | **Enhancement** - Add status configuration |
| **JobPriority** | ❌ Missing | ✅ Priority levels | **New entity** - Add priority management |
| **DisqualificationReason** | ❌ Missing | ✅ Configurable | **New entity** - Add disqualification tracking |

### 2.4 Placement Management Domain (Major Gap)

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **Placement** | ❌ Missing | ✅ Core entity | **New domain** - Implement entire placement system |
| **CostSheet** | ❌ Missing | ✅ Financial tracking | **New entity** - Add cost management |
| **ApprovalWorkflow** | ❌ Missing | ✅ Multi-level | **New entity** - Implement approval processes |
| **OverheadExpense** | ❌ Missing | ✅ Cost calculation | **New entity** - Add expense tracking |
| **MarginFormula** | ❌ Missing | ✅ Profit calculation | **New entity** - Add margin management |
| **PayBreakup** | ❌ Missing | ✅ Salary structure | **New entity** - Add compensation breakdown |

### 2.5 Vendor Management Domain

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **Vendor** | ✅ Multi-tenant | ✅ Detailed profiles | **Enhancement** - Merge best features |
| **VendorClient** | ✅ Relationship mgmt | 🟡 Basic | **Target advantage** - Keep existing |
| **VendorContract** | ✅ Contract mgmt | ❌ Missing | **Target advantage** - Keep existing |
| **VendorPerformance** | ✅ Metrics | 🟡 Basic | **Target advantage** - Keep existing |
| **VendorDocumentType** | ❌ Missing | ✅ Configurable | **New entity** - Add document types |

### 2.6 Administrative Configuration Domain (Major Gap)

| Entity | TalentFlow ATS | ATS App | Mapping Strategy |
|--------|----------------|---------|------------------|
| **GlobalSetting** | ❌ Limited | ✅ Comprehensive | **New implementation** - Add global configuration |
| **SystemLookup** | ❌ Missing | ✅ Extensive | **New entity** - Implement lookup system |
| **CustomField** | ❌ Missing | ✅ Dynamic fields | **New entity** - Add field customization |
| **EmailTemplate** | ❌ Missing | ✅ Customizable | **New entity** - Add email templates |
| **ReminderConfiguration** | ❌ Missing | ✅ 7+ types | **New entity** - Add reminder system |
| **IntegrationAccount** | ❌ Missing | ✅ Multiple types | **New entity** - Add integration management |

---

## 3. Business Process Mapping

### 3.1 Applicant Processing Workflow

**TalentFlow ATS (Current):**
```
Application Received → Basic Review → Interview Scheduling → Decision
```

**ATS App (Enhanced):**
```
Application Received → Source Tracking → Pipeline Status → 
Configuration-based Processing → Multiple Status Transitions → 
Document Management → Resume Building → Submission Formatting
```

**Enhancement Strategy:** Implement pipeline-based status management with configurable workflows.

### 3.2 Job Management Workflow

**TalentFlow ATS (Current):**
```
Job Creation → Vendor Assignment → Candidate Matching
```

**ATS App (Enhanced):**
```
Job Request → Approval → Job Requisition → Template Application → 
Job Posting → Multi-channel Distribution → Candidate Sourcing
```

**Enhancement Strategy:** Add formal requisition workflow with approval processes.

### 3.3 Placement Process (Missing in Target)

**ATS App Process:**
```
Candidate Selection → Cost Sheet Creation → Approval Workflow → 
Overhead Calculation → Margin Analysis → Pay Breakup → 
Final Approval → Placement Confirmation
```

**Implementation Strategy:** Create entire placement management domain.

---

## 4. Data Model Enhancement Recommendations

### 4.1 High Priority Enhancements

#### Applicant Entity Enhancement
```typescript
// Current TalentFlow structure
interface Applicant {
  id: string;
  name: string;
  email: string;
  phone: string;
  // ... basic fields
}

// Enhanced structure (from ATS App)
interface ApplicantEnhanced {
  id: string;
  name: string;
  email: string;
  phone: string;
  // Add from source
  sourceId: string;
  functionId: string;
  technologies: string[];
  degreeId: string;
  languageProficiencies: LanguageProficiency[];
  noticePeriodId: string;
  pipelineStatusId: string;
  customFields: Record<string, any>;
}
```

#### Job Entity Enhancement
```typescript
// Current TalentFlow structure
interface Job {
  id: string;
  title: string;
  description: string;
  // ... basic fields
}

// Enhanced structure (from ATS App)
interface JobEnhanced {
  id: string;
  title: string;
  description: string;
  // Add from source
  requestId?: string;
  requisitionId?: string;
  templateId?: string;
  priorityId: string;
  statusId: string;
  approvalWorkflow: ApprovalStep[];
  customFields: Record<string, any>;
}
```

### 4.2 New Domain Implementations

#### Placement Management Domain
```typescript
interface Placement {
  id: string;
  applicantId: string;
  jobId: string;
  vendorId: string;
  clientId: string;
  costSheet: CostSheet;
  approvalStatus: ApprovalStatus;
  marginCalculation: MarginCalculation;
  payBreakup: PayBreakup;
}

interface CostSheet {
  baseSalary: number;
  overheadExpenses: OverheadExpense[];
  netMargin: number;
  totalCost: number;
}
```

#### Administrative Configuration Domain
```typescript
interface GlobalSetting {
  id: string;
  category: string;
  key: string;
  value: any;
  dataType: 'string' | 'number' | 'boolean' | 'json';
  isEditable: boolean;
}

interface SystemLookup {
  id: string;
  category: string;
  name: string;
  value: string;
  isActive: boolean;
  sortOrder: number;
}
```

---

## 5. Integration Points & API Mapping

### 5.1 Current TalentFlow APIs
- Authentication APIs
- Organization Management APIs
- User & Role APIs
- Applicant APIs
- Job APIs
- Vendor APIs
- Interview APIs
- Analytics APIs

### 5.2 Required New APIs (from ATS App)
- Admin Configuration APIs
- Placement Management APIs
- Job Requisition APIs
- System Lookup APIs
- Custom Field APIs
- Integration Management APIs
- Reminder Configuration APIs
- Advanced Reporting APIs

### 5.3 API Enhancement Strategy
1. **Extend existing APIs** with additional fields and functionality
2. **Create new API modules** for missing domains
3. **Implement configuration APIs** for admin features
4. **Add workflow APIs** for approval processes

---

## 6. Migration & Implementation Strategy

### 6.1 Phase 1: Foundation (Months 1-2)
- **Extend core entities** with additional fields
- **Implement lookup system** for configuration
- **Add custom fields infrastructure**
- **Create admin configuration framework**

### 6.2 Phase 2: Core Features (Months 3-4)
- **Implement placement management domain**
- **Add job requisition workflow**
- **Create integration management system**
- **Enhance applicant pipeline management**

### 6.3 Phase 3: Advanced Features (Months 5-6)
- **Add reminder and notification system**
- **Implement advanced reporting**
- **Create template management system**
- **Add career sites integration**

### 6.4 Phase 4: Optimization (Months 7-8)
- **Performance optimization**
- **Advanced security features**
- **Business intelligence enhancements**
- **Mobile application support**

---

## 7. Risk Assessment & Mitigation

### 7.1 Technical Risks
- **Data model complexity** - Mitigate with incremental implementation
- **Performance impact** - Implement caching and optimization
- **Integration challenges** - Use API-first approach

### 7.2 Business Risks
- **Feature disruption** - Maintain backward compatibility
- **User adoption** - Implement gradual rollout
- **Training requirements** - Create comprehensive documentation

---

*This domain mapping provides a comprehensive guide for enhancing TalentFlow ATS with proven features from the reference application while maintaining architectural integrity and multi-tenant capabilities.*
