# 🤖 **VA<PERSON><PERSON><PERSON>ION PROMPT TEMPLATE**
**For AI-Assisted Code Review Using 13-Step Framework**

---

## **📋 PROMPT TEMPLATE FOR AI CODE REVIEW**

Use this prompt template with AI assistants (<PERSON>, GPT, etc.) to perform comprehensive code reviews using our enhanced 13-step validation framework.

---

## **🔍 BASIC VALIDATION PROMPT**

```
Please perform a comprehensive code review of this Spring Boot microservice codebase using our enhanced 13-step validation framework:

**VALIDATION STEPS TO ASSESS:**
1. Java Coding Standards Compliance
2. SOLID Principles, DRY, YAGNI, and KISS Assessment
3. Microservices Architecture Readiness
4. JOOQ Database Access Efficiency
5. Transaction Management
6. Logging Implementation
7. Exception Handling Strategy
8. Security & Authentication
9. Observability & Monitoring
10. Testing Strategy & Coverage
11. Performance & Scalability
12. Idempotency & Safe Operations
13. DevOps & Deployment Readiness

**ANALYSIS REQUIREMENTS:**
- Provide specific code examples for each issue found
- Categorize issues as CR<PERSON><PERSON><PERSON>, HIGH, MEDIUM, or LOW priority
- Suggest concrete fixes with code samples
- Give an overall grade (A-F) for each validation step
- Provide a final production readiness assessment

**CODEBASE CONTEXT:**
- Spring Boot 3.x application
- Database: [PostgreSQL/MySQL/etc.] with [JOOQ/JPA/etc.]
- RESTful API with OpenAPI documentation
- Microservices architecture
- Production deployment target

**OUTPUT FORMAT:**
Please structure your response as a comprehensive markdown report with:
- Executive summary with overall grade
- Detailed analysis for each of the 13 steps
- Priority matrix for issues found
- Actionable recommendations
- Production readiness verdict

[PASTE YOUR CODE HERE]
```

---

## **🔄 IDEMPOTENCY-FOCUSED VALIDATION PROMPT**

For critical production systems requiring safe retry operations:

```
Please perform a focused idempotency and safe operations review using Step 12 of our validation framework:

**IDEMPOTENCY VALIDATION CRITERIA:**
- [ ] **HTTP Method Idempotency** - GET, PUT, DELETE are idempotent
- [ ] **Business Logic Idempotency** - Operations safe to retry
- [ ] **Database Idempotency** - Unique constraints and upsert patterns
- [ ] **Request Deduplication** - Idempotency keys for critical operations
- [ ] **Side Effect Management** - External calls are idempotent or compensatable

**CRITICAL AREAS TO ASSESS:**
1. **API Endpoints** - POST operations with idempotency key support
2. **Database Operations** - Upsert patterns and conditional updates
3. **External Service Calls** - File uploads, payments, notifications
4. **Concurrent Request Handling** - Race condition protection
5. **Error Recovery** - Partial failure handling and compensation

**PRODUCTION RISKS TO IDENTIFY:**
- Duplicate data creation from network retries
- Multiple charges/notifications from retry attempts
- Data inconsistency from partial failures
- Race conditions in concurrent operations
- Non-compensatable side effects

**EXPECTED OUTPUT:**
- Specific non-idempotent operations identified
- Code examples of problematic patterns
- Recommended idempotency implementation patterns
- Database schema changes needed
- Testing strategies for idempotent behavior

[PASTE YOUR CODE HERE]
```

---

## **🎯 FOCUSED VALIDATION PROMPT**

For reviewing specific areas or smaller changes:

```
Please review this code change focusing on specific validation areas from our 13-step framework:

**FOCUS AREAS:** [Select relevant steps]
□ Java Coding Standards (Step 1)
□ SOLID Principles (Step 2)
□ Architecture (Step 3)
□ Database Access (Step 4)
□ Transactions (Step 5)
□ Logging (Step 6)
□ Exception Handling (Step 7)
□ Security (Step 8)
□ Monitoring (Step 9)
□ Testing (Step 10)
□ Performance (Step 11)
□ Idempotency & Safe Operations (Step 12)
□ DevOps (Step 13)

**CHANGE CONTEXT:**
- Type of change: [Feature/Bug Fix/Refactor/etc.]
- Affected components: [Controllers/Services/Repositories/etc.]
- Risk level: [Low/Medium/High]

**SPECIFIC CONCERNS:**
[List any specific areas of concern or questions]

[PASTE YOUR CODE CHANGES HERE]
```

---

## **🚀 PR REVIEW PROMPT**

For Pull Request reviews:

```
Please review this Pull Request using our 13-step validation framework:

**PR DETAILS:**
- Title: [PR Title]
- Description: [PR Description]
- Files changed: [Number] files
- Lines added/removed: +[X]/-[Y]

**VALIDATION FOCUS:**
Since this is a PR review, please focus on:
1. Code quality issues that would block merge
2. Architecture violations
3. Security concerns
4. Performance impacts
5. Testing adequacy

**REVIEW CRITERIA:**
- All CRITICAL issues must be resolved before merge
- HIGH priority issues should be addressed
- Provide specific, actionable feedback
- Suggest improvements with code examples

**PR CHANGES:**
[PASTE PR DIFF OR CHANGED FILES HERE]

**EXPECTED OUTPUT:**
- ✅ APPROVE / ⚠️ REQUEST CHANGES / ❌ REJECT
- Detailed feedback for each validation step
- Specific line-by-line comments where applicable
- Overall assessment and recommendations
```

---

## **📊 PRODUCTION READINESS PROMPT**

For comprehensive production readiness assessment:

```
Please perform a complete production readiness assessment of this Spring Boot microservice using all 13 validation steps:

**ASSESSMENT SCOPE:**
- Complete codebase review
- Architecture evaluation
- Security assessment
- Performance analysis
- Operational readiness

**PRODUCTION CONTEXT:**
- Expected load: [Specify expected traffic/users]
- SLA requirements: [Response time/availability requirements]
- Security requirements: [Compliance needs]
- Deployment environment: [Kubernetes/Cloud platform]

**CRITICAL EVALUATION AREAS:**
1. Security vulnerabilities and compliance
2. Performance bottlenecks and scalability
3. Monitoring and observability gaps
4. Testing coverage and quality
5. Deployment and operational readiness

**DELIVERABLE:**
Comprehensive production readiness report with:
- Executive summary with GO/NO-GO recommendation
- Detailed findings for all 13 validation steps
- Risk assessment matrix
- Remediation roadmap with priorities
- Sign-off checklist for production deployment

[PASTE COMPLETE CODEBASE OR PROVIDE REPOSITORY ACCESS]
```

---

## **🔧 CUSTOM VALIDATION PROMPT**

For specific technology stacks or requirements:

```
Please review this [TECHNOLOGY STACK] codebase using our adapted 13-step validation framework:

**TECHNOLOGY STACK:**
- Framework: [Spring Boot/Other]
- Database: [PostgreSQL/MySQL/Other]
- ORM: [JOOQ/JPA/Other]
- Security: [Spring Security/Other]
- Testing: [JUnit/TestNG/Other]

**CUSTOM VALIDATION CRITERIA:**
[Modify the 13 steps based on your specific technology stack]

**SPECIFIC REQUIREMENTS:**
- [Any specific coding standards]
- [Performance requirements]
- [Security compliance needs]
- [Deployment constraints]

**FOCUS AREAS:**
[Specify which of the 13 steps are most critical for your context]

[PASTE YOUR CODE HERE]
```

---

## **📝 USAGE INSTRUCTIONS**

### **For Development Teams:**
1. **Choose appropriate prompt** based on review scope
2. **Customize the context** with your specific details
3. **Paste your code** or provide repository access
4. **Review AI feedback** thoroughly
5. **Implement suggested improvements**

### **For Code Reviews:**
1. **Use PR Review Prompt** for pull requests
2. **Focus on blocking issues** first
3. **Provide specific feedback** to developers
4. **Track improvement trends** over time

### **For Production Deployment:**
1. **Use Production Readiness Prompt** before deployment
2. **Address all CRITICAL issues** before proceeding
3. **Document accepted risks** for any remaining issues
4. **Get stakeholder sign-off** on assessment

---

## **⚡ QUICK REFERENCE**

### **Priority Levels:**
- **🚨 CRITICAL** - Must fix before merge/deployment
- **🔴 HIGH** - Should fix before production
- **🟡 MEDIUM** - Technical debt, plan for future
- **🟢 LOW** - Nice to have improvements

### **Grade Scale:**
- **A (90-100%)** - Excellent, production-ready
- **B (80-89%)** - Good, minor improvements needed
- **C (70-79%)** - Acceptable, moderate improvements needed
- **D (60-69%)** - Poor, significant improvements required
- **F (<60%)** - Failing, major rework required

### **Common Issues to Watch For:**
- Duplicate imports and poor formatting
- SOLID principle violations
- Missing error handling
- Security vulnerabilities
- Performance bottlenecks
- Insufficient testing
- Poor logging practices
- Missing documentation

---

## **🔄 CONTINUOUS IMPROVEMENT**

### **Update This Template When:**
- New validation criteria are added
- Technology stack changes
- Team standards evolve
- Lessons learned from reviews

### **Track Metrics:**
- Review completion time
- Issue detection rate
- Fix implementation rate
- Code quality trends

---

**Use these prompts to ensure consistent, thorough code reviews that maintain high quality standards and production readiness.**
