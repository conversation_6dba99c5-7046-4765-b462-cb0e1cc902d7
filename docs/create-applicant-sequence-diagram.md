# Create Applicant - Sequence Diagram

## Overview
This sequence diagram shows the complete request flow for the `POST /api/v1/applicant/create` endpoint, which is the most complex applicant management operation involving multiple services, file uploads, validation, and database operations.

## Mermaid Sequence Diagram

```mermaid
sequenceDiagram
    participant Client
    participant ApplicantController
    participant FileValidationService
    participant ObjectMapper
    participant ApplicantValidator
    participant FileUploadService
    participant DocumentRepoService
    participant FileStoreService
    participant ApplicantService
    participant ApplicantMapper
    participant ApplicantRepository
    participant DocumentUpdateProcessor
    participant Database
    participant DocumentRepoRepository

    Note over Client, DocumentRepoRepository: POST /api/v1/applicant/create - Create Applicant with File Uploads

    %% 1. Initial Request
    Client->>+ApplicantController: POST /api/v1/applicant/create<br/>Content-Type: multipart/form-data<br/>Body: applicantData (JSON) + files[]

    %% 2. File Validation (if files present)
    alt Files Present
        ApplicantController->>+FileValidationService: validateFiles(files)
        FileValidationService->>FileValidationService: Check file types, sizes, count
        FileValidationService-->>-ApplicantController: Validation Result
        Note over FileValidationService: Validates: PDF, DOC, DOCX, JPG, JPEG, PNG, TXT, CSV<br/>Max 10MB per file, Max 20 files
    end

    %% 3. JSON Parsing and Validation
    ApplicantController->>+ObjectMapper: readValue(applicantDataJson, ApplicantRequestDTO.class)
    ObjectMapper->>ObjectMapper: Parse JSON to DTO
    ObjectMapper-->>-ApplicantController: ApplicantRequestDTO
    
    Note over ApplicantController: @ValidApplicantData annotation<br/>validates JSON structure

    %% 4. Service Layer Call
    ApplicantController->>+ApplicantService: createApplicant(requestDTO, files)

    %% 5. Email Uniqueness Validation (Outside Transaction)
    ApplicantService->>+ApplicantValidator: validateEmailUniqueness(email, orgId)
    ApplicantValidator->>+ApplicantRepository: existsByEmail(email)
    ApplicantRepository->>+Database: SELECT EXISTS(...) FROM applicant<br/>WHERE email = ? AND is_active = true
    Database-->>-ApplicantRepository: Boolean result
    ApplicantRepository-->>-ApplicantValidator: exists
    
    alt Email Already Exists
        ApplicantValidator-->>ApplicantService: DuplicateApplicantException
        ApplicantService-->>ApplicantController: Exception
        ApplicantController-->>Client: 409 Conflict
    else Email Unique
        ApplicantValidator-->>-ApplicantService: Validation Passed
    end

    %% 6. File Upload Processing (Outside Transaction)
    alt Files Present
        ApplicantService->>+FileUploadService: uploadFilesWithMetadata(files, requestDTO)
        
        loop For Each File
            FileUploadService->>+DocumentRepoService: uploadMultipleFilesWithIndividualMetadata(files, requestDTO)
            DocumentRepoService->>DocumentRepoService: validateRecipientAndSenderDetails(documentRepoDTO)
            DocumentRepoService->>DocumentRepoService: validateFileType(file)
            DocumentRepoService->>DocumentRepoService: Generate UUID for document
            
            DocumentRepoService->>+FileStoreService: storeFileWithRetry(filePath, absolutePath, fileName)
            FileStoreService->>FileStoreService: Store file to filesystem/cloud
            FileStoreService-->>-DocumentRepoService: File location
            
            DocumentRepoService->>+DocumentRepoRepository: insertDocument(documentRepo)
            DocumentRepoRepository->>+Database: INSERT INTO document_repo<br/>(id, path, category, created_on, ...)
            Database-->>-DocumentRepoRepository: Insert result
            DocumentRepoRepository-->>-DocumentRepoService: Document ID
            
            DocumentRepoService-->>-FileUploadService: DocumentUploadResponseDTO
        end
        
        FileUploadService-->>-ApplicantService: List<DocumentUploadResponseDTO>
        ApplicantService->>ApplicantService: Build documents JSONB string
    else No Files
        ApplicantService->>ApplicantService: Set documentsJsonb = "[]"
    end

    %% 7. Database Transaction (Focused Transaction)
    ApplicantService->>+ApplicantService: createApplicantRecord(requestDTO, documentsJsonb, uploadedDocuments)
    Note over ApplicantService: @Transactional(rollbackFor = Exception.class)
    
    ApplicantService->>+ApplicantMapper: toCompleteEntity(requestDTO)
    ApplicantMapper->>ApplicantMapper: Map all fields + generate system code
    ApplicantMapper->>ApplicantMapper: Set documentsJsonb, timestamps, UUID
    ApplicantMapper-->>-ApplicantService: Complete Applicant entity

    ApplicantService->>+ApplicantRepository: createApplicant(applicant)
    ApplicantRepository->>+Database: INSERT INTO applicant<br/>(id, first_name, last_name, email, documents, system_code, ...)
    Database-->>-ApplicantRepository: Insert result
    ApplicantRepository-->>-ApplicantService: Saved Applicant entity

    ApplicantService->>ApplicantService: Build CreateApplicantResponseDTO
    ApplicantService-->>-ApplicantService: CreateApplicantResponseDTO

    ApplicantService-->>-ApplicantController: CreateApplicantResponseDTO

    %% 8. Success Response
    ApplicantController->>ApplicantController: Create Location header: /api/v1/applicant/{id}
    ApplicantController-->>-Client: 201 Created<br/>Location: /api/v1/applicant/{id}<br/>Body: CreateApplicantResponseDTO

    %% Error Handling Paths
    Note over Client, DocumentRepoRepository: Error Handling Scenarios

    %% File Upload Failure Compensation
    alt File Upload Fails After Some Success
        FileUploadService->>FileUploadService: deleteFiles(uploadedDocuments)
        Note over FileUploadService: Cleanup uploaded files<br/>for transaction consistency
    end

    %% Database Transaction Rollback
    alt Database Transaction Fails
        Database-->>ApplicantRepository: SQLException
        ApplicantRepository-->>ApplicantService: DatabaseOperationException
        Note over ApplicantService: @Transactional rollback<br/>All database changes reverted
        ApplicantService-->>ApplicantController: Exception
        ApplicantController-->>Client: 500 Internal Server Error
    end

    %% Validation Failures
    alt Validation Failures
        Note over ApplicantController: Various validation points:<br/>- File validation (413, 415)<br/>- JSON parsing (400)<br/>- Email uniqueness (409)<br/>- Bean validation (400)
    end
```

## Key Flow Points

### 1. Pre-Transaction Operations
- File validation and upload processing
- Email uniqueness validation
- JSON parsing and validation

### 2. Transaction Scope
- Only the database insert operation is transactional
- Minimizes database connection hold time
- Ensures data consistency

### 3. Error Handling
- Multiple validation layers with specific error responses
- Compensation patterns for file cleanup
- Proper HTTP status code mapping

### 4. Performance Considerations
- File I/O operations outside transaction
- Optimized database queries
- Efficient validation strategies

---
*Generated from codebase analysis on 2025-09-06*
