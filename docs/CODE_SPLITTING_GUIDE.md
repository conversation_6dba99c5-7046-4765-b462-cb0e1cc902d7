# Code Splitting Strategy Guide

This guide explains the comprehensive code splitting implementation for the applicant CRUD system, designed to improve performance through intelligent lazy loading.

## Overview

Code splitting reduces initial bundle size by loading components only when needed, resulting in:
- **Faster Initial Load**: Smaller initial JavaScript bundle
- **Better User Experience**: Progressive loading with proper fallbacks
- **Reduced Memory Usage**: Components loaded only when used
- **Intelligent Preloading**: Strategic preloading based on user behavior

## Implementation Architecture

### 1. **Route-Level Splitting**
```typescript
// ❌ Before: All components loaded eagerly
import ApplicantsPage from '@/pages/applicants';

// ✅ After: Lazy loading with error boundaries
const LazyApplicantsPage = lazy(() => import('@/components/lazy/LazyApplicantsPage'));
```

### 2. **Component-Level Splitting**
```typescript
// ❌ Before: Eager form imports
import PersonalInfoForm from "./forms/PersonalInfoForm";
import ProfessionalInfoForm from "./forms/ProfessionalInfoForm";

// ✅ After: Lazy form loading
const PersonalInfoForm = lazy(() => import("./forms/PersonalInfoForm"));
const ProfessionalInfoForm = lazy(() => import("./forms/ProfessionalInfoForm"));
```

### 3. **Dynamic Step Loading**
```typescript
// ✅ Only load form steps when user navigates to them
<LazyFormStep
  stepId="personal-info"
  activeStep={activeStep}
  importFn={() => import("./forms/PersonalInfoForm")}
  componentProps={formProps}
/>
```

## Configuration Options

### Environment-Specific Settings

**Development:**
```typescript
performance: {
  enableCodeSplitting: true,
  preloadStrategy: 'user-interaction',
  chunkSize: 'medium',
}
```

**Production:**
```typescript
performance: {
  enableCodeSplitting: true,
  preloadStrategy: 'aggressive',
  chunkSize: 'large',
}
```

**Testing:**
```typescript
performance: {
  enableCodeSplitting: false, // Simpler testing
  preloadStrategy: 'lazy',
  chunkSize: 'small',
}
```

### Preloading Strategies

1. **Aggressive**: Preload all components immediately
   - Best for: Users likely to use most features
   - Trade-off: Higher initial load, instant navigation

2. **User-Interaction**: Preload on first user interaction
   - Best for: Balanced performance and user experience
   - Trade-off: Slight delay on first interaction

3. **Lazy**: Load only when needed
   - Best for: Minimal initial load requirements
   - Trade-off: Loading delay when navigating

## Implementation Examples

### 1. Basic Lazy Loading

```typescript
import { Suspense, lazy } from 'react';
import { FormFallback } from '@/components/ui/LazyLoadFallback';

const PersonalInfoForm = lazy(() => import('./forms/PersonalInfoForm'));

function FormWizard() {
  return (
    <Suspense fallback={<FormFallback />}>
      <PersonalInfoForm {...props} />
    </Suspense>
  );
}
```

### 2. Advanced Lazy Loading with Error Boundaries

```typescript
import { ErrorBoundary } from 'react-error-boundary';
import { LazyLoadErrorFallback } from '@/components/ui/LazyLoadFallback';

function FormWizardWithErrorHandling() {
  return (
    <ErrorBoundary
      FallbackComponent={({ error, resetErrorBoundary }) => (
        <LazyLoadErrorFallback
          error={error}
          resetError={resetErrorBoundary}
          componentName="Personal Info Form"
        />
      )}
    >
      <Suspense fallback={<FormFallback />}>
        <PersonalInfoForm {...props} />
      </Suspense>
    </ErrorBoundary>
  );
}
```

### 3. Dynamic Step Loading

```typescript
import { LazyFormStep } from '@/components/applicants/LazyFormStep';

function WizardWithDynamicSteps() {
  const formSteps = [
    {
      id: 'personal-info',
      importFn: () => import('./forms/PersonalInfoForm'),
    },
    {
      id: 'professional-info', 
      importFn: () => import('./forms/ProfessionalInfoForm'),
    },
  ];

  return (
    <div>
      {formSteps.map(step => (
        <LazyFormStep
          key={step.id}
          stepId={step.id}
          activeStep={activeStep}
          importFn={step.importFn}
          componentProps={getStepProps(step.id)}
        />
      ))}
    </div>
  );
}
```

### 4. Preloading Components

```typescript
import { preloadApplicantComponents } from '@/components/lazy/LazyApplicantsPage';

// Preload specific components
await preloadApplicantComponents([
  'ApplicantFormWizard',
  'PersonalInfoForm'
]);

// Preload all form steps
import { preloadAllFormSteps } from '@/components/applicants/LazyFormStep';

await preloadAllFormSteps({
  'personal-info': () => import('./forms/PersonalInfoForm'),
  'professional-info': () => import('./forms/ProfessionalInfoForm'),
});
```

## Loading States and Fallbacks

### Available Fallback Components

1. **FormFallback**: Optimized for form components
2. **WizardFallback**: Designed for wizard steps
3. **CardFallback**: General card-like components
4. **MinimalFallback**: Simple loading indicator

```typescript
import { 
  FormFallback, 
  WizardFallback, 
  CardFallback, 
  MinimalFallback 
} from '@/components/ui/LazyLoadFallback';

// Custom fallback
<LazyLoadFallback
  variant="form"
  size="lg"
  message="Loading form..."
/>
```

### Error Handling

```typescript
import { LazyLoadErrorFallback } from '@/components/ui/LazyLoadFallback';

<LazyLoadErrorFallback
  error={error}
  resetError={() => window.location.reload()}
  componentName="Applicant Form"
/>
```

## Performance Monitoring

### Component Cache Statistics

```typescript
import { getFormStepCacheStats } from '@/components/applicants/LazyFormStep';

const stats = getFormStepCacheStats();
console.log('Cached components:', stats.cached);
console.log('Currently loading:', stats.preloading);
console.log('Cache size:', stats.cacheSize);
```

### Chunk Loading Statistics

```typescript
import { getChunkLoadingStats } from '@/components/lazy/LazyApplicantsPage';

const chunkStats = getChunkLoadingStats();
console.log('Chunks loaded:', chunkStats.chunksLoaded);
console.log('Failed chunks:', chunkStats.chunksFailed);
```

## Best Practices

### 1. **Strategic Splitting Points**
- Split at route boundaries (pages)
- Split large form components
- Split rarely-used features
- Don't split small components (overhead > benefit)

### 2. **Fallback Design**
- Match the expected component layout
- Provide meaningful loading messages
- Use skeleton screens for better UX
- Include error recovery options

### 3. **Preloading Strategy**
- Preload on user interaction for balance
- Use aggressive preloading for power users
- Consider network conditions
- Monitor actual usage patterns

### 4. **Error Handling**
- Always wrap lazy components in error boundaries
- Provide retry mechanisms
- Log errors for monitoring
- Graceful degradation

## Bundle Analysis

### Webpack Bundle Analyzer
```bash
# Analyze bundle composition
npm run build:analyze

# Focus on largest chunks
npm run build && npx webpack-bundle-analyzer dist/assets
```

### Key Metrics to Monitor
- **Initial bundle size**: Target < 250KB gzipped
- **Chunk load time**: Target < 1s on 3G
- **Cache hit ratio**: Target > 80%
- **Error rate**: Target < 1%

## Migration Checklist

- [ ] **Identify splitting points** - Large components and routes
- [ ] **Implement lazy loading** - Convert imports to React.lazy()
- [ ] **Add Suspense boundaries** - Wrap with proper fallbacks
- [ ] **Add error boundaries** - Handle loading failures
- [ ] **Configure preloading** - Set appropriate strategy
- [ ] **Test loading states** - Verify fallbacks work
- [ ] **Monitor performance** - Track bundle sizes and load times
- [ ] **Update navigation** - Ensure smooth transitions

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```typescript
   // ❌ Named imports don't work with React.lazy
   const { PersonalInfoForm } = lazy(() => import('./forms'));
   
   // ✅ Default import required
   const PersonalInfoForm = lazy(() => import('./forms/PersonalInfoForm'));
   ```

2. **Missing Suspense Boundaries**
   ```typescript
   // ❌ Will cause error
   <PersonalInfoForm />
   
   // ✅ Wrapped in Suspense
   <Suspense fallback={<FormFallback />}>
     <PersonalInfoForm />
   </Suspense>
   ```

3. **Chunk Load Failures**
   ```typescript
   // ✅ Handle with error boundary
   <ErrorBoundary FallbackComponent={LazyLoadErrorFallback}>
     <Suspense fallback={<FormFallback />}>
       <PersonalInfoForm />
     </Suspense>
   </ErrorBoundary>
   ```

### Performance Issues

1. **Too Many Small Chunks**
   - Combine related components
   - Increase minimum chunk size
   - Use chunk groups

2. **Slow Loading**
   - Implement preloading
   - Optimize network requests
   - Use service workers for caching

3. **Poor User Experience**
   - Improve loading fallbacks
   - Add progress indicators
   - Implement offline handling

## Future Enhancements

1. **Service Worker Integration**: Cache chunks for offline use
2. **Network-Aware Loading**: Adjust strategy based on connection
3. **User Behavior Analytics**: Optimize preloading patterns
4. **A/B Testing**: Test different splitting strategies
5. **Performance Monitoring**: Real-time bundle performance tracking

The code splitting implementation provides a solid foundation for scalable performance optimization while maintaining excellent user experience. 