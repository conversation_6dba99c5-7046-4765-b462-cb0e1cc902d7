# 🏗️ TalentFlow ATS - Domain-Driven Design Specification

## 📋 **Executive Summary**

This document outlines the transformation of the TalentFlow ATS prototype into a Domain-Driven Design (DDD) architecture. The current prototype demonstrates excellent functionality across 10 major feature areas, and this specification provides a roadmap for evolving it into a scalable, maintainable, and business-aligned architecture.

## 🎯 **Current State Analysis**

### **✅ Existing Strengths**
- **Multi-tenant architecture** with organization-based isolation
- **Comprehensive RBAC** with 6 roles and 10 permission modules
- **Feature completeness** across all major ATS domains
- **TypeScript implementation** with strong type safety
- **React 18 + Modern tooling** (Vite, TanStack Query, shadcn/ui)

### **🔄 Areas for DDD Enhancement**
- **Business logic scattered** across UI components
- **Anemic domain models** (data structures without behavior)
- **Tight coupling** between presentation and business logic
- **Limited domain events** and cross-context communication
- **Repository pattern** not fully implemented

## 🏛️ **Domain Architecture Overview**

### **Core Business Domains Identified**

```mermaid
graph TB
    subgraph "Core Domains"
        IAM[Identity & Access Management]
        TA[Talent Acquisition]
        IM[Interview Management]
    end
    
    subgraph "Supporting Domains"
        VM[Vendor Management]
        COMM[Communication]
        FM[Form Management]
    end
    
    subgraph "Generic Domains"
        AR[Analytics & Reporting]
        SA[System Administration]
        NOT[Notifications]
    end
    
    IAM --> TA
    IAM --> IM
    TA --> IM
    VM --> TA
    COMM --> TA
    FM --> TA
```

## 🎯 **Bounded Context Design**

### **1. Identity & Access Management Context**

**Responsibility**: User authentication, authorization, role management, and organization management.

**Core Entities**:
- `User` (Aggregate Root)
- `Organization` (Aggregate Root)
- `Role` (Entity)
- `Permission` (Value Object)

**Domain Services**:
- `AuthenticationService`
- `AuthorizationService`
- `RoleAssignmentService`

**Repository Interfaces**:
- `IUserRepository`
- `IOrganizationRepository`
- `IRoleRepository`

### **2. Talent Acquisition Context**

**Responsibility**: Job management, candidate pipeline, application processing, and hiring workflows.

**Core Entities**:
- `Job` (Aggregate Root)
- `Candidate` (Aggregate Root)
- `Application` (Entity)
- `Pipeline` (Entity)

**Value Objects**:
- `JobRequirements`
- `CandidateProfile`
- `ApplicationStatus`

**Domain Services**:
- `CandidateMatchingService`
- `PipelineProgressService`
- `ApplicationProcessingService`

### **3. Interview Management Context**

**Responsibility**: Interview scheduling, feedback collection, and evaluation processes.

**Core Entities**:
- `Interview` (Aggregate Root)
- `InterviewSchedule` (Entity)
- `Feedback` (Entity)
- `Evaluation` (Entity)

**Domain Services**:
- `SchedulingService`
- `FeedbackAggregationService`
- `EvaluationScoringService`

### **4. Vendor Management Context**

**Responsibility**: Vendor relationships, performance tracking, and collaboration workflows.

**Core Entities**:
- `VendorRelationship` (Aggregate Root)
- `VendorPerformance` (Entity)
- `ServiceAgreement` (Entity)

**Domain Services**:
- `VendorPerformanceCalculationService`
- `RelationshipManagementService`

### **5. Communication Context**

**Responsibility**: Messaging, notifications, and cross-organizational communication.

**Core Entities**:
- `Conversation` (Aggregate Root)
- `Message` (Entity)
- `Notification` (Entity)

**Domain Services**:
- `MessageDeliveryService`
- `NotificationDispatchService`

### **6. Form Management Context**

**Responsibility**: Dynamic form creation, validation, and submission processing.

**Core Entities**:
- `FormTemplate` (Aggregate Root)
- `FormInstance` (Entity)
- `FormSubmission` (Entity)

**Domain Services**:
- `FormValidationService`
- `FormRenderingService`

## 🏗️ **Domain Model Implementation**

### **Example: Talent Acquisition Domain**

```typescript
// Domain/TalentAcquisition/Entities/Job.ts
export class Job {
  private constructor(
    private readonly id: JobId,
    private readonly organizationId: OrganizationId,
    private title: JobTitle,
    private requirements: JobRequirements,
    private status: JobStatus,
    private pipeline: Pipeline,
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {}

  public static create(
    organizationId: OrganizationId,
    title: JobTitle,
    requirements: JobRequirements
  ): Job {
    const id = JobId.generate();
    const pipeline = Pipeline.createDefault();
    const status = JobStatus.DRAFT;
    
    return new Job(
      id,
      organizationId,
      title,
      requirements,
      status,
      pipeline,
      new Date(),
      new Date()
    );
  }

  public publish(): void {
    if (!this.canBePublished()) {
      throw new DomainError('Job cannot be published in current state');
    }
    
    this.status = JobStatus.PUBLISHED;
    this.updatedAt = new Date();
    
    // Emit domain event
    DomainEvents.raise(new JobPublishedEvent(this.id, this.organizationId));
  }

  public addCandidate(candidate: Candidate): Application {
    if (!this.isAcceptingApplications()) {
      throw new DomainError('Job is not accepting applications');
    }

    const application = Application.create(this.id, candidate.getId());
    
    // Emit domain event
    DomainEvents.raise(new CandidateAppliedEvent(this.id, candidate.getId()));
    
    return application;
  }

  private canBePublished(): boolean {
    return this.status === JobStatus.DRAFT && 
           this.requirements.isComplete() &&
           this.title.isValid();
  }

  private isAcceptingApplications(): boolean {
    return this.status === JobStatus.PUBLISHED;
  }

  // Getters
  public getId(): JobId { return this.id; }
  public getOrganizationId(): OrganizationId { return this.organizationId; }
  public getTitle(): JobTitle { return this.title; }
  public getStatus(): JobStatus { return this.status; }
}
```

### **Value Objects Example**

```typescript
// Domain/TalentAcquisition/ValueObjects/JobTitle.ts
export class JobTitle {
  private constructor(private readonly value: string) {
    this.validate();
  }

  public static create(title: string): JobTitle {
    return new JobTitle(title);
  }

  private validate(): void {
    if (!this.value || this.value.trim().length === 0) {
      throw new DomainError('Job title cannot be empty');
    }
    
    if (this.value.length > 100) {
      throw new DomainError('Job title cannot exceed 100 characters');
    }
  }

  public isValid(): boolean {
    return this.value.trim().length > 0 && this.value.length <= 100;
  }

  public getValue(): string {
    return this.value;
  }

  public equals(other: JobTitle): boolean {
    return this.value === other.value;
  }
}
```

### **Domain Events**

```typescript
// Domain/TalentAcquisition/Events/JobPublishedEvent.ts
export class JobPublishedEvent implements IDomainEvent {
  public readonly occurredOn: Date;
  public readonly eventId: string;

  constructor(
    public readonly jobId: JobId,
    public readonly organizationId: OrganizationId
  ) {
    this.occurredOn = new Date();
    this.eventId = crypto.randomUUID();
  }
}

// Domain/TalentAcquisition/Events/CandidateAppliedEvent.ts
export class CandidateAppliedEvent implements IDomainEvent {
  public readonly occurredOn: Date;
  public readonly eventId: string;

  constructor(
    public readonly jobId: JobId,
    public readonly candidateId: CandidateId
  ) {
    this.occurredOn = new Date();
    this.eventId = crypto.randomUUID();
  }
}
```

## 📁 **Proposed Folder Structure**

```
src/
├── Domain/
│   ├── Shared/
│   │   ├── ValueObjects/
│   │   ├── Events/
│   │   └── Interfaces/
│   ├── IdentityAccess/
│   │   ├── Entities/
│   │   ├── ValueObjects/
│   │   ├── Services/
│   │   ├── Events/
│   │   └── Repositories/
│   ├── TalentAcquisition/
│   │   ├── Entities/
│   │   ├── ValueObjects/
│   │   ├── Services/
│   │   ├── Events/
│   │   └── Repositories/
│   └── [Other Contexts]/
├── Application/
│   ├── Services/
│   ├── Commands/
│   ├── Queries/
│   └── DTOs/
├── Infrastructure/
│   ├── Repositories/
│   ├── Services/
│   └── EventHandlers/
├── Presentation/
│   ├── Components/
│   ├── Pages/
│   └── Hooks/
└── Shared/
    ├── Types/
    ├── Utils/
    └── Constants/
```

## 🔄 **Migration Strategy**

### **Phase 1: Foundation (Weeks 1-2)**
1. **Setup DDD Infrastructure**
   - Create domain event system
   - Implement base classes (Entity, ValueObject, AggregateRoot)
   - Setup repository interfaces

2. **Extract Domain Models**
   - Start with Identity & Access Management context
   - Create User and Organization aggregates
   - Implement basic domain services

### **Phase 2: Core Domains (Weeks 3-6)**
1. **Talent Acquisition Context**
   - Extract Job and Candidate aggregates
   - Implement application processing logic
   - Create pipeline management services

2. **Interview Management Context**
   - Extract Interview aggregate
   - Implement scheduling domain logic
   - Create feedback aggregation services

### **Phase 3: Supporting Domains (Weeks 7-10)**
1. **Vendor Management Context**
2. **Communication Context**
3. **Form Management Context**

### **Phase 4: Integration & Optimization (Weeks 11-12)**
1. **Cross-context integration**
2. **Event-driven communication**
3. **Performance optimization**
4. **Testing and validation**

## 🎯 **Next Steps**

1. **Review and Approve** this DDD specification
2. **Create detailed implementation plan** for Phase 1
3. **Setup development environment** with DDD structure
4. **Begin migration** starting with Identity & Access Management context

## 🔧 **Implementation Patterns**

### **Repository Pattern Implementation**

```typescript
// Domain/TalentAcquisition/Repositories/IJobRepository.ts
export interface IJobRepository {
  findById(id: JobId): Promise<Job | null>;
  findByOrganization(organizationId: OrganizationId): Promise<Job[]>;
  save(job: Job): Promise<void>;
  delete(id: JobId): Promise<void>;
}

// Infrastructure/Repositories/JobRepository.ts
export class JobRepository implements IJobRepository {
  constructor(private readonly apiClient: ApiClient) {}

  async findById(id: JobId): Promise<Job | null> {
    try {
      const data = await this.apiClient.get(`/jobs/${id.getValue()}`);
      return JobMapper.toDomain(data);
    } catch (error) {
      if (error.status === 404) return null;
      throw error;
    }
  }

  async findByOrganization(organizationId: OrganizationId): Promise<Job[]> {
    const data = await this.apiClient.get(`/organizations/${organizationId.getValue()}/jobs`);
    return data.map(JobMapper.toDomain);
  }

  async save(job: Job): Promise<void> {
    const dto = JobMapper.toDTO(job);
    await this.apiClient.post('/jobs', dto);
  }
}
```

### **Application Service Pattern**

```typescript
// Application/Services/JobApplicationService.ts
export class JobApplicationService {
  constructor(
    private readonly jobRepository: IJobRepository,
    private readonly candidateRepository: ICandidateRepository,
    private readonly eventBus: IEventBus
  ) {}

  async publishJob(command: PublishJobCommand): Promise<void> {
    const job = await this.jobRepository.findById(new JobId(command.jobId));
    if (!job) {
      throw new ApplicationError('Job not found');
    }

    // Domain logic encapsulated in the aggregate
    job.publish();

    await this.jobRepository.save(job);

    // Publish domain events
    const events = job.getUncommittedEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
    job.markEventsAsCommitted();
  }

  async submitApplication(command: SubmitApplicationCommand): Promise<ApplicationId> {
    const job = await this.jobRepository.findById(new JobId(command.jobId));
    const candidate = await this.candidateRepository.findById(new CandidateId(command.candidateId));

    if (!job || !candidate) {
      throw new ApplicationError('Job or candidate not found');
    }

    // Domain logic handles business rules
    const application = job.addCandidate(candidate);

    await this.jobRepository.save(job);

    return application.getId();
  }
}
```

### **Domain Event Handling**

```typescript
// Application/EventHandlers/JobPublishedEventHandler.ts
export class JobPublishedEventHandler implements IEventHandler<JobPublishedEvent> {
  constructor(
    private readonly notificationService: INotificationService,
    private readonly vendorRepository: IVendorRepository
  ) {}

  async handle(event: JobPublishedEvent): Promise<void> {
    // Notify relevant vendors about new job
    const vendors = await this.vendorRepository.findByOrganization(event.organizationId);

    for (const vendor of vendors) {
      await this.notificationService.notifyJobPublished(vendor.getId(), event.jobId);
    }
  }
}
```

### **Multi-Tenant Domain Considerations**

```typescript
// Domain/Shared/ValueObjects/TenantId.ts
export class TenantId {
  private constructor(private readonly value: string) {
    this.validate();
  }

  public static create(value: string): TenantId {
    return new TenantId(value);
  }

  private validate(): void {
    if (!this.value || this.value.trim().length === 0) {
      throw new DomainError('Tenant ID cannot be empty');
    }
  }

  public getValue(): string {
    return this.value;
  }

  public equals(other: TenantId): boolean {
    return this.value === other.value;
  }
}

// Domain/Shared/Entities/TenantAwareEntity.ts
export abstract class TenantAwareEntity extends Entity {
  protected constructor(
    id: EntityId,
    protected readonly tenantId: TenantId
  ) {
    super(id);
  }

  public getTenantId(): TenantId {
    return this.tenantId;
  }

  protected ensureSameTenant(other: TenantAwareEntity): void {
    if (!this.tenantId.equals(other.tenantId)) {
      throw new DomainError('Cross-tenant operation not allowed');
    }
  }
}
```

### **Query Pattern (CQRS)**

```typescript
// Application/Queries/GetJobsQuery.ts
export class GetJobsQuery {
  constructor(
    public readonly organizationId: string,
    public readonly status?: string,
    public readonly page: number = 1,
    public readonly limit: number = 20
  ) {}
}

// Application/QueryHandlers/GetJobsQueryHandler.ts
export class GetJobsQueryHandler implements IQueryHandler<GetJobsQuery, JobListDTO> {
  constructor(private readonly jobReadModel: IJobReadModel) {}

  async handle(query: GetJobsQuery): Promise<JobListDTO> {
    return await this.jobReadModel.getJobs({
      organizationId: query.organizationId,
      status: query.status,
      page: query.page,
      limit: query.limit
    });
  }
}
```

## 🧪 **Testing Strategy**

### **Domain Model Testing**

```typescript
// Domain/TalentAcquisition/Entities/__tests__/Job.test.ts
describe('Job', () => {
  describe('publish', () => {
    it('should publish job when requirements are complete', () => {
      // Arrange
      const job = Job.create(
        OrganizationId.create('org-1'),
        JobTitle.create('Software Engineer'),
        JobRequirements.create({ skills: ['TypeScript'], experience: '2+ years' })
      );

      // Act
      job.publish();

      // Assert
      expect(job.getStatus()).toBe(JobStatus.PUBLISHED);
    });

    it('should throw error when requirements are incomplete', () => {
      // Arrange
      const job = Job.create(
        OrganizationId.create('org-1'),
        JobTitle.create('Software Engineer'),
        JobRequirements.createEmpty()
      );

      // Act & Assert
      expect(() => job.publish()).toThrow('Job cannot be published in current state');
    });
  });
});
```

### **Application Service Testing**

```typescript
// Application/Services/__tests__/JobApplicationService.test.ts
describe('JobApplicationService', () => {
  let service: JobApplicationService;
  let mockJobRepository: jest.Mocked<IJobRepository>;
  let mockEventBus: jest.Mocked<IEventBus>;

  beforeEach(() => {
    mockJobRepository = createMockJobRepository();
    mockEventBus = createMockEventBus();
    service = new JobApplicationService(mockJobRepository, mockEventBus);
  });

  describe('publishJob', () => {
    it('should publish job and emit events', async () => {
      // Arrange
      const job = createTestJob();
      mockJobRepository.findById.mockResolvedValue(job);

      // Act
      await service.publishJob(new PublishJobCommand('job-1'));

      // Assert
      expect(mockJobRepository.save).toHaveBeenCalledWith(job);
      expect(mockEventBus.publish).toHaveBeenCalledWith(
        expect.any(JobPublishedEvent)
      );
    });
  });
});
```

## 📈 **Benefits of DDD Implementation**

### **Business Alignment**

- **Ubiquitous Language**: Shared vocabulary between developers and domain experts
- **Business Logic Centralization**: All business rules in domain models
- **Domain Expert Collaboration**: Clear domain boundaries facilitate expert input

### **Technical Benefits**

- **Maintainability**: Clear separation of concerns and responsibilities
- **Testability**: Domain logic isolated and easily testable
- **Scalability**: Bounded contexts can be scaled independently
- **Flexibility**: Easy to modify business rules without affecting infrastructure

### **Multi-Tenant Benefits**

- **Tenant Isolation**: Built-in tenant awareness in domain models
- **Cross-Tenant Operations**: Explicit handling of vendor-client relationships
- **Security**: Tenant boundaries enforced at domain level

This specification provides the foundation for transforming your excellent ATS prototype into a world-class, domain-driven application that will scale with your business needs.
