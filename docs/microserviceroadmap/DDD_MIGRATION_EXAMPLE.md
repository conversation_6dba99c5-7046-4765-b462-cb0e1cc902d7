# 🔄 DDD Migration Example: User Management

## 📋 **Current State vs Target State**

### **Current Implementation** (Anemic Model)
```typescript
// src/types/multiTenant.ts
export interface User {
  id: string;
  organizationId: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  permissions: Permission[];
  isActive: boolean;
  // ... other properties
}

// src/pages/users/index.tsx
const handleDeactivateUser = async (userId: string) => {
  const user = users.find(u => u.id === userId);
  if (user) {
    user.isActive = false;
    user.updatedAt = new Date().toISOString();
    setUsers(prev => prev.map(u => u.id === userId ? user : u));
  }
};
```

### **Target Implementation** (Rich Domain Model)
```typescript
// src/Domain/IdentityAccess/Entities/User.ts
export class User extends AggregateRoot {
  public deactivate(): void {
    if (!this.isActive) {
      throw new DomainError('User is already deactivated');
    }
    
    this.isActive = false;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserDeactivatedEvent(
      this.getId() as UserId,
      this.organizationId
    ));
  }
}

// src/Application/Services/UserManagementService.ts
export class UserManagementService {
  async deactivateUser(command: DeactivateUserCommand): Promise<void> {
    const user = await this.userRepository.findById(new UserId(command.userId));
    if (!user) {
      throw new ApplicationError('User not found');
    }
    
    user.deactivate(); // Business logic in domain
    await this.userRepository.save(user);
    
    // Publish events
    const events = user.getUncommittedEvents();
    for (const event of events) {
      await this.eventBus.publish(event);
    }
  }
}
```

## 🚀 **Step-by-Step Migration Process**

### **Step 1: Create Domain Foundation**

```typescript
// src/Domain/Shared/Entity.ts
export abstract class Entity {
  protected constructor(protected readonly id: EntityId) {}
  
  public getId(): EntityId {
    return this.id;
  }
  
  public equals(other: Entity): boolean {
    if (!(other instanceof Entity)) {
      return false;
    }
    return this.id.equals(other.id);
  }
}

// src/Domain/Shared/EntityId.ts
export abstract class EntityId extends ValueObject {
  protected constructor(protected readonly value: string) {
    super();
    this.validate();
  }
  
  private validate(): void {
    if (!this.value || this.value.trim().length === 0) {
      throw new DomainError('Entity ID cannot be empty');
    }
  }
  
  public getValue(): string {
    return this.value;
  }
  
  public equals(other: ValueObject): boolean {
    return other instanceof EntityId && this.value === other.value;
  }
}

// src/Domain/Shared/AggregateRoot.ts
export abstract class AggregateRoot extends Entity {
  private uncommittedEvents: IDomainEvent[] = [];
  
  protected addDomainEvent(event: IDomainEvent): void {
    this.uncommittedEvents.push(event);
  }
  
  public getUncommittedEvents(): IDomainEvent[] {
    return [...this.uncommittedEvents];
  }
  
  public markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }
}
```

### **Step 2: Create Value Objects**

```typescript
// src/Domain/IdentityAccess/ValueObjects/UserId.ts
export class UserId extends EntityId {
  private constructor(value: string) {
    super(value);
  }
  
  public static create(value: string): UserId {
    return new UserId(value);
  }
  
  public static generate(): UserId {
    return new UserId(crypto.randomUUID());
  }
}

// src/Domain/IdentityAccess/ValueObjects/Email.ts
export class Email extends ValueObject {
  private constructor(private readonly value: string) {
    super();
    this.validate();
  }
  
  public static create(email: string): Email {
    return new Email(email.toLowerCase().trim());
  }
  
  private validate(): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.value)) {
      throw new DomainError('Invalid email format');
    }
    
    if (this.value.length > 254) {
      throw new DomainError('Email address too long');
    }
  }
  
  public getValue(): string {
    return this.value;
  }
  
  public getDomain(): string {
    return this.value.split('@')[1];
  }
  
  public equals(other: ValueObject): boolean {
    return other instanceof Email && this.value === other.value;
  }
}

// src/Domain/IdentityAccess/ValueObjects/UserRole.ts
export class UserRole extends ValueObject {
  private static readonly VALID_ROLES = [
    'admin', 'recruiter', 'hiring_manager', 'interviewer', 'employee', 'super_admin'
  ] as const;
  
  private constructor(private readonly value: string) {
    super();
    this.validate();
  }
  
  public static create(role: string): UserRole {
    return new UserRole(role);
  }
  
  public static admin(): UserRole {
    return new UserRole('admin');
  }
  
  public static recruiter(): UserRole {
    return new UserRole('recruiter');
  }
  
  private validate(): void {
    if (!UserRole.VALID_ROLES.includes(this.value as any)) {
      throw new DomainError(`Invalid user role: ${this.value}`);
    }
  }
  
  public getValue(): string {
    return this.value;
  }
  
  public canManageUsers(): boolean {
    return ['admin', 'super_admin'].includes(this.value);
  }
  
  public canCreateJobs(): boolean {
    return ['admin', 'recruiter', 'hiring_manager'].includes(this.value);
  }
  
  public getDefaultPermissions(): Permission[] {
    // Return default permissions based on role
    switch (this.value) {
      case 'admin':
        return Permission.getAllPermissions();
      case 'recruiter':
        return Permission.getRecruiterPermissions();
      case 'hiring_manager':
        return Permission.getHiringManagerPermissions();
      default:
        return Permission.getBasicPermissions();
    }
  }
  
  public equals(other: ValueObject): boolean {
    return other instanceof UserRole && this.value === other.value;
  }
}
```

### **Step 3: Create Domain Events**

```typescript
// src/Domain/IdentityAccess/Events/UserCreatedEvent.ts
export class UserCreatedEvent implements IDomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  
  constructor(
    public readonly userId: UserId,
    public readonly organizationId: OrganizationId,
    public readonly email: Email
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}

// src/Domain/IdentityAccess/Events/UserDeactivatedEvent.ts
export class UserDeactivatedEvent implements IDomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  
  constructor(
    public readonly userId: UserId,
    public readonly organizationId: OrganizationId
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}

// src/Domain/IdentityAccess/Events/UserRoleChangedEvent.ts
export class UserRoleChangedEvent implements IDomainEvent {
  public readonly eventId: string;
  public readonly occurredOn: Date;
  
  constructor(
    public readonly userId: UserId,
    public readonly previousRole: UserRole,
    public readonly newRole: UserRole
  ) {
    this.eventId = crypto.randomUUID();
    this.occurredOn = new Date();
  }
}
```

### **Step 4: Create User Aggregate**

```typescript
// src/Domain/IdentityAccess/Entities/User.ts
export class User extends AggregateRoot {
  private constructor(
    id: UserId,
    private email: Email,
    private firstName: string,
    private lastName: string,
    private role: UserRole,
    private organizationId: OrganizationId,
    private permissions: Permission[],
    private isActive: boolean,
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {
    super(id);
  }
  
  public static create(
    email: Email,
    firstName: string,
    lastName: string,
    role: UserRole,
    organizationId: OrganizationId
  ): User {
    const id = UserId.generate();
    const permissions = role.getDefaultPermissions();
    
    const user = new User(
      id,
      email,
      firstName,
      lastName,
      role,
      organizationId,
      permissions,
      true,
      new Date(),
      new Date()
    );
    
    user.addDomainEvent(new UserCreatedEvent(id, organizationId, email));
    
    return user;
  }
  
  public static reconstitute(
    id: UserId,
    email: Email,
    firstName: string,
    lastName: string,
    role: UserRole,
    organizationId: OrganizationId,
    permissions: Permission[],
    isActive: boolean,
    createdAt: Date,
    updatedAt: Date
  ): User {
    return new User(
      id,
      email,
      firstName,
      lastName,
      role,
      organizationId,
      permissions,
      isActive,
      createdAt,
      updatedAt
    );
  }
  
  public assignRole(newRole: UserRole): void {
    if (this.role.equals(newRole)) {
      return; // No change needed
    }
    
    const previousRole = this.role;
    this.role = newRole;
    this.permissions = newRole.getDefaultPermissions();
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserRoleChangedEvent(
      this.getId() as UserId,
      previousRole,
      newRole
    ));
  }
  
  public deactivate(): void {
    if (!this.isActive) {
      throw new DomainError('User is already deactivated');
    }
    
    this.isActive = false;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserDeactivatedEvent(
      this.getId() as UserId,
      this.organizationId
    ));
  }
  
  public activate(): void {
    if (this.isActive) {
      throw new DomainError('User is already active');
    }
    
    this.isActive = true;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserActivatedEvent(
      this.getId() as UserId,
      this.organizationId
    ));
  }
  
  public updateProfile(firstName: string, lastName: string): void {
    if (!firstName?.trim() || !lastName?.trim()) {
      throw new DomainError('First name and last name are required');
    }
    
    this.firstName = firstName.trim();
    this.lastName = lastName.trim();
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserProfileUpdatedEvent(
      this.getId() as UserId,
      firstName,
      lastName
    ));
  }
  
  public hasPermission(module: string, action: string): boolean {
    return this.permissions.some(permission => 
      permission.hasAccess(module, action)
    );
  }
  
  public canManageUsers(): boolean {
    return this.role.canManageUsers();
  }
  
  public canCreateJobs(): boolean {
    return this.role.canCreateJobs();
  }
  
  public getFullName(): string {
    return `${this.firstName} ${this.lastName}`;
  }
  
  // Getters
  public getEmail(): Email { return this.email; }
  public getFirstName(): string { return this.firstName; }
  public getLastName(): string { return this.lastName; }
  public getRole(): UserRole { return this.role; }
  public getOrganizationId(): OrganizationId { return this.organizationId; }
  public getPermissions(): Permission[] { return [...this.permissions]; }
  public isActiveUser(): boolean { return this.isActive; }
  public getCreatedAt(): Date { return this.createdAt; }
  public getUpdatedAt(): Date { return this.updatedAt; }
}
```

### **Step 5: Create Repository Interface**

```typescript
// src/Domain/IdentityAccess/Repositories/IUserRepository.ts
export interface IUserRepository {
  findById(id: UserId): Promise<User | null>;
  findByEmail(email: Email): Promise<User | null>;
  findByOrganization(organizationId: OrganizationId): Promise<User[]>;
  findActiveByOrganization(organizationId: OrganizationId): Promise<User[]>;
  save(user: User): Promise<void>;
  delete(id: UserId): Promise<void>;
  exists(email: Email, organizationId: OrganizationId): Promise<boolean>;
}
```

## 🔄 **Migration Benefits**

### **Before (Anemic Model)**
- Business logic scattered across UI components
- No validation of business rules
- Difficult to test business logic
- Tight coupling between UI and data

### **After (Rich Domain Model)**
- Business logic centralized in domain entities
- Strong validation and business rule enforcement
- Easy to test domain logic in isolation
- Clear separation of concerns
- Domain events enable loose coupling

## 🎯 **Next Steps**

1. **Implement the foundation classes** (Entity, AggregateRoot, ValueObject)
2. **Create the User aggregate** with rich business behavior
3. **Implement repository pattern** for data access
4. **Create application services** to orchestrate domain operations
5. **Update UI components** to use application services instead of direct data manipulation
6. **Add comprehensive tests** for domain logic

This migration example demonstrates how to transform anemic data models into rich domain models that encapsulate business logic and enforce business rules.
