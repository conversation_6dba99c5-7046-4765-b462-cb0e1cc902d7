# 🚀 TalentFlow ATS - DDD Implementation Roadmap

## 📋 **Implementation Overview**

This roadmap provides a step-by-step guide to transform the current TalentFlow ATS prototype into a Domain-Driven Design architecture. The migration is designed to be incremental, allowing the system to remain functional throughout the transformation.

## 🎯 **Phase 1: Foundation Setup (Weeks 1-2)**

### **Week 1: Core Infrastructure**

#### **Day 1-2: Base Classes & Interfaces**

```typescript
// src/Domain/Shared/Entity.ts
export abstract class Entity {
  protected constructor(protected readonly id: EntityId) {}
  
  public getId(): EntityId {
    return this.id;
  }
  
  public equals(other: Entity): boolean {
    return this.id.equals(other.id);
  }
}

// src/Domain/Shared/AggregateRoot.ts
export abstract class AggregateRoot extends Entity {
  private uncommittedEvents: IDomainEvent[] = [];
  
  protected addDomainEvent(event: IDomainEvent): void {
    this.uncommittedEvents.push(event);
  }
  
  public getUncommittedEvents(): IDomainEvent[] {
    return [...this.uncommittedEvents];
  }
  
  public markEventsAsCommitted(): void {
    this.uncommittedEvents = [];
  }
}

// src/Domain/Shared/ValueObject.ts
export abstract class ValueObject {
  public abstract equals(other: ValueObject): boolean;
}
```

#### **Day 3-4: Domain Event System**

```typescript
// src/Domain/Shared/IDomainEvent.ts
export interface IDomainEvent {
  readonly eventId: string;
  readonly occurredOn: Date;
}

// src/Infrastructure/Events/EventBus.ts
export class EventBus implements IEventBus {
  private handlers: Map<string, IEventHandler[]> = new Map();
  
  public subscribe<T extends IDomainEvent>(
    eventType: string,
    handler: IEventHandler<T>
  ): void {
    const handlers = this.handlers.get(eventType) || [];
    handlers.push(handler);
    this.handlers.set(eventType, handlers);
  }
  
  public async publish<T extends IDomainEvent>(event: T): Promise<void> {
    const eventType = event.constructor.name;
    const handlers = this.handlers.get(eventType) || [];
    
    await Promise.all(handlers.map(handler => handler.handle(event)));
  }
}
```

#### **Day 5: Repository Interfaces**

```typescript
// src/Domain/Shared/IRepository.ts
export interface IRepository<T extends AggregateRoot, TId extends EntityId> {
  findById(id: TId): Promise<T | null>;
  save(aggregate: T): Promise<void>;
  delete(id: TId): Promise<void>;
}

// src/Application/Shared/IUnitOfWork.ts
export interface IUnitOfWork {
  begin(): Promise<void>;
  commit(): Promise<void>;
  rollback(): Promise<void>;
}
```

### **Week 2: Identity & Access Management Context**

#### **Day 1-3: User Aggregate**

```typescript
// src/Domain/IdentityAccess/ValueObjects/UserId.ts
export class UserId extends EntityId {
  private constructor(value: string) {
    super(value);
  }
  
  public static create(value: string): UserId {
    return new UserId(value);
  }
  
  public static generate(): UserId {
    return new UserId(crypto.randomUUID());
  }
}

// src/Domain/IdentityAccess/ValueObjects/Email.ts
export class Email extends ValueObject {
  private constructor(private readonly value: string) {
    super();
    this.validate();
  }
  
  public static create(email: string): Email {
    return new Email(email);
  }
  
  private validate(): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(this.value)) {
      throw new DomainError('Invalid email format');
    }
  }
  
  public getValue(): string {
    return this.value;
  }
  
  public equals(other: ValueObject): boolean {
    return other instanceof Email && this.value === other.value;
  }
}

// src/Domain/IdentityAccess/Entities/User.ts
export class User extends AggregateRoot {
  private constructor(
    id: UserId,
    private email: Email,
    private firstName: string,
    private lastName: string,
    private role: UserRole,
    private organizationId: OrganizationId,
    private permissions: Permission[],
    private isActive: boolean,
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {
    super(id);
  }
  
  public static create(
    email: Email,
    firstName: string,
    lastName: string,
    role: UserRole,
    organizationId: OrganizationId
  ): User {
    const id = UserId.generate();
    const permissions = role.getDefaultPermissions();
    
    const user = new User(
      id,
      email,
      firstName,
      lastName,
      role,
      organizationId,
      permissions,
      true,
      new Date(),
      new Date()
    );
    
    user.addDomainEvent(new UserCreatedEvent(id, organizationId, email));
    
    return user;
  }
  
  public assignRole(role: UserRole): void {
    if (this.role.equals(role)) {
      return; // No change needed
    }
    
    const previousRole = this.role;
    this.role = role;
    this.permissions = role.getDefaultPermissions();
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserRoleChangedEvent(
      this.getId() as UserId,
      previousRole,
      role
    ));
  }
  
  public deactivate(): void {
    if (!this.isActive) {
      return; // Already deactivated
    }
    
    this.isActive = false;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new UserDeactivatedEvent(
      this.getId() as UserId,
      this.organizationId
    ));
  }
  
  public hasPermission(module: string, action: string): boolean {
    return this.permissions.some(permission => 
      permission.hasAccess(module, action)
    );
  }
  
  // Getters
  public getEmail(): Email { return this.email; }
  public getRole(): UserRole { return this.role; }
  public getOrganizationId(): OrganizationId { return this.organizationId; }
  public isActiveUser(): boolean { return this.isActive; }
}
```

#### **Day 4-5: Organization Aggregate**

```typescript
// src/Domain/IdentityAccess/Entities/Organization.ts
export class Organization extends AggregateRoot {
  private constructor(
    id: OrganizationId,
    private name: string,
    private type: OrganizationType,
    private subscriptionPlan: SubscriptionPlan,
    private settings: OrganizationSettings,
    private isActive: boolean,
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {
    super(id);
  }
  
  public static create(
    name: string,
    type: OrganizationType,
    subscriptionPlan: SubscriptionPlan
  ): Organization {
    const id = OrganizationId.generate();
    const settings = OrganizationSettings.createDefault();
    
    const organization = new Organization(
      id,
      name,
      type,
      subscriptionPlan,
      settings,
      true,
      new Date(),
      new Date()
    );
    
    organization.addDomainEvent(new OrganizationCreatedEvent(id, name, type));
    
    return organization;
  }
  
  public upgradeSubscription(newPlan: SubscriptionPlan): void {
    if (this.subscriptionPlan.equals(newPlan)) {
      return; // No change needed
    }
    
    const previousPlan = this.subscriptionPlan;
    this.subscriptionPlan = newPlan;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new SubscriptionUpgradedEvent(
      this.getId() as OrganizationId,
      previousPlan,
      newPlan
    ));
  }
  
  public canCreateUsers(): boolean {
    return this.isActive && this.subscriptionPlan.allowsUserCreation();
  }
  
  public getMaxUsers(): number {
    return this.subscriptionPlan.getMaxUsers();
  }
  
  // Getters
  public getName(): string { return this.name; }
  public getType(): OrganizationType { return this.type; }
  public getSubscriptionPlan(): SubscriptionPlan { return this.subscriptionPlan; }
  public isActiveOrganization(): boolean { return this.isActive; }
}
```

## 🎯 **Phase 2: Core Domain Implementation (Weeks 3-6)**

### **Week 3-4: Talent Acquisition Context**

#### **Job Aggregate Implementation**

```typescript
// src/Domain/TalentAcquisition/Entities/Job.ts
export class Job extends AggregateRoot {
  private constructor(
    id: JobId,
    private readonly organizationId: OrganizationId,
    private title: JobTitle,
    private description: JobDescription,
    private requirements: JobRequirements,
    private status: JobStatus,
    private pipeline: Pipeline,
    private readonly createdBy: UserId,
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {
    super(id);
  }
  
  public static create(
    organizationId: OrganizationId,
    title: JobTitle,
    description: JobDescription,
    requirements: JobRequirements,
    createdBy: UserId
  ): Job {
    const id = JobId.generate();
    const status = JobStatus.DRAFT;
    const pipeline = Pipeline.createDefault();
    
    const job = new Job(
      id,
      organizationId,
      title,
      description,
      requirements,
      status,
      pipeline,
      createdBy,
      new Date(),
      new Date()
    );
    
    job.addDomainEvent(new JobCreatedEvent(id, organizationId, createdBy));
    
    return job;
  }
  
  public publish(): void {
    if (!this.canBePublished()) {
      throw new DomainError('Job cannot be published in current state');
    }
    
    this.status = JobStatus.PUBLISHED;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new JobPublishedEvent(
      this.getId() as JobId,
      this.organizationId
    ));
  }
  
  public addCandidate(candidateId: CandidateId): Application {
    if (!this.isAcceptingApplications()) {
      throw new DomainError('Job is not accepting applications');
    }
    
    const application = Application.create(
      this.getId() as JobId,
      candidateId,
      this.pipeline.getFirstStage()
    );
    
    this.addDomainEvent(new CandidateAppliedEvent(
      this.getId() as JobId,
      candidateId
    ));
    
    return application;
  }
  
  private canBePublished(): boolean {
    return this.status === JobStatus.DRAFT &&
           this.title.isValid() &&
           this.description.isComplete() &&
           this.requirements.isComplete();
  }
  
  private isAcceptingApplications(): boolean {
    return this.status === JobStatus.PUBLISHED;
  }
}
```

### **Week 5-6: Interview Management Context**

#### **Interview Aggregate Implementation**

```typescript
// src/Domain/InterviewManagement/Entities/Interview.ts
export class Interview extends AggregateRoot {
  private constructor(
    id: InterviewId,
    private readonly applicationId: ApplicationId,
    private readonly jobId: JobId,
    private readonly candidateId: CandidateId,
    private schedule: InterviewSchedule,
    private interviewers: InterviewerId[],
    private status: InterviewStatus,
    private feedback: InterviewFeedback[],
    private readonly createdAt: Date,
    private updatedAt: Date
  ) {
    super(id);
  }
  
  public static schedule(
    applicationId: ApplicationId,
    jobId: JobId,
    candidateId: CandidateId,
    schedule: InterviewSchedule,
    interviewers: InterviewerId[]
  ): Interview {
    const id = InterviewId.generate();
    const status = InterviewStatus.SCHEDULED;
    
    const interview = new Interview(
      id,
      applicationId,
      jobId,
      candidateId,
      schedule,
      interviewers,
      status,
      [],
      new Date(),
      new Date()
    );
    
    interview.addDomainEvent(new InterviewScheduledEvent(
      id,
      candidateId,
      schedule.getDateTime(),
      interviewers
    ));
    
    return interview;
  }
  
  public reschedule(newSchedule: InterviewSchedule): void {
    if (this.status !== InterviewStatus.SCHEDULED) {
      throw new DomainError('Can only reschedule scheduled interviews');
    }
    
    const oldSchedule = this.schedule;
    this.schedule = newSchedule;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new InterviewRescheduledEvent(
      this.getId() as InterviewId,
      oldSchedule.getDateTime(),
      newSchedule.getDateTime()
    ));
  }
  
  public complete(): void {
    if (this.status !== InterviewStatus.SCHEDULED) {
      throw new DomainError('Can only complete scheduled interviews');
    }
    
    this.status = InterviewStatus.COMPLETED;
    this.updatedAt = new Date();
    
    this.addDomainEvent(new InterviewCompletedEvent(
      this.getId() as InterviewId,
      this.candidateId
    ));
  }
  
  public addFeedback(feedback: InterviewFeedback): void {
    if (this.status !== InterviewStatus.COMPLETED) {
      throw new DomainError('Can only add feedback to completed interviews');
    }
    
    this.feedback.push(feedback);
    this.updatedAt = new Date();
    
    this.addDomainEvent(new InterviewFeedbackAddedEvent(
      this.getId() as InterviewId,
      feedback.getInterviewerId(),
      feedback.getScore()
    ));
  }
  
  public getAverageScore(): number {
    if (this.feedback.length === 0) {
      return 0;
    }
    
    const totalScore = this.feedback.reduce(
      (sum, feedback) => sum + feedback.getScore(),
      0
    );
    
    return totalScore / this.feedback.length;
  }
}
```

## 📊 **Migration Checklist**

### **Phase 1 Completion Criteria**
- [ ] Base DDD infrastructure classes implemented
- [ ] Domain event system functional
- [ ] Repository interfaces defined
- [ ] User aggregate migrated and tested
- [ ] Organization aggregate migrated and tested
- [ ] Basic application services implemented

### **Phase 2 Completion Criteria**
- [ ] Job aggregate implemented with business logic
- [ ] Candidate aggregate implemented
- [ ] Application entity implemented
- [ ] Interview aggregate implemented
- [ ] Domain events flowing between contexts
- [ ] Application services orchestrating domain operations

### **Phase 3 Completion Criteria**
- [ ] All remaining contexts implemented
- [ ] Cross-context integration complete
- [ ] Event-driven communication established
- [ ] Performance optimized
- [ ] Full test coverage achieved

## 🎯 **Success Metrics**

1. **Code Quality**: Reduced cyclomatic complexity, improved maintainability index
2. **Test Coverage**: >90% coverage on domain models and application services
3. **Performance**: No degradation in response times
4. **Business Alignment**: Domain experts can understand and validate business rules
5. **Developer Experience**: Faster feature development and easier debugging

This roadmap provides a structured approach to implementing DDD while maintaining system functionality throughout the migration process.
