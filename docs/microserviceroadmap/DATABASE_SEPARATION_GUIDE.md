# 🗄️ Database Separation Guide: Monolith to Microservices

## 📋 **Overview**

This guide provides detailed strategies for separating databases during the monolith-to-microservices migration, with specific focus on maintaining data consistency, minimizing downtime, and ensuring rollback capabilities.

## 🎯 **Database Separation Strategies**

### **Strategy 1: Schema-First Separation (Recommended)**

#### **Phase 1: Logical Separation within Single Database**

```sql
-- Current: Single schema approach
CREATE DATABASE talentflow_ats;
USE talentflow_ats;

CREATE TABLE users (...);
CREATE TABLE jobs (...);
CREATE TABLE interviews (...);

-- Target: Schema-per-bounded-context
CREATE SCHEMA identity_access;
CREATE SCHEMA talent_acquisition;
CREATE SCHEMA interview_management;
CREATE SCHEMA vendor_management;
CREATE SCHEMA communication;
CREATE SCHEMA form_management;
CREATE SCHEMA analytics_reporting;
CREATE SCHEMA system_admin;

-- Migrate tables to appropriate schemas
ALTER TABLE users SET SCHEMA identity_access;
ALTER TABLE jobs SET SCHEMA talent_acquisition;
ALTER TABLE interviews SET SCHEMA interview_management;
```

#### **Phase 2: Physical Database Separation**

```sql
-- Create separate databases for each service
CREATE DATABASE identity_service_db;
CREATE DATABASE talent_acquisition_db;
CREATE DATABASE interview_management_db;
CREATE DATABASE vendor_management_db;
CREATE DATABASE communication_db;
CREATE DATABASE form_management_db;
CREATE DATABASE analytics_reporting_db;
CREATE DATABASE system_admin_db;

-- Migration script example for talent acquisition
-- Step 1: Create tables in new database
\c talent_acquisition_db;
CREATE TABLE jobs (
    id UUID PRIMARY KEY,
    organization_id UUID NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    requirements JSONB,
    status VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Step 2: Copy data with verification
INSERT INTO talent_acquisition_db.jobs
SELECT * FROM talentflow_ats.talent_acquisition.jobs;

-- Step 3: Verify data integrity
SELECT 
    (SELECT COUNT(*) FROM talentflow_ats.talent_acquisition.jobs) as source_count,
    (SELECT COUNT(*) FROM talent_acquisition_db.jobs) as target_count;
```

### **Strategy 2: Dual-Write Pattern Implementation**

```typescript
// Dual-write repository implementation
export class DualWriteJobRepository implements IJobRepository {
  constructor(
    private readonly primaryDb: IPrimaryDatabase,
    private readonly secondaryDb: ISecondaryDatabase,
    private readonly consistencyChecker: IConsistencyChecker,
    private readonly featureFlags: IFeatureFlags,
    private readonly logger: ILogger
  ) {}

  async save(job: Job): Promise<void> {
    const jobDto = JobMapper.toDTO(job);
    
    try {
      // Always write to primary (legacy) database first
      await this.primaryDb.jobs.save(jobDto);
      
      // Write to secondary (new) database if enabled
      if (this.featureFlags.isEnabled('dual_write_jobs')) {
        try {
          await this.secondaryDb.jobs.save(jobDto);
          
          // Async consistency check
          this.scheduleConsistencyCheck(job.getId());
          
        } catch (secondaryError) {
          // Log error but don't fail the operation
          this.logger.error('Secondary database write failed', {
            jobId: job.getId().getValue(),
            error: secondaryError
          });
          
          // Emit metric for monitoring
          this.metrics.increment('dual_write_failures', {
            service: 'talent_acquisition',
            operation: 'save'
          });
        }
      }
      
    } catch (primaryError) {
      this.logger.error('Primary database write failed', {
        jobId: job.getId().getValue(),
        error: primaryError
      });
      throw primaryError;
    }
  }

  async findById(id: JobId): Promise<Job | null> {
    const readFromSecondary = this.featureFlags.isEnabled('read_from_secondary_jobs');
    
    if (readFromSecondary) {
      try {
        const jobDto = await this.secondaryDb.jobs.findById(id.getValue());
        return jobDto ? JobMapper.toDomain(jobDto) : null;
      } catch (error) {
        this.logger.warn('Secondary database read failed, falling back to primary', {
          jobId: id.getValue(),
          error
        });
        // Fall back to primary database
      }
    }
    
    const jobDto = await this.primaryDb.jobs.findById(id.getValue());
    return jobDto ? JobMapper.toDomain(jobDto) : null;
  }

  private scheduleConsistencyCheck(jobId: JobId): void {
    // Schedule async consistency check
    setTimeout(async () => {
      await this.consistencyChecker.checkJobConsistency(jobId);
    }, 5000); // Check after 5 seconds
  }
}
```

### **Strategy 3: Event-Driven Data Synchronization**

```typescript
// Event-driven synchronization for data migration
export class DataSynchronizationEventHandler {
  constructor(
    private readonly newDatabase: INewDatabase,
    private readonly eventStore: IEventStore,
    private readonly logger: ILogger
  ) {}

  @EventHandler(JobCreatedEvent)
  async handleJobCreated(event: JobCreatedEvent): Promise<void> {
    try {
      const jobData = {
        id: event.jobId.getValue(),
        organizationId: event.organizationId.getValue(),
        title: event.title,
        description: event.description,
        requirements: event.requirements,
        status: 'DRAFT',
        createdAt: event.occurredOn,
        updatedAt: event.occurredOn
      };

      await this.newDatabase.jobs.insert(jobData);
      
      this.logger.info('Job synchronized to new database', {
        jobId: event.jobId.getValue(),
        eventId: event.eventId
      });

    } catch (error) {
      this.logger.error('Failed to synchronize job to new database', {
        jobId: event.jobId.getValue(),
        eventId: event.eventId,
        error
      });

      // Store failed event for retry
      await this.eventStore.storeFailed(event, error);
    }
  }

  @EventHandler(JobUpdatedEvent)
  async handleJobUpdated(event: JobUpdatedEvent): Promise<void> {
    try {
      const updateData = {
        title: event.title,
        description: event.description,
        requirements: event.requirements,
        status: event.status,
        updatedAt: event.occurredOn
      };

      await this.newDatabase.jobs.update(
        event.jobId.getValue(),
        updateData
      );

    } catch (error) {
      this.logger.error('Failed to update job in new database', {
        jobId: event.jobId.getValue(),
        error
      });
    }
  }
}
```

## 🔍 **Data Consistency Verification**

### **Consistency Checker Implementation**

```typescript
export class DatabaseConsistencyChecker {
  constructor(
    private readonly primaryDb: IPrimaryDatabase,
    private readonly secondaryDb: ISecondaryDatabase,
    private readonly logger: ILogger
  ) {}

  async checkJobConsistency(jobId: JobId): Promise<ConsistencyResult> {
    const result = new ConsistencyResult(jobId);

    try {
      const [primaryJob, secondaryJob] = await Promise.all([
        this.primaryDb.jobs.findById(jobId.getValue()),
        this.secondaryDb.jobs.findById(jobId.getValue())
      ]);

      if (!primaryJob && !secondaryJob) {
        result.status = 'CONSISTENT_MISSING';
        return result;
      }

      if (!primaryJob || !secondaryJob) {
        result.status = 'INCONSISTENT_MISSING';
        result.differences.push({
          field: 'existence',
          primaryValue: !!primaryJob,
          secondaryValue: !!secondaryJob
        });
        return result;
      }

      // Compare field by field
      const fields = ['title', 'description', 'status', 'requirements'];
      for (const field of fields) {
        if (primaryJob[field] !== secondaryJob[field]) {
          result.differences.push({
            field,
            primaryValue: primaryJob[field],
            secondaryValue: secondaryJob[field]
          });
        }
      }

      result.status = result.differences.length === 0 ? 'CONSISTENT' : 'INCONSISTENT_DATA';

    } catch (error) {
      result.status = 'ERROR';
      result.error = error;
    }

    if (result.status !== 'CONSISTENT') {
      this.logger.warn('Data inconsistency detected', {
        jobId: jobId.getValue(),
        status: result.status,
        differences: result.differences
      });
    }

    return result;
  }

  async performBulkConsistencyCheck(
    batchSize: number = 1000
  ): Promise<BulkConsistencyReport> {
    const report = new BulkConsistencyReport();
    let offset = 0;

    while (true) {
      const jobIds = await this.primaryDb.jobs.getIds(batchSize, offset);
      if (jobIds.length === 0) break;

      const checks = await Promise.all(
        jobIds.map(id => this.checkJobConsistency(JobId.create(id)))
      );

      for (const check of checks) {
        report.addResult(check);
      }

      offset += batchSize;
      
      // Progress logging
      this.logger.info(`Consistency check progress: ${offset} jobs processed`);
    }

    return report;
  }
}

export class ConsistencyResult {
  public status: 'CONSISTENT' | 'INCONSISTENT_MISSING' | 'INCONSISTENT_DATA' | 'ERROR' = 'CONSISTENT';
  public differences: FieldDifference[] = [];
  public error?: Error;

  constructor(public readonly jobId: JobId) {}
}

interface FieldDifference {
  field: string;
  primaryValue: any;
  secondaryValue: any;
}
```

## 🔄 **Migration Rollback Strategies**

### **Rollback Plan Implementation**

```typescript
export class DatabaseMigrationRollback {
  constructor(
    private readonly primaryDb: IPrimaryDatabase,
    private readonly secondaryDb: ISecondaryDatabase,
    private readonly backupService: IBackupService,
    private readonly featureFlags: IFeatureFlags
  ) {}

  async createRollbackPoint(serviceName: string): Promise<RollbackPoint> {
    const rollbackPoint = new RollbackPoint(serviceName);

    // Create database backup
    const backupId = await this.backupService.createBackup(
      this.primaryDb,
      `${serviceName}_rollback_${Date.now()}`
    );

    rollbackPoint.backupId = backupId;
    rollbackPoint.featureFlagSnapshot = await this.featureFlags.getSnapshot();
    rollbackPoint.timestamp = new Date();

    return rollbackPoint;
  }

  async rollback(rollbackPoint: RollbackPoint): Promise<void> {
    try {
      // 1. Disable feature flags
      await this.featureFlags.restoreSnapshot(rollbackPoint.featureFlagSnapshot);

      // 2. Stop writes to secondary database
      await this.featureFlags.disable(`dual_write_${rollbackPoint.serviceName}`);
      await this.featureFlags.disable(`read_from_secondary_${rollbackPoint.serviceName}`);

      // 3. Restore database if needed
      if (rollbackPoint.requiresDataRestore) {
        await this.backupService.restoreBackup(
          rollbackPoint.backupId,
          this.primaryDb
        );
      }

      // 4. Verify system health
      await this.verifySystemHealth(rollbackPoint.serviceName);

      this.logger.info('Rollback completed successfully', {
        serviceName: rollbackPoint.serviceName,
        rollbackPointId: rollbackPoint.id
      });

    } catch (error) {
      this.logger.error('Rollback failed', {
        serviceName: rollbackPoint.serviceName,
        error
      });
      throw error;
    }
  }

  private async verifySystemHealth(serviceName: string): Promise<void> {
    // Implement health checks specific to the service
    const healthChecks = [
      this.checkDatabaseConnectivity,
      this.checkApplicationHealth,
      this.checkDataIntegrity
    ];

    for (const check of healthChecks) {
      await check(serviceName);
    }
  }
}

class RollbackPoint {
  public id: string;
  public backupId?: string;
  public featureFlagSnapshot?: FeatureFlagSnapshot;
  public timestamp?: Date;
  public requiresDataRestore: boolean = false;

  constructor(public readonly serviceName: string) {
    this.id = `rollback_${serviceName}_${Date.now()}`;
  }
}
```

## 📊 **Migration Monitoring & Metrics**

### **Database Migration Metrics**

```typescript
export class MigrationMetrics {
  constructor(private readonly metricsClient: IMetricsClient) {}

  recordDualWriteLatency(serviceName: string, latency: number): void {
    this.metricsClient.histogram('dual_write_latency', latency, {
      service: serviceName
    });
  }

  recordConsistencyCheckResult(
    serviceName: string,
    status: string,
    inconsistencies: number
  ): void {
    this.metricsClient.counter('consistency_checks_total', 1, {
      service: serviceName,
      status
    });

    if (inconsistencies > 0) {
      this.metricsClient.gauge('data_inconsistencies', inconsistencies, {
        service: serviceName
      });
    }
  }

  recordMigrationProgress(
    serviceName: string,
    phase: string,
    progress: number
  ): void {
    this.metricsClient.gauge('migration_progress', progress, {
      service: serviceName,
      phase
    });
  }

  recordDatabaseOperationFailure(
    serviceName: string,
    operation: string,
    database: 'primary' | 'secondary'
  ): void {
    this.metricsClient.counter('database_operation_failures', 1, {
      service: serviceName,
      operation,
      database
    });
  }
}
```

## 🎯 **Best Practices & Recommendations**

### **Database Separation Checklist**

- [ ] **Schema Separation**: Logically separate schemas before physical separation
- [ ] **Dual-Write Implementation**: Implement dual-write with primary-secondary pattern
- [ ] **Consistency Monitoring**: Continuous monitoring of data consistency
- [ ] **Feature Flags**: Use feature flags for gradual traffic migration
- [ ] **Rollback Plan**: Always have a tested rollback plan
- [ ] **Performance Testing**: Test performance impact of dual writes
- [ ] **Data Migration Verification**: Verify data integrity after migration
- [ ] **Monitoring & Alerting**: Comprehensive monitoring during migration

### **Common Pitfalls to Avoid**

1. **Big Bang Migration**: Avoid migrating all services at once
2. **Ignoring Data Relationships**: Consider foreign key relationships carefully
3. **No Rollback Plan**: Always have a way to rollback quickly
4. **Insufficient Testing**: Test migration process thoroughly in staging
5. **Poor Monitoring**: Lack of visibility during migration process

This guide provides a comprehensive approach to database separation that minimizes risk while ensuring data integrity throughout the migration process.
