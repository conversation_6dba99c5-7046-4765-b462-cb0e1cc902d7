# Applicant Management API Analysis

## Overview
This document provides a comprehensive analysis of the REST API endpoints related to applicant management in the ATS Spring Boot backend, including architectural patterns and interaction flows.

## REST API Endpoints Summary

### 1. GET /api/v1/applicant/{id}
- **Purpose**: Retrieve complete applicant details by UUID
- **Response**: Full applicant information including JSONB field data
- **Authentication**: Currently permitAll (public access)
- **Key Features**: Complete applicant profile with all sections

### 2. PATCH /api/v1/applicant/{id}/activate
- **Purpose**: Activate a previously deactivated applicant
- **Operation**: Sets isActive flag to true
- **Idempotency**: Built-in idempotency check to prevent duplicate operations
- **Response**: ActivateDeactivateResponseDTO with operation status

### 3. DELETE /api/v1/applicant/{id}/deactivate
- **Purpose**: Deactivate an active applicant (soft delete)
- **Operation**: Sets isActive flag to false
- **Idempotency**: Built-in idempotency check to prevent duplicate operations
- **Response**: ActivateDeactivateResponseDTO with operation status

### 4. POST /api/v1/applicant
- **Purpose**: Search and retrieve paginated list of applicants
- **Features**: Filtering, sorting, pagination, and search capabilities
- **Method**: Uses POST to support complex search criteria in request body
- **Request**: GetAllApplicantsRequestDTO with optional filters
- **Response**: GetAllApplicantsResponseDTO with paginated results

### 5. POST /api/v1/applicant/create
- **Purpose**: Create new applicant with optional file uploads
- **Content-Type**: multipart/form-data
- **Features**: Complete applicant data creation with document uploads
- **Validation**: Email uniqueness, file validation, JSON parsing
- **Complex Operations**: File uploads, document management, transaction handling

### 6. PUT /api/v1/applicant/{id}
- **Purpose**: Update existing applicant with comprehensive document management
- **Content-Type**: multipart/form-data
- **Features**: Partial updates, document addition/deletion, file uploads
- **Document Operations**: Add, replace, soft delete documents
- **Advanced Features**: File-metadata mapping, transaction consistency

## Architectural Patterns

### Transaction Management Strategy
- **Outside Transaction**: Email validation and file uploads performed outside DB transaction
- **Focused Transaction**: Only database operations wrapped in transactions
- **Compensation Pattern**: File cleanup implemented for rollback scenarios

### Validation Layers
1. **Controller Level**: File validation, JSON parsing validation
2. **Service Level**: Business logic validation (email uniqueness)
3. **Repository Level**: Database constraint validation
4. **Bean Validation**: JSR-303 annotations for field validation

### Error Handling Strategy
- **Specific Exceptions**: DuplicateApplicantException, ValidationException, DatabaseOperationException
- **HTTP Status Mapping**: 409 for conflicts, 413 for file size, 415 for file type, 400 for validation
- **Rollback Support**: Transactional rollback with file cleanup compensation

### Service Layer Architecture
- **Single Responsibility**: Each service has focused responsibility
- **Dependency Injection**: Services loosely coupled through interfaces
- **Mapper Pattern**: Dedicated mappers for entity-DTO conversion

## Key Components

### Core Services
- **ApplicantService**: Main business logic service
- **ApplicantValidator**: Email uniqueness and business validation
- **FileUploadService**: File handling and upload coordination
- **DocumentRepoService**: Document storage and management
- **FileValidationService**: File type, size, and count validation

### Data Access
- **ApplicantRepository**: JOOQ-based data access with optimized queries
- **DocumentRepoRepository**: Document metadata storage
- **Database**: PostgreSQL with JSONB support for flexible document storage

### External Integrations
- **FileStoreService**: External file storage (filesystem/cloud)
- **DocumentUpdateProcessor**: Document lifecycle management

## Security Considerations
- **PII Masking**: Email addresses masked in logs
- **Input Validation**: Multiple layers of input validation
- **SQL Injection Prevention**: Parameterized queries through JOOQ
- **File Security**: Comprehensive file type and size validation

## Performance Optimizations
- **Pagination**: Efficient pagination with count queries
- **Exists Queries**: Optimized existence checks instead of count queries
- **Transaction Scope**: Minimal transaction scope to reduce lock time
- **File I/O**: File operations outside database transactions

## Future Considerations
- **Authentication**: Currently permitAll, needs proper authentication
- **Authorization**: Role-based access control implementation
- **Caching**: Consider caching for frequently accessed data
- **Monitoring**: Add metrics and monitoring for file operations

---
*Generated from codebase analysis on 2025-09-06*
