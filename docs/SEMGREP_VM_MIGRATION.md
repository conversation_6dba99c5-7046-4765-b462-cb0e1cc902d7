# Semgrep Analysis Migration: Kubernetes to VM Server

## Overview

This document describes the migration of the Semgrep static analysis workflow from a Kubernetes cluster-based approach to a dedicated VM server approach.

## Migration Summary

### Before (Kubernetes Approach)
- Used DigitalOcean Kubernetes Service (DOKS)
- Required multiple secrets: `DIGITALOCEAN_ACCESS_TOKEN`, `KUBERNETES_CLUSTER_ID`, `KUBERNETES_CONTEXT`, `SECURE_GITHUB_TOKEN`
- Deployed Kubernetes jobs, PVCs, and pods
- Complex setup with kubectl and doctl

### After (VM Server Approach)
- Uses dedicated VM server at `**************`
- Requires only: `SERVER_PASSWORD`, `SECURE_GITHUB_TOKEN`
- Direct SSH connection with Docker Compose execution
- Simplified deployment and maintenance

## Key Changes

### 1. Workflow Interface
- **Maintained backward compatibility**: Same `workflow_call` interface
- **Added input parameter**: `runner_type` with default `[self-hosted, linux]`
- **Updated secrets**: Replaced Kubernetes secrets with `SERVER_PASSWORD`

### 2. Execution Flow
1. **SSH Connection**: Test and establish connection to VM server
2. **Source Transfer**: Create archive and transfer source code via SCP
3. **Docker Compose**: Generate and execute Semgrep analysis using Docker
4. **Results Retrieval**: Download results.json back to GitHub Actions runner
5. **Cleanup**: Remove temporary files from both runner and VM server

### 3. Error Handling
- **Connection Testing**: Validates SSH connectivity before proceeding
- **File Verification**: Confirms results file exists before download
- **Cleanup on Failure**: Uses `if: always()` to ensure cleanup runs even on failure
- **Same Failure Conditions**: Maintains ERROR-level issue detection and workflow failure

## Required GitHub Secrets

### New Secrets
- `SERVER_PASSWORD`: SSH password for VM server access

### Existing Secrets (Still Required)
- `SECURE_GITHUB_TOKEN`: GitHub token for repository access (if needed for future enhancements)

### Removed Secrets
- `DIGITALOCEAN_ACCESS_TOKEN`: No longer needed
- `KUBERNETES_CLUSTER_ID`: No longer needed  
- `KUBERNETES_CONTEXT`: No longer needed

## VM Server Requirements

### Prerequisites
- VM server accessible at `**************`
- SSH access with password authentication
- Docker and Docker Compose installed
- Sufficient disk space for temporary source code storage

### Directory Structure
```
/tmp/semgrep-analysis-{run_id}-{run_attempt}/
├── src/                    # Source code
├── results/               # Semgrep output
└── docker-compose.yml     # Generated configuration
```

## Troubleshooting

### Common Issues

1. **SSH Connection Failures**
   - Verify VM server is accessible
   - Check `SERVER_PASSWORD` secret is correctly set
   - Ensure firewall allows SSH connections

2. **Docker Compose Errors**
   - Verify Docker is running on VM server
   - Check Docker Compose version compatibility
   - Ensure sufficient disk space

3. **Results File Missing**
   - Check Semgrep container logs on VM server
   - Verify source code was transferred correctly
   - Ensure results directory has write permissions

### Debugging Commands

```bash
# Test SSH connection manually
sshpass -p "PASSWORD" ssh -o StrictHostKeyChecking=no root@************** "echo 'Connection successful'"

# Check Docker status on VM
ssh root@************** "docker --version && docker-compose --version"

# View container logs
ssh root@************** "cd /tmp/semgrep-analysis-* && docker-compose logs"
```

## Benefits of Migration

1. **Simplified Infrastructure**: No Kubernetes cluster management
2. **Reduced Complexity**: Fewer moving parts and dependencies
3. **Cost Optimization**: Dedicated VM vs. managed Kubernetes service
4. **Easier Debugging**: Direct server access for troubleshooting
5. **Faster Execution**: No pod scheduling delays

## Backward Compatibility

- **CI Integration**: No changes required in `ci.yml` calling workflow
- **Artifact Output**: Same `results.json` artifact structure
- **Failure Behavior**: Identical ERROR-level issue detection
- **Interface**: Same workflow inputs and outputs

## Security Considerations

- SSH password stored as GitHub secret
- Source code temporarily stored on VM server (cleaned up after execution)
- Results transferred over SSH connection
- No persistent data storage on VM server
