paths:
/list-values:
get:
summary: Get all list values
description: Retrieves all list values without any filters or pagination. Returns only `id` and `name`.
operationId: getAllListValues
tags:
- ListValues
responses:
'200':
description: Successfully retrieved list values
headers:
Content-Type:
schema:
type: string
example: application/vnd-chidhagni-ats.list-values.get.all.res-v1+json
content:
application/vnd-chidhagni-ats.list-values.get.all.res-v1+json:
schema:
type: array
items:
type: object
properties:
id:
type: string
format: uuid
description: List value ID
example: "2e9c51f4-c2f5-4745-9a29-8f3e4287152c"
name:
type: string
description: List value name
example: "Placed"
'401':
$ref: '#/components/responses/Unauthorized'
'403':
$ref: '#/components/responses/Forbidden'
'500':
$ref: '#/components/responses/InternalServerError'
