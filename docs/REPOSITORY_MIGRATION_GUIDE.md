# Repository Pattern Migration Guide

This guide helps you migrate from direct API calls to the repository pattern for better testing, maintainability, and decoupling.

## Overview

The repository pattern provides an abstraction layer between your components and data sources, enabling:
- **Better Testing**: Easy mocking of data operations
- **Loose Coupling**: Components don't depend directly on API implementations  
- **Configuration Management**: Environment-specific behavior through centralized config
- **Caching & Performance**: Built-in caching and retry mechanisms
- **Feature Flags**: Runtime switching between API and mock implementations

## Quick Migration Examples

### Before: Direct API Hook Usage
```typescript
// ❌ Old way - direct API dependency
import { useCreateApplicant } from '@/hooks/useApplicantsApi';

function MyComponent() {
  const { execute: createApplicant } = useCreateApplicant();
  
  const handleSubmit = async (data) => {
    await createApplicant(data);
  };
}
```

### After: Repository Pattern
```typescript
// ✅ New way - repository pattern
import { useCreateApplicant } from '@/repositories';

function MyComponent() {
  const { createApplicant } = useCreateApplicant();
  
  const handleSubmit = async (data) => {
    await createApplicant(data);
  };
}
```

## Step-by-Step Migration

### 1. Update Imports

**Before:**
```typescript
import { 
  useCreateApplicant,
  useListApplicants,
  useGetApplicant 
} from '@/hooks/useApplicantsApi';
```

**After:**
```typescript
import { 
  useCreateApplicant,
  useListApplicants,
  useGetApplicant 
} from '@/repositories';
```

### 2. Update Hook Usage

The new repository hooks have cleaner APIs:

**Before:**
```typescript
const { execute: createApplicant, loading, error } = useCreateApplicant();

// Usage
const result = await createApplicant({
  body: applicantData
}, (error) => {
  showErrorToast(error.message);
});
```

**After:**
```typescript
const { createApplicant } = useCreateApplicant();

// Usage - error handling and toasts are built-in
const result = await createApplicant(applicantData);
```

### 3. Environment Configuration

Set environment variables to control behavior:

```bash
# .env.development
VITE_ENABLE_MOCKING=true  # Use mock data in development
VITE_API_URL=http://localhost:8080/ats/api/v1

# .env.production  
VITE_ENABLE_MOCKING=false # Use real API in production
VITE_API_URL=https://api.talentflow.com/ats/api/v1
```

### 4. Custom Configuration

Override default behavior when needed:

```typescript
// Use mock data for this specific component
const { createApplicant } = useCreateApplicant();
const repository = useApplicantRepository({ 
  enableMocking: true,
  enableCaching: false 
});
```

## API Compatibility Matrix

| Old Hook | New Hook | Status | Notes |
|----------|----------|--------|-------|
| `useCreateApplicant` | `useCreateApplicant` | ✅ Compatible | Simplified API |
| `useCreateApplicantWithFiles` | `useCreateApplicantWithFiles` | ✅ Compatible | Same interface |
| `useListApplicants` | `useListApplicants` | ✅ Compatible | Enhanced caching |
| `useGetApplicant` | `useGetApplicant` | ✅ Compatible | Auto error handling |
| `usePatchApplicant` | `useUpdateApplicant` | ⚠️ Renamed | Consistent naming |
| `useDeleteApplicant` | `useDeleteApplicant` | ✅ Compatible | Same interface |
| `useActivateApplicant` | `useActivateApplicant` | ✅ Compatible | Same interface |

## Configuration Examples

### Development with Mocking
```typescript
// Automatically uses mock data in development
const { listApplicants } = useListApplicants();
```

### Testing Configuration
```typescript
// Force mock usage for testing
const repository = useApplicantRepository({ enableMocking: true });
```

### Production Optimization
```typescript
// Production automatically uses:
// - Real API calls
// - Caching enabled
// - Retry logic
// - Error reporting
const { createApplicant } = useCreateApplicant();
```

## Advanced Features

### 1. Cache Management
```typescript
const { getCacheStats, clearCache } = useRepositoryManager();

// Monitor cache performance
const stats = getCacheStats();
console.log(`Cache size: ${stats?.size}, Keys: ${stats?.keys}`);

// Clear cache when needed
clearCache();
```

### 2. Repository Type Detection
```typescript
const { isApiRepository, isMockRepository } = useRepositoryManager();

if (isApiRepository()) {
  console.log('Using real API');
} else if (isMockRepository()) {
  console.log('Using mock data');
}
```

### 3. Comprehensive Operations
```typescript
// Get all operations in one hook
const {
  listApplicants,
  getApplicant,
  createApplicant,
  updateApplicant,
  deleteApplicant
} = useApplicantOperations();
```

## Migration Checklist

- [ ] **Update imports** from `@/hooks/useApplicantsApi` to `@/repositories`
- [ ] **Remove manual error handling** - now built into repository hooks
- [ ] **Update hook destructuring** - new hooks return direct functions
- [ ] **Set environment variables** for API URLs and feature flags
- [ ] **Test both API and mock modes** to ensure compatibility
- [ ] **Update component tests** to use repository mocking
- [ ] **Remove old API hook imports** after migration is complete

## Testing with Repository Pattern

### Component Testing
```typescript
// jest.setup.ts
import { config } from '@/config/appConfig';

// Force mock mode for all tests
config.features.enableMocking = true;
```

### Individual Test Mocking
```typescript
import { useCreateApplicant } from '@/repositories';
import { MockApplicantRepository } from '@/repositories';

// Mock specific repository behavior
jest.mock('@/repositories', () => ({
  useCreateApplicant: () => ({
    createApplicant: jest.fn().mockResolvedValue({ success: true })
  })
}));
```

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```typescript
   // ❌ Wrong
   import { useCreateApplicant } from '@/hooks/useApplicantsApi';
   
   // ✅ Correct
   import { useCreateApplicant } from '@/repositories';
   ```

2. **API Parameter Changes**
   ```typescript
   // ❌ Old way
   await createApplicant({ body: data }, errorCallback);
   
   // ✅ New way
   await createApplicant(data); // Error handling is automatic
   ```

3. **Environment Configuration**
   ```bash
   # Missing environment variables can cause issues
   VITE_API_URL=your-api-url
   VITE_ENABLE_MOCKING=false
   ```

### Performance Tips

- Repository hooks automatically cache API responses
- Use `clearCache()` after mutations to ensure fresh data
- Mock mode is faster for development and testing
- Production mode includes retry logic and error reporting

## Migration Support

If you encounter issues during migration:

1. Check the console for configuration warnings
2. Verify environment variables are set correctly
3. Test with `enableMocking: true` to isolate API issues
4. Use the repository manager hooks to debug cache and configuration

The repository pattern maintains full backward compatibility while providing enhanced features for better development experience. 