paths:
  /applicant:
    post:
      summary: Get all applicants
      description: Retrieves a paginated list of all applicants with filtering and sorting options.  
                   If no filters are provided, returns all applicants with default pagination.
      operationId: getAllApplicants
      tags:
        - Applicants
      requestBody:
        description: Filtering, sorting, and pagination parameters (all optional)
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                page:
                  type: integer
                  minimum: 1
                  default: 1
                  description: Page number (default 1)
                pageSize:
                  type: integer
                  minimum: 1
                  maximum: 100
                  default: 20
                  description: Number of items per page (default 20)
                sortDirection:
                  type: string
                  enum: ["ASC", "DESC"]
                  default: ASC
                  description: Sort direction (default ASC)
                status:
                  type: string
                  format: uuid
                  description: Applicant status UUID (optional)
                search:
                  type: string
                  description: Search term applied to firstName, lastName, middleName, nickName, or email (optional)
      responses:
        '200':
          description: Successfully retrieved applicants
          headers:
            Content-Type:
              schema:
                type: string
                example: application/vnd-chidhagni-ats.applicant.get.all.res-v1+json
          content:
            application/vnd-chidhagni-ats.applicant.get.all.res-v1+json:
              schema:
                $ref: '#/components/schemas/GetAllApplicantsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '500':
          $ref: '#/components/responses/InternalServerError'
