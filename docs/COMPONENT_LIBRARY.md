# TalentFlow ATS - Candidate Component Library

## Overview
This document provides a comprehensive reference for the new reusable component library created for the TalentFlow ATS candidate management system.

## Architecture

```
src/
├── components/candidates/
│   ├── form-fields/           # Reusable form input components
│   │   ├── FormField.tsx      # Base form field wrapper
│   │   ├── TextInput.tsx      # Text input with validation
│   │   ├── SelectInput.tsx    # Select dropdown with validation
│   │   ├── DateInput.tsx      # Date input with validation
│   │   ├── PhoneInput.tsx     # Phone input with country code
│   │   ├── TextAreaInput.tsx  # Textarea with validation
│   │   └── index.ts           # Barrel export
│   ├── ui/                    # UI components for candidates
│   │   ├── StatusBadge.tsx    # Standardized status display
│   │   ├── CandidateCard.tsx  # Reusable candidate card
│   │   ├── FormSection.tsx    # Form section containers
│   │   └── index.ts           # Barrel export
│   └── hooks/                 # Business logic hooks
│       ├── useFormValidation.ts    # Centralized validation
│       ├── useFormInteraction.ts   # Interaction tracking
│       └── index.ts               # Barrel export
├── utils/candidates/          # Utility functions
│   ├── formOptions.ts         # Centralized dropdown options
│   ├── dataTransforms.ts      # Data transformation utilities
│   └── index.ts               # Barrel export
└── docs/                      # Documentation
    ├── MIGRATION_GUIDE.md     # Step-by-step migration guide
    └── COMPONENT_LIBRARY.md   # This file
```

## Component Reference

### Form Field Components

#### FormField
Base wrapper component that provides consistent structure for all form inputs.

**Props:**
```tsx
interface FormFieldProps {
  id: string;
  label: string;
  required?: boolean;
  error?: string;
  className?: string;
  children: React.ReactNode;
  description?: string;
}
```

**Features:**
- Consistent label styling with required indicator
- Error message display with ARIA live regions
- Accessibility attributes (htmlFor, aria-describedby)
- Optional description text

#### TextInput
Text input component with built-in validation and error handling.

**Props:**
```tsx
interface TextInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'email' | 'tel' | 'url' | 'password';
  disabled?: boolean;
  maxLength?: number;
  autoComplete?: string;
}
```

**Usage:**
```tsx
<TextInput
  id="firstName"
  label="First Name"
  required
  value={data.firstName}
  onChange={(value) => handleChange('firstName', value)}
  placeholder="Enter first name"
  error={errors.firstName}
/>
```

#### SelectInput
Select dropdown component with consistent option handling.

**Props:**
```tsx
interface SelectInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  options: SelectOption[];
  disabled?: boolean;
}

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}
```

**Usage:**
```tsx
<SelectInput
  id="degree"
  label="Degree"
  required
  value={data.degree}
  onChange={(value) => handleChange('degree', value)}
  options={degreeOptions}
  placeholder="Select degree"
  error={errors.degree}
/>
```

#### PhoneInput
Phone input with country code selector.

**Props:**
```tsx
interface PhoneInputProps extends Omit<FormFieldProps, 'children'> {
  value: string;
  onChange: (value: string) => void;
  countryCode: string;
  onCountryCodeChange: (code: string) => void;
  placeholder?: string;
  disabled?: boolean;
  countryCodes?: CountryCode[];
}
```

**Usage:**
```tsx
<PhoneInput
  id="mobilePhone"
  label="Mobile Phone"
  value={data.mobilePhone}
  countryCode={data.countryCode || '+1'}
  onChange={(value) => handleChange('mobilePhone', value)}
  onCountryCodeChange={(code) => handleChange('countryCode', code)}
  error={errors.mobilePhone}
/>
```

### UI Components

#### StatusBadge
Standardized status badge with consistent color coding.

**Props:**
```tsx
interface StatusBadgeProps {
  status: CandidateStatus;
  className?: string;
}

type CandidateStatus = 
  | 'applied' | 'screening' | 'interview' 
  | 'offer' | 'hired' | 'onboarding' | 'rejected';
```

**Usage:**
```tsx
<StatusBadge status="interview" />
```

#### CandidateCard
Reusable candidate card for listings.

**Props:**
```tsx
interface CandidateCardProps {
  candidate: CandidateCardData;
  onEdit: (candidate: CandidateCardData) => void;
  onDelete: (candidateId: string) => void;
  className?: string;
}
```

**Usage:**
```tsx
<CandidateCard
  candidate={candidateData}
  onEdit={handleEdit}
  onDelete={handleDelete}
/>
```

#### FormSection
Container components for organizing form content.

**Props:**
```tsx
interface FormSectionProps {
  title: string;
  icon?: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  className?: string;
  headerActions?: React.ReactNode;
}

interface ArrayFormSectionProps extends FormSectionProps {
  onAdd: () => void;
  addButtonText?: string;
  showAddButton?: boolean;
}
```

**Usage:**
```tsx
<FormSection title="Personal Information" icon={User}>
  {/* Form fields */}
</FormSection>

<ArrayFormSection
  title="Work Experience"
  icon={Clock}
  onAdd={addExperience}
  addButtonText="Add Experience"
>
  {experiences.map((exp, index) => (
    <ArrayItem
      key={exp.id}
      title="Experience"
      index={index}
      onRemove={() => removeExperience(index)}
    >
      {/* Experience fields */}
    </ArrayItem>
  ))}
</ArrayFormSection>
```

### Hooks

#### useFormValidation
Centralized validation logic with configurable rules.

**API:**
```tsx
const { errors, validateField, validateAll, clearErrors, isValid } = 
  useFormValidation(validationRules, hasInteracted);
```

**Validation Rules:**
```tsx
const validationRules = {
  firstName: { required: true },
  email: { 
    required: true, 
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value) => value.includes('@') ? null : 'Invalid email'
  },
  phone: { pattern: /^[\d\s\-\+\(\)]+$/ }
};
```

#### useFormInteraction
Tracks user interaction to prevent premature validation.

**API:**
```tsx
const { hasInteracted, markAsInteracted, reset, shouldShowValidation } = 
  useFormInteraction();
```

### Utilities

#### Form Options
Centralized dropdown options for consistency across forms.

**Available Options:**
- `courtesyTitleOptions` - Mr, Mrs, Ms, Dr, Prof
- `degreeOptions` - High School through PhD
- `technologyOptions` - 40+ technology options
- `industryOptions` - 15+ industry categories
- `workAuthorizationOptions` - US work authorization types
- `currencyOptions` - Major world currencies
- `languageOptions` - 20+ languages
- `countryOptions` - Major countries

#### Data Transforms
Utility functions for data conversion between different formats.

**Functions:**
- `convertFormDataToCandidate()` - Form data → Candidate object
- `convertCandidateToFormData()` - Candidate object → Form data
- `convertCandidateToCardData()` - Candidate → Card display data
- `getCandidateInitials()` - Generate initials from name
- `formatApplicationDate()` - Format dates for display
- `calculateTotalExperience()` - Calculate years from work history

## Performance Optimizations

### React.memo Usage
All components use React.memo to prevent unnecessary re-renders:

```tsx
export const TextInput = React.memo<TextInputProps>(({ ... }) => {
  // Component implementation
});
```

### useMemo and useCallback
Expensive computations and functions are memoized:

```tsx
const validationRules = useMemo(() => ({
  firstName: commonValidationRules.required,
  email: commonValidationRules.email,
}), []);

const handleChange = useCallback((field, value) => {
  // Change handler logic
}, [dependencies]);
```

## Accessibility Features

### ARIA Labels
All form components include proper ARIA attributes:
- `aria-invalid` for validation state
- `aria-describedby` for error messages
- `role="alert"` for error text
- `aria-live="polite"` for dynamic content

### Keyboard Navigation
- Tab order follows logical flow
- Enter key submits forms
- Escape key closes modals
- Arrow keys navigate select options

### Screen Reader Support
- Semantic HTML structure
- Descriptive labels and help text
- Error announcements
- Status updates

## Testing Strategy

### Unit Tests
Each component should have tests covering:
- Rendering with different props
- User interactions (typing, clicking, selecting)
- Validation behavior
- Error states
- Accessibility attributes

### Integration Tests
Form components should be tested together:
- Form submission workflows
- Validation across multiple fields
- Data persistence and retrieval

### Example Test Structure
```tsx
describe('TextInput', () => {
  it('renders with label and placeholder', () => {
    // Test basic rendering
  });

  it('shows error message when invalid', () => {
    // Test error display
  });

  it('calls onChange when user types', () => {
    // Test user interaction
  });

  it('has proper accessibility attributes', () => {
    // Test ARIA attributes
  });
});
```

## Future Enhancements

### Planned Components
- FileUpload component for document handling
- MultiSelect component for skills/tags
- DateRangePicker for employment periods
- RichTextEditor for descriptions
- ImageUpload for candidate photos

### Internationalization
- Extract all text strings to translation files
- Support for RTL languages
- Locale-specific date/number formatting
- Currency and phone number localization

### Advanced Validation
- Async validation for email uniqueness
- Cross-field validation rules
- Custom validation rule builder UI
- Integration with external validation services

This component library provides a solid foundation for scalable, maintainable candidate management functionality while significantly improving developer experience and code quality.
