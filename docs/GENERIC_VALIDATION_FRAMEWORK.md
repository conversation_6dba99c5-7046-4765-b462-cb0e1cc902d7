# 🔍 **COMPREHENSIVE 12-STEP VALIDATION FRAMEWORK**
**For Production-Ready Spring Boot Microservices**

---

## **📋 OVERVIEW**

This document establishes a comprehensive validation framework for ensuring production-ready quality in Spring Boot microservices. Use this framework for all code reviews, PR validations, and production readiness assessments.

**Target Audience:** Development Team, Tech Leads, DevOps Engineers  
**Scope:** Spring Boot Microservices Architecture  
**Version:** 1.0.0  
**Last Updated:** 2025-01-03  

---

## **🎯 VALIDATION FRAMEWORK STRUCTURE**

### **📊 FOUNDATION STEPS (1-7) - Code Quality & Architecture**
1. **Java Coding Standards Compliance**
2. **SOLID Principles, DRY, YAGNI, and KISS Assessment**
3. **Microservices Architecture Readiness**
4. **Database Access Efficiency (JOOQ/JPA)**
5. **Transaction Management**
6. **Logging Implementation**
7. **Exception Handling Strategy**

### **🚀 PRODUCTION READINESS STEPS (8-12) - Operational Excellence**
8. **Security & Authentication**
9. **Observability & Monitoring**
10. **Testing Strategy & Coverage**
11. **Performance & Scalability**
12. **DevOps & Deployment Readiness**

---

## **📝 HOW TO USE THIS FRAMEWORK**

### **For Code Reviews:**
```bash
# Run validation checklist for each PR
1. Clone the PR branch
2. Review against steps 1-7 (mandatory)
3. Assess steps 8-12 based on PR scope
4. Document findings using provided templates
5. Provide actionable feedback with examples
```

### **For Production Readiness:**
```bash
# Complete assessment before production deployment
1. Validate all 12 steps comprehensively
2. Ensure all CRITICAL items are resolved
3. Address HIGH priority items
4. Document any accepted technical debt
5. Sign-off from Tech Lead required
```

### **For Continuous Improvement:**
```bash
# Regular codebase health checks
1. Monthly assessment of entire codebase
2. Track improvement metrics over time
3. Update framework based on lessons learned
4. Share best practices across teams
```

---

## **🔍 DETAILED VALIDATION STEPS**

### **STEP 1: Java Coding Standards Compliance**

#### **🎯 Validation Criteria:**
- [ ] **Import Organization** - Proper grouping with blank lines
- [ ] **Naming Conventions** - CamelCase, meaningful names
- [ ] **Code Formatting** - Consistent indentation, line breaks
- [ ] **JavaDoc Documentation** - All public methods documented
- [ ] **Constants Usage** - No magic numbers/strings

#### **✅ Good Example:**
```java
// Application imports
import com.company.project.entity.dto.request.EntityRequestDTO;
import com.company.project.entity.service.EntityService;

// Third-party imports
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;

// Standard library imports
import java.util.List;
import java.util.UUID;

/**
 * REST Controller for entity management operations.
 * Provides endpoints for CRUD operations on entities.
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@RestController
@RequiredArgsConstructor
public class EntityController {
    
    private static final String ENTITY_CREATED_MESSAGE = "Entity created successfully";
    private static final int MAX_NAME_LENGTH = 50;
    
    /**
     * Creates a new entity with the provided data.
     * 
     * @param request The entity creation request
     * @return Response containing created entity details
     */
    public ResponseEntity<EntityResponseDTO> createEntity(
            @Valid @RequestBody EntityRequestDTO request) {
        // Implementation
    }
}
```

#### **❌ Issues to Flag:**
```java
// Poor example - multiple issues
import java.util.List;
import com.company.project.entity.dto.request.EntityRequestDTO; // Wrong order
import java.util.UUID;
import com.company.project.entity.service.EntityService; // No grouping

public class entityController { // Wrong naming convention
    public ResponseEntity createEntity(EntityRequestDTO req) { // No JavaDoc, poor naming
        if(req.getName().length()>50) { // Magic number, poor formatting
            // Implementation
        }
    }
}
```

---

### **STEP 2: SOLID Principles Assessment**

#### **🎯 Validation Criteria:**
- [ ] **Single Responsibility** - Each class has one clear purpose
- [ ] **Open/Closed** - Extensible without modification
- [ ] **Liskov Substitution** - Subtypes are substitutable
- [ ] **Interface Segregation** - Focused, cohesive interfaces
- [ ] **Dependency Inversion** - Depend on abstractions

#### **✅ Good Example - SOLID Principles:**
```java
// Single Responsibility - Only handles entity operations
@Service
public class EntityService {
    private final EntityRepository repository;
    private final EntityValidator validator;
    private final EntityMapper mapper;

    public EntityResponseDTO createEntity(EntityRequestDTO request) {
        validator.validate(request);
        Entity entity = mapper.toEntity(request);
        Entity saved = repository.save(entity);
        return mapper.toResponse(saved);
    }
}

// Dependency Inversion - Depends on abstraction
public interface EntityRepository {
    Entity save(Entity entity);
    Optional<Entity> findById(UUID id);
}

// Interface Segregation - Focused interface
public interface EntityValidator {
    void validate(EntityRequestDTO request);
}

// Open/Closed Principle - Extensible validation
@Component
public class CompositeEntityValidator implements EntityValidator {
    private final List<ValidationRule<EntityRequestDTO>> rules;

    @Override
    public void validate(EntityRequestDTO request) {
        rules.forEach(rule -> rule.validate(request));
    }
}
```

#### **❌ Issues to Flag - SOLID Violations:**
```java
// Violates Single Responsibility - doing too much
@Service
public class EntityService {
    public EntityResponseDTO createEntity(EntityRequestDTO request) {
        // Validation logic (should be in validator)
        if (request.getEmail() == null) throw new ValidationException("Email required");

        // File upload logic (should be in file service)
        uploadFiles(request.getFiles());

        // Email sending logic (should be in notification service)
        sendWelcomeEmail(request.getEmail());

        // Database logic (should be in repository)
        // ... database operations
    }
}
```

---

### **DRY (Don't Repeat Yourself) Principle Assessment**

#### **🎯 Validation Criteria:**
- [ ] **Code Duplication** - No repeated logic blocks
- [ ] **Configuration Duplication** - Centralized configuration
- [ ] **Utility Methods** - Common operations extracted
- [ ] **Constants Management** - Shared constants in one place
- [ ] **Template Patterns** - Reusable code templates

#### **✅ Good Example - DRY Implementation:**
```java
// Centralized utility for common operations
@Component
public class JsonProcessingUtil {
    private final ObjectMapper objectMapper;

    public <T> T deserialize(String json, TypeReference<T> typeRef) {
        try {
            return objectMapper.readValue(json, typeRef);
        } catch (JsonProcessingException e) {
            throw new JsonProcessingException("Failed to deserialize JSON", e);
        }
    }

    public String serialize(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new JsonProcessingException("Failed to serialize object", e);
        }
    }
}

// Shared constants
public final class ValidationConstants {
    public static final int MAX_NAME_LENGTH = 50;
    public static final int MIN_PASSWORD_LENGTH = 8;
    public static final String EMAIL_REGEX = "^[A-Za-z0-9+_.-]+@(.+)$";

    private ValidationConstants() {
        throw new UnsupportedOperationException("Utility class");
    }
}

// Template method pattern for common operations
@Service
public abstract class BaseEntityService<T, R, S> {

    protected final BaseRepository<T> repository;
    protected final BaseMapper<T, R, S> mapper;
    protected final BaseValidator<R> validator;

    public S createEntity(R request) {
        validator.validate(request);
        T entity = mapper.toEntity(request);
        T saved = repository.save(entity);
        return mapper.toResponse(saved);
    }

    // Template method - subclasses implement specific logic
    protected abstract void performAdditionalValidation(R request);
}
```

#### **❌ Issues to Flag - DRY Violations:**
```java
// Repeated JSON processing logic
@Service
public class UserService {
    public UserResponseDTO createUser(UserRequestDTO request) {
        // Repeated JSON processing
        try {
            String json = objectMapper.writeValueAsString(request);
            // Process...
        } catch (JsonProcessingException e) {
            throw new ProcessingException("JSON error", e);
        }
    }
}

@Service
public class ProductService {
    public ProductResponseDTO createProduct(ProductRequestDTO request) {
        // Same JSON processing repeated
        try {
            String json = objectMapper.writeValueAsString(request);
            // Process...
        } catch (JsonProcessingException e) {
            throw new ProcessingException("JSON error", e);
        }
    }
}

// Repeated validation logic
public class UserValidator {
    public void validate(UserRequestDTO request) {
        if (request.getEmail() == null || !request.getEmail().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new ValidationException("Invalid email");
        }
    }
}

public class CustomerValidator {
    public void validate(CustomerRequestDTO request) {
        // Same email validation repeated
        if (request.getEmail() == null || !request.getEmail().matches("^[A-Za-z0-9+_.-]+@(.+)$")) {
            throw new ValidationException("Invalid email");
        }
    }
}
```

---

### **YAGNI (You Aren't Gonna Need It) Principle Assessment**

#### **🎯 Validation Criteria:**
- [ ] **Over-Engineering** - No unnecessary complexity
- [ ] **Premature Optimization** - No early performance optimizations
- [ ] **Unused Features** - No speculative functionality
- [ ] **Configuration Complexity** - Simple, focused configuration
- [ ] **Abstraction Levels** - Appropriate abstraction without over-design

#### **✅ Good Example - YAGNI Compliance:**
```java
// Simple, focused implementation
@Service
public class UserService {
    private final UserRepository repository;
    private final UserMapper mapper;

    public UserResponseDTO createUser(UserRequestDTO request) {
        User user = mapper.toEntity(request);
        User saved = repository.save(user);
        return mapper.toResponse(saved);
    }

    public UserResponseDTO getUserById(UUID id) {
        return repository.findById(id)
            .map(mapper::toResponse)
            .orElseThrow(() -> new UserNotFoundException("User not found"));
    }
}

// Simple configuration - only what's needed
@Configuration
public class DatabaseConfig {

    @Bean
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        config.setMaximumPoolSize(10); // Simple pool size
        return new HikariDataSource(config);
    }
}
```

#### **❌ Issues to Flag - YAGNI Violations:**
```java
// Over-engineered for simple requirements
@Service
public class UserService {

    // Unnecessary abstraction layers
    private final UserBusinessLogicProcessor processor;
    private final UserDataTransformationEngine transformer;
    private final UserValidationOrchestrator validator;
    private final UserPersistenceManager persistenceManager;
    private final UserEventPublishingService eventPublisher;
    private final UserAuditTrackingService auditService;

    public UserResponseDTO createUser(UserRequestDTO request) {
        // Over-complex workflow for simple user creation
        ValidationContext context = validator.createValidationContext(request);
        ProcessingPipeline pipeline = processor.buildProcessingPipeline(context);
        TransformationResult result = transformer.executeTransformation(request, pipeline);
        PersistenceOperation operation = persistenceManager.preparePersistenceOperation(result);
        User saved = persistenceManager.executePersistence(operation);
        auditService.recordUserCreation(saved, context);
        eventPublisher.publishUserCreatedEvent(saved);
        return transformer.transformToResponse(saved);
    }
}

// Premature optimization - complex caching for simple use case
@Configuration
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        // Over-engineered caching for simple lookup
        return RedisCacheManager.builder(redisConnectionFactory())
            .cacheDefaults(cacheConfiguration())
            .withCacheConfiguration("users",
                RedisCacheConfiguration.defaultCacheConfig()
                    .entryTtl(Duration.ofMinutes(10))
                    .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                    .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer())))
            .transactionAware()
            .build();
    }
}

// Speculative features not currently needed
@Service
public class UserService {

    // Features that might be needed "someday"
    public void scheduleUserDeletion(UUID userId, LocalDateTime deletionTime) {
        // Not currently used
    }

    public List<UserResponseDTO> findUsersByComplexCriteria(
            String firstName, String lastName, String email,
            LocalDate birthDateFrom, LocalDate birthDateTo,
            String city, String country, String zipCode,
            UserStatus status, UserType type, UserRole role) {
        // Overly complex search not required yet
    }

    public UserAnalyticsDTO generateUserAnalytics(UUID userId) {
        // Analytics feature not in current requirements
    }
}
```

---

### **KISS (Keep It Simple, Stupid) Principle Assessment**

#### **🎯 Validation Criteria:**
- [ ] **Code Simplicity** - Easy to read and understand
- [ ] **Method Complexity** - Small, focused methods
- [ ] **Class Design** - Simple, clear responsibilities
- [ ] **Configuration Simplicity** - Minimal, straightforward setup
- [ ] **Architecture Simplicity** - No unnecessary layers or patterns

#### **✅ Good Example - KISS Implementation:**
```java
// Simple, clear implementation
@RestController
@RequestMapping("/api/v1/users")
public class UserController {

    private final UserService userService;

    @PostMapping
    public ResponseEntity<UserResponseDTO> createUser(@Valid @RequestBody UserRequestDTO request) {
        UserResponseDTO response = userService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserResponseDTO> getUser(@PathVariable UUID id) {
        UserResponseDTO response = userService.getUserById(id);
        return ResponseEntity.ok(response);
    }
}

// Simple service with clear methods
@Service
public class UserService {

    private final UserRepository repository;
    private final UserMapper mapper;

    public UserResponseDTO createUser(UserRequestDTO request) {
        User user = mapper.toEntity(request);
        User saved = repository.save(user);
        return mapper.toResponse(saved);
    }

    public UserResponseDTO getUserById(UUID id) {
        return repository.findById(id)
            .map(mapper::toResponse)
            .orElseThrow(() -> new UserNotFoundException("User not found: " + id));
    }
}

// Simple validation
@Component
public class UserValidator {

    public void validate(UserRequestDTO request) {
        if (request.getEmail() == null || request.getEmail().trim().isEmpty()) {
            throw new ValidationException("Email is required");
        }

        if (!isValidEmail(request.getEmail())) {
            throw new ValidationException("Invalid email format");
        }
    }

    private boolean isValidEmail(String email) {
        return email.contains("@") && email.contains(".");
    }
}
```

#### **❌ Issues to Flag - KISS Violations:**
```java
// Overly complex method
@Service
public class UserService {

    public UserResponseDTO createUser(UserRequestDTO request) {
        // Complex nested logic - hard to follow
        return Optional.ofNullable(request)
            .filter(req -> req.getEmail() != null)
            .map(req -> {
                try {
                    return Optional.ofNullable(validateAndTransform(req))
                        .map(validatedReq -> {
                            Entity entity = Optional.ofNullable(mapper.toEntity(validatedReq))
                                .orElseThrow(() -> new MappingException("Mapping failed"));
                            return Optional.ofNullable(repository.save(entity))
                                .map(savedEntity -> {
                                    try {
                                        return mapper.toResponse(savedEntity);
                                    } catch (Exception e) {
                                        throw new ResponseMappingException("Response mapping failed", e);
                                    }
                                })
                                .orElseThrow(() -> new PersistenceException("Save failed"));
                        })
                        .orElseThrow(() -> new ValidationException("Validation failed"));
                } catch (Exception e) {
                    throw new UserCreationException("User creation failed", e);
                }
            })
            .orElseThrow(() -> new InvalidRequestException("Invalid request"));
    }
}

// Unnecessary complexity in configuration
@Configuration
public class ComplexConfig {

    @Bean
    @ConditionalOnProperty(name = "app.feature.advanced-user-processing", havingValue = "true")
    @ConditionalOnClass(AdvancedUserProcessor.class)
    @ConditionalOnMissingBean(UserProcessor.class)
    public UserProcessor advancedUserProcessor(
            @Qualifier("primaryDataSource") DataSource dataSource,
            @Value("${app.user.processing.batch-size:100}") int batchSize,
            @Value("${app.user.processing.thread-pool-size:10}") int threadPoolSize,
            Environment environment) {

        AdvancedUserProcessorConfig config = AdvancedUserProcessorConfig.builder()
            .dataSource(dataSource)
            .batchSize(batchSize)
            .threadPoolSize(threadPoolSize)
            .enableMetrics(environment.getProperty("app.metrics.enabled", Boolean.class, false))
            .enableAuditing(environment.getProperty("app.audit.enabled", Boolean.class, false))
            .retryPolicy(RetryPolicy.builder()
                .maxAttempts(3)
                .backoffStrategy(BackoffStrategy.EXPONENTIAL)
                .build())
            .build();

        return new AdvancedUserProcessor(config);
    }
}
```

---

### **STEP 3: Microservices Architecture Readiness**

#### **🎯 Validation Criteria:**
- [ ] **API Versioning** - Consistent `/api/v1/` pattern
- [ ] **OpenAPI Documentation** - Complete specifications
- [ ] **Stateless Design** - No session dependencies
- [ ] **Configuration Externalization** - Environment-based config
- [ ] **Service Boundaries** - Clear domain separation

#### **✅ Good Example:**
```java
@RestController
@RequestMapping("/api/v1/entities")
@Tag(name = "Entities", description = "Entity management operations")
public class EntityController {
    
    @PostMapping(produces = "application/vnd-company-project.entity.create.res-v1+json")
    @Operation(
        summary = "Create new entity",
        description = "Creates a new entity with validation and document upload support"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Entity created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "409", description = "Entity already exists")
    })
    public ResponseEntity<EntityResponseDTO> createEntity(
            @Valid @RequestBody EntityRequestDTO request) {
        // Implementation
    }
}
```

---

### **STEP 4: Database Access Efficiency**

#### **🎯 Validation Criteria:**
- [ ] **Type-Safe Queries** - Using JOOQ DSL or JPA Criteria API
- [ ] **Efficient Field Selection** - Only required fields
- [ ] **Proper Indexing** - Database indexes for query patterns
- [ ] **Connection Pooling** - HikariCP configuration
- [ ] **Query Performance Monitoring** - Slow query detection

#### **✅ Good Example (JOOQ):**
```java
@Repository
public class EntityRepository {
    
    public List<EntitySummaryDTO> findEntitySummaries(EntitySearchCriteria criteria) {
        return dslContext.select(
                ENTITY.ID,
                ENTITY.NAME,
                ENTITY.EMAIL,
                ENTITY.STATUS)
            .from(ENTITY)
            .where(buildSearchConditions(criteria))
            .orderBy(ENTITY.CREATED_DATE.desc())
            .limit(criteria.getPageSize())
            .offset(criteria.getOffset())
            .fetchInto(EntitySummaryDTO.class);
    }
    
    public boolean existsByEmail(String email) {
        return dslContext.fetchExists(
            dslContext.selectOne()
                .from(ENTITY)
                .where(ENTITY.EMAIL.equalIgnoreCase(email))
        );
    }
}
```

#### **✅ Good Example (JPA):**
```java
@Repository
public interface EntityRepository extends JpaRepository<Entity, UUID> {
    
    @Query("SELECT new com.company.project.dto.EntitySummaryDTO(e.id, e.name, e.email, e.status) " +
           "FROM Entity e WHERE e.status = :status ORDER BY e.createdDate DESC")
    Page<EntitySummaryDTO> findEntitySummaries(@Param("status") String status, Pageable pageable);
    
    boolean existsByEmailIgnoreCase(String email);
}
```

---

## **🚨 CRITICAL VALIDATION POINTS**

### **Before Every PR Merge:**
1. **All CRITICAL issues resolved** - No exceptions
2. **Security vulnerabilities addressed** - OWASP compliance
3. **Test coverage maintained** - Minimum 80%
4. **Performance impact assessed** - No degradation
5. **Documentation updated** - API specs, README

### **Before Production Deployment:**
1. **Complete 12-step validation** - All steps assessed
2. **Load testing completed** - Performance validated
3. **Security scan passed** - Vulnerability assessment
4. **Monitoring configured** - Alerts and dashboards
5. **Rollback plan documented** - Deployment safety

---

## **📊 VALIDATION SCORING**

### **Grading Scale:**
- **A (90-100%)** - Excellent, production-ready
- **B (80-89%)** - Good, minor improvements needed
- **C (70-79%)** - Acceptable, moderate improvements needed
- **D (60-69%)** - Poor, significant improvements required
- **F (<60%)** - Failing, major rework required

### **Priority Levels:**
- **🚨 CRITICAL** - Must fix before merge/deployment
- **🔴 HIGH** - Should fix before production
- **🟡 MEDIUM** - Nice to have, technical debt
- **🟢 LOW** - Polish items, future improvements

---

### **STEP 5: Transaction Management**

#### **🎯 Validation Criteria:**
- [ ] **Read-Only Transactions** - For query operations
- [ ] **Proper Boundaries** - Minimal transaction scope
- [ ] **Rollback Configuration** - Exception handling
- [ ] **Isolation Levels** - Appropriate for use case
- [ ] **Deadlock Prevention** - Consistent lock ordering

#### **✅ Good Example:**
```java
@Service
public class EntityService {

    @Transactional(readOnly = true)
    public List<EntityResponseDTO> getAllEntities(SearchCriteria criteria) {
        // Read-only transaction for queries
        return repository.findByCriteria(criteria);
    }

    @Transactional(rollbackFor = Exception.class)
    public EntityResponseDTO createEntity(EntityRequestDTO request) {
        // Write transaction with proper rollback
        validateRequest(request);
        Entity entity = mapper.toEntity(request);
        return mapper.toResponse(repository.save(entity));
    }
}
```

---

### **STEP 6: Logging Implementation**

#### **🎯 Validation Criteria:**
- [ ] **Structured Logging** - Consistent format and levels
- [ ] **PII Protection** - Sensitive data masking
- [ ] **Performance Logging** - Operation timing
- [ ] **Correlation IDs** - Request tracing
- [ ] **Appropriate Levels** - DEBUG, INFO, WARN, ERROR

#### **✅ Good Example:**
```java
@Service
@Slf4j
public class EntityService {

    public EntityResponseDTO createEntity(EntityRequestDTO request) {
        String correlationId = MDC.get("correlationId");
        log.info("Creating entity - correlationId: {}, email: {}",
            correlationId, PiiMaskingUtil.maskEmail(request.getEmail()));

        long startTime = System.currentTimeMillis();
        try {
            EntityResponseDTO response = processEntity(request);
            long duration = System.currentTimeMillis() - startTime;
            log.info("Entity created successfully - correlationId: {}, duration: {}ms",
                correlationId, duration);
            return response;
        } catch (Exception e) {
            log.error("Failed to create entity - correlationId: {}, error: {}",
                correlationId, e.getMessage(), e);
            throw e;
        }
    }
}
```

---

### **STEP 7: Exception Handling Strategy**

#### **🎯 Validation Criteria:**
- [ ] **Centralized Handling** - @ControllerAdvice implementation
- [ ] **Proper Exception Hierarchy** - Custom exception types
- [ ] **Meaningful Error Messages** - User-friendly responses
- [ ] **Correlation Tracking** - Request tracing in errors
- [ ] **Metrics Integration** - Error rate monitoring

#### **✅ Good Example:**
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<ErrorResponse> handleValidationException(
            ValidationException ex, HttpServletRequest request) {

        String correlationId = getOrCreateCorrelationId();
        log.warn("Validation error - correlationId: {}, message: {}",
            correlationId, ex.getMessage());

        ErrorResponse error = ErrorResponse.builder()
            .timestamp(LocalDateTime.now())
            .status(HttpStatus.BAD_REQUEST.value())
            .error("Validation Failed")
            .message(ex.getMessage())
            .path(request.getRequestURI())
            .correlationId(correlationId)
            .build();

        return ResponseEntity.badRequest().body(error);
    }
}
```

---

### **STEP 8: Security & Authentication**

#### **🎯 Validation Criteria:**
- [ ] **Authentication/Authorization** - JWT, OAuth2, Spring Security
- [ ] **API Security** - Rate limiting, CORS, CSRF protection
- [ ] **Data Protection** - Encryption, PII handling
- [ ] **Vulnerability Assessment** - OWASP compliance
- [ ] **Secret Management** - Externalized secrets

#### **✅ Good Example:**
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .sessionManagement(session ->
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            .oauth2ResourceServer(oauth2 -> oauth2.jwt())
            .authorizeHttpRequests(authz -> authz
                .requestMatchers("/api/public/**").permitAll()
                .requestMatchers("/api/v1/entities/**").hasRole("USER")
                .anyRequest().authenticated())
            .cors(cors -> cors.configurationSource(corsConfigurationSource()))
            .csrf(csrf -> csrf.disable())
            .build();
    }
}
```

---

### **STEP 9: Observability & Monitoring**

#### **🎯 Validation Criteria:**
- [ ] **Metrics Collection** - Micrometer, Prometheus
- [ ] **Distributed Tracing** - Sleuth, Zipkin/Jaeger
- [ ] **Health Checks** - Actuator endpoints
- [ ] **Performance Monitoring** - Response times, error rates
- [ ] **Business Metrics** - Domain-specific KPIs

#### **✅ Good Example:**
```java
@Component
public class EntityMetrics {
    private final MeterRegistry meterRegistry;
    private final Counter entityCreatedCounter;
    private final Timer entityProcessingTimer;

    public EntityMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.entityCreatedCounter = Counter.builder("entities.created")
            .description("Number of entities created")
            .register(meterRegistry);
        this.entityProcessingTimer = Timer.builder("entities.processing.time")
            .description("Time taken to process entity creation")
            .register(meterRegistry);
    }

    public void recordEntityCreated(String status) {
        entityCreatedCounter.increment(Tags.of("status", status));
    }
}

@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    private final DataSource dataSource;

    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            return Health.up()
                .withDetail("database", "Connected")
                .withDetail("validationQuery", "SELECT 1")
                .build();
        } catch (Exception e) {
            return Health.down(e)
                .withDetail("database", "Connection failed")
                .withDetail("error", e.getMessage())
                .build();
        }
    }
}
```

---

### **STEP 10: Testing Strategy & Coverage**

#### **🎯 MANDATORY Validation Criteria:**
- [ ] **Unit Test Compliance** - STRICT naming: `*UTest.class` + `@Tag("unit")`
- [ ] **Integration Test Compliance** - STRICT naming: `*ITest.class` + `@Tag("integration")`
- [ ] **API Test Compliance** - STRICT naming: `*ApiTest.class` + `@Tag("api")`
- [ ] **Gradle Task Compatibility** - Tests MUST run with designated tasks
- [ ] **Base Class Inheritance** - Proper base test class usage
- [ ] **JUnit 5 Platform** - No JUnit 4 patterns accepted
- [ ] **Test Coverage** - Minimum 80% with JaCoCo

#### **🚨 CRITICAL: Build.gradle Compliance MANDATORY**

**ALL tests MUST follow these EXACT patterns from build.gradle configuration:**

```gradle
// MANDATORY Test Tasks Configuration
task unitTest(type: Test) {
    useJUnitPlatform { includeTags 'unit' }
    include '**/*UTest.class'           // STRICT naming requirement
}

task integrationTest(type: Test) {
    useJUnitPlatform { includeTags 'integration' }
    include '**/*ITest.class'           // STRICT naming requirement
}

task apiTest(type: Test) {
    useJUnitPlatform { includeTags 'api' }
    include '**/*ApiTest.class'         // STRICT naming requirement
}
```

#### **✅ MANDATORY Unit Test Pattern:**
```java
// MANDATORY: Class name MUST end with 'UTest'
@ExtendWith(MockitoExtension.class)
@Tag("unit")                                    // MANDATORY @Tag annotation
class ApplicantServiceUTest {                   // MANDATORY naming: *UTest.class

    @Mock private ApplicantRepository repository;
    @Mock private ApplicantMapper mapper;
    @Mock private ApplicantValidator validator;
    @InjectMocks private ApplicantServiceImpl service;

    @Test
    @DisplayName("Should create applicant successfully with valid data")
    void shouldCreateApplicantSuccessfully() {
        // Given
        ApplicantRequestDTO request = ApplicantTestData.validRequest();
        Applicant entity = ApplicantTestData.validEntity();
        ApplicantResponseDTO expected = ApplicantTestData.validResponse();

        when(mapper.toEntity(request)).thenReturn(entity);
        when(repository.save(entity)).thenReturn(entity);
        when(mapper.toResponse(entity)).thenReturn(expected);

        // When
        ApplicantResponseDTO result = service.createApplicant(request);

        // Then
        assertThat(result).isEqualTo(expected);
        verify(validator).validate(request);
        verify(repository).save(entity);
    }

    @Test
    @DisplayName("Should throw exception when applicant not found")
    void shouldThrowExceptionWhenApplicantNotFound() {
        // Given
        UUID nonExistentId = UUID.randomUUID();
        when(repository.findById(nonExistentId)).thenReturn(Optional.empty());

        // When & Then
        assertThatThrownBy(() -> service.getApplicantById(nonExistentId))
            .isInstanceOf(EntityNotFoundException.class)
            .hasMessage("Applicant not found with ID: " + nonExistentId);
    }
}
```

#### **✅ MANDATORY Integration Test Pattern:**
```java
// MANDATORY: Class name MUST end with 'ITest'
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Tag("integration")                             // MANDATORY @Tag annotation
class ApplicantRepositoryITest extends BaseIntegrationTest {  // MANDATORY naming: *ITest.class

    @Autowired private ApplicantRepository repository;
    @Autowired private TestEntityManager entityManager;

    @Test
    @DisplayName("Should persist applicant to database with all fields")
    void shouldPersistApplicantToDatabase() {
        // Given
        Applicant applicant = Applicant.builder()
            .id(UUID.randomUUID())
            .firstName("John")
            .lastName("Doe")
            .email("<EMAIL>")
            .isActive(true)
            .createdOn(LocalDateTime.now())
            .updatedOn(LocalDateTime.now())
            .build();

        // When
        Applicant saved = repository.save(applicant);
        entityManager.flush();
        entityManager.clear();

        // Then
        Optional<Applicant> found = repository.findById(saved.getId());
        assertThat(found).isPresent();
        assertThat(found.get().getEmail()).isEqualTo("<EMAIL>");
        assertThat(found.get().getIsActive()).isTrue();
    }

    @Test
    @DisplayName("Should find applicant by email with case insensitive search")
    void shouldFindApplicantByEmailCaseInsensitive() {
        // Given
        Applicant applicant = createTestApplicant("<EMAIL>");
        repository.save(applicant);
        entityManager.flush();

        // When
        Optional<Applicant> found = repository.findByEmailIgnoreCase("<EMAIL>");

        // Then
        assertThat(found).isPresent();
        assertThat(found.get().getEmail()).isEqualTo("<EMAIL>");
    }
}
```

#### **✅ MANDATORY API Test Pattern:**
```java
// MANDATORY: Class name MUST end with 'ApiTest'
@BaseApiTest(controllers = ApplicantController.class)  // MANDATORY base class usage
@Tag("api")                                           // MANDATORY @Tag annotation
@DisplayName("Applicant Controller API Tests")
class ApplicantControllerApiTest {                    // MANDATORY naming: *ApiTest.class

    @Autowired private MockMvc mockMvc;
    @MockBean private ApplicantService applicantService;
    @Autowired private ObjectMapper objectMapper;

    @Test
    @DisplayName("GET /api/v1/applicant/{id} should return applicant details")
    void shouldReturnApplicantDetails() throws Exception {
        // Given
        UUID applicantId = UUID.randomUUID();
        ApplicantResponseDTO response = ApplicantTestData.validResponse();
        when(applicantService.getApplicantById(applicantId)).thenReturn(response);

        // When & Then
        mockMvc.perform(get("/api/v1/applicant/{id}", applicantId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isOk())
            .andExpect(jsonPath("$.id").value(response.getId().toString()))
            .andExpect(jsonPath("$.firstName").value(response.getFirstName()))
            .andExpect(jsonPath("$.email").value(response.getEmail()));

        verify(applicantService).getApplicantById(applicantId);
    }

    @Test
    @DisplayName("POST /api/v1/applicant/create should create applicant successfully")
    void shouldCreateApplicantSuccessfully() throws Exception {
        // Given
        ApplicantRequestDTO request = ApplicantTestData.validRequest();
        CreateApplicantResponseDTO response = ApplicantTestData.validCreateResponse();

        when(applicantService.createApplicant(any(ApplicantRequestDTO.class), anyList()))
            .thenReturn(response);

        // When & Then
        mockMvc.perform(multipart("/api/v1/applicant/create")
                .part(new MockPart("applicantData", objectMapper.writeValueAsBytes(request)))
                .contentType(MediaType.MULTIPART_FORM_DATA))
            .andExpect(status().isCreated())
            .andExpect(jsonPath("$.id").value(response.getId().toString()))
            .andExpect(jsonPath("$.message").value("Applicant created successfully"));
    }

    @Test
    @DisplayName("GET /api/v1/applicant/{id} should return 404 when applicant not found")
    void shouldReturn404WhenApplicantNotFound() throws Exception {
        // Given
        UUID nonExistentId = UUID.randomUUID();
        when(applicantService.getApplicantById(nonExistentId))
            .thenThrow(new EntityNotFoundException("Applicant not found"));

        // When & Then
        mockMvc.perform(get("/api/v1/applicant/{id}", nonExistentId)
                .contentType(MediaType.APPLICATION_JSON))
            .andExpect(status().isNotFound())
            .andExpect(jsonPath("$.error").value("Not Found"))
            .andExpect(jsonPath("$.message").value("Applicant not found"));
    }
}
```

#### **❌ CRITICAL Issues to Flag - NON-COMPLIANT Test Patterns:**

**🚨 IMMEDIATE REJECTION - These patterns are NOT ACCEPTABLE:**

```java
// ❌ WRONG: Missing @Tag annotation
class ApplicantServiceTest {  // Missing @Tag("unit") - REJECTED
    // Test methods...
}

// ❌ WRONG: Incorrect naming convention
@Tag("unit")
class ApplicantServiceTest {  // Should be ApplicantServiceUTest - REJECTED
    // Test methods...
}

// ❌ WRONG: Mixed test types in single class
@Tag("unit")
@Tag("integration")  // Multiple tags not allowed - REJECTED
class ApplicantServiceUTest {
    // Test methods...
}

// ❌ WRONG: JUnit 4 patterns
@RunWith(MockitoJUnitRunner.class)  // JUnit 4 - REJECTED
@Tag("unit")
class ApplicantServiceUTest {
    @Mock private ApplicantRepository repository;

    @Test
    public void testCreateApplicant() {  // JUnit 4 style - REJECTED
        // Old assertion style
        assertEquals(expected, actual);  // Should use AssertJ - REJECTED
    }
}

// ❌ WRONG: Integration test without proper base class
@SpringBootTest
@Tag("integration")
class ApplicantRepositoryITest {  // Should extend BaseIntegrationTest - REJECTED
    // Missing Testcontainers setup - REJECTED
}

// ❌ WRONG: API test without proper base annotation
@WebMvcTest(ApplicantController.class)  // Should use @BaseApiTest - REJECTED
@Tag("api")
class ApplicantControllerApiTest {
    // Test methods...
}

// ❌ WRONG: Test that won't run with Gradle tasks
@Tag("unit")
class ApplicantServiceIntegrationTest {  // Wrong naming for unit test - REJECTED
    @Autowired  // Real dependencies in unit test - REJECTED
    private ApplicantService service;
}
```

#### **🔧 MANDATORY Test Execution Validation:**

**All tests MUST pass these Gradle task executions:**

```bash
# Unit tests MUST run successfully
./gradlew unitTest
# Should execute only *UTest.class files with @Tag("unit")

# Integration tests MUST run successfully
./gradlew integrationTest
# Should execute only *ITest.class files with @Tag("integration")

# API tests MUST run successfully
./gradlew apiTest
# Should execute only *ApiTest.class files with @Tag("api")

# Combined test execution MUST work
./gradlew test
# Should run all tests with proper JaCoCo coverage
```

#### **📊 MANDATORY Coverage Requirements:**

```gradle
// JaCoCo coverage thresholds - MUST be met
jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = 0.80  // 80% minimum coverage - MANDATORY
            }
        }
    }
}
```

#### **🎯 Test Quality Gates - ALL MUST PASS:**

1. **Naming Convention Compliance**: 100% adherence to `*UTest`, `*ITest`, `*ApiTest`
2. **Tag Annotation Compliance**: 100% proper `@Tag` usage
3. **Gradle Task Compatibility**: All tests run with designated tasks
4. **Base Class Inheritance**: Proper base class usage where required
5. **JUnit 5 Platform**: No JUnit 4 patterns or annotations
6. **Coverage Threshold**: Minimum 80% line coverage
7. **Test Isolation**: Unit tests use mocks, integration tests use real components
8. **Assertion Style**: AssertJ fluent assertions preferred over JUnit assertions

---

### **STEP 11: Performance & Scalability**

#### **🎯 Validation Criteria:**
- [ ] **Response Time Optimization** - API responses < 200ms
- [ ] **Database Performance** - Query optimization
- [ ] **Caching Strategy** - Redis/Hazelcast integration
- [ ] **Async Processing** - Non-blocking operations
- [ ] **Load Testing** - Performance under load

#### **✅ Good Example:**
```java
@Service
public class EntityService {

    @Cacheable(value = "entities", key = "#id", unless = "#result == null")
    @Transactional(readOnly = true)
    public EntityResponseDTO getEntityById(UUID id) {
        return repository.findById(id)
            .map(mapper::toResponse)
            .orElseThrow(() -> new EntityNotFoundException("Entity not found"));
    }

    @CacheEvict(value = "entities", key = "#result.id")
    @Transactional
    public EntityResponseDTO createEntity(EntityRequestDTO request) {
        // Create and invalidate cache
        return processEntity(request);
    }

    @Async("taskExecutor")
    @EventListener
    public void handleEntityCreated(EntityCreatedEvent event) {
        // Async notification processing
        notificationService.sendWelcomeEmail(event.getEntityId());
    }
}

@Configuration
@EnableAsync
public class AsyncConfig {

    @Bean(name = "taskExecutor")
    public TaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(10);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("async-");
        executor.initialize();
        return executor;
    }
}
```

---

### **STEP 12: Idempotency & Safe Operations**

#### **🎯 Validation Criteria:**
- [ ] **HTTP Method Idempotency** - GET, PUT, DELETE are idempotent
- [ ] **Business Logic Idempotency** - Operations safe to retry
- [ ] **Database Idempotency** - Unique constraints and upsert patterns
- [ ] **Request Deduplication** - Idempotency keys for critical operations
- [ ] **Side Effect Management** - External calls are idempotent or compensatable

#### **✅ Good Example - Idempotent API Design:**
```java
// Idempotent entity creation with natural key
@PostMapping("/entities")
public ResponseEntity<EntityResponseDTO> createEntity(
        @RequestHeader("Idempotency-Key") String idempotencyKey,
        @Valid @RequestBody EntityRequestDTO request) {

    // Check if operation already completed
    Optional<Entity> existing = entityService.findByIdempotencyKey(idempotencyKey);
    if (existing.isPresent()) {
        // Return same result - idempotent behavior
        EntityResponseDTO response = entityMapper.toResponse(existing.get());
        return ResponseEntity.ok(response);
    }

    // Perform creation with idempotency key
    EntityResponseDTO response = entityService.createEntityIdempotent(request, idempotencyKey);
    return ResponseEntity.status(HttpStatus.CREATED).body(response);
}

// Idempotent update operation
@PutMapping("/entities/{id}")
public ResponseEntity<EntityResponseDTO> updateEntity(
        @PathVariable UUID id,
        @Valid @RequestBody EntityRequestDTO request) {

    // PUT is naturally idempotent - same request produces same result
    EntityResponseDTO response = entityService.updateEntity(id, request);
    return ResponseEntity.ok(response);
}

// Idempotent activation/deactivation
@PatchMapping("/entities/{id}/activate")
public ResponseEntity<ActivationResponseDTO> activateEntity(@PathVariable UUID id) {

    // Idempotent status change
    ActivationResponseDTO response = entityService.activateEntity(id);
    return ResponseEntity.ok(response);
}
```

#### **✅ Good Example - Idempotent Service Layer:**
```java
@Service
@Transactional
public class EntityService {

    // Idempotent creation with business key
    public EntityResponseDTO createEntityIdempotent(EntityRequestDTO request, String idempotencyKey) {
        // Try to find existing entity by business key (email, external ID, etc.)
        Optional<Entity> existingByBusinessKey = entityRepository.findByEmail(request.getEmail());
        if (existingByBusinessKey.isPresent()) {
            log.info("Entity already exists with email: {}, returning existing", request.getEmail());
            return entityMapper.toResponse(existingByBusinessKey.get());
        }

        // Check idempotency key to prevent duplicate processing
        Optional<Entity> existingByIdempotencyKey = entityRepository.findByIdempotencyKey(idempotencyKey);
        if (existingByIdempotencyKey.isPresent()) {
            log.info("Entity already created with idempotency key: {}", idempotencyKey);
            return entityMapper.toResponse(existingByIdempotencyKey.get());
        }

        // Create new entity with idempotency key
        Entity entity = entityMapper.toEntity(request);
        entity.setIdempotencyKey(idempotencyKey);
        entity.setCreatedAt(LocalDateTime.now());

        Entity saved = entityRepository.save(entity);
        log.info("Created new entity with ID: {} and idempotency key: {}", saved.getId(), idempotencyKey);

        return entityMapper.toResponse(saved);
    }

    // Idempotent status change
    public ActivationResponseDTO activateEntity(UUID entityId) {
        Entity entity = entityRepository.findById(entityId)
            .orElseThrow(() -> new EntityNotFoundException("Entity not found: " + entityId));

        // Idempotent check - if already active, return success
        if (Boolean.TRUE.equals(entity.getIsActive())) {
            log.debug("Entity {} is already active", entityId);
            return ActivationResponseDTO.builder()
                .id(entityId)
                .status("ACTIVE")
                .message("Entity is already active")
                .success(true)
                .build();
        }

        // Perform activation
        entity.setIsActive(true);
        entity.setUpdatedAt(LocalDateTime.now());
        entityRepository.save(entity);

        log.info("Successfully activated entity: {}", entityId);
        return ActivationResponseDTO.builder()
            .id(entityId)
            .status("ACTIVE")
            .message("Entity activated successfully")
            .success(true)
            .build();
    }

    // Idempotent update with optimistic locking
    public EntityResponseDTO updateEntity(UUID id, EntityRequestDTO request) {
        Entity entity = entityRepository.findById(id)
            .orElseThrow(() -> new EntityNotFoundException("Entity not found: " + id));

        // Check if update is needed (avoid unnecessary writes)
        if (isUpdateNeeded(entity, request)) {
            entityMapper.updateEntityFromRequest(entity, request);
            entity.setUpdatedAt(LocalDateTime.now());

            try {
                Entity updated = entityRepository.save(entity);
                log.info("Updated entity: {}", id);
                return entityMapper.toResponse(updated);
            } catch (OptimisticLockingFailureException e) {
                log.warn("Optimistic locking failure for entity: {}, retrying", id);
                throw new ConcurrentUpdateException("Entity was modified by another process", e);
            }
        } else {
            log.debug("No update needed for entity: {}", id);
            return entityMapper.toResponse(entity);
        }
    }

    private boolean isUpdateNeeded(Entity entity, EntityRequestDTO request) {
        return !Objects.equals(entity.getName(), request.getName()) ||
               !Objects.equals(entity.getDescription(), request.getDescription()) ||
               !Objects.equals(entity.getEmail(), request.getEmail());
    }
}
```

#### **✅ Good Example - Database Idempotency Patterns:**
```java
// Repository with idempotent operations
@Repository
public class EntityRepository {

    // Upsert operation - idempotent create or update
    public Entity upsertByEmail(Entity entity) {
        return dslContext.insertInto(ENTITY)
            .set(ENTITY.ID, entity.getId())
            .set(ENTITY.EMAIL, entity.getEmail())
            .set(ENTITY.NAME, entity.getName())
            .set(ENTITY.CREATED_AT, entity.getCreatedAt())
            .set(ENTITY.UPDATED_AT, entity.getUpdatedAt())
            .onConflict(ENTITY.EMAIL)  // Conflict on unique email
            .doUpdate()
            .set(ENTITY.NAME, entity.getName())
            .set(ENTITY.UPDATED_AT, LocalDateTime.now())
            .returning()
            .fetchOne()
            .into(Entity.class);
    }

    // Idempotent status update with conditional logic
    public boolean updateStatusIfChanged(UUID id, String newStatus) {
        int updated = dslContext.update(ENTITY)
            .set(ENTITY.STATUS, newStatus)
            .set(ENTITY.UPDATED_AT, LocalDateTime.now())
            .where(ENTITY.ID.eq(id))
            .and(ENTITY.STATUS.ne(newStatus))  // Only update if status is different
            .execute();

        return updated > 0;
    }

    // Find by idempotency key for deduplication
    public Optional<Entity> findByIdempotencyKey(String idempotencyKey) {
        return dslContext.selectFrom(ENTITY)
            .where(ENTITY.IDEMPOTENCY_KEY.eq(idempotencyKey))
            .fetchOptional()
            .map(record -> record.into(Entity.class));
    }
}

// Database schema with idempotency support
CREATE TABLE entity (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,           -- Natural key for idempotency
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'ACTIVE',
    idempotency_key VARCHAR(255) UNIQUE,          -- Request deduplication
    version INTEGER NOT NULL DEFAULT 1,          -- Optimistic locking
    created_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,

    -- Indexes for idempotent operations
    INDEX idx_entity_email (email),
    INDEX idx_entity_idempotency_key (idempotency_key),
    INDEX idx_entity_status (status)
);
```

#### **❌ Issues to Flag - Non-Idempotent Operations:**
```java
// Non-idempotent creation - creates duplicates
@PostMapping("/entities")
public ResponseEntity<EntityResponseDTO> createEntity(@RequestBody EntityRequestDTO request) {
    // Problem: No deduplication, multiple calls create multiple entities
    Entity entity = new Entity();
    entity.setId(UUID.randomUUID());  // Always generates new ID
    entity.setEmail(request.getEmail());
    entity.setCreatedAt(LocalDateTime.now());  // Always current time

    Entity saved = entityRepository.save(entity);  // No uniqueness check
    return ResponseEntity.status(HttpStatus.CREATED).body(entityMapper.toResponse(saved));
}

// Non-idempotent counter increment
@PostMapping("/entities/{id}/increment-counter")
public ResponseEntity<Void> incrementCounter(@PathVariable UUID id) {
    // Problem: Multiple calls increment counter multiple times
    Entity entity = entityRepository.findById(id).orElseThrow();
    entity.setCounter(entity.getCounter() + 1);  // Always increments
    entityRepository.save(entity);
    return ResponseEntity.ok().build();
}

// Non-idempotent external API calls
@PostMapping("/entities/{id}/send-notification")
public ResponseEntity<Void> sendNotification(@PathVariable UUID id) {
    Entity entity = entityRepository.findById(id).orElseThrow();

    // Problem: Multiple calls send multiple notifications
    notificationService.sendEmail(entity.getEmail(), "Welcome!");  // No deduplication

    // Problem: Status updated even if email fails
    entity.setNotificationSent(true);
    entityRepository.save(entity);

    return ResponseEntity.ok().build();
}

// Non-idempotent batch operations
@PostMapping("/entities/batch-update")
public ResponseEntity<BatchUpdateResponseDTO> batchUpdate(@RequestBody List<EntityUpdateDTO> updates) {
    List<String> results = new ArrayList<>();

    for (EntityUpdateDTO update : updates) {
        // Problem: Partial failure leaves system in inconsistent state
        Entity entity = entityRepository.findById(update.getId()).orElseThrow();
        entity.setName(update.getName());
        entityRepository.save(entity);
        results.add("Updated: " + update.getId());  // No rollback on later failures
    }

    return ResponseEntity.ok(new BatchUpdateResponseDTO(results));
}

// Non-idempotent money transfer
@PostMapping("/accounts/{fromId}/transfer")
public ResponseEntity<TransferResponseDTO> transferMoney(
        @PathVariable UUID fromId,
        @RequestBody TransferRequestDTO request) {

    // Problem: Multiple calls transfer money multiple times
    Account fromAccount = accountRepository.findById(fromId).orElseThrow();
    Account toAccount = accountRepository.findById(request.getToAccountId()).orElseThrow();

    // No idempotency check - dangerous for financial operations
    fromAccount.setBalance(fromAccount.getBalance().subtract(request.getAmount()));
    toAccount.setBalance(toAccount.getBalance().add(request.getAmount()));

    accountRepository.save(fromAccount);
    accountRepository.save(toAccount);

    return ResponseEntity.ok(new TransferResponseDTO("Transfer completed"));
}
```

#### **✅ Good Example - Idempotent External Service Integration:**
```java
@Service
public class NotificationService {

    // Idempotent notification with deduplication
    public void sendWelcomeEmail(UUID entityId, String email, String idempotencyKey) {
        // Check if notification already sent
        Optional<NotificationLog> existing = notificationRepository
            .findByEntityIdAndTypeAndIdempotencyKey(entityId, "WELCOME_EMAIL", idempotencyKey);

        if (existing.isPresent()) {
            log.info("Welcome email already sent for entity: {} with key: {}", entityId, idempotencyKey);
            return;  // Idempotent - no duplicate notification
        }

        try {
            // Send email with retry logic
            emailService.sendEmail(email, "Welcome!", "Welcome to our platform!");

            // Record successful notification
            NotificationLog log = NotificationLog.builder()
                .entityId(entityId)
                .type("WELCOME_EMAIL")
                .recipient(email)
                .status("SENT")
                .idempotencyKey(idempotencyKey)
                .sentAt(LocalDateTime.now())
                .build();

            notificationRepository.save(log);

        } catch (EmailServiceException e) {
            // Record failed notification for retry
            NotificationLog log = NotificationLog.builder()
                .entityId(entityId)
                .type("WELCOME_EMAIL")
                .recipient(email)
                .status("FAILED")
                .idempotencyKey(idempotencyKey)
                .errorMessage(e.getMessage())
                .sentAt(LocalDateTime.now())
                .build();

            notificationRepository.save(log);
            throw new NotificationException("Failed to send welcome email", e);
        }
    }
}

// Idempotent payment processing
@Service
public class PaymentService {

    public PaymentResponseDTO processPayment(PaymentRequestDTO request, String idempotencyKey) {
        // Check for existing payment with same idempotency key
        Optional<Payment> existingPayment = paymentRepository.findByIdempotencyKey(idempotencyKey);
        if (existingPayment.isPresent()) {
            Payment payment = existingPayment.get();
            log.info("Payment already processed with key: {}, status: {}", idempotencyKey, payment.getStatus());
            return paymentMapper.toResponse(payment);
        }

        // Create payment record first (for idempotency)
        Payment payment = Payment.builder()
            .id(UUID.randomUUID())
            .amount(request.getAmount())
            .currency(request.getCurrency())
            .customerId(request.getCustomerId())
            .idempotencyKey(idempotencyKey)
            .status(PaymentStatus.PENDING)
            .createdAt(LocalDateTime.now())
            .build();

        payment = paymentRepository.save(payment);

        try {
            // Process with external payment gateway
            PaymentGatewayResponse gatewayResponse = paymentGateway.processPayment(
                request.getAmount(),
                request.getCurrency(),
                request.getPaymentMethod(),
                idempotencyKey  // Pass idempotency key to gateway
            );

            // Update payment status
            payment.setStatus(gatewayResponse.isSuccess() ? PaymentStatus.COMPLETED : PaymentStatus.FAILED);
            payment.setGatewayTransactionId(gatewayResponse.getTransactionId());
            payment.setUpdatedAt(LocalDateTime.now());

            payment = paymentRepository.save(payment);

            return paymentMapper.toResponse(payment);

        } catch (PaymentGatewayException e) {
            // Update payment status to failed
            payment.setStatus(PaymentStatus.FAILED);
            payment.setErrorMessage(e.getMessage());
            payment.setUpdatedAt(LocalDateTime.now());

            paymentRepository.save(payment);

            throw new PaymentProcessingException("Payment processing failed", e);
        }
    }
}
```

#### **✅ Good Example - Testing Idempotent Behavior:**
```java
@SpringBootTest
@Transactional
class EntityServiceIdempotencyTest {

    @Autowired
    private EntityService entityService;

    @Autowired
    private EntityRepository entityRepository;

    @Test
    void createEntity_WithSameIdempotencyKey_ShouldReturnSameResult() {
        // Given
        EntityRequestDTO request = EntityRequestDTO.builder()
            .name("Test Entity")
            .email("<EMAIL>")
            .build();
        String idempotencyKey = "test-key-123";

        // When - First call
        EntityResponseDTO firstResponse = entityService.createEntityIdempotent(request, idempotencyKey);

        // When - Second call with same idempotency key
        EntityResponseDTO secondResponse = entityService.createEntityIdempotent(request, idempotencyKey);

        // Then - Should return same result
        assertThat(firstResponse.getId()).isEqualTo(secondResponse.getId());
        assertThat(firstResponse.getName()).isEqualTo(secondResponse.getName());
        assertThat(firstResponse.getEmail()).isEqualTo(secondResponse.getEmail());

        // Verify only one entity was created
        List<Entity> entities = entityRepository.findByEmail("<EMAIL>");
        assertThat(entities).hasSize(1);
    }

    @Test
    void activateEntity_WhenAlreadyActive_ShouldBeIdempotent() {
        // Given
        Entity entity = createTestEntity();
        entity.setIsActive(true);
        entityRepository.save(entity);

        // When - Multiple activation calls
        ActivationResponseDTO firstResponse = entityService.activateEntity(entity.getId());
        ActivationResponseDTO secondResponse = entityService.activateEntity(entity.getId());

        // Then - Both should succeed with same result
        assertThat(firstResponse.isSuccess()).isTrue();
        assertThat(secondResponse.isSuccess()).isTrue();
        assertThat(firstResponse.getStatus()).isEqualTo("ACTIVE");
        assertThat(secondResponse.getStatus()).isEqualTo("ACTIVE");

        // Verify entity is still active
        Entity updated = entityRepository.findById(entity.getId()).orElseThrow();
        assertThat(updated.getIsActive()).isTrue();
    }

    @Test
    void updateEntity_WithSameData_ShouldBeIdempotent() {
        // Given
        Entity entity = createTestEntity();
        EntityRequestDTO updateRequest = EntityRequestDTO.builder()
            .name(entity.getName())  // Same name
            .email(entity.getEmail()) // Same email
            .description(entity.getDescription()) // Same description
            .build();

        LocalDateTime originalUpdatedAt = entity.getUpdatedAt();

        // When
        EntityResponseDTO response = entityService.updateEntity(entity.getId(), updateRequest);

        // Then - Should not perform unnecessary update
        Entity updated = entityRepository.findById(entity.getId()).orElseThrow();
        assertThat(updated.getUpdatedAt()).isEqualTo(originalUpdatedAt); // No timestamp change

        assertThat(response.getName()).isEqualTo(entity.getName());
        assertThat(response.getEmail()).isEqualTo(entity.getEmail());
    }

    @Test
    void processPayment_WithSameIdempotencyKey_ShouldNotChargeTwice() {
        // Given
        PaymentRequestDTO request = PaymentRequestDTO.builder()
            .amount(new BigDecimal("100.00"))
            .currency("USD")
            .customerId(UUID.randomUUID())
            .paymentMethod("card_123")
            .build();
        String idempotencyKey = "payment-key-456";

        // When - First payment
        PaymentResponseDTO firstResponse = paymentService.processPayment(request, idempotencyKey);

        // When - Retry with same idempotency key
        PaymentResponseDTO secondResponse = paymentService.processPayment(request, idempotencyKey);

        // Then - Should return same payment, not charge twice
        assertThat(firstResponse.getId()).isEqualTo(secondResponse.getId());
        assertThat(firstResponse.getStatus()).isEqualTo(secondResponse.getStatus());

        // Verify only one payment record exists
        List<Payment> payments = paymentRepository.findByIdempotencyKey(idempotencyKey);
        assertThat(payments).hasSize(1);
    }

    @Test
    void concurrentRequests_WithSameIdempotencyKey_ShouldHandleRaceCondition() throws InterruptedException {
        // Given
        EntityRequestDTO request = EntityRequestDTO.builder()
            .name("Concurrent Test")
            .email("<EMAIL>")
            .build();
        String idempotencyKey = "concurrent-key-789";

        int threadCount = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        List<EntityResponseDTO> responses = Collections.synchronizedList(new ArrayList<>());
        List<Exception> exceptions = Collections.synchronizedList(new ArrayList<>());

        // When - Multiple concurrent requests
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    EntityResponseDTO response = entityService.createEntityIdempotent(request, idempotencyKey);
                    responses.add(response);
                } catch (Exception e) {
                    exceptions.add(e);
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await(10, TimeUnit.SECONDS);
        executor.shutdown();

        // Then - All successful responses should be identical
        assertThat(exceptions).isEmpty();
        assertThat(responses).hasSize(threadCount);

        UUID firstId = responses.get(0).getId();
        responses.forEach(response -> {
            assertThat(response.getId()).isEqualTo(firstId);
            assertThat(response.getName()).isEqualTo("Concurrent Test");
            assertThat(response.getEmail()).isEqualTo("<EMAIL>");
        });

        // Verify only one entity was created
        List<Entity> entities = entityRepository.findByEmail("<EMAIL>");
        assertThat(entities).hasSize(1);
    }

    private Entity createTestEntity() {
        Entity entity = Entity.builder()
            .id(UUID.randomUUID())
            .name("Test Entity")
            .email("<EMAIL>")
            .description("Test Description")
            .isActive(false)
            .createdAt(LocalDateTime.now())
            .updatedAt(LocalDateTime.now())
            .build();
        return entityRepository.save(entity);
    }
}

// Load testing for idempotent operations
@Test
void loadTest_IdempotentOperations_ShouldMaintainConsistency() {
    // Given
    int requestCount = 1000;
    String idempotencyKey = "load-test-key";
    EntityRequestDTO request = EntityRequestDTO.builder()
        .name("Load Test Entity")
        .email("<EMAIL>")
        .build();

    // When - High volume of identical requests
    List<CompletableFuture<EntityResponseDTO>> futures = IntStream.range(0, requestCount)
        .mapToObj(i -> CompletableFuture.supplyAsync(() ->
            entityService.createEntityIdempotent(request, idempotencyKey)))
        .collect(Collectors.toList());

    List<EntityResponseDTO> responses = futures.stream()
        .map(CompletableFuture::join)
        .collect(Collectors.toList());

    // Then - All responses should be identical
    UUID expectedId = responses.get(0).getId();
    responses.forEach(response -> {
        assertThat(response.getId()).isEqualTo(expectedId);
        assertThat(response.getName()).isEqualTo("Load Test Entity");
    });

    // Verify database consistency
    List<Entity> entities = entityRepository.findByEmail("<EMAIL>");
    assertThat(entities).hasSize(1);
}
```

---

### **STEP 13: DevOps & Deployment Readiness**

#### **🎯 Validation Criteria:**
- [ ] **Containerization** - Docker configuration
- [ ] **Configuration Management** - Environment-specific settings
- [ ] **Database Migration** - Liquibase/Flyway scripts
- [ ] **CI/CD Pipeline** - Automated deployment
- [ ] **Infrastructure as Code** - Kubernetes manifests

#### **✅ Good Example:**
```dockerfile
# Multi-stage Docker build
FROM openjdk:17-jdk-slim as builder
WORKDIR /app
COPY . .
RUN ./gradlew build -x test

FROM openjdk:17-jre-slim
WORKDIR /app
COPY --from=builder /app/build/libs/*.jar app.jar

# Security: Run as non-root user
RUN addgroup --system spring && adduser --system spring --ingroup spring
USER spring:spring

EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

ENTRYPOINT ["java", "-jar", "app.jar"]
```

```yaml
# Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: microservice-backend
  labels:
    app: microservice-backend
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: microservice-backend
  template:
    metadata:
      labels:
        app: microservice-backend
        version: v1
    spec:
      containers:
      - name: microservice-backend
        image: microservice-backend:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: db-secret
              key: url
        livenessProbe:
          httpGet:
            path: /actuator/health/liveness
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

---

## **🔧 IMPLEMENTATION GUIDE**

### **For Development Teams:**
1. **Use this checklist** for every PR review
2. **Document findings** with specific examples
3. **Provide actionable feedback** with code samples
4. **Track improvements** over time
5. **Share learnings** across team

### **For Tech Leads:**
1. **Enforce validation standards** consistently
2. **Review validation results** before approvals
3. **Identify training needs** based on common issues
4. **Update framework** based on project evolution
5. **Maintain quality metrics** and trends

---

## **📚 NEXT STEPS**

1. **Review this framework** with the development team
2. **Integrate into PR templates** and review process
3. **Set up automated checks** where possible
4. **Schedule regular assessments** of entire codebase
5. **Continuously improve** based on feedback and results

---

**This comprehensive framework ensures consistent, high-quality code that is production-ready and maintainable. Use it as a living document that evolves with your project needs.**
